"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/balicky/[slug]",{

/***/ "./pages/balicky/[slug].tsx":
/*!**********************************!*\
  !*** ./pages/balicky/[slug].tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSG: function() { return /* binding */ __N_SSG; },\n/* harmony export */   \"default\": function() { return /* binding */ PackagePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/LoadingSpinner */ \"./components/LoadingSpinner.tsx\");\n/* harmony import */ var _components_Breadcrumb__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/Breadcrumb */ \"./components/Breadcrumb.tsx\");\n/* harmony import */ var _utils_analytics__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../utils/analytics */ \"./utils/analytics.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nvar __N_SSG = true;\nfunction PackagePage(param) {\n    let { packageData } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        if (packageData) {\n            (0,_utils_analytics__WEBPACK_IMPORTED_MODULE_7__.trackPageView)(\"\".concat(packageData.name, \" - Detail\"));\n        }\n    }, [\n        packageData\n    ]);\n    if (router.isFallback) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        size: \"lg\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 213,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Nač\\xedtavam bal\\xedček...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                lineNumber: 212,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n            lineNumber: 211,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: [\n                            packageData.name,\n                            \" - eSpomienka\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"\".concat(packageData.name, \" - \").concat(packageData.description)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"keywords\",\n                        content: \"memorial, spomienky, \".concat(packageData.slug, \", video spomienky, QR k\\xf3d, hrob\")\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:title\",\n                        content: \"\".concat(packageData.name, \" - eSpomienka\")\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:description\",\n                        content: packageData.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:type\",\n                        content: \"website\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:card\",\n                        content: \"summary_large_image\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:title\",\n                        content: \"\".concat(packageData.name, \" - eSpomienka\")\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:description\",\n                        content: packageData.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"canonical\",\n                        href: \"https://espomienka.sk/balicky/\".concat(packageData.slug)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        children: JSON.stringify({\n                            \"@context\": \"https://schema.org\",\n                            \"@type\": \"Service\",\n                            \"name\": packageData.name,\n                            \"description\": packageData.description,\n                            \"provider\": {\n                                \"@type\": \"Organization\",\n                                \"name\": \"eSpomienka\",\n                                \"telephone\": \"+421951553464\",\n                                \"email\": \"<EMAIL>\"\n                            },\n                            \"offers\": {\n                                \"@type\": \"Offer\",\n                                \"price\": packageData.price.replace(\"€\", \"\"),\n                                \"priceCurrency\": \"EUR\"\n                            }\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 233,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"bg-primary shadow-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/\",\n                                            className: \"font-playfair text-2xl font-semibold text-white\",\n                                            children: \"eSpomienka\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"flex items-center space-x-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/\",\n                                                className: \"text-white hover:text-gold transition-colors\",\n                                                children: \"Domov\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/blog\",\n                                                className: \"text-white hover:text-gold transition-colors\",\n                                                children: \"Blog\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"tel:+421951553464\",\n                                                className: \"text-white hover:text-gold transition-colors\",\n                                                children: \"+421 951 553 464\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                lineNumber: 258,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 256,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Breadcrumb__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        items: [\n                            {\n                                label: \"Domov\",\n                                href: \"/\"\n                            },\n                            {\n                                label: \"Bal\\xedčky\",\n                                href: \"/#packages\"\n                            },\n                            {\n                                label: packageData.name\n                            }\n                        ]\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 280,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-gradient-to-r from-primary to-primary-dark\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-6xl mb-4\",\n                                    children: packageData.emoji\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl md:text-5xl font-playfair font-bold text-white mb-4\",\n                                    children: packageData.name\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-5xl font-bold text-gold mb-6\",\n                                    children: packageData.price\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-200 max-w-2xl mx-auto mb-8\",\n                                    children: packageData.description\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"tel:+421951553464\",\n                                    className: \"bg-gold hover:bg-gold-dark text-white px-8 py-3 rounded-lg font-semibold transition-colors inline-block\",\n                                    onClick: ()=>(0,_utils_analytics__WEBPACK_IMPORTED_MODULE_7__.trackCTAClick)(\"phone\", \"hero\"),\n                                    children: \"Objednať konzult\\xe1ciu\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-playfair font-bold text-center text-gray-800 mb-12\",\n                                    children: \"Čo dostanete\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                    children: packageData.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-6 h-6 bg-gold rounded-full flex items-center justify-center flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-white\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                            lineNumber: 320,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-700\",\n                                                    children: feature\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 310,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-cream\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-playfair font-bold text-center text-gray-800 mb-12\",\n                                    children: \"Ako to funguje\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-4xl mx-auto\",\n                                    children: packageData.process.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-4 mb-8 last:mb-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gold rounded-full flex items-center justify-center flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-bold\",\n                                                        children: step.step\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-semibold text-gray-800 mb-2\",\n                                                            children: step.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: step.description\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 332,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 331,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-playfair font-bold text-center text-gray-800 mb-12\",\n                                    children: \"Uk\\xe1žky našej pr\\xe1ce\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                    children: [\n                                        1,\n                                        2,\n                                        3\n                                    ].map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-100 rounded-lg p-8 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-gray-300 rounded-full mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-600 mb-2\",\n                                                    children: \"Pripravujeme uk\\xe1žky\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500\",\n                                                    children: \"Segera hadir\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, item, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 354,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 353,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-cream\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-playfair font-bold text-center text-gray-800 mb-12\",\n                                    children: \"Porovnanie bal\\xedčkov\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-4xl mx-auto bg-white rounded-lg shadow-lg overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-4 gap-4 p-6 bg-gray-50 font-semibold\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: \"Funkcie\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: \"Z\\xe1kladn\\xe1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: \"Premium\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: \"Exclusive\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"divide-y\",\n                                            children: [\n                                                \"Memorial webstr\\xe1nka\",\n                                                \"QR k\\xf3d\",\n                                                \"Video spomienky\",\n                                                \"Neobmedzen\\xe9 fotky\",\n                                                \"Kovov\\xe1/Granitov\\xe1 tabuľka\",\n                                                \"VIP servis\"\n                                            ].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-4 gap-4 p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-700\",\n                                                            children: feature\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: index < 2 ? \"✅\" : \"❌\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: index < 5 ? \"✅\" : \"❌\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                            lineNumber: 397,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: \"✅\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 372,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 371,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-primary\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-playfair font-bold text-white mb-6\",\n                                    children: \"Pripraven\\xed začať?\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-200 max-w-2xl mx-auto mb-8\",\n                                    children: \"Kontaktujte n\\xe1s pre bezplatn\\xfa konzult\\xe1ciu. Plat\\xedte až keď ste \\xfaplne spokojn\\xed.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"tel:+421951553464\",\n                                            className: \"bg-gold hover:bg-gold-dark text-white px-8 py-3 rounded-lg font-semibold transition-colors\",\n                                            children: \"Zavolať +421 951 553 464\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"mailto:<EMAIL>\",\n                                            className: \"bg-white hover:bg-gray-100 text-primary px-8 py-3 rounded-lg font-semibold transition-colors\",\n                                            children: \"Nap\\xedsať email\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 410,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 409,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-playfair font-bold text-center text-gray-800 mb-12\",\n                                    children: \"Často kladen\\xe9 ot\\xe1zky\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-3xl mx-auto space-y-6\",\n                                    children: packageData.faq.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 rounded-lg p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-800 mb-3\",\n                                                    children: item.question\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: item.answer\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 440,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 436,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 435,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"tel:+421951553464\",\n                        className: \"sticky-cta bg-gold hover:bg-gold-dark text-white px-6 py-3 rounded-full font-semibold\",\n                        children: \"\\uD83D\\uDCDE Zavolať\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 456,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"bg-gray-800 text-white py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-3 mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-playfair text-2xl font-semibold\",\n                                        children: \"eSpomienka\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400\",\n                                    children: \"\\xa9 2025 eSpomienka. Všetky pr\\xe1va vyhraden\\xe9.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 469,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 465,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 464,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                lineNumber: 254,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(PackagePage, \"vQduR7x+OPXj6PSmJyFnf+hU7bg=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = PackagePage;\nvar _c;\n$RefreshReg$(_c, \"PackagePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/balicky/[slug].tsx\n"));

/***/ })

});