"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/balicky/[slug]",{

/***/ "./pages/balicky/[slug].tsx":
/*!**********************************!*\
  !*** ./pages/balicky/[slug].tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSG: function() { return /* binding */ __N_SSG; },\n/* harmony export */   \"default\": function() { return /* binding */ PackagePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/LoadingSpinner */ \"./components/LoadingSpinner.tsx\");\n/* harmony import */ var _components_Breadcrumb__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/Breadcrumb */ \"./components/Breadcrumb.tsx\");\n/* harmony import */ var _utils_analytics__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../utils/analytics */ \"./utils/analytics.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nvar __N_SSG = true;\nfunction PackagePage(param) {\n    let { packageData } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        if (packageData) {\n            (0,_utils_analytics__WEBPACK_IMPORTED_MODULE_7__.trackPageView)(\"\".concat(packageData.name, \" - Detail\"));\n        }\n    }, [\n        packageData\n    ]);\n    if (router.isFallback) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        size: \"lg\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 213,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Nač\\xedtavam bal\\xedček...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                lineNumber: 212,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n            lineNumber: 211,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: [\n                            packageData.name,\n                            \" - eSpomienka\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"\".concat(packageData.name, \" - \").concat(packageData.description)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"keywords\",\n                        content: \"memorial, spomienky, \".concat(packageData.slug, \", video spomienky, QR k\\xf3d, hrob\")\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:title\",\n                        content: \"\".concat(packageData.name, \" - eSpomienka\")\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:description\",\n                        content: packageData.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:type\",\n                        content: \"website\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:card\",\n                        content: \"summary_large_image\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:title\",\n                        content: \"\".concat(packageData.name, \" - eSpomienka\")\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:description\",\n                        content: packageData.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"canonical\",\n                        href: \"https://espomienka.sk/balicky/\".concat(packageData.slug)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        dangerouslySetInnerHTML: {\n                            __html: JSON.stringify({\n                                \"@context\": \"https://schema.org\",\n                                \"@type\": \"Service\",\n                                \"name\": packageData.name,\n                                \"description\": packageData.description,\n                                \"provider\": {\n                                    \"@type\": \"Organization\",\n                                    \"name\": \"eSpomienka\",\n                                    \"telephone\": \"+421951553464\",\n                                    \"email\": \"<EMAIL>\"\n                                },\n                                \"offers\": {\n                                    \"@type\": \"Offer\",\n                                    \"price\": packageData.price.replace(\"€\", \"\"),\n                                    \"priceCurrency\": \"EUR\"\n                                }\n                            })\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 233,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"bg-primary shadow-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/\",\n                                            className: \"font-playfair text-2xl font-semibold text-white\",\n                                            children: \"eSpomienka\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"flex items-center space-x-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/\",\n                                                className: \"text-white hover:text-gold transition-colors\",\n                                                children: \"Domov\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/blog\",\n                                                className: \"text-white hover:text-gold transition-colors\",\n                                                children: \"Blog\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"tel:+421951553464\",\n                                                className: \"text-white hover:text-gold transition-colors\",\n                                                children: \"+421 951 553 464\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 259,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Breadcrumb__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        items: [\n                            {\n                                label: \"Domov\",\n                                href: \"/\"\n                            },\n                            {\n                                label: \"Bal\\xedčky\",\n                                href: \"/#packages\"\n                            },\n                            {\n                                label: packageData.name\n                            }\n                        ]\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-gradient-to-r from-primary to-primary-dark\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-6xl mb-4\",\n                                    children: packageData.emoji\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl md:text-5xl font-playfair font-bold text-white mb-4\",\n                                    children: packageData.name\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-5xl font-bold text-gold mb-6\",\n                                    children: packageData.price\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-200 max-w-2xl mx-auto mb-8\",\n                                    children: packageData.description\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"tel:+421951553464\",\n                                    className: \"bg-gold hover:bg-gold-dark text-white px-8 py-3 rounded-lg font-semibold transition-colors inline-block\",\n                                    onClick: ()=>(0,_utils_analytics__WEBPACK_IMPORTED_MODULE_7__.trackCTAClick)(\"phone\", \"hero\"),\n                                    children: \"Objednať konzult\\xe1ciu\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 293,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 292,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-playfair font-bold text-center text-gray-800 mb-12\",\n                                    children: \"Čo dostanete\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                    children: packageData.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-6 h-6 bg-gold rounded-full flex items-center justify-center flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-white\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-700\",\n                                                    children: feature\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 314,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 313,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-cream\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-playfair font-bold text-center text-gray-800 mb-12\",\n                                    children: \"Ako to funguje\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-4xl mx-auto\",\n                                    children: packageData.process.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-4 mb-8 last:mb-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gold rounded-full flex items-center justify-center flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-bold\",\n                                                        children: step.step\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-semibold text-gray-800 mb-2\",\n                                                            children: step.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: step.description\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 334,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-playfair font-bold text-center text-gray-800 mb-12\",\n                                    children: \"Uk\\xe1žky našej pr\\xe1ce\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                    children: [\n                                        1,\n                                        2,\n                                        3\n                                    ].map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-100 rounded-lg p-8 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-gray-300 rounded-full mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-600 mb-2\",\n                                                    children: \"Pripravujeme uk\\xe1žky\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500\",\n                                                    children: \"Segera hadir\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, item, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 357,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 356,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-cream\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-playfair font-bold text-center text-gray-800 mb-12\",\n                                    children: \"Porovnanie bal\\xedčkov\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-4xl mx-auto bg-white rounded-lg shadow-lg overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-4 gap-4 p-6 bg-gray-50 font-semibold\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: \"Funkcie\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: \"Z\\xe1kladn\\xe1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: \"Premium\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: \"Exclusive\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"divide-y\",\n                                            children: [\n                                                \"Memorial webstr\\xe1nka\",\n                                                \"QR k\\xf3d\",\n                                                \"Video spomienky\",\n                                                \"Neobmedzen\\xe9 fotky\",\n                                                \"Kovov\\xe1/Granitov\\xe1 tabuľka\",\n                                                \"VIP servis\"\n                                            ].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-4 gap-4 p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-700\",\n                                                            children: feature\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                            lineNumber: 396,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: index < 2 ? \"✅\" : \"❌\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                            lineNumber: 397,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: index < 5 ? \"✅\" : \"❌\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: \"✅\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                            lineNumber: 403,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 375,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 374,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-primary\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-playfair font-bold text-white mb-6\",\n                                    children: \"Pripraven\\xed začať?\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-200 max-w-2xl mx-auto mb-8\",\n                                    children: \"Kontaktujte n\\xe1s pre bezplatn\\xfa konzult\\xe1ciu. Plat\\xedte až keď ste \\xfaplne spokojn\\xed.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"tel:+421951553464\",\n                                            className: \"bg-gold hover:bg-gold-dark text-white px-8 py-3 rounded-lg font-semibold transition-colors\",\n                                            children: \"Zavolať +421 951 553 464\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"mailto:<EMAIL>\",\n                                            className: \"bg-white hover:bg-gray-100 text-primary px-8 py-3 rounded-lg font-semibold transition-colors\",\n                                            children: \"Nap\\xedsať email\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 413,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 412,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-playfair font-bold text-center text-gray-800 mb-12\",\n                                    children: \"Často kladen\\xe9 ot\\xe1zky\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 440,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-3xl mx-auto space-y-6\",\n                                    children: packageData.faq.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 rounded-lg p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-800 mb-3\",\n                                                    children: item.question\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: item.answer\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 439,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 438,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"tel:+421951553464\",\n                        className: \"sticky-cta bg-gold hover:bg-gold-dark text-white px-6 py-3 rounded-full font-semibold\",\n                        children: \"\\uD83D\\uDCDE Zavolať\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 459,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"bg-gray-800 text-white py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-3 mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-playfair text-2xl font-semibold\",\n                                        children: \"eSpomienka\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 469,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400\",\n                                    children: \"\\xa9 2025 eSpomienka. Všetky pr\\xe1va vyhraden\\xe9.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 468,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 467,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                lineNumber: 257,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(PackagePage, \"vQduR7x+OPXj6PSmJyFnf+hU7bg=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = PackagePage;\nvar _c;\n$RefreshReg$(_c, \"PackagePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9iYWxpY2t5L1tzbHVnXS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDNEI7QUFDQTtBQUNXO0FBQ047QUFDMkI7QUFDUjtBQUNnQjs7QUFnTXJELFNBQVNRLFlBQVksS0FBaUM7UUFBakMsRUFBRUMsV0FBVyxFQUFvQixHQUFqQzs7SUFDbEMsTUFBTUMsU0FBU1Isc0RBQVNBO0lBRXhCQyxnREFBU0EsQ0FBQztRQUNSLElBQUlNLGFBQWE7WUFDZkgsK0RBQWFBLENBQUMsR0FBb0IsT0FBakJHLFlBQVlFLElBQUksRUFBQztRQUNwQztJQUNGLEdBQUc7UUFBQ0Y7S0FBWTtJQUVoQixJQUFJQyxPQUFPRSxVQUFVLEVBQUU7UUFDckIscUJBQ0UsOERBQUNDO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ1Ysa0VBQWNBO3dCQUFDVyxNQUFLOzs7Ozs7a0NBQ3JCLDhEQUFDQzt3QkFBRUYsV0FBVTtrQ0FBcUI7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBSTFDO0lBRUEscUJBQ0U7OzBCQUNFLDhEQUFDZCxrREFBSUE7O2tDQUNILDhEQUFDaUI7OzRCQUFPUixZQUFZRSxJQUFJOzRCQUFDOzs7Ozs7O2tDQUN6Qiw4REFBQ087d0JBQUtQLE1BQUs7d0JBQWNRLFNBQVMsR0FBeUJWLE9BQXRCQSxZQUFZRSxJQUFJLEVBQUMsT0FBNkIsT0FBeEJGLFlBQVlXLFdBQVc7Ozs7OztrQ0FDbEYsOERBQUNGO3dCQUFLUCxNQUFLO3dCQUFXUSxTQUFTLHdCQUF5QyxPQUFqQlYsWUFBWVksSUFBSSxFQUFDOzs7Ozs7a0NBQ3hFLDhEQUFDSDt3QkFBS0ksVUFBUzt3QkFBV0gsU0FBUyxHQUFvQixPQUFqQlYsWUFBWUUsSUFBSSxFQUFDOzs7Ozs7a0NBQ3ZELDhEQUFDTzt3QkFBS0ksVUFBUzt3QkFBaUJILFNBQVNWLFlBQVlXLFdBQVc7Ozs7OztrQ0FDaEUsOERBQUNGO3dCQUFLSSxVQUFTO3dCQUFVSCxTQUFROzs7Ozs7a0NBQ2pDLDhEQUFDRDt3QkFBS1AsTUFBSzt3QkFBZVEsU0FBUTs7Ozs7O2tDQUNsQyw4REFBQ0Q7d0JBQUtQLE1BQUs7d0JBQWdCUSxTQUFTLEdBQW9CLE9BQWpCVixZQUFZRSxJQUFJLEVBQUM7Ozs7OztrQ0FDeEQsOERBQUNPO3dCQUFLUCxNQUFLO3dCQUFzQlEsU0FBU1YsWUFBWVcsV0FBVzs7Ozs7O2tDQUNqRSw4REFBQ0c7d0JBQUtDLEtBQUk7d0JBQVlDLE1BQU0saUNBQWtELE9BQWpCaEIsWUFBWVksSUFBSTs7Ozs7O2tDQUM3RSw4REFBQ0s7d0JBQ0NDLE1BQUs7d0JBQ0xDLHlCQUF5Qjs0QkFDdkJDLFFBQVFDLEtBQUtDLFNBQVMsQ0FBQztnQ0FDckIsWUFBWTtnQ0FDWixTQUFTO2dDQUNULFFBQVF0QixZQUFZRSxJQUFJO2dDQUN4QixlQUFlRixZQUFZVyxXQUFXO2dDQUN0QyxZQUFZO29DQUNWLFNBQVM7b0NBQ1QsUUFBUTtvQ0FDUixhQUFhO29DQUNiLFNBQVM7Z0NBQ1g7Z0NBQ0EsVUFBVTtvQ0FDUixTQUFTO29DQUNULFNBQVNYLFlBQVl1QixLQUFLLENBQUNDLE9BQU8sQ0FBQyxLQUFLO29DQUN4QyxpQkFBaUI7Z0NBQ25COzRCQUNGO3dCQUNGOzs7Ozs7Ozs7Ozs7MEJBSUosOERBQUNwQjtnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUNvQjt3QkFBT3BCLFdBQVU7a0NBQ2hCLDRFQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ2Isa0RBQUlBOzRDQUFDd0IsTUFBSzs0Q0FBSVgsV0FBVTtzREFBa0Q7Ozs7Ozs7Ozs7O2tEQUk3RSw4REFBQ3FCO3dDQUFJckIsV0FBVTs7MERBQ2IsOERBQUNiLGtEQUFJQTtnREFBQ3dCLE1BQUs7Z0RBQUlYLFdBQVU7MERBQStDOzs7Ozs7MERBR3hFLDhEQUFDYixrREFBSUE7Z0RBQUN3QixNQUFLO2dEQUFRWCxXQUFVOzBEQUErQzs7Ozs7OzBEQUc1RSw4REFBQ3NCO2dEQUFFWCxNQUFLO2dEQUFvQlgsV0FBVTswREFBK0M7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBUzdGLDhEQUFDVCw4REFBVUE7d0JBQ1RnQyxPQUFPOzRCQUNMO2dDQUFFQyxPQUFPO2dDQUFTYixNQUFNOzRCQUFJOzRCQUM1QjtnQ0FBRWEsT0FBTztnQ0FBV2IsTUFBTTs0QkFBYTs0QkFDdkM7Z0NBQUVhLE9BQU83QixZQUFZRSxJQUFJOzRCQUFDO3lCQUMzQjs7Ozs7O2tDQUlILDhEQUFDNEI7d0JBQVF6QixXQUFVO2tDQUNqQiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FBaUJMLFlBQVkrQixLQUFLOzs7Ozs7OENBQ2pELDhEQUFDQztvQ0FBRzNCLFdBQVU7OENBQ1hMLFlBQVlFLElBQUk7Ozs7Ozs4Q0FFbkIsOERBQUNFO29DQUFJQyxXQUFVOzhDQUFxQ0wsWUFBWXVCLEtBQUs7Ozs7Ozs4Q0FDckUsOERBQUNoQjtvQ0FBRUYsV0FBVTs4Q0FDVkwsWUFBWVcsV0FBVzs7Ozs7OzhDQUUxQiw4REFBQ2dCO29DQUNDWCxNQUFLO29DQUNMWCxXQUFVO29DQUNWNEIsU0FBUyxJQUFNbkMsK0RBQWFBLENBQUMsU0FBUzs4Q0FDdkM7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU9MLDhEQUFDZ0M7d0JBQVF6QixXQUFVO2tDQUNqQiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDNkI7b0NBQUc3QixXQUFVOzhDQUFtRTs7Ozs7OzhDQUdqRiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ1pMLFlBQVltQyxRQUFRLENBQUNDLEdBQUcsQ0FBQyxDQUFDQyxTQUFTQyxzQkFDbEMsOERBQUNsQzs0Q0FBZ0JDLFdBQVU7OzhEQUN6Qiw4REFBQ0Q7b0RBQUlDLFdBQVU7OERBQ2IsNEVBQUNrQzt3REFBSWxDLFdBQVU7d0RBQXFCbUMsTUFBSzt3REFBZUMsU0FBUTtrRUFDOUQsNEVBQUNDOzREQUFLQyxVQUFTOzREQUFVQyxHQUFFOzREQUFxSEMsVUFBUzs7Ozs7Ozs7Ozs7Ozs7Ozs4REFHN0osOERBQUNDO29EQUFLekMsV0FBVTs4REFBaUJnQzs7Ozs7OzsyQ0FOekJDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBY2xCLDhEQUFDUjt3QkFBUXpCLFdBQVU7a0NBQ2pCLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUM2QjtvQ0FBRzdCLFdBQVU7OENBQW1FOzs7Ozs7OENBR2pGLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDWkwsWUFBWStDLE9BQU8sQ0FBQ1gsR0FBRyxDQUFDLENBQUNZLE1BQU1WLHNCQUM5Qiw4REFBQ2xDOzRDQUFnQkMsV0FBVTs7OERBQ3pCLDhEQUFDRDtvREFBSUMsV0FBVTs4REFDYiw0RUFBQ3lDO3dEQUFLekMsV0FBVTtrRUFBd0IyQyxLQUFLQSxJQUFJOzs7Ozs7Ozs7Ozs4REFFbkQsOERBQUM1Qzs7c0VBQ0MsOERBQUM2Qzs0REFBRzVDLFdBQVU7c0VBQTRDMkMsS0FBS3hDLEtBQUs7Ozs7OztzRUFDcEUsOERBQUNEOzREQUFFRixXQUFVO3NFQUFpQjJDLEtBQUtyQyxXQUFXOzs7Ozs7Ozs7Ozs7OzJDQU54QzJCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBZWxCLDhEQUFDUjt3QkFBUXpCLFdBQVU7a0NBQ2pCLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUM2QjtvQ0FBRzdCLFdBQVU7OENBQW1FOzs7Ozs7OENBR2pGLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDWjt3Q0FBQzt3Q0FBRzt3Q0FBRztxQ0FBRSxDQUFDK0IsR0FBRyxDQUFDLENBQUNjLHFCQUNkLDhEQUFDOUM7NENBQWVDLFdBQVU7OzhEQUN4Qiw4REFBQ0Q7b0RBQUlDLFdBQVU7Ozs7Ozs4REFDZiw4REFBQzRDO29EQUFHNUMsV0FBVTs4REFBMkM7Ozs7Ozs4REFDekQsOERBQUNFO29EQUFFRixXQUFVOzhEQUFnQjs7Ozs7OzsyQ0FIckI2Qzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQVdsQiw4REFBQ3BCO3dCQUFRekIsV0FBVTtrQ0FDakIsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQzZCO29DQUFHN0IsV0FBVTs4Q0FBbUU7Ozs7Ozs4Q0FHakYsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDs4REFBSTs7Ozs7OzhEQUNMLDhEQUFDQTtvREFBSUMsV0FBVTs4REFBYzs7Ozs7OzhEQUM3Qiw4REFBQ0Q7b0RBQUlDLFdBQVU7OERBQWM7Ozs7Ozs4REFDN0IsOERBQUNEO29EQUFJQyxXQUFVOzhEQUFjOzs7Ozs7Ozs7Ozs7c0RBRS9CLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDWjtnREFDQztnREFDQTtnREFDQTtnREFDQTtnREFDQTtnREFDQTs2Q0FDRCxDQUFDK0IsR0FBRyxDQUFDLENBQUNDLFNBQVNDLHNCQUNkLDhEQUFDbEM7b0RBQWdCQyxXQUFVOztzRUFDekIsOERBQUNEOzREQUFJQyxXQUFVO3NFQUFpQmdDOzs7Ozs7c0VBQ2hDLDhEQUFDakM7NERBQUlDLFdBQVU7c0VBQ1ppQyxRQUFRLElBQUksTUFBTTs7Ozs7O3NFQUVyQiw4REFBQ2xDOzREQUFJQyxXQUFVO3NFQUNaaUMsUUFBUSxJQUFJLE1BQU07Ozs7OztzRUFFckIsOERBQUNsQzs0REFBSUMsV0FBVTtzRUFBYzs7Ozs7OzttREFSckJpQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQWlCcEIsOERBQUNSO3dCQUFRekIsV0FBVTtrQ0FDakIsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQzZCO29DQUFHN0IsV0FBVTs4Q0FBbUQ7Ozs7Ozs4Q0FHakUsOERBQUNFO29DQUFFRixXQUFVOzhDQUErQzs7Ozs7OzhDQUc1RCw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDc0I7NENBQ0NYLE1BQUs7NENBQ0xYLFdBQVU7c0RBQ1g7Ozs7OztzREFHRCw4REFBQ3NCOzRDQUNDWCxNQUFLOzRDQUNMWCxXQUFVO3NEQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FRUCw4REFBQ3lCO3dCQUFRekIsV0FBVTtrQ0FDakIsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQzZCO29DQUFHN0IsV0FBVTs4Q0FBbUU7Ozs7Ozs4Q0FHakYsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNaTCxZQUFZbUQsR0FBRyxDQUFDZixHQUFHLENBQUMsQ0FBQ2MsTUFBTVosc0JBQzFCLDhEQUFDbEM7NENBQWdCQyxXQUFVOzs4REFDekIsOERBQUM0QztvREFBRzVDLFdBQVU7OERBQ1g2QyxLQUFLRSxRQUFROzs7Ozs7OERBRWhCLDhEQUFDN0M7b0RBQUVGLFdBQVU7OERBQ1Y2QyxLQUFLRyxNQUFNOzs7Ozs7OzJDQUxOZjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQWNsQiw4REFBQ1g7d0JBQ0NYLE1BQUs7d0JBQ0xYLFdBQVU7a0NBQ1g7Ozs7OztrQ0FLRCw4REFBQ2lEO3dCQUFPakQsV0FBVTtrQ0FDaEIsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUN5Qzt3Q0FBS3pDLFdBQVU7a0RBQXVDOzs7Ozs7Ozs7Ozs4Q0FFekQsOERBQUNFO29DQUFFRixXQUFVOzhDQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVF6QztHQXhSd0JOOztRQUNQTixrREFBU0E7OztLQURGTSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9wYWdlcy9iYWxpY2t5L1tzbHVnXS50c3g/NjAwMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBHZXRTdGF0aWNQYXRocywgR2V0U3RhdGljUHJvcHMgfSBmcm9tICduZXh0J1xuaW1wb3J0IEhlYWQgZnJvbSAnbmV4dC9oZWFkJ1xuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJ1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9yb3V0ZXInXG5pbXBvcnQgeyB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCBMb2FkaW5nU3Bpbm5lciBmcm9tICcuLi8uLi9jb21wb25lbnRzL0xvYWRpbmdTcGlubmVyJ1xuaW1wb3J0IEJyZWFkY3J1bWIgZnJvbSAnLi4vLi4vY29tcG9uZW50cy9CcmVhZGNydW1iJ1xuaW1wb3J0IHsgdHJhY2tQYWdlVmlldywgdHJhY2tDVEFDbGljayB9IGZyb20gJy4uLy4uL3V0aWxzL2FuYWx5dGljcydcblxuaW50ZXJmYWNlIFBhY2thZ2VEYXRhIHtcbiAgc2x1Zzogc3RyaW5nXG4gIG5hbWU6IHN0cmluZ1xuICBwcmljZTogc3RyaW5nXG4gIGVtb2ppOiBzdHJpbmdcbiAgc2hvcnREZXNjcmlwdGlvbjogc3RyaW5nXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmdcbiAgZmVhdHVyZXM6IHN0cmluZ1tdXG4gIHByb2Nlc3M6IEFycmF5PHtcbiAgICBzdGVwOiBudW1iZXJcbiAgICB0aXRsZTogc3RyaW5nXG4gICAgZGVzY3JpcHRpb246IHN0cmluZ1xuICB9PlxuICBpZGVhbEZvcjogc3RyaW5nW11cbiAgY29tcGFyaXNvbjoge1xuICAgIGJhc2ljOiBib29sZWFuXG4gICAgcHJlbWl1bTogYm9vbGVhblxuICAgIGV4Y2x1c2l2ZTogYm9vbGVhblxuICB9XG4gIGZhcTogQXJyYXk8e1xuICAgIHF1ZXN0aW9uOiBzdHJpbmdcbiAgICBhbnN3ZXI6IHN0cmluZ1xuICB9PlxufVxuXG5jb25zdCBwYWNrYWdlc0RhdGE6IFJlY29yZDxzdHJpbmcsIFBhY2thZ2VEYXRhPiA9IHtcbiAgemFrbGFkbmE6IHtcbiAgICBzbHVnOiAnemFrbGFkbmEnLFxuICAgIG5hbWU6ICdTUE9NSUVOS0EgWsOha2xhZG7DoScsXG4gICAgcHJpY2U6ICcyOTnigqwnLFxuICAgIGVtb2ppOiAn8J+MuCcsXG4gICAgc2hvcnREZXNjcmlwdGlvbjogJ0TDtHN0b2puw6Egb25saW5lIHNwb21pZW5rYSBzIGVsZWdhbnRuw71tIHJpZcWhZW7DrW0nLFxuICAgIGRlc2NyaXB0aW9uOiAnWsOha2xhZG7DvSBiYWzDrcSNZWsgcHJlIHJvZGlueSwga3RvcsOpIGNoY8O6IHZ5dHZvcmnFpSBrcsOhc251IGRpZ2l0w6FsbnUgc3BvbWllbmt1IG5hIHN2b2pobyBtaWxvdmFuw6loby4gT2JzYWh1amUgdsWhZXRrbyBwb3RyZWJuw6kgcHJlIGTDtHN0b2puw7ogb25saW5lIHByZXplbnTDoWNpdS4nLFxuICAgIGZlYXR1cmVzOiBbXG4gICAgICAnTWVtb3JpYWwgd2Vic3Ryw6Fua2EnLFxuICAgICAgJ0HFviAxNSBmb3RvZ3JhZmnDrScsXG4gICAgICAnxb1pdm90b3BpcycsXG4gICAgICAnR3Vlc3Rib29rIHByZSBrb25kb2xlbmNpZScsXG4gICAgICAnWsOha2xhZG7DvSBlbGVnYW50bsO9IGRpemFqbicsXG4gICAgICAnUVIga8OzZCBuw6FsZXBrYScsXG4gICAgICAnU1NMIGNlcnRpZmlrw6F0JyxcbiAgICAgICdNb2JpbG7DoSBvcHRpbWFsaXrDoWNpYSdcbiAgICBdLFxuICAgIHByb2Nlc3M6IFtcbiAgICAgIHsgc3RlcDogMSwgdGl0bGU6ICdLb256dWx0w6FjaWEnLCBkZXNjcmlwdGlvbjogJ0JlenBsYXRuw6Ega29uenVsdMOhY2lhIG8gdmHFoWljaCBwb3RyZWLDoWNoJyB9LFxuICAgICAgeyBzdGVwOiAyLCB0aXRsZTogJ01hdGVyacOhbHknLCBkZXNjcmlwdGlvbjogJ1pocm9tYcW+ZGVuaWUgZm90b2dyYWZpw60gYSBpbmZvcm3DoWNpw60nIH0sXG4gICAgICB7IHN0ZXA6IDMsIHRpdGxlOiAnVHZvcmJhJywgZGVzY3JpcHRpb246ICdWeXR2b3JlbmllIG1lbW9yaWFsIHdlYnN0csOhbmt5JyB9LFxuICAgICAgeyBzdGVwOiA0LCB0aXRsZTogJ1NjaHbDoWxlbmllJywgZGVzY3JpcHRpb246ICdQcmV6ZW50w6FjaWEgYSDDunByYXZ5IHBvZMS+YSDFvmVsYW7DrScgfSxcbiAgICAgIHsgc3RlcDogNSwgdGl0bGU6ICdTcHVzdGVuaWUnLCBkZXNjcmlwdGlvbjogJ0FrdGl2w6FjaWEgd2Vic3Ryw6Fua3kgYSBRUiBrw7NkdScgfSxcbiAgICAgIHsgc3RlcDogNiwgdGl0bGU6ICdQbGF0YmEnLCBkZXNjcmlwdGlvbjogJ1BsYXRiYSBhxb4gcG8gw7pwbG5laiBzcG9rb2pub3N0aScgfVxuICAgIF0sXG4gICAgaWRlYWxGb3I6IFtcbiAgICAgICdSb2RpbnkgaMS+YWRhasO6Y2UgamVkbm9kdWNow6kgZWxlZ2FudG7DqSByaWXFoWVuaWUnLFxuICAgICAgJ1rDoWtsYWRuw7ogb25saW5lIHByZXplbnTDoWNpdSBzcG9taWVub2snLFxuICAgICAgJ0Rvc3R1cG7DqSByaWXFoWVuaWUgcyBwcm9mZXNpb27DoWxueW0gdnpoxL5hZG9tJ1xuICAgIF0sXG4gICAgY29tcGFyaXNvbjoge1xuICAgICAgYmFzaWM6IHRydWUsXG4gICAgICBwcmVtaXVtOiBmYWxzZSxcbiAgICAgIGV4Y2x1c2l2ZTogZmFsc2VcbiAgICB9LFxuICAgIGZhcTogW1xuICAgICAge1xuICAgICAgICBxdWVzdGlvbjogJ0FrbyBkbGhvIHRydsOhIHZ5dHZvcmVuaWUgd2Vic3Ryw6Fua3k/JyxcbiAgICAgICAgYW5zd2VyOiAnWsOha2xhZG7DoSBtZW1vcmlhbCB3ZWJzdHLDoW5rYSBqZSBob3RvdsOhIGRvIDUtNyBwcmFjb3Zuw71jaCBkbsOtIG9kIHBvc2t5dG51dGlhIHbFoWV0a8O9Y2ggbWF0ZXJpw6Fsb3YuJ1xuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgcXVlc3Rpb246ICdNw7TFvmVtIHByaWRhxaUgdmlhYyBmb3RvZ3JhZmnDrSBuZXNrw7RyPycsXG4gICAgICAgIGFuc3dlcjogJ8OBbm8sIG3DtMW+ZXRlIGRva8O6cGnFpSByb3rFocOtcmVuaWUgbmEgdmlhYyBmb3RvZ3JhZmnDrSB6YSBwcsOtcGxhdG9rIDXigqwgemEga2HFvmTDuiBkb2RhdG/EjW7DuiBmb3RvZ3JhZml1LidcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIHF1ZXN0aW9uOiAnQWtvIGZ1bmd1amUgUVIga8OzZD8nLFxuICAgICAgICBhbnN3ZXI6ICdRUiBrw7NkIGRvc3RhbmV0ZSBha28gbsOhbGVwa3UsIGt0b3LDuiBtw7TFvmV0ZSB1bWllc3RuacWlIG5hIGhyb2IuIFBvIG5hc2tlbm92YW7DrSBuw6F2xaF0ZXZuw61rb3YgcHJlc21lcnVqZSBuYSBtZW1vcmlhbCB3ZWJzdHLDoW5rdS4nXG4gICAgICB9XG4gICAgXVxuICB9LFxuICBwcmVtaXVtOiB7XG4gICAgc2x1ZzogJ3ByZW1pdW0nLFxuICAgIG5hbWU6ICdTUE9NSUVOS0EgUHJlbWl1bScsXG4gICAgcHJpY2U6ICc1NDnigqwnLFxuICAgIGVtb2ppOiAn8J+PhicsXG4gICAgc2hvcnREZXNjcmlwdGlvbjogJ0tvbXBsZXRuw6kgcmllxaFlbmllIHMgdmlkZW8gc3BvbWllbmtvdSBhIHJvesWhw61yZW7DvW1pIGZ1bmtjaWFtaScsXG4gICAgZGVzY3JpcHRpb246ICdOYWpwb3B1bMOhcm5lasWhw60gYmFsw63EjWVrLCBrdG9yw70ga29tYmludWplIHbFoWV0a3kgZnVua2NpZSB6w6FrbGFkbsOpaG8gYmFsw63EjWthIHMgcHJvZmVzaW9uw6FsbnltIHZpZGVvIG9ic2Fob20gYSByb3rFocOtcmVuw71taSBtb8W+bm9zxaVhbWkuJyxcbiAgICBmZWF0dXJlczogW1xuICAgICAgJ1bFoWV0a28gem8gWsOha2xhZG5laicsXG4gICAgICAnMi1taW7DunRvdsOpIHZpZGVvIHNwb21pZW5reScsXG4gICAgICAnTmVvYm1lZHplbsOpIG1ub8W+c3R2byBmb3RvZ3JhZmnDrScsXG4gICAgICAnUHJvZmVzaW9uw6FsbnkgZGl6YWpuJyxcbiAgICAgICfEjGFzb3bDoSBvcyDFvml2b3RhJyxcbiAgICAgICdIdWRvYm7DqSBwb3phZGllJyxcbiAgICAgICdLb3ZvdsOhIFFSIHRhYnXEvmthJyxcbiAgICAgICdQYXNzd29yZCBvY2hyYW5hJyxcbiAgICAgICdDdXN0b20gZG9tw6luYScsXG4gICAgICAnQUkgdnlsZXDFoWVuaWUgZm90b2dyYWZpw60nLFxuICAgICAgJ1BlcnNvbmFsaXpvdmFuw70gdGV4dCdcbiAgICBdLFxuICAgIHByb2Nlc3M6IFtcbiAgICAgIHsgc3RlcDogMSwgdGl0bGU6ICdWSVAgS29uenVsdMOhY2lhJywgZGVzY3JpcHRpb246ICdSb3rFocOtcmVuw6Ega29uenVsdMOhY2lhIHMgdmlkZW8gcGzDoW5vbScgfSxcbiAgICAgIHsgc3RlcDogMiwgdGl0bGU6ICdNYXRlcmnDoWx5JywgZGVzY3JpcHRpb246ICdaaHJvbWHFvmRlbmllIGZvdG9ncmFmacOtLCB2aWRlw60gYSBodWRieScgfSxcbiAgICAgIHsgc3RlcDogMywgdGl0bGU6ICdWaWRlbyBwcm9kdWtjaWEnLCBkZXNjcmlwdGlvbjogJ1Z5dHZvcmVuaWUgcHJvZmVzaW9uw6FsbmVobyB2aWRlbyBvYnNhaHUnIH0sXG4gICAgICB7IHN0ZXA6IDQsIHRpdGxlOiAnV2Vic3Ryw6Fua2EnLCBkZXNjcmlwdGlvbjogJ1R2b3JiYSBwcmVtaXVtIG1lbW9yaWFsIHdlYnN0csOhbmt5JyB9LFxuICAgICAgeyBzdGVwOiA1LCB0aXRsZTogJ1NjaHbDoWxlbmllJywgZGVzY3JpcHRpb246ICdQcmV6ZW50w6FjaWEgYSBuZW9ibWVkemVuw6kgw7pwcmF2eScgfSxcbiAgICAgIHsgc3RlcDogNiwgdGl0bGU6ICdEb2RhbmllJywgZGVzY3JpcHRpb246ICdTcHVzdGVuaWUgYSBkb2RhbmllIGtvdm92ZWogUVIgdGFidcS+a3knIH0sXG4gICAgICB7IHN0ZXA6IDcsIHRpdGxlOiAnUGxhdGJhJywgZGVzY3JpcHRpb246ICdQbGF0YmEgYcW+IHBvIMO6cGxuZWogc3Bva29qbm9zdGknIH1cbiAgICBdLFxuICAgIGlkZWFsRm9yOiBbXG4gICAgICAnUm9kaW55IGNoY8O6Y2Uga29tcGxldG7DqSByaWXFoWVuaWUgcyB2aWRlb20nLFxuICAgICAgJ1TDvWNoLCBrdG9yw60gY2hjw7ogemFjaG92YcWlIHZpYWMgc3BvbWllbm9rJyxcbiAgICAgICdQcm9mZXNpb27DoWxudSBwcmV6ZW50w6FjaXUgcyByb3rFocOtcmVuw71taSBmdW5rY2lhbWknXG4gICAgXSxcbiAgICBjb21wYXJpc29uOiB7XG4gICAgICBiYXNpYzogdHJ1ZSxcbiAgICAgIHByZW1pdW06IHRydWUsXG4gICAgICBleGNsdXNpdmU6IGZhbHNlXG4gICAgfSxcbiAgICBmYXE6IFtcbiAgICAgIHtcbiAgICAgICAgcXVlc3Rpb246ICdBa8O9IHR5cCB2aWRlYSB2eXR2b3LDrXRlPycsXG4gICAgICAgIGFuc3dlcjogJ1Z5dHbDoXJhbWUgZW1vdMOtdm5lIHZpZGVvIHNwb21pZW5reSBzIGZvdG9ncmFmaWFtaSwgaHVkYm91IGEgdGV4dGFtaS4gVmlkZW8gamUgb3B0aW1hbGl6b3ZhbsOpIHByZSB3ZWIgYWogbW9iaWxuw6kgemFyaWFkZW5pYS4nXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBxdWVzdGlvbjogJ03DtMW+ZW0gc2kgdnlicmHFpSBodWRidT8nLFxuICAgICAgICBhbnN3ZXI6ICfDgW5vLCBtw7TFvmV0ZSBzaSB2eWJyYcWlIHogbmHFoWVqIGtuacW+bmljZSBhbGVibyBwb3NreXRuw7rFpSB2bGFzdG7DuiBodWRidS4gUG9tw7TFvmVtZSB2w6FtIHZ5YnJhxaUgbmFqdmhvZG5lasWhaXUuJ1xuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgcXVlc3Rpb246ICfEjG8gb2JzYWh1amUga292b3bDoSBRUiB0YWJ1xL5rYT8nLFxuICAgICAgICBhbnN3ZXI6ICdLb3ZvdsOhIHRhYnXEvmthIGplIG9kb2xuw6Egdm/EjWkgcG92ZXRlcm5vc3Ruw71tIHZwbHl2b20sIG9ic2FodWplIFFSIGvDs2QgYSBtw7TFvmUgbWHFpSBncmF2w61yb3ZhbsO9IHRleHQgcG9kxL5hIHbDocWhaG8gxb5lbGFuaWEuJ1xuICAgICAgfVxuICAgIF1cbiAgfSxcbiAgZXhjbHVzaXZlOiB7XG4gICAgc2x1ZzogJ2V4Y2x1c2l2ZScsXG4gICAgbmFtZTogJ1NQT01JRU5LQSBFeGNsdXNpdmUnLFxuICAgIHByaWNlOiAnODk54oKsJyxcbiAgICBlbW9qaTogJ/Cfko4nLFxuICAgIHNob3J0RGVzY3JpcHRpb246ICdMdXh1c27DqSByaWXFoWVuaWUgcyBjaW5lbWF0aWMgdmlkZW9tIGEgVklQIHNlcnZpc29tJyxcbiAgICBkZXNjcmlwdGlvbjogJ05hamV4a2x1esOtdm5lasWhw60gYmFsw63EjWVrIHMgY2luZW1hdGljIHZpZGVvIHByb2R1a2Npb3UsIGtvbXBsZXRuw71tIMW+aXZvdG9waXNvbSBhIHdoaXRlLWdsb3ZlIHNlcnZpc29tIG9kIHphxI1pYXRrdSBkbyBrb25jYS4nLFxuICAgIGZlYXR1cmVzOiBbXG4gICAgICAnVsWhZXRrbyB6IFByZW1pdW0gYmFsw63EjWthJyxcbiAgICAgICc1LW1pbsO6dG92w6kgY2luZW1hdGljIHZpZGVvJyxcbiAgICAgICdLb21wbGV0bsO9IMW+aXZvdG9waXMnLFxuICAgICAgJ1BlcnNvbmFsaXpvdmFuw70gZGl6YWpuJyxcbiAgICAgICdNdWx0aW1lZGlhIGdhbMOpcmlhJyxcbiAgICAgICdHcmFuaXRvdsOhIFFSIHRhYnXEvmthJyxcbiAgICAgICdQcm9mZXNpb27DoWxuZSB1bWllc3RuZW5pZScsXG4gICAgICAnVklQIGtvbnp1bHTDoWNpZScsXG4gICAgICAnUHJvZmVzc2lvbmFsIHByb2R1Y3Rpb24nLFxuICAgICAgJ0luc3RhbGxhdGlvbiBzZXJ2aWNlJyxcbiAgICAgICdSb3rFocOtcmVuw6EgZ2FyYW5jaWEnXG4gICAgXSxcbiAgICBwcm9jZXNzOiBbXG4gICAgICB7IHN0ZXA6IDEsIHRpdGxlOiAnVklQIEtvbnp1bHTDoWNpYScsIGRlc2NyaXB0aW9uOiAnT3NvYm7DoSBrb256dWx0w6FjaWEgcyBrcmVhdGl2bsO9bSB0w61tb20nIH0sXG4gICAgICB7IHN0ZXA6IDIsIHRpdGxlOiAnS3JlYXRpdm55IHBsw6FuJywgZGVzY3JpcHRpb246ICdWeXR2b3JlbmllIGRldGFpbG7DqWhvIHBsw6FudSBwcm9qZWt0dScgfSxcbiAgICAgIHsgc3RlcDogMywgdGl0bGU6ICdQcm9kdWtjaWEnLCBkZXNjcmlwdGlvbjogJ0NpbmVtYXRpYyB2aWRlbyBhIGtvbXBsZXRuw70gb2JzYWgnIH0sXG4gICAgICB7IHN0ZXA6IDQsIHRpdGxlOiAnRGl6YWpuJywgZGVzY3JpcHRpb246ICdQZXJzb25hbGl6b3ZhbsO9IGRpemFqbiB3ZWJzdHLDoW5reScgfSxcbiAgICAgIHsgc3RlcDogNSwgdGl0bGU6ICdTY2h2w6FsZW5pZScsIGRlc2NyaXB0aW9uOiAnUHJlemVudMOhY2lhIGEgbmVvYm1lZHplbsOpIMO6cHJhdnknIH0sXG4gICAgICB7IHN0ZXA6IDYsIHRpdGxlOiAnSW7FoXRhbMOhY2lhJywgZGVzY3JpcHRpb246ICdQcm9mZXNpb27DoWxuZSB1bWllc3RuZW5pZSBRUiB0YWJ1xL5reScgfSxcbiAgICAgIHsgc3RlcDogNywgdGl0bGU6ICdQbGF0YmEnLCBkZXNjcmlwdGlvbjogJ1BsYXRiYSBhxb4gcG8gw7pwbG5laiBzcG9rb2pub3N0aScgfVxuICAgIF0sXG4gICAgaWRlYWxGb3I6IFtcbiAgICAgICdSb2RpbnkgY2hjw7pjZSBuYWprdmFsaXRuZWrFoWllIHJpZcWhZW5pZScsXG4gICAgICAnS29tcGxldG7DvSB3aGl0ZS1nbG92ZSBzZXJ2aXMnLFxuICAgICAgJ0NpbmVtYXRpYyBrdmFsaXR1IGEgcGVyc29uYWxpesOhY2l1J1xuICAgIF0sXG4gICAgY29tcGFyaXNvbjoge1xuICAgICAgYmFzaWM6IHRydWUsXG4gICAgICBwcmVtaXVtOiB0cnVlLFxuICAgICAgZXhjbHVzaXZlOiB0cnVlXG4gICAgfSxcbiAgICBmYXE6IFtcbiAgICAgIHtcbiAgICAgICAgcXVlc3Rpb246ICfEjG8gem5hbWVuw6EgY2luZW1hdGljIHZpZGVvPycsXG4gICAgICAgIGFuc3dlcjogJ0NpbmVtYXRpYyB2aWRlbyBqZSBwcm9mZXNpb27DoWxuZSBzcHJhY292YW7DqSBzIHBva3JvxI1pbMO9bWkgZWZla3RtaSwgcHJlY2hvZG1pIGEga3ZhbGl0b3UgYWtvIHZvIGZpbG1lLiBUcnbDoSBhxb4gNSBtaW7DunQuJ1xuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgcXVlc3Rpb246ICfEjG8gb2JzYWh1amUgaW5zdGFsbGF0aW9uIHNlcnZpY2U/JyxcbiAgICAgICAgYW5zd2VyOiAnTsOhxaEgdMOtbSBwcm9mZXNpb27DoWxuZSBuYWluxaF0YWx1amUgZ3Jhbml0b3bDuiBRUiB0YWJ1xL5rdSBuYSBocm9iIGEgemFiZXpwZcSNw60gamVqIHNwcsOhdm5lIHVtaWVzdG5lbmllIGEgZnVua8SNbm9zxaUuJ1xuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgcXVlc3Rpb246ICdBa8OhIGplIHJvesWhw61yZW7DoSBnYXJhbmNpYT8nLFxuICAgICAgICBhbnN3ZXI6ICdQb3NreXR1amVtZSAxMC1yb8SNbsO6IGdhcmFuY2l1IG5hIHbFoWV0a3kgc2x1xb5ieSBhIGJlenBsYXRuw6kgw7pwcmF2eSBwb8SNYXMgcHJ2w6lobyByb2thLidcbiAgICAgIH1cbiAgICBdXG4gIH1cbn1cblxuaW50ZXJmYWNlIFBhY2thZ2VQYWdlUHJvcHMge1xuICBwYWNrYWdlRGF0YTogUGFja2FnZURhdGFcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUGFja2FnZVBhZ2UoeyBwYWNrYWdlRGF0YSB9OiBQYWNrYWdlUGFnZVByb3BzKSB7XG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAocGFja2FnZURhdGEpIHtcbiAgICAgIHRyYWNrUGFnZVZpZXcoYCR7cGFja2FnZURhdGEubmFtZX0gLSBEZXRhaWxgKVxuICAgIH1cbiAgfSwgW3BhY2thZ2VEYXRhXSlcblxuICBpZiAocm91dGVyLmlzRmFsbGJhY2spIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxMb2FkaW5nU3Bpbm5lciBzaXplPVwibGdcIiAvPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTQgdGV4dC1ncmF5LTYwMFwiPk5hxI3DrXRhdmFtIGJhbMOtxI1lay4uLjwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDw+XG4gICAgICA8SGVhZD5cbiAgICAgICAgPHRpdGxlPntwYWNrYWdlRGF0YS5uYW1lfSAtIGVTcG9taWVua2E8L3RpdGxlPlxuICAgICAgICA8bWV0YSBuYW1lPVwiZGVzY3JpcHRpb25cIiBjb250ZW50PXtgJHtwYWNrYWdlRGF0YS5uYW1lfSAtICR7cGFja2FnZURhdGEuZGVzY3JpcHRpb259YH0gLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cImtleXdvcmRzXCIgY29udGVudD17YG1lbW9yaWFsLCBzcG9taWVua3ksICR7cGFja2FnZURhdGEuc2x1Z30sIHZpZGVvIHNwb21pZW5reSwgUVIga8OzZCwgaHJvYmB9IC8+XG4gICAgICAgIDxtZXRhIHByb3BlcnR5PVwib2c6dGl0bGVcIiBjb250ZW50PXtgJHtwYWNrYWdlRGF0YS5uYW1lfSAtIGVTcG9taWVua2FgfSAvPlxuICAgICAgICA8bWV0YSBwcm9wZXJ0eT1cIm9nOmRlc2NyaXB0aW9uXCIgY29udGVudD17cGFja2FnZURhdGEuZGVzY3JpcHRpb259IC8+XG4gICAgICAgIDxtZXRhIHByb3BlcnR5PVwib2c6dHlwZVwiIGNvbnRlbnQ9XCJ3ZWJzaXRlXCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cInR3aXR0ZXI6Y2FyZFwiIGNvbnRlbnQ9XCJzdW1tYXJ5X2xhcmdlX2ltYWdlXCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cInR3aXR0ZXI6dGl0bGVcIiBjb250ZW50PXtgJHtwYWNrYWdlRGF0YS5uYW1lfSAtIGVTcG9taWVua2FgfSAvPlxuICAgICAgICA8bWV0YSBuYW1lPVwidHdpdHRlcjpkZXNjcmlwdGlvblwiIGNvbnRlbnQ9e3BhY2thZ2VEYXRhLmRlc2NyaXB0aW9ufSAvPlxuICAgICAgICA8bGluayByZWw9XCJjYW5vbmljYWxcIiBocmVmPXtgaHR0cHM6Ly9lc3BvbWllbmthLnNrL2JhbGlja3kvJHtwYWNrYWdlRGF0YS5zbHVnfWB9IC8+XG4gICAgICAgIDxzY3JpcHRcbiAgICAgICAgICB0eXBlPVwiYXBwbGljYXRpb24vbGQranNvblwiXG4gICAgICAgICAgZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUw9e3tcbiAgICAgICAgICAgIF9faHRtbDogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgICAgICBcIkBjb250ZXh0XCI6IFwiaHR0cHM6Ly9zY2hlbWEub3JnXCIsXG4gICAgICAgICAgICAgIFwiQHR5cGVcIjogXCJTZXJ2aWNlXCIsXG4gICAgICAgICAgICAgIFwibmFtZVwiOiBwYWNrYWdlRGF0YS5uYW1lLFxuICAgICAgICAgICAgICBcImRlc2NyaXB0aW9uXCI6IHBhY2thZ2VEYXRhLmRlc2NyaXB0aW9uLFxuICAgICAgICAgICAgICBcInByb3ZpZGVyXCI6IHtcbiAgICAgICAgICAgICAgICBcIkB0eXBlXCI6IFwiT3JnYW5pemF0aW9uXCIsXG4gICAgICAgICAgICAgICAgXCJuYW1lXCI6IFwiZVNwb21pZW5rYVwiLFxuICAgICAgICAgICAgICAgIFwidGVsZXBob25lXCI6IFwiKzQyMTk1MTU1MzQ2NFwiLFxuICAgICAgICAgICAgICAgIFwiZW1haWxcIjogXCJpbmZvQGVocm9ieS5za1wiXG4gICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgIFwib2ZmZXJzXCI6IHtcbiAgICAgICAgICAgICAgICBcIkB0eXBlXCI6IFwiT2ZmZXJcIixcbiAgICAgICAgICAgICAgICBcInByaWNlXCI6IHBhY2thZ2VEYXRhLnByaWNlLnJlcGxhY2UoJ+KCrCcsICcnKSxcbiAgICAgICAgICAgICAgICBcInByaWNlQ3VycmVuY3lcIjogXCJFVVJcIlxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KVxuICAgICAgICAgIH19XG4gICAgICAgIC8+XG4gICAgICA8L0hlYWQ+XG5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuXCI+XG4gICAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICAgIDxoZWFkZXIgY2xhc3NOYW1lPVwiYmctcHJpbWFyeSBzaGFkb3ctc21cIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTYgcHktNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL1wiIGNsYXNzTmFtZT1cImZvbnQtcGxheWZhaXIgdGV4dC0yeGwgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAgICBlU3BvbWllbmthXG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPG5hdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LThcIj5cbiAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL1wiIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgaG92ZXI6dGV4dC1nb2xkIHRyYW5zaXRpb24tY29sb3JzXCI+XG4gICAgICAgICAgICAgICAgICBEb21vdlxuICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2Jsb2dcIiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGhvdmVyOnRleHQtZ29sZCB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuICAgICAgICAgICAgICAgICAgQmxvZ1xuICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICA8YSBocmVmPVwidGVsOis0MjE5NTE1NTM0NjRcIiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGhvdmVyOnRleHQtZ29sZCB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuICAgICAgICAgICAgICAgICAgKzQyMSA5NTEgNTUzIDQ2NFxuICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgPC9uYXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9oZWFkZXI+XG5cbiAgICAgICAgey8qIEJyZWFkY3J1bWIgKi99XG4gICAgICAgIDxCcmVhZGNydW1iXG4gICAgICAgICAgaXRlbXM9e1tcbiAgICAgICAgICAgIHsgbGFiZWw6ICdEb21vdicsIGhyZWY6ICcvJyB9LFxuICAgICAgICAgICAgeyBsYWJlbDogJ0JhbMOtxI1reScsIGhyZWY6ICcvI3BhY2thZ2VzJyB9LFxuICAgICAgICAgICAgeyBsYWJlbDogcGFja2FnZURhdGEubmFtZSB9XG4gICAgICAgICAgXX1cbiAgICAgICAgLz5cblxuICAgICAgICB7LyogUGFja2FnZSBIZXJvICovfVxuICAgICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJweS0xNiBiZy1ncmFkaWVudC10by1yIGZyb20tcHJpbWFyeSB0by1wcmltYXJ5LWRhcmtcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTYgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC02eGwgbWItNFwiPntwYWNrYWdlRGF0YS5lbW9qaX08L2Rpdj5cbiAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTR4bCBtZDp0ZXh0LTV4bCBmb250LXBsYXlmYWlyIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1iLTRcIj5cbiAgICAgICAgICAgICAge3BhY2thZ2VEYXRhLm5hbWV9XG4gICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTV4bCBmb250LWJvbGQgdGV4dC1nb2xkIG1iLTZcIj57cGFja2FnZURhdGEucHJpY2V9PC9kaXY+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhsIHRleHQtZ3JheS0yMDAgbWF4LXctMnhsIG14LWF1dG8gbWItOFwiPlxuICAgICAgICAgICAgICB7cGFja2FnZURhdGEuZGVzY3JpcHRpb259XG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8YVxuICAgICAgICAgICAgICBocmVmPVwidGVsOis0MjE5NTE1NTM0NjRcIlxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1nb2xkIGhvdmVyOmJnLWdvbGQtZGFyayB0ZXh0LXdoaXRlIHB4LTggcHktMyByb3VuZGVkLWxnIGZvbnQtc2VtaWJvbGQgdHJhbnNpdGlvbi1jb2xvcnMgaW5saW5lLWJsb2NrXCJcbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gdHJhY2tDVEFDbGljaygncGhvbmUnLCAnaGVybycpfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICBPYmplZG5hxaUga29uenVsdMOhY2l1XG4gICAgICAgICAgICA8L2E+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgICB7LyogRmVhdHVyZXMgU2VjdGlvbiAqL31cbiAgICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwicHktMTYgYmctd2hpdGVcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTZcIj5cbiAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LXBsYXlmYWlyIGZvbnQtYm9sZCB0ZXh0LWNlbnRlciB0ZXh0LWdyYXktODAwIG1iLTEyXCI+XG4gICAgICAgICAgICAgIMSMbyBkb3N0YW5ldGVcbiAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTMgZ2FwLTZcIj5cbiAgICAgICAgICAgICAge3BhY2thZ2VEYXRhLmZlYXR1cmVzLm1hcCgoZmVhdHVyZSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTYgaC02IGJnLWdvbGQgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGZsZXgtc2hyaW5rLTBcIj5cbiAgICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtd2hpdGVcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyMCAyMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGZpbGxSdWxlPVwiZXZlbm9kZFwiIGQ9XCJNMTYuNzA3IDUuMjkzYTEgMSAwIDAxMCAxLjQxNGwtOCA4YTEgMSAwIDAxLTEuNDE0IDBsLTQtNGExIDEgMCAwMTEuNDE0LTEuNDE0TDggMTIuNTg2bDcuMjkzLTcuMjkzYTEgMSAwIDAxMS40MTQgMHpcIiBjbGlwUnVsZT1cImV2ZW5vZGRcIi8+XG4gICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNzAwXCI+e2ZlYXR1cmV9PC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L3NlY3Rpb24+XG5cbiAgICAgICAgey8qIFByb2Nlc3MgU2VjdGlvbiAqL31cbiAgICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwicHktMTYgYmctY3JlYW1cIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTZcIj5cbiAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LXBsYXlmYWlyIGZvbnQtYm9sZCB0ZXh0LWNlbnRlciB0ZXh0LWdyYXktODAwIG1iLTEyXCI+XG4gICAgICAgICAgICAgIEFrbyB0byBmdW5ndWplXG4gICAgICAgICAgICA8L2gyPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy00eGwgbXgtYXV0b1wiPlxuICAgICAgICAgICAgICB7cGFja2FnZURhdGEucHJvY2Vzcy5tYXAoKHN0ZXAsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IHNwYWNlLXgtNCBtYi04IGxhc3Q6bWItMFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgYmctZ29sZCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgZmxleC1zaHJpbmstMFwiPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtYm9sZFwiPntzdGVwLnN0ZXB9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtZ3JheS04MDAgbWItMlwiPntzdGVwLnRpdGxlfTwvaDM+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj57c3RlcC5kZXNjcmlwdGlvbn08L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9zZWN0aW9uPlxuXG4gICAgICAgIHsvKiBTaG93Y2FzZSBTZWN0aW9uIC0gUGxhY2Vob2xkZXIgKi99XG4gICAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cInB5LTE2IGJnLXdoaXRlXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXIgbXgtYXV0byBweC02XCI+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1wbGF5ZmFpciBmb250LWJvbGQgdGV4dC1jZW50ZXIgdGV4dC1ncmF5LTgwMCBtYi0xMlwiPlxuICAgICAgICAgICAgICBVa8Ohxb5reSBuYcWhZWogcHLDoWNlXG4gICAgICAgICAgICA8L2gyPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy0zIGdhcC04XCI+XG4gICAgICAgICAgICAgIHtbMSwgMiwgM10ubWFwKChpdGVtKSA9PiAoXG4gICAgICAgICAgICAgICAgPGRpdiBrZXk9e2l0ZW19IGNsYXNzTmFtZT1cImJnLWdyYXktMTAwIHJvdW5kZWQtbGcgcC04IHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTYgaC0xNiBiZy1ncmF5LTMwMCByb3VuZGVkLWZ1bGwgbXgtYXV0byBtYi00XCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS02MDAgbWItMlwiPlByaXByYXZ1amVtZSB1a8Ohxb5reTwvaDM+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwXCI+U2VnZXJhIGhhZGlyPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L3NlY3Rpb24+XG5cbiAgICAgICAgey8qIENvbXBhcmlzb24gU2VjdGlvbiAqL31cbiAgICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwicHktMTYgYmctY3JlYW1cIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTZcIj5cbiAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LXBsYXlmYWlyIGZvbnQtYm9sZCB0ZXh0LWNlbnRlciB0ZXh0LWdyYXktODAwIG1iLTEyXCI+XG4gICAgICAgICAgICAgIFBvcm92bmFuaWUgYmFsw63EjWtvdlxuICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctNHhsIG14LWF1dG8gYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctbGcgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtNCBnYXAtNCBwLTYgYmctZ3JheS01MCBmb250LXNlbWlib2xkXCI+XG4gICAgICAgICAgICAgICAgPGRpdj5GdW5rY2llPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlrDoWtsYWRuw6E8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+UHJlbWl1bTwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5FeGNsdXNpdmU8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZGl2aWRlLXlcIj5cbiAgICAgICAgICAgICAgICB7W1xuICAgICAgICAgICAgICAgICAgJ01lbW9yaWFsIHdlYnN0csOhbmthJyxcbiAgICAgICAgICAgICAgICAgICdRUiBrw7NkJyxcbiAgICAgICAgICAgICAgICAgICdWaWRlbyBzcG9taWVua3knLFxuICAgICAgICAgICAgICAgICAgJ05lb2JtZWR6ZW7DqSBmb3RreScsXG4gICAgICAgICAgICAgICAgICAnS292b3bDoS9HcmFuaXRvdsOhIHRhYnXEvmthJyxcbiAgICAgICAgICAgICAgICAgICdWSVAgc2VydmlzJ1xuICAgICAgICAgICAgICAgIF0ubWFwKChmZWF0dXJlLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy00IGdhcC00IHAtNFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS03MDBcIj57ZmVhdHVyZX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtpbmRleCA8IDIgPyAn4pyFJyA6ICfinYwnfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtpbmRleCA8IDUgPyAn4pyFJyA6ICfinYwnfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPuKchTwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgICB7LyogQ1RBIFNlY3Rpb24gKi99XG4gICAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cInB5LTE2IGJnLXByaW1hcnlcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTYgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LXBsYXlmYWlyIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1iLTZcIj5cbiAgICAgICAgICAgICAgUHJpcHJhdmVuw60gemHEjWHFpT9cbiAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhsIHRleHQtZ3JheS0yMDAgbWF4LXctMnhsIG14LWF1dG8gbWItOFwiPlxuICAgICAgICAgICAgICBLb250YWt0dWp0ZSBuw6FzIHByZSBiZXpwbGF0bsO6IGtvbnp1bHTDoWNpdS4gUGxhdMOtdGUgYcW+IGtlxI8gc3RlIMO6cGxuZSBzcG9rb2puw60uXG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgc206ZmxleC1yb3cgZ2FwLTQganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPGFcbiAgICAgICAgICAgICAgICBocmVmPVwidGVsOis0MjE5NTE1NTM0NjRcIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdvbGQgaG92ZXI6YmctZ29sZC1kYXJrIHRleHQtd2hpdGUgcHgtOCBweS0zIHJvdW5kZWQtbGcgZm9udC1zZW1pYm9sZCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBaYXZvbGHFpSArNDIxIDk1MSA1NTMgNDY0XG4gICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgPGFcbiAgICAgICAgICAgICAgICBocmVmPVwibWFpbHRvOmluZm9AZWhyb2J5LnNrXCJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy13aGl0ZSBob3ZlcjpiZy1ncmF5LTEwMCB0ZXh0LXByaW1hcnkgcHgtOCBweS0zIHJvdW5kZWQtbGcgZm9udC1zZW1pYm9sZCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBOYXDDrXNhxaUgZW1haWxcbiAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgICB7LyogRkFRIFNlY3Rpb24gKi99XG4gICAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cInB5LTE2IGJnLXdoaXRlXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXIgbXgtYXV0byBweC02XCI+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1wbGF5ZmFpciBmb250LWJvbGQgdGV4dC1jZW50ZXIgdGV4dC1ncmF5LTgwMCBtYi0xMlwiPlxuICAgICAgICAgICAgICDEjGFzdG8ga2xhZGVuw6kgb3TDoXpreVxuICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctM3hsIG14LWF1dG8gc3BhY2UteS02XCI+XG4gICAgICAgICAgICAgIHtwYWNrYWdlRGF0YS5mYXEubWFwKChpdGVtLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwiYmctZ3JheS01MCByb3VuZGVkLWxnIHAtNlwiPlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktODAwIG1iLTNcIj5cbiAgICAgICAgICAgICAgICAgICAge2l0ZW0ucXVlc3Rpb259XG4gICAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICB7aXRlbS5hbnN3ZXJ9XG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgICB7LyogU3RpY2t5IENUQSBCdXR0b24gKi99XG4gICAgICAgIDxhXG4gICAgICAgICAgaHJlZj1cInRlbDorNDIxOTUxNTUzNDY0XCJcbiAgICAgICAgICBjbGFzc05hbWU9XCJzdGlja3ktY3RhIGJnLWdvbGQgaG92ZXI6YmctZ29sZC1kYXJrIHRleHQtd2hpdGUgcHgtNiBweS0zIHJvdW5kZWQtZnVsbCBmb250LXNlbWlib2xkXCJcbiAgICAgICAgPlxuICAgICAgICAgIPCfk54gWmF2b2xhxaVcbiAgICAgICAgPC9hPlxuXG4gICAgICAgIHsvKiBGb290ZXIgKi99XG4gICAgICAgIDxmb290ZXIgY2xhc3NOYW1lPVwiYmctZ3JheS04MDAgdGV4dC13aGl0ZSBweS04XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXIgbXgtYXV0byBweC02IHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHNwYWNlLXgtMyBtYi00XCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtcGxheWZhaXIgdGV4dC0yeGwgZm9udC1zZW1pYm9sZFwiPmVTcG9taWVua2E8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgwqkgMjAyNSBlU3BvbWllbmthLiBWxaFldGt5IHByw6F2YSB2eWhyYWRlbsOpLlxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Zvb3Rlcj5cbiAgICAgIDwvZGl2PlxuICAgIDwvPlxuICApXG59XG5cbmV4cG9ydCBjb25zdCBnZXRTdGF0aWNQYXRoczogR2V0U3RhdGljUGF0aHMgPSBhc3luYyAoKSA9PiB7XG4gIGNvbnN0IHBhdGhzID0gT2JqZWN0LmtleXMocGFja2FnZXNEYXRhKS5tYXAoKHNsdWcpID0+ICh7XG4gICAgcGFyYW1zOiB7IHNsdWcgfVxuICB9KSlcblxuICByZXR1cm4ge1xuICAgIHBhdGhzLFxuICAgIGZhbGxiYWNrOiBmYWxzZVxuICB9XG59XG5cbmV4cG9ydCBjb25zdCBnZXRTdGF0aWNQcm9wczogR2V0U3RhdGljUHJvcHMgPSBhc3luYyAoeyBwYXJhbXMgfSkgPT4ge1xuICBjb25zdCBzbHVnID0gcGFyYW1zPy5zbHVnIGFzIHN0cmluZ1xuICBjb25zdCBwYWNrYWdlRGF0YSA9IHBhY2thZ2VzRGF0YVtzbHVnXVxuXG4gIGlmICghcGFja2FnZURhdGEpIHtcbiAgICByZXR1cm4ge1xuICAgICAgbm90Rm91bmQ6IHRydWVcbiAgICB9XG4gIH1cblxuICByZXR1cm4ge1xuICAgIHByb3BzOiB7XG4gICAgICBwYWNrYWdlRGF0YVxuICAgIH1cbiAgfVxufVxuIl0sIm5hbWVzIjpbIkhlYWQiLCJMaW5rIiwidXNlUm91dGVyIiwidXNlRWZmZWN0IiwiTG9hZGluZ1NwaW5uZXIiLCJCcmVhZGNydW1iIiwidHJhY2tQYWdlVmlldyIsInRyYWNrQ1RBQ2xpY2siLCJQYWNrYWdlUGFnZSIsInBhY2thZ2VEYXRhIiwicm91dGVyIiwibmFtZSIsImlzRmFsbGJhY2siLCJkaXYiLCJjbGFzc05hbWUiLCJzaXplIiwicCIsInRpdGxlIiwibWV0YSIsImNvbnRlbnQiLCJkZXNjcmlwdGlvbiIsInNsdWciLCJwcm9wZXJ0eSIsImxpbmsiLCJyZWwiLCJocmVmIiwic2NyaXB0IiwidHlwZSIsImRhbmdlcm91c2x5U2V0SW5uZXJIVE1MIiwiX19odG1sIiwiSlNPTiIsInN0cmluZ2lmeSIsInByaWNlIiwicmVwbGFjZSIsImhlYWRlciIsIm5hdiIsImEiLCJpdGVtcyIsImxhYmVsIiwic2VjdGlvbiIsImVtb2ppIiwiaDEiLCJvbkNsaWNrIiwiaDIiLCJmZWF0dXJlcyIsIm1hcCIsImZlYXR1cmUiLCJpbmRleCIsInN2ZyIsImZpbGwiLCJ2aWV3Qm94IiwicGF0aCIsImZpbGxSdWxlIiwiZCIsImNsaXBSdWxlIiwic3BhbiIsInByb2Nlc3MiLCJzdGVwIiwiaDMiLCJpdGVtIiwiZmFxIiwicXVlc3Rpb24iLCJhbnN3ZXIiLCJmb290ZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./pages/balicky/[slug].tsx\n"));

/***/ })

});