"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/balicky/[slug]",{

/***/ "./pages/balicky/[slug].tsx":
/*!**********************************!*\
  !*** ./pages/balicky/[slug].tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSG: function() { return /* binding */ __N_SSG; },\n/* harmony export */   \"default\": function() { return /* binding */ PackagePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/LoadingSpinner */ \"./components/LoadingSpinner.tsx\");\n/* harmony import */ var _components_Breadcrumb__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/Breadcrumb */ \"./components/Breadcrumb.tsx\");\n/* harmony import */ var _utils_analytics__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../utils/analytics */ \"./utils/analytics.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nvar __N_SSG = true;\nfunction PackagePage(param) {\n    let { packageData } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        if (packageData) {\n            (0,_utils_analytics__WEBPACK_IMPORTED_MODULE_7__.trackPageView)(\"\".concat(packageData.name, \" - Detail\"));\n        }\n    }, [\n        packageData\n    ]);\n    if (router.isFallback) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        size: \"lg\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 213,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Nač\\xedtavam bal\\xedček...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                lineNumber: 212,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n            lineNumber: 211,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: [\n                            packageData.name,\n                            \" - eSpomienka\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"\".concat(packageData.name, \" - \").concat(packageData.description)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"keywords\",\n                        content: \"memorial, spomienky, \".concat(packageData.slug, \", video spomienky, QR k\\xf3d, hrob\")\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:title\",\n                        content: \"\".concat(packageData.name, \" - eSpomienka\")\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:description\",\n                        content: packageData.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:type\",\n                        content: \"website\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:card\",\n                        content: \"summary_large_image\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:title\",\n                        content: \"\".concat(packageData.name, \" - eSpomienka\")\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:description\",\n                        content: packageData.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"canonical\",\n                        href: \"https://espomienka.sk/balicky/\".concat(packageData.slug)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        children: JSON.stringify({\n                            \"@context\": \"https://schema.org\",\n                            \"@type\": \"Service\",\n                            \"name\": packageData.name,\n                            \"description\": packageData.description,\n                            \"provider\": {\n                                \"@type\": \"Organization\",\n                                \"name\": \"eSpomienka\",\n                                \"telephone\": \"+421951553464\",\n                                \"email\": \"<EMAIL>\"\n                            },\n                            \"offers\": {\n                                \"@type\": \"Offer\",\n                                \"price\": packageData.price.replace(\"€\", \"\"),\n                                \"priceCurrency\": \"EUR\"\n                            }\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 233,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"bg-primary shadow-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/\",\n                                            className: \"font-playfair text-2xl font-semibold text-white\",\n                                            children: \"eSpomienka\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"flex items-center space-x-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/\",\n                                                className: \"text-white hover:text-gold transition-colors\",\n                                                children: \"Domov\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/blog\",\n                                                className: \"text-white hover:text-gold transition-colors\",\n                                                children: \"Blog\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"tel:+421951553464\",\n                                                className: \"text-white hover:text-gold transition-colors\",\n                                                children: \"+421 951 553 464\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                lineNumber: 258,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 256,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Breadcrumb__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        items: [\n                            {\n                                label: \"Domov\",\n                                href: \"/\"\n                            },\n                            {\n                                label: \"Bal\\xedčky\",\n                                href: \"/#packages\"\n                            },\n                            {\n                                label: packageData.name\n                            }\n                        ]\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 280,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-gradient-to-r from-primary to-primary-dark\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-6xl mb-4\",\n                                    children: packageData.emoji\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl md:text-5xl font-playfair font-bold text-white mb-4\",\n                                    children: packageData.name\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-5xl font-bold text-gold mb-6\",\n                                    children: packageData.price\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-200 max-w-2xl mx-auto mb-8\",\n                                    children: packageData.description\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"tel:+421951553464\",\n                                    className: \"bg-gold hover:bg-gold-dark text-white px-8 py-3 rounded-lg font-semibold transition-colors inline-block\",\n                                    children: \"Objednať konzult\\xe1ciu\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-playfair font-bold text-center text-gray-800 mb-12\",\n                                    children: \"Čo dostanete\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                    children: packageData.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-6 h-6 bg-gold rounded-full flex items-center justify-center flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-white\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-700\",\n                                                    children: feature\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 310,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 309,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-cream\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-playfair font-bold text-center text-gray-800 mb-12\",\n                                    children: \"Ako to funguje\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-4xl mx-auto\",\n                                    children: packageData.process.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-4 mb-8 last:mb-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gold rounded-full flex items-center justify-center flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-bold\",\n                                                        children: step.step\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-semibold text-gray-800 mb-2\",\n                                                            children: step.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: step.description\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 331,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 330,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-playfair font-bold text-center text-gray-800 mb-12\",\n                                    children: \"Uk\\xe1žky našej pr\\xe1ce\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                    children: [\n                                        1,\n                                        2,\n                                        3\n                                    ].map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-100 rounded-lg p-8 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-gray-300 rounded-full mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-600 mb-2\",\n                                                    children: \"Pripravujeme uk\\xe1žky\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500\",\n                                                    children: \"Segera hadir\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, item, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 353,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 352,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-cream\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-playfair font-bold text-center text-gray-800 mb-12\",\n                                    children: \"Porovnanie bal\\xedčkov\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-4xl mx-auto bg-white rounded-lg shadow-lg overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-4 gap-4 p-6 bg-gray-50 font-semibold\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: \"Funkcie\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: \"Z\\xe1kladn\\xe1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: \"Premium\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: \"Exclusive\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"divide-y\",\n                                            children: [\n                                                \"Memorial webstr\\xe1nka\",\n                                                \"QR k\\xf3d\",\n                                                \"Video spomienky\",\n                                                \"Neobmedzen\\xe9 fotky\",\n                                                \"Kovov\\xe1/Granitov\\xe1 tabuľka\",\n                                                \"VIP servis\"\n                                            ].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-4 gap-4 p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-700\",\n                                                            children: feature\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: index < 2 ? \"✅\" : \"❌\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: index < 5 ? \"✅\" : \"❌\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                            lineNumber: 396,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: \"✅\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 375,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 371,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 370,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-primary\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-playfair font-bold text-white mb-6\",\n                                    children: \"Pripraven\\xed začať?\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-200 max-w-2xl mx-auto mb-8\",\n                                    children: \"Kontaktujte n\\xe1s pre bezplatn\\xfa konzult\\xe1ciu. Plat\\xedte až keď ste \\xfaplne spokojn\\xed.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"tel:+421951553464\",\n                                            className: \"bg-gold hover:bg-gold-dark text-white px-8 py-3 rounded-lg font-semibold transition-colors\",\n                                            children: \"Zavolať +421 951 553 464\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"mailto:<EMAIL>\",\n                                            className: \"bg-white hover:bg-gray-100 text-primary px-8 py-3 rounded-lg font-semibold transition-colors\",\n                                            children: \"Nap\\xedsať email\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 409,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 408,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-playfair font-bold text-center text-gray-800 mb-12\",\n                                    children: \"Často kladen\\xe9 ot\\xe1zky\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-3xl mx-auto space-y-6\",\n                                    children: packageData.faq.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 rounded-lg p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-800 mb-3\",\n                                                    children: item.question\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: item.answer\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 435,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 434,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"tel:+421951553464\",\n                        className: \"sticky-cta bg-gold hover:bg-gold-dark text-white px-6 py-3 rounded-full font-semibold\",\n                        children: \"\\uD83D\\uDCDE Zavolať\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 455,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"bg-gray-800 text-white py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-3 mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-playfair text-2xl font-semibold\",\n                                        children: \"eSpomienka\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400\",\n                                    children: \"\\xa9 2025 eSpomienka. Všetky pr\\xe1va vyhraden\\xe9.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 464,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 463,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                lineNumber: 254,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(PackagePage, \"vQduR7x+OPXj6PSmJyFnf+hU7bg=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = PackagePage;\nvar _c;\n$RefreshReg$(_c, \"PackagePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/balicky/[slug].tsx\n"));

/***/ }),

/***/ "./utils/analytics.ts":
/*!****************************!*\
  !*** ./utils/analytics.ts ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   trackCTAClick: function() { return /* binding */ trackCTAClick; },\n/* harmony export */   trackEvent: function() { return /* binding */ trackEvent; },\n/* harmony export */   trackPackageClick: function() { return /* binding */ trackPackageClick; },\n/* harmony export */   trackPageView: function() { return /* binding */ trackPageView; }\n/* harmony export */ });\n// Google Analytics tracking functions\nconst trackEvent = (action, category, label, value)=>{\n    if ( true && window.gtag) {\n        window.gtag(\"event\", action, {\n            event_category: category,\n            event_label: label,\n            value: value\n        });\n    }\n};\nconst trackPackageClick = (packageName, packagePrice)=>{\n    trackEvent(\"click\", \"package\", packageName, parseInt(packagePrice.replace(\"€\", \"\")));\n};\nconst trackCTAClick = (ctaType, location)=>{\n    trackEvent(\"click\", \"cta\", \"\".concat(ctaType, \"_\").concat(location));\n};\nconst trackPageView = (pageName)=>{\n    if ( true && window.gtag) {\n        window.gtag(\"config\", \"GA_MEASUREMENT_ID\", {\n            page_title: pageName,\n            page_location: window.location.href\n        });\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/analytics.ts\n"));

/***/ })

});