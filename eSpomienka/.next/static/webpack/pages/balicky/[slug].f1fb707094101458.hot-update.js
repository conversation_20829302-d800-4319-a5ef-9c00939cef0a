"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/balicky/[slug]",{

/***/ "./pages/balicky/[slug].tsx":
/*!**********************************!*\
  !*** ./pages/balicky/[slug].tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSG: function() { return /* binding */ __N_SSG; },\n/* harmony export */   \"default\": function() { return /* binding */ PackagePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/LoadingSpinner */ \"./components/LoadingSpinner.tsx\");\n/* harmony import */ var _components_Breadcrumb__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/Breadcrumb */ \"./components/Breadcrumb.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nvar __N_SSG = true;\nfunction PackagePage(param) {\n    let { packageData } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    if (router.isFallback) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        size: \"lg\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 207,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Nač\\xedtavam bal\\xedček...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                lineNumber: 206,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n            lineNumber: 205,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: [\n                            packageData.name,\n                            \" - eSpomienka\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"\".concat(packageData.name, \" - \").concat(packageData.description)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"keywords\",\n                        content: \"memorial, spomienky, \".concat(packageData.slug, \", video spomienky, QR k\\xf3d, hrob\")\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:title\",\n                        content: \"\".concat(packageData.name, \" - eSpomienka\")\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:description\",\n                        content: packageData.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:type\",\n                        content: \"website\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:card\",\n                        content: \"summary_large_image\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:title\",\n                        content: \"\".concat(packageData.name, \" - eSpomienka\")\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:description\",\n                        content: packageData.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"canonical\",\n                        href: \"https://espomienka.sk/balicky/\".concat(packageData.slug)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        children: JSON.stringify({\n                            \"@context\": \"https://schema.org\",\n                            \"@type\": \"Service\",\n                            \"name\": packageData.name,\n                            \"description\": packageData.description,\n                            \"provider\": {\n                                \"@type\": \"Organization\",\n                                \"name\": \"eSpomienka\",\n                                \"telephone\": \"+421951553464\",\n                                \"email\": \"<EMAIL>\"\n                            },\n                            \"offers\": {\n                                \"@type\": \"Offer\",\n                                \"price\": packageData.price.replace(\"€\", \"\"),\n                                \"priceCurrency\": \"EUR\"\n                            }\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"bg-primary shadow-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/\",\n                                            className: \"font-playfair text-2xl font-semibold text-white\",\n                                            children: \"eSpomienka\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"flex items-center space-x-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/\",\n                                                className: \"text-white hover:text-gold transition-colors\",\n                                                children: \"Domov\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/blog\",\n                                                className: \"text-white hover:text-gold transition-colors\",\n                                                children: \"Blog\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"tel:+421951553464\",\n                                                className: \"text-white hover:text-gold transition-colors\",\n                                                children: \"+421 951 553 464\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                lineNumber: 252,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Breadcrumb__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        items: [\n                            {\n                                label: \"Domov\",\n                                href: \"/\"\n                            },\n                            {\n                                label: \"Bal\\xedčky\",\n                                href: \"/#packages\"\n                            },\n                            {\n                                label: packageData.name\n                            }\n                        ]\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-gradient-to-r from-primary to-primary-dark\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-6xl mb-4\",\n                                    children: packageData.emoji\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl md:text-5xl font-playfair font-bold text-white mb-4\",\n                                    children: packageData.name\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-5xl font-bold text-gold mb-6\",\n                                    children: packageData.price\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-200 max-w-2xl mx-auto mb-8\",\n                                    children: packageData.description\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"tel:+421951553464\",\n                                    className: \"bg-gold hover:bg-gold-dark text-white px-8 py-3 rounded-lg font-semibold transition-colors inline-block\",\n                                    children: \"Objednať konzult\\xe1ciu\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 284,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-playfair font-bold text-center text-gray-800 mb-12\",\n                                    children: \"Čo dostanete\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                    children: packageData.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-6 h-6 bg-gold rounded-full flex items-center justify-center flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-white\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-700\",\n                                                    children: feature\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 303,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-cream\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-playfair font-bold text-center text-gray-800 mb-12\",\n                                    children: \"Ako to funguje\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-4xl mx-auto\",\n                                    children: packageData.process.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-4 mb-8 last:mb-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gold rounded-full flex items-center justify-center flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-bold\",\n                                                        children: step.step\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-semibold text-gray-800 mb-2\",\n                                                            children: step.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: step.description\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 325,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 324,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-playfair font-bold text-center text-gray-800 mb-12\",\n                                    children: \"Uk\\xe1žky našej pr\\xe1ce\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                    children: [\n                                        1,\n                                        2,\n                                        3\n                                    ].map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-100 rounded-lg p-8 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-gray-300 rounded-full mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-600 mb-2\",\n                                                    children: \"Pripravujeme uk\\xe1žky\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500\",\n                                                    children: \"Segera hadir\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, item, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 347,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 346,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-cream\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-playfair font-bold text-center text-gray-800 mb-12\",\n                                    children: \"Porovnanie bal\\xedčkov\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-4xl mx-auto bg-white rounded-lg shadow-lg overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-4 gap-4 p-6 bg-gray-50 font-semibold\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: \"Funkcie\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: \"Z\\xe1kladn\\xe1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: \"Premium\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: \"Exclusive\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"divide-y\",\n                                            children: [\n                                                \"Memorial webstr\\xe1nka\",\n                                                \"QR k\\xf3d\",\n                                                \"Video spomienky\",\n                                                \"Neobmedzen\\xe9 fotky\",\n                                                \"Kovov\\xe1/Granitov\\xe1 tabuľka\",\n                                                \"VIP servis\"\n                                            ].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-4 gap-4 p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-700\",\n                                                            children: feature\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: index < 2 ? \"✅\" : \"❌\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: index < 5 ? \"✅\" : \"❌\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: \"✅\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 365,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 364,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-primary\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-playfair font-bold text-white mb-6\",\n                                    children: \"Pripraven\\xed začať?\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-200 max-w-2xl mx-auto mb-8\",\n                                    children: \"Kontaktujte n\\xe1s pre bezplatn\\xfa konzult\\xe1ciu. Plat\\xedte až keď ste \\xfaplne spokojn\\xed.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"tel:+421951553464\",\n                                            className: \"bg-gold hover:bg-gold-dark text-white px-8 py-3 rounded-lg font-semibold transition-colors\",\n                                            children: \"Zavolať +421 951 553 464\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"mailto:<EMAIL>\",\n                                            className: \"bg-white hover:bg-gray-100 text-primary px-8 py-3 rounded-lg font-semibold transition-colors\",\n                                            children: \"Nap\\xedsať email\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 403,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 402,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-playfair font-bold text-center text-gray-800 mb-12\",\n                                    children: \"Často kladen\\xe9 ot\\xe1zky\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-3xl mx-auto space-y-6\",\n                                    children: packageData.faq.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 rounded-lg p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-800 mb-3\",\n                                                    children: item.question\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: item.answer\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                                    lineNumber: 439,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 429,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 428,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"tel:+421951553464\",\n                        className: \"sticky-cta bg-gold hover:bg-gold-dark text-white px-6 py-3 rounded-full font-semibold\",\n                        children: \"\\uD83D\\uDCDE Zavolať\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 449,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"bg-gray-800 text-white py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-3 mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-playfair text-2xl font-semibold\",\n                                        children: \"eSpomienka\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400\",\n                                    children: \"\\xa9 2025 eSpomienka. Všetky pr\\xe1va vyhraden\\xe9.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                                    lineNumber: 462,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                            lineNumber: 458,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                        lineNumber: 457,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/eSpomienka/pages/balicky/[slug].tsx\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(PackagePage, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = PackagePage;\nvar _c;\n$RefreshReg$(_c, \"PackagePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/balicky/[slug].tsx\n"));

/***/ })

});