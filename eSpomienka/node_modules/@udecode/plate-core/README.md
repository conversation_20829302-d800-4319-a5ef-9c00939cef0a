# Plate core architecture

This package implements the Plate core architecture: a plugin system which
glues everything together.

## Documentation

Check out:
- [Basic Editor](https://plate.udecode.io/docs/basic-editor)
- [Plate](https://plate.udecode.io/docs/Plate) and the following guides.

## API

See the [API documentation](https://plate-api.udecode.io/globals.html). 

## License

[MIT](../../LICENSE)
