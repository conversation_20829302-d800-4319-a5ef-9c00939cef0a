"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StorageAPI = void 0;
function isQuotaError(storage, e) {
    return (e instanceof DOMException &&
        (e.code === 22 ||
            e.code === 1014 ||
            e.name === 'QuotaExceededError' ||
            e.name === 'NS_ERROR_DOM_QUOTA_REACHED') &&
        storage.length !== 0);
}
var StorageAPI = (function () {
    function StorageAPI(storage) {
        if (storage) {
            this.storage = storage;
        }
        else if (storage === null) {
            this.storage = null;
        }
        else if (typeof window === 'undefined') {
            this.storage = null;
        }
        else {
            this.storage = {
                getItem: window.localStorage.getItem.bind(window.localStorage),
                setItem: window.localStorage.setItem.bind(window.localStorage),
                removeItem: window.localStorage.removeItem.bind(window.localStorage),
                get length() {
                    var keys = 0;
                    for (var key in window.localStorage) {
                        if (key.indexOf("".concat(STORAGE_NAMESPACE, ":")) === 0) {
                            keys += 1;
                        }
                    }
                    return keys;
                },
                clear: function () {
                    for (var key in window.localStorage) {
                        if (key.indexOf("".concat(STORAGE_NAMESPACE, ":")) === 0) {
                            window.localStorage.removeItem(key);
                        }
                    }
                },
            };
        }
    }
    StorageAPI.prototype.get = function (name) {
        if (!this.storage) {
            return null;
        }
        var key = "".concat(STORAGE_NAMESPACE, ":").concat(name);
        var value = this.storage.getItem(key);
        if (value === 'null' || value === 'undefined') {
            this.storage.removeItem(key);
            return null;
        }
        return value || null;
    };
    StorageAPI.prototype.set = function (name, value) {
        var quotaError = false;
        var error = null;
        if (this.storage) {
            var key = "".concat(STORAGE_NAMESPACE, ":").concat(name);
            if (value) {
                try {
                    this.storage.setItem(key, value);
                }
                catch (e) {
                    error = e instanceof Error ? e : new Error("".concat(e));
                    quotaError = isQuotaError(this.storage, e);
                }
            }
            else {
                this.storage.removeItem(key);
            }
        }
        return { isQuotaError: quotaError, error: error };
    };
    StorageAPI.prototype.clear = function () {
        if (this.storage) {
            this.storage.clear();
        }
    };
    return StorageAPI;
}());
exports.StorageAPI = StorageAPI;
var STORAGE_NAMESPACE = 'graphiql';
//# sourceMappingURL=base.js.map