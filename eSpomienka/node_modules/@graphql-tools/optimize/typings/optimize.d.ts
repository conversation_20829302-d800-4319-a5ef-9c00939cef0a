import { DocumentNode } from 'graphql';
import { DocumentOptimizer } from './types.js';
/**
 * This method accept a DocumentNode and applies the optimizations you wish to use.
 * You can override the default ones or provide you own optimizers if you wish.
 *
 * @param node document to optimize
 * @param optimizers optional, list of optimizer to use
 */
export declare function optimizeDocumentNode(node: DocumentNode, optimizers?: DocumentOptimizer[]): DocumentNode;
