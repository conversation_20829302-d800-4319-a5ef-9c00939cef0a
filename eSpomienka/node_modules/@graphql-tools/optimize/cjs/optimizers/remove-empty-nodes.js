"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.removeEmptyNodes = void 0;
const graphql_1 = require("graphql");
/**
 * This optimizer removes empty nodes/arrays (directives/argument/variableDefinitions) from a given DocumentNode of operation/fragment.
 * @param input
 */
const removeEmptyNodes = input => {
    function transformNode(node) {
        let resultNode = node;
        if (resultNode.directives && Array.isArray(resultNode.directives) && resultNode.directives.length === 0) {
            const { directives, ...rest } = resultNode;
            resultNode = rest;
        }
        if (resultNode.arguments && Array.isArray(resultNode.arguments) && resultNode.arguments.length === 0) {
            const { arguments: args, ...rest } = resultNode;
            resultNode = rest;
        }
        if (resultNode.variableDefinitions &&
            Array.isArray(resultNode.variableDefinitions) &&
            resultNode.variableDefinitions.length === 0) {
            const { variableDefinitions, ...rest } = resultNode;
            resultNode = rest;
        }
        return resultNode;
    }
    return (0, graphql_1.visit)(input, {
        // Request/Operation:
        FragmentDefinition: transformNode,
        OperationDefinition: transformNode,
        VariableDefinition: transformNode,
        Field: transformNode,
        FragmentSpread: transformNode,
        InlineFragment: transformNode,
        Name: transformNode,
        Directive: transformNode,
        // Schema Definition:
        SchemaDefinition: transformNode,
        ScalarTypeDefinition: transformNode,
        ObjectTypeDefinition: transformNode,
        InterfaceTypeDefinition: transformNode,
        UnionTypeDefinition: transformNode,
        EnumTypeDefinition: transformNode,
        EnumValueDefinition: transformNode,
        InputObjectTypeDefinition: transformNode,
        FieldDefinition: transformNode,
        InputValueDefinition: transformNode,
        // Schema Extension:
        SchemaExtension: transformNode,
        ScalarTypeExtension: transformNode,
        ObjectTypeExtension: transformNode,
        InterfaceTypeExtension: transformNode,
        UnionTypeExtension: transformNode,
        EnumTypeExtension: transformNode,
        InputObjectTypeExtension: transformNode,
    });
};
exports.removeEmptyNodes = removeEmptyNodes;
