import {
  d
} from "./chunk-WIXYZC3K.js";
import "./chunk-MEKKV4OY.js";
import {
  getDiagnostics
} from "./chunk-4LQY6QSN.js";
import "./chunk-HD22INE4.js";
import "./chunk-AUZ3RYOM.js";

// node_modules/@graphiql/react/dist/lint.es2.js
var a = ["error", "warning", "information", "hint"];
var l = {
  "GraphQL: Validation": "validation",
  "GraphQL: Deprecation": "deprecation",
  "GraphQL: Syntax": "syntax"
};
d.registerHelper("lint", "graphql", (n, s) => {
  const { schema: i, validationRules: r, externalFragments: o } = s;
  return getDiagnostics(n, i, r, void 0, o).map((e) => ({
    message: e.message,
    severity: e.severity ? a[e.severity - 1] : a[0],
    type: e.source ? l[e.source] : void 0,
    from: d.<PERSON><PERSON>(e.range.start.line, e.range.start.character),
    to: d.<PERSON>s(e.range.end.line, e.range.end.character)
  }));
});
//# sourceMappingURL=lint.es2-CF5U2NXE.js.map
