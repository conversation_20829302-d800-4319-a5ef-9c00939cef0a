import {
  __commonJS,
  __require,
  __toESM
} from "./chunk-AUZ3RYOM.js";

// node_modules/async-lock/lib/index.js
var require_lib = __commonJS({
  "node_modules/async-lock/lib/index.js"(exports, module) {
    "use strict";
    var AsyncLock2 = function(opts) {
      opts = opts || {};
      this.Promise = opts.Promise || Promise;
      this.queues = /* @__PURE__ */ Object.create(null);
      this.domainReentrant = opts.domainReentrant || false;
      if (this.domainReentrant) {
        if (typeof process === "undefined" || typeof process.domain === "undefined") {
          throw new Error(
            "Domain-reentrant locks require `process.domain` to exist. Please flip `opts.domainReentrant = false`, use a NodeJS version that still implements Domain, or install a browser polyfill."
          );
        }
        this.domains = /* @__PURE__ */ Object.create(null);
      }
      this.timeout = opts.timeout || AsyncLock2.DEFAULT_TIMEOUT;
      this.maxOccupationTime = opts.maxOccupationTime || AsyncLock2.DEFAULT_MAX_OCCUPATION_TIME;
      this.maxExecutionTime = opts.maxExecutionTime || AsyncLock2.DEFAULT_MAX_EXECUTION_TIME;
      if (opts.maxPending === Infinity || Number.isInteger(opts.maxPending) && opts.maxPending >= 0) {
        this.maxPending = opts.maxPending;
      } else {
        this.maxPending = AsyncLock2.DEFAULT_MAX_PENDING;
      }
    };
    AsyncLock2.DEFAULT_TIMEOUT = 0;
    AsyncLock2.DEFAULT_MAX_OCCUPATION_TIME = 0;
    AsyncLock2.DEFAULT_MAX_EXECUTION_TIME = 0;
    AsyncLock2.DEFAULT_MAX_PENDING = 1e3;
    AsyncLock2.prototype.acquire = function(key, fn, cb, opts) {
      if (Array.isArray(key)) {
        return this._acquireBatch(key, fn, cb, opts);
      }
      if (typeof fn !== "function") {
        throw new Error("You must pass a function to execute");
      }
      var deferredResolve = null;
      var deferredReject = null;
      var deferred = null;
      if (typeof cb !== "function") {
        opts = cb;
        cb = null;
        deferred = new this.Promise(function(resolve, reject) {
          deferredResolve = resolve;
          deferredReject = reject;
        });
      }
      opts = opts || {};
      var resolved = false;
      var timer = null;
      var occupationTimer = null;
      var executionTimer = null;
      var self = this;
      var done = function(locked, err, ret) {
        if (occupationTimer) {
          clearTimeout(occupationTimer);
          occupationTimer = null;
        }
        if (executionTimer) {
          clearTimeout(executionTimer);
          executionTimer = null;
        }
        if (locked) {
          if (!!self.queues[key] && self.queues[key].length === 0) {
            delete self.queues[key];
          }
          if (self.domainReentrant) {
            delete self.domains[key];
          }
        }
        if (!resolved) {
          if (!deferred) {
            if (typeof cb === "function") {
              cb(err, ret);
            }
          } else {
            if (err) {
              deferredReject(err);
            } else {
              deferredResolve(ret);
            }
          }
          resolved = true;
        }
        if (locked) {
          if (!!self.queues[key] && self.queues[key].length > 0) {
            self.queues[key].shift()();
          }
        }
      };
      var exec = function(locked) {
        if (resolved) {
          return done(locked);
        }
        if (timer) {
          clearTimeout(timer);
          timer = null;
        }
        if (self.domainReentrant && locked) {
          self.domains[key] = process.domain;
        }
        var maxExecutionTime = opts.maxExecutionTime || self.maxExecutionTime;
        if (maxExecutionTime) {
          executionTimer = setTimeout(function() {
            if (!!self.queues[key]) {
              done(locked, new Error("Maximum execution time is exceeded " + key));
            }
          }, maxExecutionTime);
        }
        if (fn.length === 1) {
          var called = false;
          try {
            fn(function(err, ret) {
              if (!called) {
                called = true;
                done(locked, err, ret);
              }
            });
          } catch (err) {
            if (!called) {
              called = true;
              done(locked, err);
            }
          }
        } else {
          self._promiseTry(function() {
            return fn();
          }).then(function(ret) {
            done(locked, void 0, ret);
          }, function(error) {
            done(locked, error);
          });
        }
      };
      if (self.domainReentrant && !!process.domain) {
        exec = process.domain.bind(exec);
      }
      var maxPending = opts.maxPending || self.maxPending;
      if (!self.queues[key]) {
        self.queues[key] = [];
        exec(true);
      } else if (self.domainReentrant && !!process.domain && process.domain === self.domains[key]) {
        exec(false);
      } else if (self.queues[key].length >= maxPending) {
        done(false, new Error("Too many pending tasks in queue " + key));
      } else {
        var taskFn = function() {
          exec(true);
        };
        if (opts.skipQueue) {
          self.queues[key].unshift(taskFn);
        } else {
          self.queues[key].push(taskFn);
        }
        var timeout = opts.timeout || self.timeout;
        if (timeout) {
          timer = setTimeout(function() {
            timer = null;
            done(false, new Error("async-lock timed out in queue " + key));
          }, timeout);
        }
      }
      var maxOccupationTime = opts.maxOccupationTime || self.maxOccupationTime;
      if (maxOccupationTime) {
        occupationTimer = setTimeout(function() {
          if (!!self.queues[key]) {
            done(false, new Error("Maximum occupation time is exceeded in queue " + key));
          }
        }, maxOccupationTime);
      }
      if (deferred) {
        return deferred;
      }
    };
    AsyncLock2.prototype._acquireBatch = function(keys, fn, cb, opts) {
      if (typeof cb !== "function") {
        opts = cb;
        cb = null;
      }
      var self = this;
      var getFn = function(key, fn2) {
        return function(cb2) {
          self.acquire(key, fn2, cb2, opts);
        };
      };
      var fnx = keys.reduceRight(function(prev, key) {
        return getFn(key, prev);
      }, fn);
      if (typeof cb === "function") {
        fnx(cb);
      } else {
        return new this.Promise(function(resolve, reject) {
          if (fnx.length === 1) {
            fnx(function(err, ret) {
              if (err) {
                reject(err);
              } else {
                resolve(ret);
              }
            });
          } else {
            resolve(fnx());
          }
        });
      }
    };
    AsyncLock2.prototype.isBusy = function(key) {
      if (!key) {
        return Object.keys(this.queues).length > 0;
      } else {
        return !!this.queues[key];
      }
    };
    AsyncLock2.prototype._promiseTry = function(fn) {
      try {
        return this.Promise.resolve(fn());
      } catch (e) {
        return this.Promise.reject(e);
      }
    };
    module.exports = AsyncLock2;
  }
});

// node_modules/async-lock/index.js
var require_async_lock = __commonJS({
  "node_modules/async-lock/index.js"(exports, module) {
    "use strict";
    module.exports = require_lib();
  }
});

// node_modules/@tinacms/app/node_modules/tinacms/dist/client.mjs
var import_async_lock = __toESM(require_async_lock(), 1);
var TINA_HOST = "content.tinajs.io";
function replaceGithubPathSplit(url, replacement) {
  const parts = url.split("github/");
  if (parts.length > 1 && replacement) {
    return parts[0] + "github/" + replacement;
  } else {
    return url;
  }
}
var TinaClient = class {
  constructor({
    token,
    url,
    queries,
    errorPolicy,
    cacheDir
  }) {
    this.initialized = false;
    this.apiUrl = url;
    this.readonlyToken = token == null ? void 0 : token.trim();
    this.queries = queries(this);
    this.errorPolicy = errorPolicy || "throw";
    this.cacheDir = cacheDir || "";
  }
  async init() {
    if (this.initialized) {
      return;
    }
    try {
      if (this.cacheDir && typeof window === "undefined" && typeof __require !== "undefined") {
        const { NodeCache } = await import("./node-cache-5e8db9f0-L56A52NC.js");
        this.cache = await NodeCache(this.cacheDir);
        this.cacheLock = new import_async_lock.default();
      }
    } catch (e) {
      console.error(e);
    }
    this.initialized = true;
  }
  async request({ errorPolicy, ...args }, options) {
    var _a;
    await this.init();
    const errorPolicyDefined = errorPolicy || this.errorPolicy;
    const headers = new Headers();
    if (this.readonlyToken) {
      headers.append("X-API-KEY", this.readonlyToken);
    }
    headers.append("Content-Type", "application/json");
    if (options == null ? void 0 : options.fetchOptions) {
      if ((_a = options == null ? void 0 : options.fetchOptions) == null ? void 0 : _a.headers) {
        Object.entries(options.fetchOptions.headers).forEach(([key2, value]) => {
          headers.append(key2, value);
        });
      }
    }
    const { headers: _, ...providedFetchOptions } = (options == null ? void 0 : options.fetchOptions) || {};
    const bodyString = JSON.stringify({
      query: args.query,
      variables: (args == null ? void 0 : args.variables) || {}
    });
    const optionsObject = {
      method: "POST",
      headers,
      body: bodyString,
      redirect: "follow",
      ...providedFetchOptions
    };
    const draftBranch = headers.get("x-branch");
    const url = replaceGithubPathSplit((args == null ? void 0 : args.url) || this.apiUrl, draftBranch);
    let key = "";
    let result;
    if (this.cache) {
      key = this.cache.makeKey(bodyString);
      await this.cacheLock.acquire(key, async () => {
        result = await this.cache.get(key);
        if (!result) {
          result = await requestFromServer(
            url,
            args.query,
            optionsObject,
            errorPolicyDefined
          );
          await this.cache.set(key, result);
        }
      });
    } else {
      result = await requestFromServer(
        url,
        args.query,
        optionsObject,
        errorPolicyDefined
      );
    }
    return result;
  }
};
async function requestFromServer(url, query, optionsObject, errorPolicyDefined) {
  const res = await fetch(url, optionsObject);
  if (!res.ok) {
    let additionalInfo = "";
    if (res.status === 401) {
      additionalInfo = "Please check that your client ID, URL and read only token are configured properly.";
    }
    throw new Error(
      `Server responded with status code ${res.status}, ${res.statusText}. ${additionalInfo ? additionalInfo : ""} Please see our FAQ for more information: https://tina.io/docs/errors/faq/`
    );
  }
  const json = await res.json();
  if (json.errors && errorPolicyDefined === "throw") {
    throw new Error(
      `Unable to fetch, please see our FAQ for more information: https://tina.io/docs/errors/faq/
      Errors: 
	${json.errors.map((error) => error.message).join("\n")}`
    );
  }
  const result = {
    data: json == null ? void 0 : json.data,
    errors: (json == null ? void 0 : json.errors) || null,
    query
  };
  return result;
}
function createClient(args) {
  const client = new TinaClient(args);
  return client;
}
export {
  TINA_HOST,
  TinaClient,
  createClient
};
//# sourceMappingURL=tinacms_dist_client.js.map
