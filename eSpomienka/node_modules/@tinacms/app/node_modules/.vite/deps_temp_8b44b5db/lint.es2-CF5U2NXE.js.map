{"version": 3, "sources": ["../../../../../@graphiql/codemirror-graphql/esm/lint.js"], "sourcesContent": ["import CodeMirror from 'codemirror';\nimport { getDiagnostics } from 'graphql-language-service';\nconst SEVERITY = ['error', 'warning', 'information', 'hint'];\nconst TYPE = {\n    'GraphQL: Validation': 'validation',\n    'GraphQL: Deprecation': 'deprecation',\n    'GraphQL: Syntax': 'syntax',\n};\nCodeMirror.registerHelper('lint', 'graphql', (text, options) => {\n    const { schema, validationRules, externalFragments } = options;\n    const rawResults = getDiagnostics(text, schema, validationRules, undefined, externalFragments);\n    const results = rawResults.map(error => ({\n        message: error.message,\n        severity: error.severity ? SEVERITY[error.severity - 1] : SEVERITY[0],\n        type: error.source ? TYPE[error.source] : undefined,\n        from: CodeMirror.Pos(error.range.start.line, error.range.start.character),\n        to: CodeMirror.Pos(error.range.end.line, error.range.end.character),\n    }));\n    return results;\n});\n//# sourceMappingURL=lint.js.map"], "mappings": ";;;;;;;;;;;AAEA,IAAMA,IAAW,CAAC,SAAS,WAAW,eAAe,MAAM;AAA3D,IACMC,IAAO;EACT,uBAAuB;EACvB,wBAAwB;EACxB,mBAAmB;AACvB;AACAC,EAAW,eAAe,QAAQ,WAAW,CAACC,GAAMC,MAAY;AAC5D,QAAM,EAAE,QAAAC,GAAQ,iBAAAC,GAAiB,mBAAAC,EAAiB,IAAKH;AASvD,SARmBI,eAAeL,GAAME,GAAQC,GAAiB,QAAWC,CAAiB,EAClE,IAAI,CAAAE,OAAU;IACrC,SAASA,EAAM;IACf,UAAUA,EAAM,WAAWT,EAASS,EAAM,WAAW,CAAC,IAAIT,EAAS,CAAC;IACpE,MAAMS,EAAM,SAASR,EAAKQ,EAAM,MAAM,IAAI;IAC1C,MAAMP,EAAW,IAAIO,EAAM,MAAM,MAAM,MAAMA,EAAM,MAAM,MAAM,SAAS;IACxE,IAAIP,EAAW,IAAIO,EAAM,MAAM,IAAI,MAAMA,EAAM,MAAM,IAAI,SAAS;EACrE,EAAC;AAEN,CAAC;", "names": ["SEVERITY", "TYPE", "CodeMirror", "text", "options", "schema", "validationRules", "externalFragments", "getDiagnostics", "error"]}