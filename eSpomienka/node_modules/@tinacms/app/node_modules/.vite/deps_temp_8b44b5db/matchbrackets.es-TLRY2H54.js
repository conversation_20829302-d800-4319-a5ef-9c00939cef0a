import {
  j
} from "./chunk-A5NBTC5H.js";
import {
  hu
} from "./chunk-MEKKV4OY.js";
import "./chunk-AUZ3RYOM.js";

// node_modules/@graphiql/react/dist/matchbrackets.es.js
var f = Object.defineProperty;
var s = (e, o) => f(e, "name", { value: o, configurable: true });
function p(e, o) {
  for (var a = 0; a < o.length; a++) {
    const t = o[a];
    if (typeof t != "string" && !Array.isArray(t)) {
      for (const r in t)
        if (r !== "default" && !(r in e)) {
          const c = Object.getOwnPropertyDescriptor(t, r);
          c && Object.defineProperty(e, r, c.get ? c : {
            enumerable: true,
            get: () => t[r]
          });
        }
    }
  }
  return Object.freeze(Object.defineProperty(e, Symbol.toStringTag, { value: "Module" }));
}
s(p, "_mergeNamespaces");
var n = j();
var u = hu(n);
var y = p({
  __proto__: null,
  default: u
}, [n]);
export {
  y as m
};
//# sourceMappingURL=matchbrackets.es-TLRY2H54.js.map
