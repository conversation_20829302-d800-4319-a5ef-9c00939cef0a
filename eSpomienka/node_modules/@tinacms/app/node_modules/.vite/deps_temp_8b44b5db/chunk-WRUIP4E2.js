import {
  _extends
} from "./chunk-DNGFK2RG.js";

// node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js
function _objectWithoutPropertiesLoose(r, e) {
  if (null == r)
    return {};
  var t = {};
  for (var n in r)
    if ({}.hasOwnProperty.call(r, n)) {
      if (-1 !== e.indexOf(n))
        continue;
      t[n] = r[n];
    }
  return t;
}

// node_modules/final-form/dist/final-form.es.js
var charCodeOfDot = ".".charCodeAt(0);
var reEscapeChar = /\\(\\)?/g;
var rePropName = RegExp(
  // Match anything that isn't a dot or bracket.
  `[^.[\\]]+|\\[(?:([^"'][^[]*)|(["'])((?:(?!\\2)[^\\\\]|\\\\.)*?)\\2)\\]|(?=(?:\\.|\\[\\])(?:\\.|\\[\\]|$))`,
  "g"
);
var stringToPath = function stringToPath2(string) {
  var result = [];
  if (string.charCodeAt(0) === charCodeOfDot) {
    result.push("");
  }
  string.replace(rePropName, function(match, expression, quote, subString) {
    var key = match;
    if (quote) {
      key = subString.replace(reEscapeChar, "$1");
    } else if (expression) {
      key = expression.trim();
    }
    result.push(key);
  });
  return result;
};
var keysCache = {};
var keysRegex = /[.[\]]+/;
var toPath = function toPath2(key) {
  if (key === null || key === void 0 || !key.length) {
    return [];
  }
  if (typeof key !== "string") {
    throw new Error("toPath() expects a string");
  }
  if (keysCache[key] == null) {
    if (key.endsWith("[]")) {
      keysCache[key] = key.split(keysRegex).filter(Boolean);
    } else {
      keysCache[key] = stringToPath(key);
    }
  }
  return keysCache[key];
};
var getIn = function getIn2(state, complexKey) {
  var path = toPath(complexKey);
  var current = state;
  for (var i = 0; i < path.length; i++) {
    var key = path[i];
    if (current === void 0 || current === null || typeof current !== "object" || Array.isArray(current) && isNaN(key)) {
      return void 0;
    }
    current = current[key];
  }
  return current;
};
function _toPropertyKey(arg) {
  var key = _toPrimitive(arg, "string");
  return typeof key === "symbol" ? key : String(key);
}
function _toPrimitive(input, hint) {
  if (typeof input !== "object" || input === null)
    return input;
  var prim = input[Symbol.toPrimitive];
  if (prim !== void 0) {
    var res = prim.call(input, hint || "default");
    if (typeof res !== "object")
      return res;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return (hint === "string" ? String : Number)(input);
}
var setInRecursor = function setInRecursor2(current, index, path, value, destroyArrays) {
  if (index >= path.length) {
    return value;
  }
  var key = path[index];
  if (isNaN(key)) {
    var _extends2;
    if (current === void 0 || current === null) {
      var _ref;
      var _result = setInRecursor2(void 0, index + 1, path, value, destroyArrays);
      return _result === void 0 ? void 0 : (_ref = {}, _ref[key] = _result, _ref);
    }
    if (Array.isArray(current)) {
      throw new Error("Cannot set a non-numeric property on an array");
    }
    var _result2 = setInRecursor2(current[key], index + 1, path, value, destroyArrays);
    if (_result2 === void 0) {
      var numKeys = Object.keys(current).length;
      if (current[key] === void 0 && numKeys === 0) {
        return void 0;
      }
      if (current[key] !== void 0 && numKeys <= 1) {
        if (!isNaN(path[index - 1]) && !destroyArrays) {
          return {};
        } else {
          return void 0;
        }
      }
      current[key];
      var _final = _objectWithoutPropertiesLoose(current, [key].map(_toPropertyKey));
      return _final;
    }
    return _extends({}, current, (_extends2 = {}, _extends2[key] = _result2, _extends2));
  }
  var numericKey = Number(key);
  if (current === void 0 || current === null) {
    var _result3 = setInRecursor2(void 0, index + 1, path, value, destroyArrays);
    if (_result3 === void 0) {
      return void 0;
    }
    var _array = [];
    _array[numericKey] = _result3;
    return _array;
  }
  if (!Array.isArray(current)) {
    throw new Error("Cannot set a numeric property on an object");
  }
  var existingValue = current[numericKey];
  var result = setInRecursor2(existingValue, index + 1, path, value, destroyArrays);
  var array = [].concat(current);
  if (destroyArrays && result === void 0) {
    array.splice(numericKey, 1);
    if (array.length === 0) {
      return void 0;
    }
  } else {
    array[numericKey] = result;
  }
  return array;
};
var setIn = function setIn2(state, key, value, destroyArrays) {
  if (destroyArrays === void 0) {
    destroyArrays = false;
  }
  if (state === void 0 || state === null) {
    throw new Error("Cannot call setIn() with " + String(state) + " state");
  }
  if (key === void 0 || key === null) {
    throw new Error("Cannot call setIn() with " + String(key) + " key");
  }
  return setInRecursor(state, 0, toPath(key), value, destroyArrays);
};
var FORM_ERROR = "FINAL_FORM/form-error";
var ARRAY_ERROR = "FINAL_FORM/array-error";
function publishFieldState(formState, field) {
  var errors = formState.errors, initialValues = formState.initialValues, lastSubmittedValues = formState.lastSubmittedValues, submitErrors = formState.submitErrors, submitFailed = formState.submitFailed, submitSucceeded = formState.submitSucceeded, submitting = formState.submitting, values = formState.values;
  var active = field.active, blur = field.blur, change = field.change, data = field.data, focus = field.focus, modified = field.modified, modifiedSinceLastSubmit = field.modifiedSinceLastSubmit, name = field.name, touched = field.touched, validating = field.validating, visited = field.visited;
  var value = getIn(values, name);
  var error = getIn(errors, name);
  if (error && error[ARRAY_ERROR]) {
    error = error[ARRAY_ERROR];
  }
  var submitError = submitErrors && getIn(submitErrors, name);
  var initial = initialValues && getIn(initialValues, name);
  var pristine = field.isEqual(initial, value);
  var dirtySinceLastSubmit = !!(lastSubmittedValues && !field.isEqual(getIn(lastSubmittedValues, name), value));
  var valid = !error && !submitError;
  return {
    active,
    blur,
    change,
    data,
    dirty: !pristine,
    dirtySinceLastSubmit,
    error,
    focus,
    initial,
    invalid: !valid,
    length: Array.isArray(value) ? value.length : void 0,
    modified,
    modifiedSinceLastSubmit,
    name,
    pristine,
    submitError,
    submitFailed,
    submitSucceeded,
    submitting,
    touched,
    valid,
    value,
    visited,
    validating
  };
}
var fieldSubscriptionItems = ["active", "data", "dirty", "dirtySinceLastSubmit", "error", "initial", "invalid", "length", "modified", "modifiedSinceLastSubmit", "pristine", "submitError", "submitFailed", "submitSucceeded", "submitting", "touched", "valid", "value", "visited", "validating"];
var shallowEqual = function shallowEqual2(a, b) {
  if (a === b) {
    return true;
  }
  if (typeof a !== "object" || !a || typeof b !== "object" || !b) {
    return false;
  }
  var keysA = Object.keys(a);
  var keysB = Object.keys(b);
  if (keysA.length !== keysB.length) {
    return false;
  }
  var bHasOwnProperty = Object.prototype.hasOwnProperty.bind(b);
  for (var idx = 0; idx < keysA.length; idx++) {
    var key = keysA[idx];
    if (!bHasOwnProperty(key) || a[key] !== b[key]) {
      return false;
    }
  }
  return true;
};
function subscriptionFilter(dest, src, previous, subscription, keys, shallowEqualKeys2) {
  var different = false;
  keys.forEach(function(key) {
    if (subscription[key]) {
      dest[key] = src[key];
      if (!previous || (~shallowEqualKeys2.indexOf(key) ? !shallowEqual(src[key], previous[key]) : src[key] !== previous[key])) {
        different = true;
      }
    }
  });
  return different;
}
var shallowEqualKeys$1 = ["data"];
var filterFieldState = function filterFieldState2(state, previousState, subscription, force) {
  var result = {
    blur: state.blur,
    change: state.change,
    focus: state.focus,
    name: state.name
  };
  var different = subscriptionFilter(result, state, previousState, subscription, fieldSubscriptionItems, shallowEqualKeys$1) || !previousState;
  return different || force ? result : void 0;
};
var formSubscriptionItems = ["active", "dirty", "dirtyFields", "dirtyFieldsSinceLastSubmit", "dirtySinceLastSubmit", "error", "errors", "hasSubmitErrors", "hasValidationErrors", "initialValues", "invalid", "modified", "modifiedSinceLastSubmit", "pristine", "submitting", "submitError", "submitErrors", "submitFailed", "submitSucceeded", "touched", "valid", "validating", "values", "visited"];
var shallowEqualKeys = ["touched", "visited"];
function filterFormState(state, previousState, subscription, force) {
  var result = {};
  var different = subscriptionFilter(result, state, previousState, subscription, formSubscriptionItems, shallowEqualKeys) || !previousState;
  return different || force ? result : void 0;
}
var memoize = function memoize2(fn) {
  var lastArgs;
  var lastResult;
  return function() {
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    if (!lastArgs || args.length !== lastArgs.length || args.some(function(arg, index) {
      return !shallowEqual(lastArgs[index], arg);
    })) {
      lastArgs = args;
      lastResult = fn.apply(void 0, args);
    }
    return lastResult;
  };
};
var isPromise = function(obj) {
  return !!obj && (typeof obj === "object" || typeof obj === "function") && typeof obj.then === "function";
};
var version = "4.20.10";
var configOptions = ["debug", "initialValues", "keepDirtyOnReinitialize", "mutators", "onSubmit", "validate", "validateOnBlur"];
var tripleEquals = function tripleEquals2(a, b) {
  return a === b;
};
var hasAnyError = function hasAnyError2(errors) {
  return Object.keys(errors).some(function(key) {
    var value = errors[key];
    if (value && typeof value === "object" && !(value instanceof Error)) {
      return hasAnyError2(value);
    }
    return typeof value !== "undefined";
  });
};
function convertToExternalFormState(_ref) {
  var active = _ref.active, dirtySinceLastSubmit = _ref.dirtySinceLastSubmit, modifiedSinceLastSubmit = _ref.modifiedSinceLastSubmit, error = _ref.error, errors = _ref.errors, initialValues = _ref.initialValues, pristine = _ref.pristine, submitting = _ref.submitting, submitFailed = _ref.submitFailed, submitSucceeded = _ref.submitSucceeded, submitError = _ref.submitError, submitErrors = _ref.submitErrors, valid = _ref.valid, validating = _ref.validating, values = _ref.values;
  return {
    active,
    dirty: !pristine,
    dirtySinceLastSubmit,
    modifiedSinceLastSubmit,
    error,
    errors,
    hasSubmitErrors: !!(submitError || submitErrors && hasAnyError(submitErrors)),
    hasValidationErrors: !!(error || hasAnyError(errors)),
    invalid: !valid,
    initialValues,
    pristine,
    submitting,
    submitFailed,
    submitSucceeded,
    submitError,
    submitErrors,
    valid,
    validating: validating > 0,
    values
  };
}
function notifySubscriber(subscriber, subscription, state, lastState, filter, force) {
  var notification = filter(state, lastState, subscription, force);
  if (notification) {
    subscriber(notification);
    return true;
  }
  return false;
}
function notify(_ref2, state, lastState, filter, force) {
  var entries = _ref2.entries;
  Object.keys(entries).forEach(function(key) {
    var entry = entries[Number(key)];
    if (entry) {
      var subscription = entry.subscription, subscriber = entry.subscriber, notified = entry.notified;
      if (notifySubscriber(subscriber, subscription, state, lastState, filter, force || !notified)) {
        entry.notified = true;
      }
    }
  });
}
function createForm(config) {
  if (!config) {
    throw new Error("No config specified");
  }
  var debug = config.debug, destroyOnUnregister = config.destroyOnUnregister, keepDirtyOnReinitialize = config.keepDirtyOnReinitialize, initialValues = config.initialValues, mutators = config.mutators, onSubmit = config.onSubmit, validate = config.validate, validateOnBlur = config.validateOnBlur;
  if (!onSubmit) {
    throw new Error("No onSubmit function specified");
  }
  var state = {
    subscribers: {
      index: 0,
      entries: {}
    },
    fieldSubscribers: {},
    fields: {},
    formState: {
      asyncErrors: {},
      dirtySinceLastSubmit: false,
      modifiedSinceLastSubmit: false,
      errors: {},
      initialValues: initialValues && _extends({}, initialValues),
      invalid: false,
      pristine: true,
      submitting: false,
      submitFailed: false,
      submitSucceeded: false,
      resetWhileSubmitting: false,
      valid: true,
      validating: 0,
      values: initialValues ? _extends({}, initialValues) : {}
    },
    lastFormState: void 0
  };
  var inBatch = 0;
  var validationPaused = false;
  var validationBlocked = false;
  var preventNotificationWhileValidationPaused = false;
  var nextAsyncValidationKey = 0;
  var asyncValidationPromises = {};
  var clearAsyncValidationPromise = function clearAsyncValidationPromise2(key) {
    return function(result) {
      delete asyncValidationPromises[key];
      return result;
    };
  };
  var changeValue = function changeValue2(state2, name, mutate) {
    var before = getIn(state2.formState.values, name);
    var after = mutate(before);
    state2.formState.values = setIn(state2.formState.values, name, after) || {};
  };
  var renameField = function renameField2(state2, from, to) {
    if (state2.fields[from]) {
      var _extends2, _extends3;
      state2.fields = _extends({}, state2.fields, (_extends2 = {}, _extends2[to] = _extends({}, state2.fields[from], {
        name: to,
        // rebind event handlers
        blur: function blur() {
          return api.blur(to);
        },
        change: function change(value2) {
          return api.change(to, value2);
        },
        focus: function focus() {
          return api.focus(to);
        },
        lastFieldState: void 0
      }), _extends2));
      delete state2.fields[from];
      state2.fieldSubscribers = _extends({}, state2.fieldSubscribers, (_extends3 = {}, _extends3[to] = state2.fieldSubscribers[from], _extends3));
      delete state2.fieldSubscribers[from];
      var value = getIn(state2.formState.values, from);
      state2.formState.values = setIn(state2.formState.values, from, void 0) || {};
      state2.formState.values = setIn(state2.formState.values, to, value);
      delete state2.lastFormState;
    }
  };
  var getMutatorApi = function getMutatorApi2(key) {
    return function() {
      if (mutators) {
        var mutatableState = {
          formState: state.formState,
          fields: state.fields,
          fieldSubscribers: state.fieldSubscribers,
          lastFormState: state.lastFormState
        };
        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
          args[_key] = arguments[_key];
        }
        var returnValue = mutators[key](args, mutatableState, {
          changeValue,
          getIn,
          renameField,
          resetFieldState: api.resetFieldState,
          setIn,
          shallowEqual
        });
        state.formState = mutatableState.formState;
        state.fields = mutatableState.fields;
        state.fieldSubscribers = mutatableState.fieldSubscribers;
        state.lastFormState = mutatableState.lastFormState;
        runValidation(void 0, function() {
          notifyFieldListeners();
          notifyFormListeners();
        });
        return returnValue;
      }
    };
  };
  var mutatorsApi = mutators ? Object.keys(mutators).reduce(function(result, key) {
    result[key] = getMutatorApi(key);
    return result;
  }, {}) : {};
  var runRecordLevelValidation = function runRecordLevelValidation2(setErrors) {
    var promises = [];
    if (validate) {
      var errorsOrPromise = validate(_extends({}, state.formState.values));
      if (isPromise(errorsOrPromise)) {
        promises.push(errorsOrPromise.then(function(errors) {
          return setErrors(errors, true);
        }));
      } else {
        setErrors(errorsOrPromise, false);
      }
    }
    return promises;
  };
  var getValidators = function getValidators2(field) {
    return Object.keys(field.validators).reduce(function(result, index) {
      var validator = field.validators[Number(index)]();
      if (validator) {
        result.push(validator);
      }
      return result;
    }, []);
  };
  var runFieldLevelValidation = function runFieldLevelValidation2(field, setError) {
    var promises = [];
    var validators = getValidators(field);
    if (validators.length) {
      var error;
      validators.forEach(function(validator) {
        var errorOrPromise = validator(getIn(state.formState.values, field.name), state.formState.values, validator.length === 0 || validator.length === 3 ? publishFieldState(state.formState, state.fields[field.name]) : void 0);
        if (errorOrPromise && isPromise(errorOrPromise)) {
          field.validating = true;
          var promise = errorOrPromise.then(function(error2) {
            if (state.fields[field.name]) {
              state.fields[field.name].validating = false;
              setError(error2);
            }
          });
          promises.push(promise);
        } else if (!error) {
          error = errorOrPromise;
        }
      });
      setError(error);
    }
    return promises;
  };
  var runValidation = function runValidation2(fieldChanged, callback) {
    if (validationPaused) {
      validationBlocked = true;
      callback();
      return;
    }
    var fields = state.fields, formState = state.formState;
    var safeFields = _extends({}, fields);
    var fieldKeys = Object.keys(safeFields);
    if (!validate && !fieldKeys.some(function(key) {
      return getValidators(safeFields[key]).length;
    })) {
      callback();
      return;
    }
    var limitedFieldLevelValidation = false;
    if (fieldChanged) {
      var changedField = safeFields[fieldChanged];
      if (changedField) {
        var validateFields = changedField.validateFields;
        if (validateFields) {
          limitedFieldLevelValidation = true;
          fieldKeys = validateFields.length ? validateFields.concat(fieldChanged) : [fieldChanged];
        }
      }
    }
    var recordLevelErrors = {};
    var asyncRecordLevelErrors = {};
    var fieldLevelErrors = {};
    var promises = [].concat(runRecordLevelValidation(function(errors, wasAsync) {
      if (wasAsync) {
        asyncRecordLevelErrors = errors || {};
      } else {
        recordLevelErrors = errors || {};
      }
    }), fieldKeys.reduce(function(result, name) {
      return result.concat(runFieldLevelValidation(fields[name], function(error) {
        fieldLevelErrors[name] = error;
      }));
    }, []));
    var hasAsyncValidations = promises.length > 0;
    var asyncValidationPromiseKey = ++nextAsyncValidationKey;
    var promise = Promise.all(promises).then(clearAsyncValidationPromise(asyncValidationPromiseKey));
    if (hasAsyncValidations) {
      asyncValidationPromises[asyncValidationPromiseKey] = promise;
    }
    var processErrors = function processErrors2(afterAsync) {
      var merged = _extends({}, limitedFieldLevelValidation ? formState.errors : {}, recordLevelErrors, afterAsync ? asyncRecordLevelErrors : formState.asyncErrors);
      var forEachError = function forEachError2(fn) {
        fieldKeys.forEach(function(name) {
          if (fields[name]) {
            var recordLevelError = getIn(recordLevelErrors, name);
            var errorFromParent = getIn(merged, name);
            var hasFieldLevelValidation = getValidators(safeFields[name]).length;
            var fieldLevelError = fieldLevelErrors[name];
            fn(name, hasFieldLevelValidation && fieldLevelError || validate && recordLevelError || (!recordLevelError && !limitedFieldLevelValidation ? errorFromParent : void 0));
          }
        });
      };
      forEachError(function(name, error) {
        merged = setIn(merged, name, error) || {};
      });
      forEachError(function(name, error) {
        if (error && error[ARRAY_ERROR]) {
          var existing = getIn(merged, name);
          var copy = [].concat(existing);
          copy[ARRAY_ERROR] = error[ARRAY_ERROR];
          merged = setIn(merged, name, copy);
        }
      });
      if (!shallowEqual(formState.errors, merged)) {
        formState.errors = merged;
      }
      if (afterAsync) {
        formState.asyncErrors = asyncRecordLevelErrors;
      }
      formState.error = recordLevelErrors[FORM_ERROR];
    };
    if (hasAsyncValidations) {
      state.formState.validating++;
      callback();
    }
    processErrors(false);
    callback();
    if (hasAsyncValidations) {
      var afterPromise = function afterPromise2() {
        state.formState.validating--;
        callback();
        if (state.formState.validating === 0 && state.lastFormState.validating) {
          notifyFormListeners();
        }
      };
      promise.then(function() {
        if (nextAsyncValidationKey > asyncValidationPromiseKey) {
          return;
        }
        processErrors(true);
      }).then(afterPromise, afterPromise);
    }
  };
  var notifyFieldListeners = function notifyFieldListeners2(name) {
    if (inBatch) {
      return;
    }
    var fields = state.fields, fieldSubscribers = state.fieldSubscribers, formState = state.formState;
    var safeFields = _extends({}, fields);
    var notifyField = function notifyField2(name2) {
      var field = safeFields[name2];
      var fieldState = publishFieldState(formState, field);
      var lastFieldState = field.lastFieldState;
      field.lastFieldState = fieldState;
      var fieldSubscriber = fieldSubscribers[name2];
      if (fieldSubscriber) {
        notify(fieldSubscriber, fieldState, lastFieldState, filterFieldState, lastFieldState === void 0);
      }
    };
    if (name) {
      notifyField(name);
    } else {
      Object.keys(safeFields).forEach(notifyField);
    }
  };
  var markAllFieldsTouched = function markAllFieldsTouched2() {
    Object.keys(state.fields).forEach(function(key) {
      state.fields[key].touched = true;
    });
  };
  var hasSyncErrors = function hasSyncErrors2() {
    return !!(state.formState.error || hasAnyError(state.formState.errors));
  };
  var calculateNextFormState = function calculateNextFormState2() {
    var fields = state.fields, formState = state.formState, lastFormState = state.lastFormState;
    var safeFields = _extends({}, fields);
    var safeFieldKeys = Object.keys(safeFields);
    var foundDirty = false;
    var dirtyFields = safeFieldKeys.reduce(function(result, key) {
      var dirty = !safeFields[key].isEqual(getIn(formState.values, key), getIn(formState.initialValues || {}, key));
      if (dirty) {
        foundDirty = true;
        result[key] = true;
      }
      return result;
    }, {});
    var dirtyFieldsSinceLastSubmit = safeFieldKeys.reduce(function(result, key) {
      var nonNullLastSubmittedValues = formState.lastSubmittedValues || {};
      if (!safeFields[key].isEqual(getIn(formState.values, key), getIn(nonNullLastSubmittedValues, key))) {
        result[key] = true;
      }
      return result;
    }, {});
    formState.pristine = !foundDirty;
    formState.dirtySinceLastSubmit = !!(formState.lastSubmittedValues && Object.values(dirtyFieldsSinceLastSubmit).some(function(value) {
      return value;
    }));
    formState.modifiedSinceLastSubmit = !!(formState.lastSubmittedValues && // Object.values would treat values as mixed (facebook/flow#2221)
    Object.keys(safeFields).some(function(value) {
      return safeFields[value].modifiedSinceLastSubmit;
    }));
    formState.valid = !formState.error && !formState.submitError && !hasAnyError(formState.errors) && !(formState.submitErrors && hasAnyError(formState.submitErrors));
    var nextFormState = convertToExternalFormState(formState);
    var _safeFieldKeys$reduce = safeFieldKeys.reduce(function(result, key) {
      result.modified[key] = safeFields[key].modified;
      result.touched[key] = safeFields[key].touched;
      result.visited[key] = safeFields[key].visited;
      return result;
    }, {
      modified: {},
      touched: {},
      visited: {}
    }), modified = _safeFieldKeys$reduce.modified, touched = _safeFieldKeys$reduce.touched, visited = _safeFieldKeys$reduce.visited;
    nextFormState.dirtyFields = lastFormState && shallowEqual(lastFormState.dirtyFields, dirtyFields) ? lastFormState.dirtyFields : dirtyFields;
    nextFormState.dirtyFieldsSinceLastSubmit = lastFormState && shallowEqual(lastFormState.dirtyFieldsSinceLastSubmit, dirtyFieldsSinceLastSubmit) ? lastFormState.dirtyFieldsSinceLastSubmit : dirtyFieldsSinceLastSubmit;
    nextFormState.modified = lastFormState && shallowEqual(lastFormState.modified, modified) ? lastFormState.modified : modified;
    nextFormState.touched = lastFormState && shallowEqual(lastFormState.touched, touched) ? lastFormState.touched : touched;
    nextFormState.visited = lastFormState && shallowEqual(lastFormState.visited, visited) ? lastFormState.visited : visited;
    return lastFormState && shallowEqual(lastFormState, nextFormState) ? lastFormState : nextFormState;
  };
  var callDebug = function callDebug2() {
    return debug && true && debug(calculateNextFormState(), Object.keys(state.fields).reduce(function(result, key) {
      result[key] = state.fields[key];
      return result;
    }, {}));
  };
  var notifying = false;
  var scheduleNotification = false;
  var notifyFormListeners = function notifyFormListeners2() {
    if (notifying) {
      scheduleNotification = true;
    } else {
      notifying = true;
      callDebug();
      if (!inBatch && !(validationPaused && preventNotificationWhileValidationPaused)) {
        var lastFormState = state.lastFormState;
        var nextFormState = calculateNextFormState();
        if (nextFormState !== lastFormState) {
          state.lastFormState = nextFormState;
          notify(state.subscribers, nextFormState, lastFormState, filterFormState);
        }
      }
      notifying = false;
      if (scheduleNotification) {
        scheduleNotification = false;
        notifyFormListeners2();
      }
    }
  };
  var beforeSubmit = function beforeSubmit2() {
    return Object.keys(state.fields).some(function(name) {
      return state.fields[name].beforeSubmit && state.fields[name].beforeSubmit() === false;
    });
  };
  var afterSubmit = function afterSubmit2() {
    return Object.keys(state.fields).forEach(function(name) {
      return state.fields[name].afterSubmit && state.fields[name].afterSubmit();
    });
  };
  var resetModifiedAfterSubmit = function resetModifiedAfterSubmit2() {
    return Object.keys(state.fields).forEach(function(key) {
      return state.fields[key].modifiedSinceLastSubmit = false;
    });
  };
  runValidation(void 0, function() {
    notifyFormListeners();
  });
  var api = {
    batch: function batch(fn) {
      inBatch++;
      fn();
      inBatch--;
      notifyFieldListeners();
      notifyFormListeners();
    },
    blur: function blur(name) {
      var fields = state.fields, formState = state.formState;
      var previous = fields[name];
      if (previous) {
        delete formState.active;
        fields[name] = _extends({}, previous, {
          active: false,
          touched: true
        });
        if (validateOnBlur) {
          runValidation(name, function() {
            notifyFieldListeners();
            notifyFormListeners();
          });
        } else {
          notifyFieldListeners();
          notifyFormListeners();
        }
      }
    },
    change: function change(name, value) {
      var fields = state.fields, formState = state.formState;
      if (getIn(formState.values, name) !== value) {
        changeValue(state, name, function() {
          return value;
        });
        var previous = fields[name];
        if (previous) {
          fields[name] = _extends({}, previous, {
            modified: true,
            modifiedSinceLastSubmit: !!formState.lastSubmittedValues
          });
        }
        if (validateOnBlur) {
          notifyFieldListeners();
          notifyFormListeners();
        } else {
          runValidation(name, function() {
            notifyFieldListeners();
            notifyFormListeners();
          });
        }
      }
    },
    get destroyOnUnregister() {
      return !!destroyOnUnregister;
    },
    set destroyOnUnregister(value) {
      destroyOnUnregister = value;
    },
    focus: function focus(name) {
      var field = state.fields[name];
      if (field && !field.active) {
        state.formState.active = name;
        field.active = true;
        field.visited = true;
        notifyFieldListeners();
        notifyFormListeners();
      }
    },
    mutators: mutatorsApi,
    getFieldState: function getFieldState(name) {
      var field = state.fields[name];
      return field && field.lastFieldState;
    },
    getRegisteredFields: function getRegisteredFields() {
      return Object.keys(state.fields);
    },
    getState: function getState() {
      return calculateNextFormState();
    },
    initialize: function initialize(data) {
      var fields = state.fields, formState = state.formState;
      var safeFields = _extends({}, fields);
      var values = typeof data === "function" ? data(formState.values) : data;
      if (!keepDirtyOnReinitialize) {
        formState.values = values;
      }
      var savedDirtyValues = keepDirtyOnReinitialize ? Object.keys(safeFields).reduce(function(result, key) {
        var field = safeFields[key];
        var pristine = field.isEqual(getIn(formState.values, key), getIn(formState.initialValues || {}, key));
        if (!pristine) {
          result[key] = getIn(formState.values, key);
        }
        return result;
      }, {}) : {};
      formState.initialValues = values;
      formState.values = values;
      Object.keys(savedDirtyValues).forEach(function(key) {
        formState.values = setIn(formState.values, key, savedDirtyValues[key]) || {};
      });
      runValidation(void 0, function() {
        notifyFieldListeners();
        notifyFormListeners();
      });
    },
    isValidationPaused: function isValidationPaused() {
      return validationPaused;
    },
    pauseValidation: function pauseValidation(preventNotification) {
      if (preventNotification === void 0) {
        preventNotification = true;
      }
      validationPaused = true;
      preventNotificationWhileValidationPaused = preventNotification;
    },
    registerField: function registerField(name, subscriber, subscription, fieldConfig) {
      if (subscription === void 0) {
        subscription = {};
      }
      if (!state.fieldSubscribers[name]) {
        state.fieldSubscribers[name] = {
          index: 0,
          entries: {}
        };
      }
      var index = state.fieldSubscribers[name].index++;
      state.fieldSubscribers[name].entries[index] = {
        subscriber: memoize(subscriber),
        subscription,
        notified: false
      };
      var field = state.fields[name] || {
        active: false,
        afterSubmit: fieldConfig && fieldConfig.afterSubmit,
        beforeSubmit: fieldConfig && fieldConfig.beforeSubmit,
        data: fieldConfig && fieldConfig.data || {},
        isEqual: fieldConfig && fieldConfig.isEqual || tripleEquals,
        lastFieldState: void 0,
        modified: false,
        modifiedSinceLastSubmit: false,
        name,
        touched: false,
        valid: true,
        validateFields: fieldConfig && fieldConfig.validateFields,
        validators: {},
        validating: false,
        visited: false
      };
      field.blur = field.blur || function() {
        return api.blur(name);
      };
      field.change = field.change || function(value) {
        return api.change(name, value);
      };
      field.focus = field.focus || function() {
        return api.focus(name);
      };
      state.fields[name] = field;
      var haveValidator = false;
      var silent = fieldConfig && fieldConfig.silent;
      var notify2 = function notify3() {
        if (silent && state.fields[name]) {
          notifyFieldListeners(name);
        } else {
          notifyFormListeners();
          notifyFieldListeners();
        }
      };
      if (fieldConfig) {
        haveValidator = !!(fieldConfig.getValidator && fieldConfig.getValidator());
        if (fieldConfig.getValidator) {
          state.fields[name].validators[index] = fieldConfig.getValidator;
        }
        var noValueInFormState = getIn(state.formState.values, name) === void 0;
        if (fieldConfig.initialValue !== void 0 && (noValueInFormState || getIn(state.formState.values, name) === getIn(state.formState.initialValues, name))) {
          state.formState.initialValues = setIn(state.formState.initialValues || {}, name, fieldConfig.initialValue);
          state.formState.values = setIn(state.formState.values, name, fieldConfig.initialValue);
          runValidation(void 0, notify2);
        }
        if (fieldConfig.defaultValue !== void 0 && fieldConfig.initialValue === void 0 && getIn(state.formState.initialValues, name) === void 0 && noValueInFormState) {
          state.formState.values = setIn(state.formState.values, name, fieldConfig.defaultValue);
        }
      }
      if (haveValidator) {
        runValidation(void 0, notify2);
      } else {
        notify2();
      }
      return function() {
        var validatorRemoved = false;
        if (state.fields[name]) {
          validatorRemoved = !!(state.fields[name].validators[index] && state.fields[name].validators[index]());
          delete state.fields[name].validators[index];
        }
        var hasFieldSubscribers = !!state.fieldSubscribers[name];
        if (hasFieldSubscribers) {
          delete state.fieldSubscribers[name].entries[index];
        }
        var lastOne = hasFieldSubscribers && !Object.keys(state.fieldSubscribers[name].entries).length;
        if (lastOne) {
          delete state.fieldSubscribers[name];
          delete state.fields[name];
          if (validatorRemoved) {
            state.formState.errors = setIn(state.formState.errors, name, void 0) || {};
          }
          if (destroyOnUnregister) {
            state.formState.values = setIn(state.formState.values, name, void 0, true) || {};
          }
        }
        if (!silent) {
          if (validatorRemoved) {
            runValidation(void 0, function() {
              notifyFormListeners();
              notifyFieldListeners();
            });
          } else if (lastOne) {
            notifyFormListeners();
          }
        }
      };
    },
    reset: function reset(initialValues2) {
      if (initialValues2 === void 0) {
        initialValues2 = state.formState.initialValues;
      }
      if (state.formState.submitting) {
        state.formState.resetWhileSubmitting = true;
      }
      state.formState.submitFailed = false;
      state.formState.submitSucceeded = false;
      delete state.formState.submitError;
      delete state.formState.submitErrors;
      delete state.formState.lastSubmittedValues;
      api.initialize(initialValues2 || {});
    },
    /**
     * Resets all field flags (e.g. touched, visited, etc.) to their initial state
     */
    resetFieldState: function resetFieldState(name) {
      state.fields[name] = _extends({}, state.fields[name], {
        active: false,
        lastFieldState: void 0,
        modified: false,
        touched: false,
        valid: true,
        validating: false,
        visited: false
      });
      runValidation(void 0, function() {
        notifyFieldListeners();
        notifyFormListeners();
      });
    },
    /**
     * Returns the form to a clean slate; that is:
     * - Clear all values
     * - Resets all fields to their initial state
     */
    restart: function restart(initialValues2) {
      if (initialValues2 === void 0) {
        initialValues2 = state.formState.initialValues;
      }
      api.batch(function() {
        for (var name in state.fields) {
          api.resetFieldState(name);
          state.fields[name] = _extends({}, state.fields[name], {
            active: false,
            lastFieldState: void 0,
            modified: false,
            modifiedSinceLastSubmit: false,
            touched: false,
            valid: true,
            validating: false,
            visited: false
          });
        }
        api.reset(initialValues2);
      });
    },
    resumeValidation: function resumeValidation() {
      validationPaused = false;
      preventNotificationWhileValidationPaused = false;
      if (validationBlocked) {
        runValidation(void 0, function() {
          notifyFieldListeners();
          notifyFormListeners();
        });
      }
      validationBlocked = false;
    },
    setConfig: function setConfig(name, value) {
      switch (name) {
        case "debug":
          debug = value;
          break;
        case "destroyOnUnregister":
          destroyOnUnregister = value;
          break;
        case "initialValues":
          api.initialize(value);
          break;
        case "keepDirtyOnReinitialize":
          keepDirtyOnReinitialize = value;
          break;
        case "mutators":
          mutators = value;
          if (value) {
            Object.keys(mutatorsApi).forEach(function(key) {
              if (!(key in value)) {
                delete mutatorsApi[key];
              }
            });
            Object.keys(value).forEach(function(key) {
              mutatorsApi[key] = getMutatorApi(key);
            });
          } else {
            Object.keys(mutatorsApi).forEach(function(key) {
              delete mutatorsApi[key];
            });
          }
          break;
        case "onSubmit":
          onSubmit = value;
          break;
        case "validate":
          validate = value;
          runValidation(void 0, function() {
            notifyFieldListeners();
            notifyFormListeners();
          });
          break;
        case "validateOnBlur":
          validateOnBlur = value;
          break;
        default:
          throw new Error("Unrecognised option " + name);
      }
    },
    submit: function submit() {
      var formState = state.formState;
      if (formState.submitting) {
        return;
      }
      delete formState.submitErrors;
      delete formState.submitError;
      formState.lastSubmittedValues = _extends({}, formState.values);
      if (hasSyncErrors()) {
        markAllFieldsTouched();
        resetModifiedAfterSubmit();
        state.formState.submitFailed = true;
        notifyFormListeners();
        notifyFieldListeners();
        return;
      }
      var asyncValidationPromisesKeys = Object.keys(asyncValidationPromises);
      if (asyncValidationPromisesKeys.length) {
        Promise.all(asyncValidationPromisesKeys.map(function(key) {
          return asyncValidationPromises[Number(key)];
        })).then(api.submit, console.error);
        return;
      }
      var submitIsBlocked = beforeSubmit();
      if (submitIsBlocked) {
        return;
      }
      var resolvePromise;
      var completeCalled = false;
      var complete = function complete2(errors) {
        formState.submitting = false;
        var resetWhileSubmitting = formState.resetWhileSubmitting;
        if (resetWhileSubmitting) {
          formState.resetWhileSubmitting = false;
        }
        if (errors && hasAnyError(errors)) {
          formState.submitFailed = true;
          formState.submitSucceeded = false;
          formState.submitErrors = errors;
          formState.submitError = errors[FORM_ERROR];
          markAllFieldsTouched();
        } else {
          if (!resetWhileSubmitting) {
            formState.submitFailed = false;
            formState.submitSucceeded = true;
          }
          afterSubmit();
        }
        notifyFormListeners();
        notifyFieldListeners();
        completeCalled = true;
        if (resolvePromise) {
          resolvePromise(errors);
        }
        return errors;
      };
      formState.submitting = true;
      formState.submitFailed = false;
      formState.submitSucceeded = false;
      formState.lastSubmittedValues = _extends({}, formState.values);
      resetModifiedAfterSubmit();
      var result = onSubmit(formState.values, api, complete);
      if (!completeCalled) {
        if (result && isPromise(result)) {
          notifyFormListeners();
          notifyFieldListeners();
          return result.then(complete, function(error) {
            complete();
            throw error;
          });
        } else if (onSubmit.length >= 3) {
          notifyFormListeners();
          notifyFieldListeners();
          return new Promise(function(resolve) {
            resolvePromise = resolve;
          });
        } else {
          complete(result);
        }
      }
    },
    subscribe: function subscribe(subscriber, subscription) {
      if (!subscriber) {
        throw new Error("No callback given.");
      }
      if (!subscription) {
        throw new Error("No subscription provided. What values do you want to listen to?");
      }
      var memoized = memoize(subscriber);
      var subscribers = state.subscribers;
      var index = subscribers.index++;
      subscribers.entries[index] = {
        subscriber: memoized,
        subscription,
        notified: false
      };
      var nextFormState = calculateNextFormState();
      notifySubscriber(memoized, subscription, nextFormState, nextFormState, filterFormState, true);
      return function() {
        delete subscribers.entries[index];
      };
    }
  };
  return api;
}

export {
  _objectWithoutPropertiesLoose,
  getIn,
  setIn,
  FORM_ERROR,
  ARRAY_ERROR,
  fieldSubscriptionItems,
  formSubscriptionItems,
  version,
  configOptions,
  createForm
};
//# sourceMappingURL=chunk-WRUIP4E2.js.map
