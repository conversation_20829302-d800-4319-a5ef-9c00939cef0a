import {
  cu,
  hu
} from "./chunk-MEKKV4OY.js";
import "./chunk-AUZ3RYOM.js";

// node_modules/@graphiql/react/dist/comment.es.js
var w = Object.defineProperty;
var I = (O, E) => w(O, "name", { value: E, configurable: true });
function J(O, E) {
  for (var u = 0; u < E.length; u++) {
    const C = E[u];
    if (typeof C != "string" && !Array.isArray(C)) {
      for (const s in C)
        if (s !== "default" && !(s in O)) {
          const r = Object.getOwnPropertyDescriptor(C, s);
          r && Object.defineProperty(O, s, r.get ? r : {
            enumerable: true,
            get: () => C[s]
          });
        }
    }
  }
  return Object.freeze(Object.defineProperty(O, Symbol.toStringTag, { value: "Module" }));
}
I(J, "_mergeNamespaces");
var K = { exports: {} };
(function(O, E) {
  (function(u) {
    u(cu());
  })(function(u) {
    var C = {}, s = /[^\s\u00a0]/, r = u.Pos, B = u.cmpPos;
    function N(t) {
      var l = t.search(s);
      return l == -1 ? 0 : l;
    }
    I(N, "firstNonWS"), u.commands.toggleComment = function(t) {
      t.toggleComment();
    }, u.defineExtension("toggleComment", function(t) {
      t || (t = C);
      for (var l = this, n = 1 / 0, e = this.listSelections(), f = null, m = e.length - 1; m >= 0; m--) {
        var a = e[m].from(), i = e[m].to();
        a.line >= n || (i.line >= n && (i = r(n, 0)), n = a.line, f == null ? l.uncomment(a, i, t) ? f = "un" : (l.lineComment(a, i, t), f = "line") : f == "un" ? l.uncomment(a, i, t) : l.lineComment(a, i, t));
      }
    });
    function F(t, l, n) {
      return /\bstring\b/.test(t.getTokenTypeAt(r(l.line, 0))) && !/^[\'\"\`]/.test(n);
    }
    I(F, "probablyInsideString");
    function P(t, l) {
      var n = t.getMode();
      return n.useInnerComments === false || !n.innerMode ? n : t.getModeAt(l);
    }
    I(P, "getMode"), u.defineExtension("lineComment", function(t, l, n) {
      n || (n = C);
      var e = this, f = P(e, t), m = e.getLine(t.line);
      if (!(m == null || F(e, t, m))) {
        var a = n.lineComment || f.lineComment;
        if (!a) {
          (n.blockCommentStart || f.blockCommentStart) && (n.fullLines = true, e.blockComment(t, l, n));
          return;
        }
        var i = Math.min(l.ch != 0 || l.line == t.line ? l.line + 1 : l.line, e.lastLine() + 1), k = n.padding == null ? " " : n.padding, g = n.commentBlankLines || t.line == l.line;
        e.operation(function() {
          if (n.indent) {
            for (var d = null, c = t.line; c < i; ++c) {
              var v = e.getLine(c), h = v.slice(0, N(v));
              (d == null || d.length > h.length) && (d = h);
            }
            for (var c = t.line; c < i; ++c) {
              var v = e.getLine(c), o = d.length;
              !g && !s.test(v) || (v.slice(0, o) != d && (o = N(v)), e.replaceRange(d + a + k, r(c, 0), r(c, o)));
            }
          } else
            for (var c = t.line; c < i; ++c)
              (g || s.test(e.getLine(c))) && e.replaceRange(a + k, r(c, 0));
        });
      }
    }), u.defineExtension("blockComment", function(t, l, n) {
      n || (n = C);
      var e = this, f = P(e, t), m = n.blockCommentStart || f.blockCommentStart, a = n.blockCommentEnd || f.blockCommentEnd;
      if (!m || !a) {
        (n.lineComment || f.lineComment) && n.fullLines != false && e.lineComment(t, l, n);
        return;
      }
      if (!/\bcomment\b/.test(e.getTokenTypeAt(r(t.line, 0)))) {
        var i = Math.min(l.line, e.lastLine());
        i != t.line && l.ch == 0 && s.test(e.getLine(i)) && --i;
        var k = n.padding == null ? " " : n.padding;
        t.line > i || e.operation(function() {
          if (n.fullLines != false) {
            var g = s.test(e.getLine(i));
            e.replaceRange(k + a, r(i)), e.replaceRange(m + k, r(t.line, 0));
            var d = n.blockCommentLead || f.blockCommentLead;
            if (d != null)
              for (var c = t.line + 1; c <= i; ++c)
                (c != i || g) && e.replaceRange(d + k, r(c, 0));
          } else {
            var v = B(e.getCursor("to"), l) == 0, h = !e.somethingSelected();
            e.replaceRange(a, l), v && e.setSelection(h ? l : e.getCursor("from"), l), e.replaceRange(m, t);
          }
        });
      }
    }), u.defineExtension("uncomment", function(t, l, n) {
      n || (n = C);
      var e = this, f = P(e, t), m = Math.min(l.ch != 0 || l.line == t.line ? l.line : l.line - 1, e.lastLine()), a = Math.min(t.line, m), i = n.lineComment || f.lineComment, k = [], g = n.padding == null ? " " : n.padding, d;
      e: {
        if (!i)
          break e;
        for (var c = a; c <= m; ++c) {
          var v = e.getLine(c), h = v.indexOf(i);
          if (h > -1 && !/comment/.test(e.getTokenTypeAt(r(c, h + 1))) && (h = -1), h == -1 && s.test(v) || h > -1 && s.test(v.slice(0, h)))
            break e;
          k.push(v);
        }
        if (e.operation(function() {
          for (var b = a; b <= m; ++b) {
            var x = k[b - a], p = x.indexOf(i), L = p + i.length;
            p < 0 || (x.slice(L, L + g.length) == g && (L += g.length), d = true, e.replaceRange("", r(b, p), r(b, L)));
          }
        }), d)
          return true;
      }
      var o = n.blockCommentStart || f.blockCommentStart, S = n.blockCommentEnd || f.blockCommentEnd;
      if (!o || !S)
        return false;
      var D = n.blockCommentLead || f.blockCommentLead, A = e.getLine(a), j = A.indexOf(o);
      if (j == -1)
        return false;
      var _ = m == a ? A : e.getLine(m), y = _.indexOf(S, m == a ? j + o.length : 0), W = r(a, j + 1), $ = r(m, y + 1);
      if (y == -1 || !/comment/.test(e.getTokenTypeAt(W)) || !/comment/.test(e.getTokenTypeAt($)) || e.getRange(W, $, `
`).indexOf(S) > -1)
        return false;
      var R = A.lastIndexOf(o, t.ch), T = R == -1 ? -1 : A.slice(0, t.ch).indexOf(S, R + o.length);
      if (R != -1 && T != -1 && T + S.length != t.ch)
        return false;
      T = _.indexOf(S, l.ch);
      var q = _.slice(l.ch).lastIndexOf(o, T - l.ch);
      return R = T == -1 || q == -1 ? -1 : l.ch + q, T != -1 && R != -1 && R != l.ch ? false : (e.operation(function() {
        e.replaceRange(
          "",
          r(m, y - (g && _.slice(y - g.length, y) == g ? g.length : 0)),
          r(m, y + S.length)
        );
        var b = j + o.length;
        if (g && A.slice(b, b + g.length) == g && (b += g.length), e.replaceRange("", r(a, j), r(a, b)), D)
          for (var x = a + 1; x <= m; ++x) {
            var p = e.getLine(x), L = p.indexOf(D);
            if (!(L == -1 || s.test(p.slice(0, L)))) {
              var M = L + D.length;
              g && p.slice(M, M + g.length) == g && (M += g.length), e.replaceRange("", r(x, L), r(x, M));
            }
          }
      }), true);
    });
  });
})();
var z = K.exports;
var Q = hu(z);
var X = J({
  __proto__: null,
  default: Q
}, [z]);
export {
  X as c
};
//# sourceMappingURL=comment.es-6G2M2AYB.js.map
