import {
  Kn,
  Wn,
  Xn,
  Zn,
  et,
  nt
} from "./chunk-ROL6L7B7.js";
import "./chunk-PEYRUYRT.js";
import {
  d
} from "./chunk-WIXYZC3K.js";
import "./chunk-MEKKV4OY.js";
import "./chunk-HD22INE4.js";
import "./chunk-AUZ3RYOM.js";

// node_modules/@graphiql/react/dist/jump.es.js
var l = Object.defineProperty;
var u = (e, n) => l(e, "name", { value: n, configurable: true });
d.defineOption("jump", false, (e, n, r) => {
  if (r && r !== d.Init) {
    const t = e.state.jump.onMouseOver;
    d.off(e.getWrapperElement(), "mouseover", t);
    const i = e.state.jump.onMouseOut;
    d.off(e.getWrapperElement(), "mouseout", i), d.off(document, "keydown", e.state.jump.onKeyDown), delete e.state.jump;
  }
  if (n) {
    const t = e.state.jump = {
      options: n,
      onMouseOver: y.bind(null, e),
      onMouseOut: v.bind(null, e),
      onKeyDown: D.bind(null, e)
    };
    d.on(e.getWrapperElement(), "mouseover", t.onMouseOver), d.on(e.getWrapperElement(), "mouseout", t.onMouseOut), d.on(document, "keydown", t.onKeyDown);
  }
});
function y(e, n) {
  const r = n.target || n.srcElement;
  if (!(r instanceof HTMLElement) || (r == null ? void 0 : r.nodeName) !== "SPAN")
    return;
  const t = r.getBoundingClientRect(), i = {
    left: (t.left + t.right) / 2,
    top: (t.top + t.bottom) / 2
  };
  e.state.jump.cursor = i, e.state.jump.isHoldingModifier && p(e);
}
u(y, "onMouseOver");
function v(e) {
  if (!e.state.jump.isHoldingModifier && e.state.jump.cursor) {
    e.state.jump.cursor = null;
    return;
  }
  e.state.jump.isHoldingModifier && e.state.jump.marker && d2(e);
}
u(v, "onMouseOut");
function D(e, n) {
  if (e.state.jump.isHoldingModifier || !w(n.key))
    return;
  e.state.jump.isHoldingModifier = true, e.state.jump.cursor && p(e);
  const r = u((o) => {
    o.code === n.code && (e.state.jump.isHoldingModifier = false, e.state.jump.marker && d2(e), d.off(document, "keyup", r), d.off(document, "click", t), e.off("mousedown", i));
  }, "onKeyUp"), t = u((o) => {
    const { destination: a, options: f } = e.state.jump;
    a && f.onClick(a, o);
  }, "onClick"), i = u((o, a) => {
    e.state.jump.destination && (a.codemirrorIgnore = true);
  }, "onMouseDown");
  d.on(document, "keyup", r), d.on(document, "click", t), e.on("mousedown", i);
}
u(D, "onKeyDown");
var O = typeof navigator < "u" && navigator && navigator.appVersion.includes("Mac");
function w(e) {
  return e === (O ? "Meta" : "Control");
}
u(w, "isJumpModifier");
function p(e) {
  if (e.state.jump.marker)
    return;
  const { cursor: n, options: r } = e.state.jump, t = e.coordsChar(n), i = e.getTokenAt(t, true), o = r.getDestination || e.getHelper(t, "jump");
  if (o) {
    const a = o(i, r, e);
    if (a) {
      const f = e.markText({ line: t.line, ch: i.start }, { line: t.line, ch: i.end }, { className: "CodeMirror-jump-token" });
      e.state.jump.marker = f, e.state.jump.destination = a;
    }
  }
}
u(p, "enableJumpMode");
function d2(e) {
  const { marker: n } = e.state.jump;
  e.state.jump.marker = null, e.state.jump.destination = null, n.clear();
}
u(d2, "disableJumpMode");
d.registerHelper("jump", "graphql", (e, n) => {
  if (!n.schema || !n.onClick || !e.state)
    return;
  const { state: r } = e, { kind: t, step: i } = r, o = Xn(n.schema, r);
  if (t === "Field" && i === 0 && o.fieldDef || t === "AliasedField" && i === 2 && o.fieldDef)
    return Wn(o);
  if (t === "Directive" && i === 1 && o.directiveDef)
    return Zn(o);
  if (t === "Argument" && i === 0 && o.argDef)
    return Kn(o);
  if (t === "EnumValue" && o.enumValue)
    return et(o);
  if (t === "NamedType" && o.type)
    return nt(o);
});
//# sourceMappingURL=jump.es-YXD3DYEL.js.map
