{"version": 3, "sources": ["../../../../../node_modules/codemirror/addon/search/searchcursor.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"))\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod)\n  else // Plain browser env\n    mod(CodeMirror)\n})(function(CodeMirror) {\n  \"use strict\"\n  var Pos = CodeMirror.Pos\n\n  function regexpFlags(regexp) {\n    var flags = regexp.flags\n    return flags != null ? flags : (regexp.ignoreCase ? \"i\" : \"\")\n      + (regexp.global ? \"g\" : \"\")\n      + (regexp.multiline ? \"m\" : \"\")\n  }\n\n  function ensureFlags(regexp, flags) {\n    var current = regexpFlags(regexp), target = current\n    for (var i = 0; i < flags.length; i++) if (target.indexOf(flags.charAt(i)) == -1)\n      target += flags.charAt(i)\n    return current == target ? regexp : new RegExp(regexp.source, target)\n  }\n\n  function maybeMultiline(regexp) {\n    return /\\\\s|\\\\n|\\n|\\\\W|\\\\D|\\[\\^/.test(regexp.source)\n  }\n\n  function searchRegexpForward(doc, regexp, start) {\n    regexp = ensureFlags(regexp, \"g\")\n    for (var line = start.line, ch = start.ch, last = doc.lastLine(); line <= last; line++, ch = 0) {\n      regexp.lastIndex = ch\n      var string = doc.getLine(line), match = regexp.exec(string)\n      if (match)\n        return {from: Pos(line, match.index),\n                to: Pos(line, match.index + match[0].length),\n                match: match}\n    }\n  }\n\n  function searchRegexpForwardMultiline(doc, regexp, start) {\n    if (!maybeMultiline(regexp)) return searchRegexpForward(doc, regexp, start)\n\n    regexp = ensureFlags(regexp, \"gm\")\n    var string, chunk = 1\n    for (var line = start.line, last = doc.lastLine(); line <= last;) {\n      // This grows the search buffer in exponentially-sized chunks\n      // between matches, so that nearby matches are fast and don't\n      // require concatenating the whole document (in case we're\n      // searching for something that has tons of matches), but at the\n      // same time, the amount of retries is limited.\n      for (var i = 0; i < chunk; i++) {\n        if (line > last) break\n        var curLine = doc.getLine(line++)\n        string = string == null ? curLine : string + \"\\n\" + curLine\n      }\n      chunk = chunk * 2\n      regexp.lastIndex = start.ch\n      var match = regexp.exec(string)\n      if (match) {\n        var before = string.slice(0, match.index).split(\"\\n\"), inside = match[0].split(\"\\n\")\n        var startLine = start.line + before.length - 1, startCh = before[before.length - 1].length\n        return {from: Pos(startLine, startCh),\n                to: Pos(startLine + inside.length - 1,\n                        inside.length == 1 ? startCh + inside[0].length : inside[inside.length - 1].length),\n                match: match}\n      }\n    }\n  }\n\n  function lastMatchIn(string, regexp, endMargin) {\n    var match, from = 0\n    while (from <= string.length) {\n      regexp.lastIndex = from\n      var newMatch = regexp.exec(string)\n      if (!newMatch) break\n      var end = newMatch.index + newMatch[0].length\n      if (end > string.length - endMargin) break\n      if (!match || end > match.index + match[0].length)\n        match = newMatch\n      from = newMatch.index + 1\n    }\n    return match\n  }\n\n  function searchRegexpBackward(doc, regexp, start) {\n    regexp = ensureFlags(regexp, \"g\")\n    for (var line = start.line, ch = start.ch, first = doc.firstLine(); line >= first; line--, ch = -1) {\n      var string = doc.getLine(line)\n      var match = lastMatchIn(string, regexp, ch < 0 ? 0 : string.length - ch)\n      if (match)\n        return {from: Pos(line, match.index),\n                to: Pos(line, match.index + match[0].length),\n                match: match}\n    }\n  }\n\n  function searchRegexpBackwardMultiline(doc, regexp, start) {\n    if (!maybeMultiline(regexp)) return searchRegexpBackward(doc, regexp, start)\n    regexp = ensureFlags(regexp, \"gm\")\n    var string, chunkSize = 1, endMargin = doc.getLine(start.line).length - start.ch\n    for (var line = start.line, first = doc.firstLine(); line >= first;) {\n      for (var i = 0; i < chunkSize && line >= first; i++) {\n        var curLine = doc.getLine(line--)\n        string = string == null ? curLine : curLine + \"\\n\" + string\n      }\n      chunkSize *= 2\n\n      var match = lastMatchIn(string, regexp, endMargin)\n      if (match) {\n        var before = string.slice(0, match.index).split(\"\\n\"), inside = match[0].split(\"\\n\")\n        var startLine = line + before.length, startCh = before[before.length - 1].length\n        return {from: Pos(startLine, startCh),\n                to: Pos(startLine + inside.length - 1,\n                        inside.length == 1 ? startCh + inside[0].length : inside[inside.length - 1].length),\n                match: match}\n      }\n    }\n  }\n\n  var doFold, noFold\n  if (String.prototype.normalize) {\n    doFold = function(str) { return str.normalize(\"NFD\").toLowerCase() }\n    noFold = function(str) { return str.normalize(\"NFD\") }\n  } else {\n    doFold = function(str) { return str.toLowerCase() }\n    noFold = function(str) { return str }\n  }\n\n  // Maps a position in a case-folded line back to a position in the original line\n  // (compensating for codepoints increasing in number during folding)\n  function adjustPos(orig, folded, pos, foldFunc) {\n    if (orig.length == folded.length) return pos\n    for (var min = 0, max = pos + Math.max(0, orig.length - folded.length);;) {\n      if (min == max) return min\n      var mid = (min + max) >> 1\n      var len = foldFunc(orig.slice(0, mid)).length\n      if (len == pos) return mid\n      else if (len > pos) max = mid\n      else min = mid + 1\n    }\n  }\n\n  function searchStringForward(doc, query, start, caseFold) {\n    // Empty string would match anything and never progress, so we\n    // define it to match nothing instead.\n    if (!query.length) return null\n    var fold = caseFold ? doFold : noFold\n    var lines = fold(query).split(/\\r|\\n\\r?/)\n\n    search: for (var line = start.line, ch = start.ch, last = doc.lastLine() + 1 - lines.length; line <= last; line++, ch = 0) {\n      var orig = doc.getLine(line).slice(ch), string = fold(orig)\n      if (lines.length == 1) {\n        var found = string.indexOf(lines[0])\n        if (found == -1) continue search\n        var start = adjustPos(orig, string, found, fold) + ch\n        return {from: Pos(line, adjustPos(orig, string, found, fold) + ch),\n                to: Pos(line, adjustPos(orig, string, found + lines[0].length, fold) + ch)}\n      } else {\n        var cutFrom = string.length - lines[0].length\n        if (string.slice(cutFrom) != lines[0]) continue search\n        for (var i = 1; i < lines.length - 1; i++)\n          if (fold(doc.getLine(line + i)) != lines[i]) continue search\n        var end = doc.getLine(line + lines.length - 1), endString = fold(end), lastLine = lines[lines.length - 1]\n        if (endString.slice(0, lastLine.length) != lastLine) continue search\n        return {from: Pos(line, adjustPos(orig, string, cutFrom, fold) + ch),\n                to: Pos(line + lines.length - 1, adjustPos(end, endString, lastLine.length, fold))}\n      }\n    }\n  }\n\n  function searchStringBackward(doc, query, start, caseFold) {\n    if (!query.length) return null\n    var fold = caseFold ? doFold : noFold\n    var lines = fold(query).split(/\\r|\\n\\r?/)\n\n    search: for (var line = start.line, ch = start.ch, first = doc.firstLine() - 1 + lines.length; line >= first; line--, ch = -1) {\n      var orig = doc.getLine(line)\n      if (ch > -1) orig = orig.slice(0, ch)\n      var string = fold(orig)\n      if (lines.length == 1) {\n        var found = string.lastIndexOf(lines[0])\n        if (found == -1) continue search\n        return {from: Pos(line, adjustPos(orig, string, found, fold)),\n                to: Pos(line, adjustPos(orig, string, found + lines[0].length, fold))}\n      } else {\n        var lastLine = lines[lines.length - 1]\n        if (string.slice(0, lastLine.length) != lastLine) continue search\n        for (var i = 1, start = line - lines.length + 1; i < lines.length - 1; i++)\n          if (fold(doc.getLine(start + i)) != lines[i]) continue search\n        var top = doc.getLine(line + 1 - lines.length), topString = fold(top)\n        if (topString.slice(topString.length - lines[0].length) != lines[0]) continue search\n        return {from: Pos(line + 1 - lines.length, adjustPos(top, topString, top.length - lines[0].length, fold)),\n                to: Pos(line, adjustPos(orig, string, lastLine.length, fold))}\n      }\n    }\n  }\n\n  function SearchCursor(doc, query, pos, options) {\n    this.atOccurrence = false\n    this.afterEmptyMatch = false\n    this.doc = doc\n    pos = pos ? doc.clipPos(pos) : Pos(0, 0)\n    this.pos = {from: pos, to: pos}\n\n    var caseFold\n    if (typeof options == \"object\") {\n      caseFold = options.caseFold\n    } else { // Backwards compat for when caseFold was the 4th argument\n      caseFold = options\n      options = null\n    }\n\n    if (typeof query == \"string\") {\n      if (caseFold == null) caseFold = false\n      this.matches = function(reverse, pos) {\n        return (reverse ? searchStringBackward : searchStringForward)(doc, query, pos, caseFold)\n      }\n    } else {\n      query = ensureFlags(query, \"gm\")\n      if (!options || options.multiline !== false)\n        this.matches = function(reverse, pos) {\n          return (reverse ? searchRegexpBackwardMultiline : searchRegexpForwardMultiline)(doc, query, pos)\n        }\n      else\n        this.matches = function(reverse, pos) {\n          return (reverse ? searchRegexpBackward : searchRegexpForward)(doc, query, pos)\n        }\n    }\n  }\n\n  SearchCursor.prototype = {\n    findNext: function() {return this.find(false)},\n    findPrevious: function() {return this.find(true)},\n\n    find: function(reverse) {\n      var head = this.doc.clipPos(reverse ? this.pos.from : this.pos.to);\n      if (this.afterEmptyMatch && this.atOccurrence) {\n        // do not return the same 0 width match twice\n        head = Pos(head.line, head.ch)\n        if (reverse) {\n          head.ch--;\n          if (head.ch < 0) {\n            head.line--;\n            head.ch = (this.doc.getLine(head.line) || \"\").length;\n          }\n        } else {\n          head.ch++;\n          if (head.ch > (this.doc.getLine(head.line) || \"\").length) {\n            head.ch = 0;\n            head.line++;\n          }\n        }\n        if (CodeMirror.cmpPos(head, this.doc.clipPos(head)) != 0) {\n           return this.atOccurrence = false\n        }\n      }\n      var result = this.matches(reverse, head)\n      this.afterEmptyMatch = result && CodeMirror.cmpPos(result.from, result.to) == 0\n\n      if (result) {\n        this.pos = result\n        this.atOccurrence = true\n        return this.pos.match || true\n      } else {\n        var end = Pos(reverse ? this.doc.firstLine() : this.doc.lastLine() + 1, 0)\n        this.pos = {from: end, to: end}\n        return this.atOccurrence = false\n      }\n    },\n\n    from: function() {if (this.atOccurrence) return this.pos.from},\n    to: function() {if (this.atOccurrence) return this.pos.to},\n\n    replace: function(newText, origin) {\n      if (!this.atOccurrence) return\n      var lines = CodeMirror.splitLines(newText)\n      this.doc.replaceRange(lines, this.pos.from, this.pos.to, origin)\n      this.pos.to = Pos(this.pos.from.line + lines.length - 1,\n                        lines[lines.length - 1].length + (lines.length == 1 ? this.pos.from.ch : 0))\n    }\n  }\n\n  CodeMirror.defineExtension(\"getSearchCursor\", function(query, pos, caseFold) {\n    return new SearchCursor(this.doc, query, pos, caseFold)\n  })\n  CodeMirror.defineDocExtension(\"getSearchCursor\", function(query, pos, caseFold) {\n    return new SearchCursor(this, query, pos, caseFold)\n  })\n\n  CodeMirror.defineExtension(\"selectMatches\", function(query, caseFold) {\n    var ranges = []\n    var cur = this.getSearchCursor(query, this.getCursor(\"from\"), caseFold)\n    while (cur.findNext()) {\n      if (CodeMirror.cmpPos(cur.to(), this.getCursor(\"to\")) > 0) break\n      ranges.push({anchor: cur.from(), head: cur.to()})\n    }\n    if (ranges.length)\n      this.setSelections(ranges, 0)\n  })\n});\n"], "mappings": ";;;;;;;;;;;AAGA,KAAC,SAASA,GAAK;AAEXA,QAAIC,GAAA,CAA+B;IAKtC,GAAE,SAASC,GAAY;AAEtB,UAAIC,IAAMD,EAAW;AAErB,eAASE,EAAYC,GAAQ;AAC3B,YAAIC,IAAQD,EAAO;AACnB,eAAOC,MAAyBD,EAAO,aAAa,MAAM,OACrDA,EAAO,SAAS,MAAM,OACtBA,EAAO,YAAY,MAAM;MAC/B;AALQE,QAAAH,GAAA,aAAA;AAOT,eAASI,EAAYH,GAAQC,GAAO;AAElC,iBADIG,IAAUL,EAAYC,CAAM,GAAGK,IAASD,GACnCE,IAAI,GAAGA,IAAIL,EAAM,QAAQK;AAASD,YAAO,QAAQJ,EAAM,OAAOK,CAAC,CAAC,KAAK,OAC5ED,KAAUJ,EAAM,OAAOK,CAAC;AAC1B,eAAOF,KAAWC,IAASL,IAAS,IAAI,OAAOA,EAAO,QAAQK,CAAM;MACrE;AALQH,QAAAC,GAAA,aAAA;AAOT,eAASI,EAAeP,GAAQ;AAC9B,eAAO,0BAA0B,KAAKA,EAAO,MAAM;MACpD;AAFQE,QAAAK,GAAA,gBAAA;AAIT,eAASC,EAAoBC,GAAKT,GAAQU,GAAO;AAC/CV,YAASG,EAAYH,GAAQ,GAAG;AAChC,iBAASW,IAAOD,EAAM,MAAME,IAAKF,EAAM,IAAIG,IAAOJ,EAAI,SAAU,GAAEE,KAAQE,GAAMF,KAAQC,IAAK,GAAG;AAC9FZ,YAAO,YAAYY;AACnB,cAAIE,IAASL,EAAI,QAAQE,CAAI,GAAGI,IAAQf,EAAO,KAAKc,CAAM;AAC1D,cAAIC;AACF,mBAAO;cAAC,MAAMjB,EAAIa,GAAMI,EAAM,KAAK;cAC3B,IAAIjB,EAAIa,GAAMI,EAAM,QAAQA,EAAM,CAAC,EAAE,MAAM;cAC3C,OAAOA;YAAK;QAAA;MAEzB;AAVQb,QAAAM,GAAA,qBAAA;AAYT,eAASQ,EAA6BP,GAAKT,GAAQU,GAAO;AACxD,YAAI,CAACH,EAAeP,CAAM;AAAG,iBAAOQ,EAAoBC,GAAKT,GAAQU,CAAK;AAE1EV,YAASG,EAAYH,GAAQ,IAAI;AAEjC,iBADIc,GAAQG,IAAQ,GACXN,IAAOD,EAAM,MAAMG,IAAOJ,EAAI,SAAQ,GAAIE,KAAQE,KAAO;AAMhE,mBAASP,IAAI,GAAGA,IAAIW,KACd,EAAAN,IAAOE,IADcP,KAAK;AAE9B,gBAAIY,IAAUT,EAAI,QAAQE,GAAM;AAChCG,gBAASA,KAAU,OAAOI,IAAUJ,IAAS;IAAOI;UAAA;AAEtDD,cAAQA,IAAQ,GAChBjB,EAAO,YAAYU,EAAM;AACzB,cAAIK,IAAQf,EAAO,KAAKc,CAAM;AAC9B,cAAIC,GAAO;AACT,gBAAII,IAASL,EAAO,MAAM,GAAGC,EAAM,KAAK,EAAE,MAAM;CAAI,GAAGK,IAASL,EAAM,CAAC,EAAE,MAAM;CAAI,GAC/EM,IAAYX,EAAM,OAAOS,EAAO,SAAS,GAAGG,IAAUH,EAAOA,EAAO,SAAS,CAAC,EAAE;AACpF,mBAAO;cAAC,MAAMrB,EAAIuB,GAAWC,CAAO;cAC5B,IAAIxB;gBAAIuB,IAAYD,EAAO,SAAS;gBAC5BA,EAAO,UAAU,IAAIE,IAAUF,EAAO,CAAC,EAAE,SAASA,EAAOA,EAAO,SAAS,CAAC,EAAE;cAAM;cAC1F,OAAOL;YAAK;UAAA;QAAA;MAGzB;AA5BQb,QAAAc,GAAA,8BAAA;AA8BT,eAASO,EAAYT,GAAQd,GAAQwB,GAAW;AAE9C,iBADIT,GAAOU,IAAO,GACXA,KAAQX,EAAO,UAAQ;AAC5Bd,YAAO,YAAYyB;AACnB,cAAIC,IAAW1B,EAAO,KAAKc,CAAM;AACjC,cAAI,CAACY;AAAU;AACf,cAAIC,IAAMD,EAAS,QAAQA,EAAS,CAAC,EAAE;AACvC,cAAIC,IAAMb,EAAO,SAASU;AAAW;AACrC,WAAI,CAACT,KAASY,IAAMZ,EAAM,QAAQA,EAAM,CAAC,EAAE,YACzCA,IAAQW,IACVD,IAAOC,EAAS,QAAQ;QAAA;AAE1B,eAAOX;MACR;AAbQb,QAAAqB,GAAA,aAAA;AAeT,eAASK,EAAqBnB,GAAKT,GAAQU,GAAO;AAChDV,YAASG,EAAYH,GAAQ,GAAG;AAChC,iBAASW,IAAOD,EAAM,MAAME,IAAKF,EAAM,IAAImB,IAAQpB,EAAI,UAAS,GAAIE,KAAQkB,GAAOlB,KAAQC,IAAK,IAAI;AAClG,cAAIE,IAASL,EAAI,QAAQE,CAAI,GACzBI,IAAQQ,EAAYT,GAAQd,GAAQY,IAAK,IAAI,IAAIE,EAAO,SAASF,CAAE;AACvE,cAAIG;AACF,mBAAO;cAAC,MAAMjB,EAAIa,GAAMI,EAAM,KAAK;cAC3B,IAAIjB,EAAIa,GAAMI,EAAM,QAAQA,EAAM,CAAC,EAAE,MAAM;cAC3C,OAAOA;YAAK;QAAA;MAEzB;AAVQb,QAAA0B,GAAA,sBAAA;AAYT,eAASE,EAA8BrB,GAAKT,GAAQU,GAAO;AACzD,YAAI,CAACH,EAAeP,CAAM;AAAG,iBAAO4B,EAAqBnB,GAAKT,GAAQU,CAAK;AAC3EV,YAASG,EAAYH,GAAQ,IAAI;AAEjC,iBADIc,GAAQiB,IAAY,GAAGP,IAAYf,EAAI,QAAQC,EAAM,IAAI,EAAE,SAASA,EAAM,IACrEC,IAAOD,EAAM,MAAMmB,IAAQpB,EAAI,UAAS,GAAIE,KAAQkB,KAAQ;AACnE,mBAASvB,IAAI,GAAGA,IAAIyB,KAAapB,KAAQkB,GAAOvB,KAAK;AACnD,gBAAIY,IAAUT,EAAI,QAAQE,GAAM;AAChCG,gBAASA,KAAU,OAAOI,IAAUA,IAAU;IAAOJ;UAAA;AAEvDiB,eAAa;AAEb,cAAIhB,IAAQQ,EAAYT,GAAQd,GAAQwB,CAAS;AACjD,cAAIT,GAAO;AACT,gBAAII,IAASL,EAAO,MAAM,GAAGC,EAAM,KAAK,EAAE,MAAM;CAAI,GAAGK,IAASL,EAAM,CAAC,EAAE,MAAM;CAAI,GAC/EM,IAAYV,IAAOQ,EAAO,QAAQG,IAAUH,EAAOA,EAAO,SAAS,CAAC,EAAE;AAC1E,mBAAO;cAAC,MAAMrB,EAAIuB,GAAWC,CAAO;cAC5B,IAAIxB;gBAAIuB,IAAYD,EAAO,SAAS;gBAC5BA,EAAO,UAAU,IAAIE,IAAUF,EAAO,CAAC,EAAE,SAASA,EAAOA,EAAO,SAAS,CAAC,EAAE;cAAM;cAC1F,OAAOL;YAAK;UAAA;QAAA;MAGzB;AArBQb,QAAA4B,GAAA,+BAAA;AAuBT,UAAIE,GAAQC;AACR,aAAO,UAAU,aACnBD,IAAS9B,EAAA,SAASgC,GAAK;AAAE,eAAOA,EAAI,UAAU,KAAK,EAAE,YAAA;MAAe,GAA3D,QAAA,GACTD,IAAS/B,EAAA,SAASgC,GAAK;AAAE,eAAOA,EAAI,UAAU,KAAK;MAAG,GAA7C,QAAA,MAETF,IAAS9B,EAAA,SAASgC,GAAK;AAAE,eAAOA,EAAI,YAAW;MAAI,GAA1C,QAAA,GACTD,IAAS/B,EAAA,SAASgC,GAAK;AAAE,eAAOA;MAAK,GAA5B,QAAA;AAKX,eAASC,EAAUC,GAAMC,GAAQC,GAAKC,GAAU;AAC9C,YAAIH,EAAK,UAAUC,EAAO;AAAQ,iBAAOC;AACzC,iBAASE,IAAM,GAAGC,IAAMH,IAAM,KAAK,IAAI,GAAGF,EAAK,SAASC,EAAO,MAAM,OAAK;AACxE,cAAIG,KAAOC;AAAK,mBAAOD;AACvB,cAAIE,IAAOF,IAAMC,KAAQ,GACrBE,IAAMJ,EAASH,EAAK,MAAM,GAAGM,CAAG,CAAC,EAAE;AACvC,cAAIC,KAAOL;AAAK,mBAAOI;AACdC,cAAML,IAAKG,IAAMC,IACrBF,IAAME,IAAM;QAAA;MAEpB;AAVQxC,QAAAiC,GAAA,WAAA;AAYT,eAASS,EAAoBnC,GAAKoC,GAAOnC,GAAOoC,GAAU;AAGxD,YAAI,CAACD,EAAM;AAAQ,iBAAO;AAC1B,YAAIE,IAAOD,IAAWd,IAASC,GAC3Be,IAAQD,EAAKF,CAAK,EAAE,MAAM,UAAU;AAExCI;AAAQ,mBAAStC,IAAOD,EAAM,MAAME,IAAKF,EAAM,IAAIG,IAAOJ,EAAI,SAAA,IAAa,IAAIuC,EAAM,QAAQrC,KAAQE,GAAMF,KAAQC,IAAK,GAAG;AACzH,gBAAIwB,IAAO3B,EAAI,QAAQE,CAAI,EAAE,MAAMC,CAAE,GAAGE,IAASiC,EAAKX,CAAI;AAC1D,gBAAIY,EAAM,UAAU,GAAG;AACrB,kBAAIE,IAAQpC,EAAO,QAAQkC,EAAM,CAAC,CAAC;AACnC,kBAAIE,KAAS;AAAI,yBAASD;AAC1B,kBAAIvC,IAAQyB,EAAUC,GAAMtB,GAAQoC,GAAOH,CAAI,IAAInC;AACnD,qBAAO;gBAAC,MAAMd,EAAIa,GAAMwB,EAAUC,GAAMtB,GAAQoC,GAAOH,CAAI,IAAInC,CAAE;gBACzD,IAAId,EAAIa,GAAMwB,EAAUC,GAAMtB,GAAQoC,IAAQF,EAAM,CAAC,EAAE,QAAQD,CAAI,IAAInC,CAAE;cAAC;YAAA,OAC7E;AACL,kBAAIuC,IAAUrC,EAAO,SAASkC,EAAM,CAAC,EAAE;AACvC,kBAAIlC,EAAO,MAAMqC,CAAO,KAAKH,EAAM,CAAC;AAAG,yBAASC;AAChD,uBAAS3C,IAAI,GAAGA,IAAI0C,EAAM,SAAS,GAAG1C;AACpC,oBAAIyC,EAAKtC,EAAI,QAAQE,IAAOL,CAAC,CAAC,KAAK0C,EAAM1C,CAAC;AAAG,2BAAS2C;AACxD,kBAAItB,IAAMlB,EAAI,QAAQE,IAAOqC,EAAM,SAAS,CAAC,GAAGI,IAAYL,EAAKpB,CAAG,GAAG0B,IAAWL,EAAMA,EAAM,SAAS,CAAC;AACxG,kBAAII,EAAU,MAAM,GAAGC,EAAS,MAAM,KAAKA;AAAU,yBAASJ;AAC9D,qBAAO;gBAAC,MAAMnD,EAAIa,GAAMwB,EAAUC,GAAMtB,GAAQqC,GAASJ,CAAI,IAAInC,CAAE;gBAC3D,IAAId,EAAIa,IAAOqC,EAAM,SAAS,GAAGb,EAAUR,GAAKyB,GAAWC,EAAS,QAAQN,CAAI,CAAC;cAAC;YAAA;UAAA;MAG/F;AA1BQ7C,QAAA0C,GAAA,qBAAA;AA4BT,eAASU,EAAqB7C,GAAKoC,GAAOnC,GAAOoC,GAAU;AACzD,YAAI,CAACD,EAAM;AAAQ,iBAAO;AAC1B,YAAIE,IAAOD,IAAWd,IAASC,GAC3Be,IAAQD,EAAKF,CAAK,EAAE,MAAM,UAAU;AAExCI;AAAQ,mBAAStC,IAAOD,EAAM,MAAME,IAAKF,EAAM,IAAImB,IAAQpB,EAAI,UAAS,IAAK,IAAIuC,EAAM,QAAQrC,KAAQkB,GAAOlB,KAAQC,IAAK,IAAI;AAC7H,gBAAIwB,IAAO3B,EAAI,QAAQE,CAAI;AACvBC,gBAAK,OAAIwB,IAAOA,EAAK,MAAM,GAAGxB,CAAE;AACpC,gBAAIE,IAASiC,EAAKX,CAAI;AACtB,gBAAIY,EAAM,UAAU,GAAG;AACrB,kBAAIE,IAAQpC,EAAO,YAAYkC,EAAM,CAAC,CAAC;AACvC,kBAAIE,KAAS;AAAI,yBAASD;AAC1B,qBAAO;gBAAC,MAAMnD,EAAIa,GAAMwB,EAAUC,GAAMtB,GAAQoC,GAAOH,CAAI,CAAC;gBACpD,IAAIjD,EAAIa,GAAMwB,EAAUC,GAAMtB,GAAQoC,IAAQF,EAAM,CAAC,EAAE,QAAQD,CAAI,CAAC;cAAC;YAAA,OACxE;AACL,kBAAIM,IAAWL,EAAMA,EAAM,SAAS,CAAC;AACrC,kBAAIlC,EAAO,MAAM,GAAGuC,EAAS,MAAM,KAAKA;AAAU,yBAASJ;AAC3D,uBAAS3C,IAAI,GAAGI,IAAQC,IAAOqC,EAAM,SAAS,GAAG1C,IAAI0C,EAAM,SAAS,GAAG1C;AACrE,oBAAIyC,EAAKtC,EAAI,QAAQC,IAAQJ,CAAC,CAAC,KAAK0C,EAAM1C,CAAC;AAAG,2BAAS2C;AACzD,kBAAIM,IAAM9C,EAAI,QAAQE,IAAO,IAAIqC,EAAM,MAAM,GAAGQ,IAAYT,EAAKQ,CAAG;AACpE,kBAAIC,EAAU,MAAMA,EAAU,SAASR,EAAM,CAAC,EAAE,MAAM,KAAKA,EAAM,CAAC;AAAG,yBAASC;AAC9E,qBAAO;gBAAC,MAAMnD,EAAIa,IAAO,IAAIqC,EAAM,QAAQb,EAAUoB,GAAKC,GAAWD,EAAI,SAASP,EAAM,CAAC,EAAE,QAAQD,CAAI,CAAC;gBAChG,IAAIjD,EAAIa,GAAMwB,EAAUC,GAAMtB,GAAQuC,EAAS,QAAQN,CAAI,CAAC;cAAC;YAAA;UAAA;MAG1E;AAzBQ7C,QAAAoD,GAAA,sBAAA;AA2BT,eAASG,EAAahD,GAAKoC,GAAOP,GAAKoB,GAAS;AAC9C,aAAK,eAAe,OACpB,KAAK,kBAAkB,OACvB,KAAK,MAAMjD,GACX6B,IAAMA,IAAM7B,EAAI,QAAQ6B,CAAG,IAAIxC,EAAI,GAAG,CAAC,GACvC,KAAK,MAAM,EAAC,MAAMwC,GAAK,IAAIA,EAAG;AAE9B,YAAIQ;AACA,eAAOY,KAAW,WACpBZ,IAAWY,EAAQ,YAEnBZ,IAAWY,GACXA,IAAU,OAGR,OAAOb,KAAS,YACdC,KAAY,SAAMA,IAAW,QACjC,KAAK,UAAU,SAASa,GAASrB,GAAK;AACpC,kBAAQqB,IAAUL,IAAuBV,GAAqBnC,GAAKoC,GAAOP,GAAKQ,CAAQ;QACxF,MAEDD,IAAQ1C,EAAY0C,GAAO,IAAI,GAC3B,CAACa,KAAWA,EAAQ,cAAc,QACpC,KAAK,UAAU,SAASC,GAASrB,GAAK;AACpC,kBAAQqB,IAAU7B,IAAgCd,GAA8BP,GAAKoC,GAAOP,CAAG;QAChG,IAED,KAAK,UAAU,SAASqB,GAASrB,GAAK;AACpC,kBAAQqB,IAAU/B,IAAuBpB,GAAqBC,GAAKoC,GAAOP,CAAG;QAC9E;MAEN;AA/BQpC,QAAAuD,GAAA,cAAA,GAiCTA,EAAa,YAAY;QACvB,UAAU,WAAW;AAAC,iBAAO,KAAK,KAAK,KAAK;QAAC;QAC7C,cAAc,WAAW;AAAC,iBAAO,KAAK,KAAK,IAAI;QAAC;QAEhD,MAAM,SAASE,GAAS;AACtB,cAAIC,IAAO,KAAK,IAAI,QAAQD,IAAU,KAAK,IAAI,OAAO,KAAK,IAAI,EAAE;AACjE,cAAI,KAAK,mBAAmB,KAAK,iBAE/BC,IAAO9D,EAAI8D,EAAK,MAAMA,EAAK,EAAE,GACzBD,KACFC,EAAK,MACDA,EAAK,KAAK,MACZA,EAAK,QACLA,EAAK,MAAM,KAAK,IAAI,QAAQA,EAAK,IAAI,KAAK,IAAI,YAGhDA,EAAK,MACDA,EAAK,MAAM,KAAK,IAAI,QAAQA,EAAK,IAAI,KAAK,IAAI,WAChDA,EAAK,KAAK,GACVA,EAAK,UAGL/D,EAAW,OAAO+D,GAAM,KAAK,IAAI,QAAQA,CAAI,CAAC,KAAK;AACpD,mBAAO,KAAK,eAAe;AAGhC,cAAIC,IAAS,KAAK,QAAQF,GAASC,CAAI;AAGvC,cAFA,KAAK,kBAAkBC,KAAUhE,EAAW,OAAOgE,EAAO,MAAMA,EAAO,EAAE,KAAK,GAE1EA;AACF,mBAAA,KAAK,MAAMA,GACX,KAAK,eAAe,MACb,KAAK,IAAI,SAAS;AAEzB,cAAIlC,IAAM7B,EAAI6D,IAAU,KAAK,IAAI,UAAA,IAAc,KAAK,IAAI,SAAA,IAAa,GAAG,CAAC;AACzE,iBAAA,KAAK,MAAM,EAAC,MAAMhC,GAAK,IAAIA,EAAG,GACvB,KAAK,eAAe;QAE9B;QAED,MAAM,WAAW;AAAC,cAAI,KAAK;AAAc,mBAAO,KAAK,IAAI;QAAI;QAC7D,IAAI,WAAW;AAAC,cAAI,KAAK;AAAc,mBAAO,KAAK,IAAI;QAAE;QAEzD,SAAS,SAASmC,GAASC,GAAQ;AACjC,cAAK,KAAK,cACV;AAAA,gBAAIf,IAAQnD,EAAW,WAAWiE,CAAO;AACzC,iBAAK,IAAI,aAAad,GAAO,KAAK,IAAI,MAAM,KAAK,IAAI,IAAIe,CAAM,GAC/D,KAAK,IAAI,KAAKjE;cAAI,KAAK,IAAI,KAAK,OAAOkD,EAAM,SAAS;cACpCA,EAAMA,EAAM,SAAS,CAAC,EAAE,UAAUA,EAAM,UAAU,IAAI,KAAK,IAAI,KAAK,KAAK;YAAE;UAAA;QAC9F;MACF,GAEDnD,EAAW,gBAAgB,mBAAmB,SAASgD,GAAOP,GAAKQ,GAAU;AAC3E,eAAO,IAAIW,EAAa,KAAK,KAAKZ,GAAOP,GAAKQ,CAAQ;MAC1D,CAAG,GACDjD,EAAW,mBAAmB,mBAAmB,SAASgD,GAAOP,GAAKQ,GAAU;AAC9E,eAAO,IAAIW,EAAa,MAAMZ,GAAOP,GAAKQ,CAAQ;MACtD,CAAG,GAEDjD,EAAW,gBAAgB,iBAAiB,SAASgD,GAAOC,GAAU;AAGpE,iBAFIkB,IAAS,CAAE,GACXC,IAAM,KAAK,gBAAgBpB,GAAO,KAAK,UAAU,MAAM,GAAGC,CAAQ,GAC/DmB,EAAI,SAAA,KACL,EAAApE,EAAW,OAAOoE,EAAI,GAAI,GAAE,KAAK,UAAU,IAAI,CAAC,IAAI;AACxDD,YAAO,KAAK,EAAC,QAAQC,EAAI,KAAM,GAAE,MAAMA,EAAI,GAAE,EAAE,CAAC;AAE9CD,UAAO,UACT,KAAK,cAAcA,GAAQ,CAAC;MAClC,CAAG;IACH,CAAC;EAAA,EAAA,IAAA,EAAA;;;", "names": ["mod", "require$$0", "CodeMirror", "Pos", "regexpFlags", "regexp", "flags", "__name", "ensureFlags", "current", "target", "i", "maybeMultiline", "searchRegexpForward", "doc", "start", "line", "ch", "last", "string", "match", "searchRegexpForwardMultiline", "chunk", "curLine", "before", "inside", "startLine", "startCh", "lastMatchIn", "<PERSON><PERSON><PERSON><PERSON>", "from", "newMatch", "end", "searchRegexpBackward", "first", "searchRegexpBackwardMultiline", "chunkSize", "do<PERSON><PERSON>", "noFold", "str", "adjustPos", "orig", "folded", "pos", "foldFunc", "min", "max", "mid", "len", "searchStringForward", "query", "caseFold", "fold", "lines", "search", "found", "cutFrom", "endString", "lastLine", "searchStringBackward", "top", "topString", "SearchCursor", "options", "reverse", "head", "result", "newText", "origin", "ranges", "cur"]}