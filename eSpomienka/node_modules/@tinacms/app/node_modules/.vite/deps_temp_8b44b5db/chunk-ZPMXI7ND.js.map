{"version": 3, "sources": ["../../../../../history/index.js", "../../../../../../../packages/react-router/lib/context.ts", "../../../../../../../packages/react-router/lib/router.ts", "../../../../../../../packages/react-router/lib/hooks.tsx", "../../../../../../../packages/react-router/lib/components.tsx", "../../../../../../../packages/react-router-dom/index.tsx"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\n\n/**\r\n * Actions represent the type of change to a location value.\r\n *\r\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#action\r\n */\nvar Action;\n\n(function (Action) {\n  /**\r\n   * A POP indicates a change to an arbitrary index in the history stack, such\r\n   * as a back or forward navigation. It does not describe the direction of the\r\n   * navigation, only that the current index changed.\r\n   *\r\n   * Note: This is the default action for newly created history objects.\r\n   */\n  Action[\"Pop\"] = \"POP\";\n  /**\r\n   * A PUSH indicates a new entry being added to the history stack, such as when\r\n   * a link is clicked and a new page loads. When this happens, all subsequent\r\n   * entries in the stack are lost.\r\n   */\n\n  Action[\"Push\"] = \"PUSH\";\n  /**\r\n   * A REPLACE indicates the entry at the current index in the history stack\r\n   * being replaced by a new one.\r\n   */\n\n  Action[\"Replace\"] = \"REPLACE\";\n})(Action || (Action = {}));\n\nvar readOnly = process.env.NODE_ENV !== \"production\" ? function (obj) {\n  return Object.freeze(obj);\n} : function (obj) {\n  return obj;\n};\n\nfunction warning(cond, message) {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== 'undefined') console.warn(message);\n\n    try {\n      // Welcome to debugging history!\n      //\n      // This error is thrown as a convenience so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message); // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n}\n\nvar BeforeUnloadEventType = 'beforeunload';\nvar HashChangeEventType = 'hashchange';\nvar PopStateEventType = 'popstate';\n/**\r\n * Browser history stores the location in regular URLs. This is the standard for\r\n * most web apps, but it requires some configuration on the server to ensure you\r\n * serve the same app at multiple URLs.\r\n *\r\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createbrowserhistory\r\n */\n\nfunction createBrowserHistory(options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      _options$window = _options.window,\n      window = _options$window === void 0 ? document.defaultView : _options$window;\n  var globalHistory = window.history;\n\n  function getIndexAndLocation() {\n    var _window$location = window.location,\n        pathname = _window$location.pathname,\n        search = _window$location.search,\n        hash = _window$location.hash;\n    var state = globalHistory.state || {};\n    return [state.idx, readOnly({\n      pathname: pathname,\n      search: search,\n      hash: hash,\n      state: state.usr || null,\n      key: state.key || 'default'\n    })];\n  }\n\n  var blockedPopTx = null;\n\n  function handlePop() {\n    if (blockedPopTx) {\n      blockers.call(blockedPopTx);\n      blockedPopTx = null;\n    } else {\n      var nextAction = Action.Pop;\n\n      var _getIndexAndLocation = getIndexAndLocation(),\n          nextIndex = _getIndexAndLocation[0],\n          nextLocation = _getIndexAndLocation[1];\n\n      if (blockers.length) {\n        if (nextIndex != null) {\n          var delta = index - nextIndex;\n\n          if (delta) {\n            // Revert the POP\n            blockedPopTx = {\n              action: nextAction,\n              location: nextLocation,\n              retry: function retry() {\n                go(delta * -1);\n              }\n            };\n            go(delta);\n          }\n        } else {\n          // Trying to POP to a location with no index. We did not create\n          // this location, so we can't effectively block the navigation.\n          process.env.NODE_ENV !== \"production\" ? warning(false, // TODO: Write up a doc that explains our blocking strategy in\n          // detail and link to it here so people can understand better what\n          // is going on and how to avoid it.\n          \"You are trying to block a POP navigation to a location that was not \" + \"created by the history library. The block will fail silently in \" + \"production, but in general you should do all navigation with the \" + \"history library (instead of using window.history.pushState directly) \" + \"to avoid this situation.\") : void 0;\n        }\n      } else {\n        applyTx(nextAction);\n      }\n    }\n  }\n\n  window.addEventListener(PopStateEventType, handlePop);\n  var action = Action.Pop;\n\n  var _getIndexAndLocation2 = getIndexAndLocation(),\n      index = _getIndexAndLocation2[0],\n      location = _getIndexAndLocation2[1];\n\n  var listeners = createEvents();\n  var blockers = createEvents();\n\n  if (index == null) {\n    index = 0;\n    globalHistory.replaceState(_extends({}, globalHistory.state, {\n      idx: index\n    }), '');\n  }\n\n  function createHref(to) {\n    return typeof to === 'string' ? to : createPath(to);\n  } // state defaults to `null` because `window.history.state` does\n\n\n  function getNextLocation(to, state) {\n    if (state === void 0) {\n      state = null;\n    }\n\n    return readOnly(_extends({\n      pathname: location.pathname,\n      hash: '',\n      search: ''\n    }, typeof to === 'string' ? parsePath(to) : to, {\n      state: state,\n      key: createKey()\n    }));\n  }\n\n  function getHistoryStateAndUrl(nextLocation, index) {\n    return [{\n      usr: nextLocation.state,\n      key: nextLocation.key,\n      idx: index\n    }, createHref(nextLocation)];\n  }\n\n  function allowTx(action, location, retry) {\n    return !blockers.length || (blockers.call({\n      action: action,\n      location: location,\n      retry: retry\n    }), false);\n  }\n\n  function applyTx(nextAction) {\n    action = nextAction;\n\n    var _getIndexAndLocation3 = getIndexAndLocation();\n\n    index = _getIndexAndLocation3[0];\n    location = _getIndexAndLocation3[1];\n    listeners.call({\n      action: action,\n      location: location\n    });\n  }\n\n  function push(to, state) {\n    var nextAction = Action.Push;\n    var nextLocation = getNextLocation(to, state);\n\n    function retry() {\n      push(to, state);\n    }\n\n    if (allowTx(nextAction, nextLocation, retry)) {\n      var _getHistoryStateAndUr = getHistoryStateAndUrl(nextLocation, index + 1),\n          historyState = _getHistoryStateAndUr[0],\n          url = _getHistoryStateAndUr[1]; // TODO: Support forced reloading\n      // try...catch because iOS limits us to 100 pushState calls :/\n\n\n      try {\n        globalHistory.pushState(historyState, '', url);\n      } catch (error) {\n        // They are going to lose state here, but there is no real\n        // way to warn them about it since the page will refresh...\n        window.location.assign(url);\n      }\n\n      applyTx(nextAction);\n    }\n  }\n\n  function replace(to, state) {\n    var nextAction = Action.Replace;\n    var nextLocation = getNextLocation(to, state);\n\n    function retry() {\n      replace(to, state);\n    }\n\n    if (allowTx(nextAction, nextLocation, retry)) {\n      var _getHistoryStateAndUr2 = getHistoryStateAndUrl(nextLocation, index),\n          historyState = _getHistoryStateAndUr2[0],\n          url = _getHistoryStateAndUr2[1]; // TODO: Support forced reloading\n\n\n      globalHistory.replaceState(historyState, '', url);\n      applyTx(nextAction);\n    }\n  }\n\n  function go(delta) {\n    globalHistory.go(delta);\n  }\n\n  var history = {\n    get action() {\n      return action;\n    },\n\n    get location() {\n      return location;\n    },\n\n    createHref: createHref,\n    push: push,\n    replace: replace,\n    go: go,\n    back: function back() {\n      go(-1);\n    },\n    forward: function forward() {\n      go(1);\n    },\n    listen: function listen(listener) {\n      return listeners.push(listener);\n    },\n    block: function block(blocker) {\n      var unblock = blockers.push(blocker);\n\n      if (blockers.length === 1) {\n        window.addEventListener(BeforeUnloadEventType, promptBeforeUnload);\n      }\n\n      return function () {\n        unblock(); // Remove the beforeunload listener so the document may\n        // still be salvageable in the pagehide event.\n        // See https://html.spec.whatwg.org/#unloading-documents\n\n        if (!blockers.length) {\n          window.removeEventListener(BeforeUnloadEventType, promptBeforeUnload);\n        }\n      };\n    }\n  };\n  return history;\n}\n/**\r\n * Hash history stores the location in window.location.hash. This makes it ideal\r\n * for situations where you don't want to send the location to the server for\r\n * some reason, either because you do cannot configure it or the URL space is\r\n * reserved for something else.\r\n *\r\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createhashhistory\r\n */\n\nfunction createHashHistory(options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options2 = options,\n      _options2$window = _options2.window,\n      window = _options2$window === void 0 ? document.defaultView : _options2$window;\n  var globalHistory = window.history;\n\n  function getIndexAndLocation() {\n    var _parsePath = parsePath(window.location.hash.substr(1)),\n        _parsePath$pathname = _parsePath.pathname,\n        pathname = _parsePath$pathname === void 0 ? '/' : _parsePath$pathname,\n        _parsePath$search = _parsePath.search,\n        search = _parsePath$search === void 0 ? '' : _parsePath$search,\n        _parsePath$hash = _parsePath.hash,\n        hash = _parsePath$hash === void 0 ? '' : _parsePath$hash;\n\n    var state = globalHistory.state || {};\n    return [state.idx, readOnly({\n      pathname: pathname,\n      search: search,\n      hash: hash,\n      state: state.usr || null,\n      key: state.key || 'default'\n    })];\n  }\n\n  var blockedPopTx = null;\n\n  function handlePop() {\n    if (blockedPopTx) {\n      blockers.call(blockedPopTx);\n      blockedPopTx = null;\n    } else {\n      var nextAction = Action.Pop;\n\n      var _getIndexAndLocation4 = getIndexAndLocation(),\n          nextIndex = _getIndexAndLocation4[0],\n          nextLocation = _getIndexAndLocation4[1];\n\n      if (blockers.length) {\n        if (nextIndex != null) {\n          var delta = index - nextIndex;\n\n          if (delta) {\n            // Revert the POP\n            blockedPopTx = {\n              action: nextAction,\n              location: nextLocation,\n              retry: function retry() {\n                go(delta * -1);\n              }\n            };\n            go(delta);\n          }\n        } else {\n          // Trying to POP to a location with no index. We did not create\n          // this location, so we can't effectively block the navigation.\n          process.env.NODE_ENV !== \"production\" ? warning(false, // TODO: Write up a doc that explains our blocking strategy in\n          // detail and link to it here so people can understand better\n          // what is going on and how to avoid it.\n          \"You are trying to block a POP navigation to a location that was not \" + \"created by the history library. The block will fail silently in \" + \"production, but in general you should do all navigation with the \" + \"history library (instead of using window.history.pushState directly) \" + \"to avoid this situation.\") : void 0;\n        }\n      } else {\n        applyTx(nextAction);\n      }\n    }\n  }\n\n  window.addEventListener(PopStateEventType, handlePop); // popstate does not fire on hashchange in IE 11 and old (trident) Edge\n  // https://developer.mozilla.org/de/docs/Web/API/Window/popstate_event\n\n  window.addEventListener(HashChangeEventType, function () {\n    var _getIndexAndLocation5 = getIndexAndLocation(),\n        nextLocation = _getIndexAndLocation5[1]; // Ignore extraneous hashchange events.\n\n\n    if (createPath(nextLocation) !== createPath(location)) {\n      handlePop();\n    }\n  });\n  var action = Action.Pop;\n\n  var _getIndexAndLocation6 = getIndexAndLocation(),\n      index = _getIndexAndLocation6[0],\n      location = _getIndexAndLocation6[1];\n\n  var listeners = createEvents();\n  var blockers = createEvents();\n\n  if (index == null) {\n    index = 0;\n    globalHistory.replaceState(_extends({}, globalHistory.state, {\n      idx: index\n    }), '');\n  }\n\n  function getBaseHref() {\n    var base = document.querySelector('base');\n    var href = '';\n\n    if (base && base.getAttribute('href')) {\n      var url = window.location.href;\n      var hashIndex = url.indexOf('#');\n      href = hashIndex === -1 ? url : url.slice(0, hashIndex);\n    }\n\n    return href;\n  }\n\n  function createHref(to) {\n    return getBaseHref() + '#' + (typeof to === 'string' ? to : createPath(to));\n  }\n\n  function getNextLocation(to, state) {\n    if (state === void 0) {\n      state = null;\n    }\n\n    return readOnly(_extends({\n      pathname: location.pathname,\n      hash: '',\n      search: ''\n    }, typeof to === 'string' ? parsePath(to) : to, {\n      state: state,\n      key: createKey()\n    }));\n  }\n\n  function getHistoryStateAndUrl(nextLocation, index) {\n    return [{\n      usr: nextLocation.state,\n      key: nextLocation.key,\n      idx: index\n    }, createHref(nextLocation)];\n  }\n\n  function allowTx(action, location, retry) {\n    return !blockers.length || (blockers.call({\n      action: action,\n      location: location,\n      retry: retry\n    }), false);\n  }\n\n  function applyTx(nextAction) {\n    action = nextAction;\n\n    var _getIndexAndLocation7 = getIndexAndLocation();\n\n    index = _getIndexAndLocation7[0];\n    location = _getIndexAndLocation7[1];\n    listeners.call({\n      action: action,\n      location: location\n    });\n  }\n\n  function push(to, state) {\n    var nextAction = Action.Push;\n    var nextLocation = getNextLocation(to, state);\n\n    function retry() {\n      push(to, state);\n    }\n\n    process.env.NODE_ENV !== \"production\" ? warning(nextLocation.pathname.charAt(0) === '/', \"Relative pathnames are not supported in hash history.push(\" + JSON.stringify(to) + \")\") : void 0;\n\n    if (allowTx(nextAction, nextLocation, retry)) {\n      var _getHistoryStateAndUr3 = getHistoryStateAndUrl(nextLocation, index + 1),\n          historyState = _getHistoryStateAndUr3[0],\n          url = _getHistoryStateAndUr3[1]; // TODO: Support forced reloading\n      // try...catch because iOS limits us to 100 pushState calls :/\n\n\n      try {\n        globalHistory.pushState(historyState, '', url);\n      } catch (error) {\n        // They are going to lose state here, but there is no real\n        // way to warn them about it since the page will refresh...\n        window.location.assign(url);\n      }\n\n      applyTx(nextAction);\n    }\n  }\n\n  function replace(to, state) {\n    var nextAction = Action.Replace;\n    var nextLocation = getNextLocation(to, state);\n\n    function retry() {\n      replace(to, state);\n    }\n\n    process.env.NODE_ENV !== \"production\" ? warning(nextLocation.pathname.charAt(0) === '/', \"Relative pathnames are not supported in hash history.replace(\" + JSON.stringify(to) + \")\") : void 0;\n\n    if (allowTx(nextAction, nextLocation, retry)) {\n      var _getHistoryStateAndUr4 = getHistoryStateAndUrl(nextLocation, index),\n          historyState = _getHistoryStateAndUr4[0],\n          url = _getHistoryStateAndUr4[1]; // TODO: Support forced reloading\n\n\n      globalHistory.replaceState(historyState, '', url);\n      applyTx(nextAction);\n    }\n  }\n\n  function go(delta) {\n    globalHistory.go(delta);\n  }\n\n  var history = {\n    get action() {\n      return action;\n    },\n\n    get location() {\n      return location;\n    },\n\n    createHref: createHref,\n    push: push,\n    replace: replace,\n    go: go,\n    back: function back() {\n      go(-1);\n    },\n    forward: function forward() {\n      go(1);\n    },\n    listen: function listen(listener) {\n      return listeners.push(listener);\n    },\n    block: function block(blocker) {\n      var unblock = blockers.push(blocker);\n\n      if (blockers.length === 1) {\n        window.addEventListener(BeforeUnloadEventType, promptBeforeUnload);\n      }\n\n      return function () {\n        unblock(); // Remove the beforeunload listener so the document may\n        // still be salvageable in the pagehide event.\n        // See https://html.spec.whatwg.org/#unloading-documents\n\n        if (!blockers.length) {\n          window.removeEventListener(BeforeUnloadEventType, promptBeforeUnload);\n        }\n      };\n    }\n  };\n  return history;\n}\n/**\r\n * Memory history stores the current location in memory. It is designed for use\r\n * in stateful non-browser environments like tests and React Native.\r\n *\r\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#creatememoryhistory\r\n */\n\nfunction createMemoryHistory(options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options3 = options,\n      _options3$initialEntr = _options3.initialEntries,\n      initialEntries = _options3$initialEntr === void 0 ? ['/'] : _options3$initialEntr,\n      initialIndex = _options3.initialIndex;\n  var entries = initialEntries.map(function (entry) {\n    var location = readOnly(_extends({\n      pathname: '/',\n      search: '',\n      hash: '',\n      state: null,\n      key: createKey()\n    }, typeof entry === 'string' ? parsePath(entry) : entry));\n    process.env.NODE_ENV !== \"production\" ? warning(location.pathname.charAt(0) === '/', \"Relative pathnames are not supported in createMemoryHistory({ initialEntries }) (invalid entry: \" + JSON.stringify(entry) + \")\") : void 0;\n    return location;\n  });\n  var index = clamp(initialIndex == null ? entries.length - 1 : initialIndex, 0, entries.length - 1);\n  var action = Action.Pop;\n  var location = entries[index];\n  var listeners = createEvents();\n  var blockers = createEvents();\n\n  function createHref(to) {\n    return typeof to === 'string' ? to : createPath(to);\n  }\n\n  function getNextLocation(to, state) {\n    if (state === void 0) {\n      state = null;\n    }\n\n    return readOnly(_extends({\n      pathname: location.pathname,\n      search: '',\n      hash: ''\n    }, typeof to === 'string' ? parsePath(to) : to, {\n      state: state,\n      key: createKey()\n    }));\n  }\n\n  function allowTx(action, location, retry) {\n    return !blockers.length || (blockers.call({\n      action: action,\n      location: location,\n      retry: retry\n    }), false);\n  }\n\n  function applyTx(nextAction, nextLocation) {\n    action = nextAction;\n    location = nextLocation;\n    listeners.call({\n      action: action,\n      location: location\n    });\n  }\n\n  function push(to, state) {\n    var nextAction = Action.Push;\n    var nextLocation = getNextLocation(to, state);\n\n    function retry() {\n      push(to, state);\n    }\n\n    process.env.NODE_ENV !== \"production\" ? warning(location.pathname.charAt(0) === '/', \"Relative pathnames are not supported in memory history.push(\" + JSON.stringify(to) + \")\") : void 0;\n\n    if (allowTx(nextAction, nextLocation, retry)) {\n      index += 1;\n      entries.splice(index, entries.length, nextLocation);\n      applyTx(nextAction, nextLocation);\n    }\n  }\n\n  function replace(to, state) {\n    var nextAction = Action.Replace;\n    var nextLocation = getNextLocation(to, state);\n\n    function retry() {\n      replace(to, state);\n    }\n\n    process.env.NODE_ENV !== \"production\" ? warning(location.pathname.charAt(0) === '/', \"Relative pathnames are not supported in memory history.replace(\" + JSON.stringify(to) + \")\") : void 0;\n\n    if (allowTx(nextAction, nextLocation, retry)) {\n      entries[index] = nextLocation;\n      applyTx(nextAction, nextLocation);\n    }\n  }\n\n  function go(delta) {\n    var nextIndex = clamp(index + delta, 0, entries.length - 1);\n    var nextAction = Action.Pop;\n    var nextLocation = entries[nextIndex];\n\n    function retry() {\n      go(delta);\n    }\n\n    if (allowTx(nextAction, nextLocation, retry)) {\n      index = nextIndex;\n      applyTx(nextAction, nextLocation);\n    }\n  }\n\n  var history = {\n    get index() {\n      return index;\n    },\n\n    get action() {\n      return action;\n    },\n\n    get location() {\n      return location;\n    },\n\n    createHref: createHref,\n    push: push,\n    replace: replace,\n    go: go,\n    back: function back() {\n      go(-1);\n    },\n    forward: function forward() {\n      go(1);\n    },\n    listen: function listen(listener) {\n      return listeners.push(listener);\n    },\n    block: function block(blocker) {\n      return blockers.push(blocker);\n    }\n  };\n  return history;\n} ////////////////////////////////////////////////////////////////////////////////\n// UTILS\n////////////////////////////////////////////////////////////////////////////////\n\nfunction clamp(n, lowerBound, upperBound) {\n  return Math.min(Math.max(n, lowerBound), upperBound);\n}\n\nfunction promptBeforeUnload(event) {\n  // Cancel the event.\n  event.preventDefault(); // Chrome (and legacy IE) requires returnValue to be set.\n\n  event.returnValue = '';\n}\n\nfunction createEvents() {\n  var handlers = [];\n  return {\n    get length() {\n      return handlers.length;\n    },\n\n    push: function push(fn) {\n      handlers.push(fn);\n      return function () {\n        handlers = handlers.filter(function (handler) {\n          return handler !== fn;\n        });\n      };\n    },\n    call: function call(arg) {\n      handlers.forEach(function (fn) {\n        return fn && fn(arg);\n      });\n    }\n  };\n}\n\nfunction createKey() {\n  return Math.random().toString(36).substr(2, 8);\n}\n/**\r\n * Creates a string URL path from the given pathname, search, and hash components.\r\n *\r\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createpath\r\n */\n\n\nfunction createPath(_ref) {\n  var _ref$pathname = _ref.pathname,\n      pathname = _ref$pathname === void 0 ? '/' : _ref$pathname,\n      _ref$search = _ref.search,\n      search = _ref$search === void 0 ? '' : _ref$search,\n      _ref$hash = _ref.hash,\n      hash = _ref$hash === void 0 ? '' : _ref$hash;\n  if (search && search !== '?') pathname += search.charAt(0) === '?' ? search : '?' + search;\n  if (hash && hash !== '#') pathname += hash.charAt(0) === '#' ? hash : '#' + hash;\n  return pathname;\n}\n/**\r\n * Parses a string URL path into its separate pathname, search, and hash components.\r\n *\r\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#parsepath\r\n */\n\nfunction parsePath(path) {\n  var parsedPath = {};\n\n  if (path) {\n    var hashIndex = path.indexOf('#');\n\n    if (hashIndex >= 0) {\n      parsedPath.hash = path.substr(hashIndex);\n      path = path.substr(0, hashIndex);\n    }\n\n    var searchIndex = path.indexOf('?');\n\n    if (searchIndex >= 0) {\n      parsedPath.search = path.substr(searchIndex);\n      path = path.substr(0, searchIndex);\n    }\n\n    if (path) {\n      parsedPath.pathname = path;\n    }\n  }\n\n  return parsedPath;\n}\n\nexport { Action, createBrowserHistory, createHashHistory, createMemoryHistory, createPath, parsePath };\n//# sourceMappingURL=index.js.map\n", "import * as React from \"react\";\nimport type { History, Location } from \"history\";\nimport { Action as NavigationType } from \"history\";\n\nimport type { RouteMatch } from \"./router\";\n\n/**\n * A Navigator is a \"location changer\"; it's how you get to different locations.\n *\n * Every history instance conforms to the Navigator interface, but the\n * distinction is useful primarily when it comes to the low-level <Router> API\n * where both the location and a navigator must be provided separately in order\n * to avoid \"tearing\" that may occur in a suspense-enabled app if the action\n * and/or location were to be read directly from the history instance.\n */\nexport type Navigator = Pick<History, \"go\" | \"push\" | \"replace\" | \"createHref\">;\n\ninterface NavigationContextObject {\n  basename: string;\n  navigator: Navigator;\n  static: boolean;\n}\n\nexport const NavigationContext = React.createContext<NavigationContextObject>(\n  null!\n);\n\nif (__DEV__) {\n  NavigationContext.displayName = \"Navigation\";\n}\n\ninterface LocationContextObject {\n  location: Location;\n  navigationType: NavigationType;\n}\n\nexport const LocationContext = React.createContext<LocationContextObject>(\n  null!\n);\n\nif (__DEV__) {\n  LocationContext.displayName = \"Location\";\n}\n\ninterface RouteContextObject {\n  outlet: React.ReactElement | null;\n  matches: RouteMatch[];\n}\n\nexport const RouteContext = React.createContext<RouteContextObject>({\n  outlet: null,\n  matches: [],\n});\n\nif (__DEV__) {\n  RouteContext.displayName = \"Route\";\n}\n", "import type { Location, Path, To } from \"history\";\nimport { parsePath } from \"history\";\n\nexport function invariant(cond: any, message: string): asserts cond {\n  if (!cond) throw new Error(message);\n}\n\nexport function warning(cond: any, message: string): void {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n\n    try {\n      // Welcome to debugging React Router!\n      //\n      // This error is thrown as a convenience so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message);\n      // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n}\n\nconst alreadyWarned: Record<string, boolean> = {};\nexport function warningOnce(key: string, cond: boolean, message: string) {\n  if (!cond && !alreadyWarned[key]) {\n    alreadyWarned[key] = true;\n    warning(false, message);\n  }\n}\n\ntype ParamParseFailed = { failed: true };\n\ntype ParamParseSegment<Segment extends string> =\n  // Check here if there exists a forward slash in the string.\n  Segment extends `${infer LeftSegment}/${infer RightSegment}`\n    ? // If there is a forward slash, then attempt to parse each side of the\n      // forward slash.\n      ParamParseSegment<LeftSegment> extends infer LeftResult\n      ? ParamParseSegment<RightSegment> extends infer RightResult\n        ? LeftResult extends string\n          ? // If the left side is successfully parsed as a param, then check if\n            // the right side can be successfully parsed as well. If both sides\n            // can be parsed, then the result is a union of the two sides\n            // (read: \"foo\" | \"bar\").\n            RightResult extends string\n            ? LeftResult | RightResult\n            : LeftResult\n          : // If the left side is not successfully parsed as a param, then check\n          // if only the right side can be successfully parse as a param. If it\n          // can, then the result is just right, else it's a failure.\n          RightResult extends string\n          ? RightResult\n          : ParamParseFailed\n        : ParamParseFailed\n      : // If the left side didn't parse into a param, then just check the right\n      // side.\n      ParamParseSegment<RightSegment> extends infer RightResult\n      ? RightResult extends string\n        ? RightResult\n        : ParamParseFailed\n      : ParamParseFailed\n    : // If there's no forward slash, then check if this segment starts with a\n    // colon. If it does, then this is a dynamic segment, so the result is\n    // just the remainder of the string. Otherwise, it's a failure.\n    Segment extends `:${infer Remaining}`\n    ? Remaining\n    : ParamParseFailed;\n\n// Attempt to parse the given string segment. If it fails, then just return the\n// plain string type as a default fallback. Otherwise return the union of the\n// parsed string literals that were referenced as dynamic segments in the route.\nexport type ParamParseKey<Segment extends string> =\n  ParamParseSegment<Segment> extends string\n    ? ParamParseSegment<Segment>\n    : string;\n\n/**\n * The parameters that were parsed from the URL path.\n */\nexport type Params<Key extends string = string> = {\n  readonly [key in Key]: string | undefined;\n};\n\n/**\n * A route object represents a logical route, with (optionally) its child\n * routes organized in a tree-like structure.\n */\nexport interface RouteObject {\n  caseSensitive?: boolean;\n  children?: RouteObject[];\n  element?: React.ReactNode;\n  index?: boolean;\n  path?: string;\n}\n\n/**\n * Returns a path with params interpolated.\n *\n * @see https://reactrouter.com/docs/en/v6/api#generatepath\n */\nexport function generatePath(path: string, params: Params = {}): string {\n  return path\n    .replace(/:(\\w+)/g, (_, key) => {\n      invariant(params[key] != null, `Missing \":${key}\" param`);\n      return params[key]!;\n    })\n    .replace(/\\/*\\*$/, (_) =>\n      params[\"*\"] == null ? \"\" : params[\"*\"].replace(/^\\/*/, \"/\")\n    );\n}\n\n/**\n * A RouteMatch contains info about how a route matched a URL.\n */\nexport interface RouteMatch<ParamKey extends string = string> {\n  /**\n   * The names and values of dynamic parameters in the URL.\n   */\n  params: Params<ParamKey>;\n  /**\n   * The portion of the URL pathname that was matched.\n   */\n  pathname: string;\n  /**\n   * The portion of the URL pathname that was matched before child routes.\n   */\n  pathnameBase: string;\n  /**\n   * The route object that was used to match.\n   */\n  route: RouteObject;\n}\n\n/**\n * Matches the given routes to a location and returns the match data.\n *\n * @see https://reactrouter.com/docs/en/v6/api#matchroutes\n */\nexport function matchRoutes(\n  routes: RouteObject[],\n  locationArg: Partial<Location> | string,\n  basename = \"/\"\n): RouteMatch[] | null {\n  let location =\n    typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n\n  let pathname = stripBasename(location.pathname || \"/\", basename);\n\n  if (pathname == null) {\n    return null;\n  }\n\n  let branches = flattenRoutes(routes);\n  rankRouteBranches(branches);\n\n  let matches = null;\n  for (let i = 0; matches == null && i < branches.length; ++i) {\n    matches = matchRouteBranch(branches[i], pathname);\n  }\n\n  return matches;\n}\n\ninterface RouteMeta {\n  relativePath: string;\n  caseSensitive: boolean;\n  childrenIndex: number;\n  route: RouteObject;\n}\n\ninterface RouteBranch {\n  path: string;\n  score: number;\n  routesMeta: RouteMeta[];\n}\n\nfunction flattenRoutes(\n  routes: RouteObject[],\n  branches: RouteBranch[] = [],\n  parentsMeta: RouteMeta[] = [],\n  parentPath = \"\"\n): RouteBranch[] {\n  routes.forEach((route, index) => {\n    let meta: RouteMeta = {\n      relativePath: route.path || \"\",\n      caseSensitive: route.caseSensitive === true,\n      childrenIndex: index,\n      route,\n    };\n\n    if (meta.relativePath.startsWith(\"/\")) {\n      invariant(\n        meta.relativePath.startsWith(parentPath),\n        `Absolute route path \"${meta.relativePath}\" nested under path ` +\n          `\"${parentPath}\" is not valid. An absolute child route path ` +\n          `must start with the combined path of all its parent routes.`\n      );\n\n      meta.relativePath = meta.relativePath.slice(parentPath.length);\n    }\n\n    let path = joinPaths([parentPath, meta.relativePath]);\n    let routesMeta = parentsMeta.concat(meta);\n\n    // Add the children before adding this route to the array so we traverse the\n    // route tree depth-first and child routes appear before their parents in\n    // the \"flattened\" version.\n    if (route.children && route.children.length > 0) {\n      invariant(\n        route.index !== true,\n        `Index routes must not have child routes. Please remove ` +\n          `all child routes from route path \"${path}\".`\n      );\n\n      flattenRoutes(route.children, branches, routesMeta, path);\n    }\n\n    // Routes without a path shouldn't ever match by themselves unless they are\n    // index routes, so don't add them to the list of possible branches.\n    if (route.path == null && !route.index) {\n      return;\n    }\n\n    branches.push({ path, score: computeScore(path, route.index), routesMeta });\n  });\n\n  return branches;\n}\n\nfunction rankRouteBranches(branches: RouteBranch[]): void {\n  branches.sort((a, b) =>\n    a.score !== b.score\n      ? b.score - a.score // Higher score first\n      : compareIndexes(\n          a.routesMeta.map((meta) => meta.childrenIndex),\n          b.routesMeta.map((meta) => meta.childrenIndex)\n        )\n  );\n}\n\nconst paramRe = /^:\\w+$/;\nconst dynamicSegmentValue = 3;\nconst indexRouteValue = 2;\nconst emptySegmentValue = 1;\nconst staticSegmentValue = 10;\nconst splatPenalty = -2;\nconst isSplat = (s: string) => s === \"*\";\n\nfunction computeScore(path: string, index: boolean | undefined): number {\n  let segments = path.split(\"/\");\n  let initialScore = segments.length;\n  if (segments.some(isSplat)) {\n    initialScore += splatPenalty;\n  }\n\n  if (index) {\n    initialScore += indexRouteValue;\n  }\n\n  return segments\n    .filter((s) => !isSplat(s))\n    .reduce(\n      (score, segment) =>\n        score +\n        (paramRe.test(segment)\n          ? dynamicSegmentValue\n          : segment === \"\"\n          ? emptySegmentValue\n          : staticSegmentValue),\n      initialScore\n    );\n}\n\nfunction compareIndexes(a: number[], b: number[]): number {\n  let siblings =\n    a.length === b.length && a.slice(0, -1).every((n, i) => n === b[i]);\n\n  return siblings\n    ? // If two routes are siblings, we should try to match the earlier sibling\n      // first. This allows people to have fine-grained control over the matching\n      // behavior by simply putting routes with identical paths in the order they\n      // want them tried.\n      a[a.length - 1] - b[b.length - 1]\n    : // Otherwise, it doesn't really make sense to rank non-siblings by index,\n      // so they sort equally.\n      0;\n}\n\nfunction matchRouteBranch<ParamKey extends string = string>(\n  branch: RouteBranch,\n  pathname: string\n): RouteMatch<ParamKey>[] | null {\n  let { routesMeta } = branch;\n\n  let matchedParams = {};\n  let matchedPathname = \"/\";\n  let matches: RouteMatch[] = [];\n  for (let i = 0; i < routesMeta.length; ++i) {\n    let meta = routesMeta[i];\n    let end = i === routesMeta.length - 1;\n    let remainingPathname =\n      matchedPathname === \"/\"\n        ? pathname\n        : pathname.slice(matchedPathname.length) || \"/\";\n    let match = matchPath(\n      { path: meta.relativePath, caseSensitive: meta.caseSensitive, end },\n      remainingPathname\n    );\n\n    if (!match) return null;\n\n    Object.assign(matchedParams, match.params);\n\n    let route = meta.route;\n\n    matches.push({\n      params: matchedParams,\n      pathname: joinPaths([matchedPathname, match.pathname]),\n      pathnameBase: normalizePathname(\n        joinPaths([matchedPathname, match.pathnameBase])\n      ),\n      route,\n    });\n\n    if (match.pathnameBase !== \"/\") {\n      matchedPathname = joinPaths([matchedPathname, match.pathnameBase]);\n    }\n  }\n\n  return matches;\n}\n\n/**\n * A PathPattern is used to match on some portion of a URL pathname.\n */\nexport interface PathPattern<Path extends string = string> {\n  /**\n   * A string to match against a URL pathname. May contain `:id`-style segments\n   * to indicate placeholders for dynamic parameters. May also end with `/*` to\n   * indicate matching the rest of the URL pathname.\n   */\n  path: Path;\n  /**\n   * Should be `true` if the static portions of the `path` should be matched in\n   * the same case.\n   */\n  caseSensitive?: boolean;\n  /**\n   * Should be `true` if this pattern should match the entire URL pathname.\n   */\n  end?: boolean;\n}\n\n/**\n * A PathMatch contains info about how a PathPattern matched on a URL pathname.\n */\nexport interface PathMatch<ParamKey extends string = string> {\n  /**\n   * The names and values of dynamic parameters in the URL.\n   */\n  params: Params<ParamKey>;\n  /**\n   * The portion of the URL pathname that was matched.\n   */\n  pathname: string;\n  /**\n   * The portion of the URL pathname that was matched before child routes.\n   */\n  pathnameBase: string;\n  /**\n   * The pattern that was used to match.\n   */\n  pattern: PathPattern;\n}\n\ntype Mutable<T> = {\n  -readonly [P in keyof T]: T[P];\n};\n\n/**\n * Performs pattern matching on a URL pathname and returns information about\n * the match.\n *\n * @see https://reactrouter.com/docs/en/v6/api#matchpath\n */\nexport function matchPath<\n  ParamKey extends ParamParseKey<Path>,\n  Path extends string\n>(\n  pattern: PathPattern<Path> | Path,\n  pathname: string\n): PathMatch<ParamKey> | null {\n  if (typeof pattern === \"string\") {\n    pattern = { path: pattern, caseSensitive: false, end: true };\n  }\n\n  let [matcher, paramNames] = compilePath(\n    pattern.path,\n    pattern.caseSensitive,\n    pattern.end\n  );\n\n  let match = pathname.match(matcher);\n  if (!match) return null;\n\n  let matchedPathname = match[0];\n  let pathnameBase = matchedPathname.replace(/(.)\\/+$/, \"$1\");\n  let captureGroups = match.slice(1);\n  let params: Params = paramNames.reduce<Mutable<Params>>(\n    (memo, paramName, index) => {\n      // We need to compute the pathnameBase here using the raw splat value\n      // instead of using params[\"*\"] later because it will be decoded then\n      if (paramName === \"*\") {\n        let splatValue = captureGroups[index] || \"\";\n        pathnameBase = matchedPathname\n          .slice(0, matchedPathname.length - splatValue.length)\n          .replace(/(.)\\/+$/, \"$1\");\n      }\n\n      memo[paramName] = safelyDecodeURIComponent(\n        captureGroups[index] || \"\",\n        paramName\n      );\n      return memo;\n    },\n    {}\n  );\n\n  return {\n    params,\n    pathname: matchedPathname,\n    pathnameBase,\n    pattern,\n  };\n}\n\nfunction compilePath(\n  path: string,\n  caseSensitive = false,\n  end = true\n): [RegExp, string[]] {\n  warning(\n    path === \"*\" || !path.endsWith(\"*\") || path.endsWith(\"/*\"),\n    `Route path \"${path}\" will be treated as if it were ` +\n      `\"${path.replace(/\\*$/, \"/*\")}\" because the \\`*\\` character must ` +\n      `always follow a \\`/\\` in the pattern. To get rid of this warning, ` +\n      `please change the route path to \"${path.replace(/\\*$/, \"/*\")}\".`\n  );\n\n  let paramNames: string[] = [];\n  let regexpSource =\n    \"^\" +\n    path\n      .replace(/\\/*\\*?$/, \"\") // Ignore trailing / and /*, we'll handle it below\n      .replace(/^\\/*/, \"/\") // Make sure it has a leading /\n      .replace(/[\\\\.*+^$?{}|()[\\]]/g, \"\\\\$&\") // Escape special regex chars\n      .replace(/:(\\w+)/g, (_: string, paramName: string) => {\n        paramNames.push(paramName);\n        return \"([^\\\\/]+)\";\n      });\n\n  if (path.endsWith(\"*\")) {\n    paramNames.push(\"*\");\n    regexpSource +=\n      path === \"*\" || path === \"/*\"\n        ? \"(.*)$\" // Already matched the initial /, just match the rest\n        : \"(?:\\\\/(.+)|\\\\/*)$\"; // Don't include the / in params[\"*\"]\n  } else {\n    regexpSource += end\n      ? \"\\\\/*$\" // When matching to the end, ignore trailing slashes\n      : // Otherwise, match a word boundary or a proceeding /. The word boundary restricts\n        // parent routes to matching only their own words and nothing more, e.g. parent\n        // route \"/home\" should not match \"/home2\".\n        // Additionally, allow paths starting with `.`, `-`, `~`, and url-encoded entities,\n        // but do not consume the character in the matched path so they can match against\n        // nested paths.\n        \"(?:(?=[.~-]|%[0-9A-F]{2})|\\\\b|\\\\/|$)\";\n  }\n\n  let matcher = new RegExp(regexpSource, caseSensitive ? undefined : \"i\");\n\n  return [matcher, paramNames];\n}\n\nfunction safelyDecodeURIComponent(value: string, paramName: string) {\n  try {\n    return decodeURIComponent(value);\n  } catch (error) {\n    warning(\n      false,\n      `The value for the URL param \"${paramName}\" will not be decoded because` +\n        ` the string \"${value}\" is a malformed URL segment. This is probably` +\n        ` due to a bad percent encoding (${error}).`\n    );\n\n    return value;\n  }\n}\n\n/**\n * Returns a resolved path object relative to the given pathname.\n *\n * @see https://reactrouter.com/docs/en/v6/api#resolvepath\n */\nexport function resolvePath(to: To, fromPathname = \"/\"): Path {\n  let {\n    pathname: toPathname,\n    search = \"\",\n    hash = \"\",\n  } = typeof to === \"string\" ? parsePath(to) : to;\n\n  let pathname = toPathname\n    ? toPathname.startsWith(\"/\")\n      ? toPathname\n      : resolvePathname(toPathname, fromPathname)\n    : fromPathname;\n\n  return {\n    pathname,\n    search: normalizeSearch(search),\n    hash: normalizeHash(hash),\n  };\n}\n\nfunction resolvePathname(relativePath: string, fromPathname: string): string {\n  let segments = fromPathname.replace(/\\/+$/, \"\").split(\"/\");\n  let relativeSegments = relativePath.split(\"/\");\n\n  relativeSegments.forEach((segment) => {\n    if (segment === \"..\") {\n      // Keep the root \"\" segment so the pathname starts at /\n      if (segments.length > 1) segments.pop();\n    } else if (segment !== \".\") {\n      segments.push(segment);\n    }\n  });\n\n  return segments.length > 1 ? segments.join(\"/\") : \"/\";\n}\n\nexport function resolveTo(\n  toArg: To,\n  routePathnames: string[],\n  locationPathname: string\n): Path {\n  let to = typeof toArg === \"string\" ? parsePath(toArg) : toArg;\n  let toPathname = toArg === \"\" || to.pathname === \"\" ? \"/\" : to.pathname;\n\n  // If a pathname is explicitly provided in `to`, it should be relative to the\n  // route context. This is explained in `Note on `<Link to>` values` in our\n  // migration guide from v5 as a means of disambiguation between `to` values\n  // that begin with `/` and those that do not. However, this is problematic for\n  // `to` values that do not provide a pathname. `to` can simply be a search or\n  // hash string, in which case we should assume that the navigation is relative\n  // to the current location's pathname and *not* the route pathname.\n  let from: string;\n  if (toPathname == null) {\n    from = locationPathname;\n  } else {\n    let routePathnameIndex = routePathnames.length - 1;\n\n    if (toPathname.startsWith(\"..\")) {\n      let toSegments = toPathname.split(\"/\");\n\n      // Each leading .. segment means \"go up one route\" instead of \"go up one\n      // URL segment\".  This is a key difference from how <a href> works and a\n      // major reason we call this a \"to\" value instead of a \"href\".\n      while (toSegments[0] === \"..\") {\n        toSegments.shift();\n        routePathnameIndex -= 1;\n      }\n\n      to.pathname = toSegments.join(\"/\");\n    }\n\n    // If there are more \"..\" segments than parent routes, resolve relative to\n    // the root / URL.\n    from = routePathnameIndex >= 0 ? routePathnames[routePathnameIndex] : \"/\";\n  }\n\n  let path = resolvePath(to, from);\n\n  // Ensure the pathname has a trailing slash if the original to value had one.\n  if (\n    toPathname &&\n    toPathname !== \"/\" &&\n    toPathname.endsWith(\"/\") &&\n    !path.pathname.endsWith(\"/\")\n  ) {\n    path.pathname += \"/\";\n  }\n\n  return path;\n}\n\nexport function getToPathname(to: To): string | undefined {\n  // Empty strings should be treated the same as / paths\n  return to === \"\" || (to as Path).pathname === \"\"\n    ? \"/\"\n    : typeof to === \"string\"\n    ? parsePath(to).pathname\n    : to.pathname;\n}\n\nexport function stripBasename(\n  pathname: string,\n  basename: string\n): string | null {\n  if (basename === \"/\") return pathname;\n\n  if (!pathname.toLowerCase().startsWith(basename.toLowerCase())) {\n    return null;\n  }\n\n  let nextChar = pathname.charAt(basename.length);\n  if (nextChar && nextChar !== \"/\") {\n    // pathname does not start with basename/\n    return null;\n  }\n\n  return pathname.slice(basename.length) || \"/\";\n}\n\nexport const joinPaths = (paths: string[]): string =>\n  paths.join(\"/\").replace(/\\/\\/+/g, \"/\");\n\nexport const normalizePathname = (pathname: string): string =>\n  pathname.replace(/\\/+$/, \"\").replace(/^\\/*/, \"/\");\n\nconst normalizeSearch = (search: string): string =>\n  !search || search === \"?\"\n    ? \"\"\n    : search.startsWith(\"?\")\n    ? search\n    : \"?\" + search;\n\nconst normalizeHash = (hash: string): string =>\n  !hash || hash === \"#\" ? \"\" : hash.startsWith(\"#\") ? hash : \"#\" + hash;\n", "import * as React from \"react\";\nimport type { Location, Path, To } from \"history\";\nimport { Action as NavigationType, parsePath } from \"history\";\n\nimport { LocationContext, NavigationContext, RouteContext } from \"./context\";\nimport type {\n  ParamParse<PERSON>ey,\n  Params,\n  PathMatch,\n  PathPattern,\n  RouteMatch,\n  RouteObject,\n} from \"./router\";\nimport {\n  getToPathname,\n  invariant,\n  joinPaths,\n  matchPath,\n  matchRoutes,\n  resolveTo,\n  warning,\n  warningOnce,\n} from \"./router\";\n\n/**\n * Returns the full href for the given \"to\" value. This is useful for building\n * custom links that are also accessible and preserve right-click behavior.\n *\n * @see https://reactrouter.com/docs/en/v6/api#usehref\n */\nexport function useHref(to: To): string {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useHref() may be used only in the context of a <Router> component.`\n  );\n\n  let { basename, navigator } = React.useContext(NavigationContext);\n  let { hash, pathname, search } = useResolvedPath(to);\n\n  let joinedPathname = pathname;\n  if (basename !== \"/\") {\n    let toPathname = getToPathname(to);\n    let endsWithSlash = toPathname != null && toPathname.endsWith(\"/\");\n    joinedPathname =\n      pathname === \"/\"\n        ? basename + (endsWithSlash ? \"/\" : \"\")\n        : joinPaths([basename, pathname]);\n  }\n\n  return navigator.createHref({ pathname: joinedPathname, search, hash });\n}\n\n/**\n * Returns true if this component is a descendant of a <Router>.\n *\n * @see https://reactrouter.com/docs/en/v6/api#useinroutercontext\n */\nexport function useInRouterContext(): boolean {\n  return React.useContext(LocationContext) != null;\n}\n\n/**\n * Returns the current location object, which represents the current URL in web\n * browsers.\n *\n * Note: If you're using this it may mean you're doing some of your own\n * \"routing\" in your app, and we'd like to know what your use case is. We may\n * be able to provide something higher-level to better suit your needs.\n *\n * @see https://reactrouter.com/docs/en/v6/api#uselocation\n */\nexport function useLocation(): Location {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useLocation() may be used only in the context of a <Router> component.`\n  );\n\n  return React.useContext(LocationContext).location;\n}\n\n/**\n * Returns the current navigation action which describes how the router came to\n * the current location, either by a pop, push, or replace on the history stack.\n *\n * @see https://reactrouter.com/docs/en/v6/api#usenavigationtype\n */\nexport function useNavigationType(): NavigationType {\n  return React.useContext(LocationContext).navigationType;\n}\n\n/**\n * Returns true if the URL for the given \"to\" value matches the current URL.\n * This is useful for components that need to know \"active\" state, e.g.\n * <NavLink>.\n *\n * @see https://reactrouter.com/docs/en/v6/api#usematch\n */\nexport function useMatch<\n  ParamKey extends ParamParseKey<Path>,\n  Path extends string\n>(pattern: PathPattern<Path> | Path): PathMatch<ParamKey> | null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useMatch() may be used only in the context of a <Router> component.`\n  );\n\n  let { pathname } = useLocation();\n  return React.useMemo(\n    () => matchPath<ParamKey, Path>(pattern, pathname),\n    [pathname, pattern]\n  );\n}\n\n/**\n * The interface for the navigate() function returned from useNavigate().\n */\nexport interface NavigateFunction {\n  (to: To, options?: NavigateOptions): void;\n  (delta: number): void;\n}\n\nexport interface NavigateOptions {\n  replace?: boolean;\n  state?: any;\n}\n\n/**\n * Returns an imperative method for changing the location. Used by <Link>s, but\n * may also be used by other elements to change the location.\n *\n * @see https://reactrouter.com/docs/en/v6/api#usenavigate\n */\nexport function useNavigate(): NavigateFunction {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useNavigate() may be used only in the context of a <Router> component.`\n  );\n\n  let { basename, navigator } = React.useContext(NavigationContext);\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n\n  let routePathnamesJson = JSON.stringify(\n    matches.map((match) => match.pathnameBase)\n  );\n\n  let activeRef = React.useRef(false);\n  React.useEffect(() => {\n    activeRef.current = true;\n  });\n\n  let navigate: NavigateFunction = React.useCallback(\n    (to: To | number, options: NavigateOptions = {}) => {\n      warning(\n        activeRef.current,\n        `You should call navigate() in a React.useEffect(), not when ` +\n          `your component is first rendered.`\n      );\n\n      if (!activeRef.current) return;\n\n      if (typeof to === \"number\") {\n        navigator.go(to);\n        return;\n      }\n\n      let path = resolveTo(\n        to,\n        JSON.parse(routePathnamesJson),\n        locationPathname\n      );\n\n      if (basename !== \"/\") {\n        path.pathname = joinPaths([basename, path.pathname]);\n      }\n\n      (!!options.replace ? navigator.replace : navigator.push)(\n        path,\n        options.state\n      );\n    },\n    [basename, navigator, routePathnamesJson, locationPathname]\n  );\n\n  return navigate;\n}\n\nconst OutletContext = React.createContext<unknown>(null);\n\n/**\n * Returns the context (if provided) for the child route at this level of the route\n * hierarchy.\n * @see https://reactrouter.com/docs/en/v6/api#useoutletcontext\n */\nexport function useOutletContext<Context = unknown>(): Context {\n  return React.useContext(OutletContext) as Context;\n}\n\n/**\n * Returns the element for the child route at this level of the route\n * hierarchy. Used internally by <Outlet> to render child routes.\n *\n * @see https://reactrouter.com/docs/en/v6/api#useoutlet\n */\nexport function useOutlet(context?: unknown): React.ReactElement | null {\n  let outlet = React.useContext(RouteContext).outlet;\n  if (outlet) {\n    return (\n      <OutletContext.Provider value={context}>{outlet}</OutletContext.Provider>\n    );\n  }\n  return outlet;\n}\n\n/**\n * Returns an object of key/value pairs of the dynamic params from the current\n * URL that were matched by the route path.\n *\n * @see https://reactrouter.com/docs/en/v6/api#useparams\n */\nexport function useParams<\n  ParamsOrKey extends string | Record<string, string | undefined> = string\n>(): Readonly<\n  [ParamsOrKey] extends [string] ? Params<ParamsOrKey> : Partial<ParamsOrKey>\n> {\n  let { matches } = React.useContext(RouteContext);\n  let routeMatch = matches[matches.length - 1];\n  return routeMatch ? (routeMatch.params as any) : {};\n}\n\n/**\n * Resolves the pathname of the given `to` value against the current location.\n *\n * @see https://reactrouter.com/docs/en/v6/api#useresolvedpath\n */\nexport function useResolvedPath(to: To): Path {\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n\n  let routePathnamesJson = JSON.stringify(\n    matches.map((match) => match.pathnameBase)\n  );\n\n  return React.useMemo(\n    () => resolveTo(to, JSON.parse(routePathnamesJson), locationPathname),\n    [to, routePathnamesJson, locationPathname]\n  );\n}\n\n/**\n * Returns the element of the route that matched the current location, prepared\n * with the correct context to render the remainder of the route tree. Route\n * elements in the tree must render an <Outlet> to render their child route's\n * element.\n *\n * @see https://reactrouter.com/docs/en/v6/api#useroutes\n */\nexport function useRoutes(\n  routes: RouteObject[],\n  locationArg?: Partial<Location> | string\n): React.ReactElement | null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useRoutes() may be used only in the context of a <Router> component.`\n  );\n\n  let { matches: parentMatches } = React.useContext(RouteContext);\n  let routeMatch = parentMatches[parentMatches.length - 1];\n  let parentParams = routeMatch ? routeMatch.params : {};\n  let parentPathname = routeMatch ? routeMatch.pathname : \"/\";\n  let parentPathnameBase = routeMatch ? routeMatch.pathnameBase : \"/\";\n  let parentRoute = routeMatch && routeMatch.route;\n\n  if (__DEV__) {\n    // You won't get a warning about 2 different <Routes> under a <Route>\n    // without a trailing *, but this is a best-effort warning anyway since we\n    // cannot even give the warning unless they land at the parent route.\n    //\n    // Example:\n    //\n    // <Routes>\n    //   {/* This route path MUST end with /* because otherwise\n    //       it will never match /blog/post/123 */}\n    //   <Route path=\"blog\" element={<Blog />} />\n    //   <Route path=\"blog/feed\" element={<BlogFeed />} />\n    // </Routes>\n    //\n    // function Blog() {\n    //   return (\n    //     <Routes>\n    //       <Route path=\"post/:id\" element={<Post />} />\n    //     </Routes>\n    //   );\n    // }\n    let parentPath = (parentRoute && parentRoute.path) || \"\";\n    warningOnce(\n      parentPathname,\n      !parentRoute || parentPath.endsWith(\"*\"),\n      `You rendered descendant <Routes> (or called \\`useRoutes()\\`) at ` +\n        `\"${parentPathname}\" (under <Route path=\"${parentPath}\">) but the ` +\n        `parent route path has no trailing \"*\". This means if you navigate ` +\n        `deeper, the parent won't match anymore and therefore the child ` +\n        `routes will never render.\\n\\n` +\n        `Please change the parent <Route path=\"${parentPath}\"> to <Route ` +\n        `path=\"${parentPath === \"/\" ? \"*\" : `${parentPath}/*`}\">.`\n    );\n  }\n\n  let locationFromContext = useLocation();\n\n  let location;\n  if (locationArg) {\n    let parsedLocationArg =\n      typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n\n    invariant(\n      parentPathnameBase === \"/\" ||\n        parsedLocationArg.pathname?.startsWith(parentPathnameBase),\n      `When overriding the location using \\`<Routes location>\\` or \\`useRoutes(routes, location)\\`, ` +\n        `the location pathname must begin with the portion of the URL pathname that was ` +\n        `matched by all parent routes. The current pathname base is \"${parentPathnameBase}\" ` +\n        `but pathname \"${parsedLocationArg.pathname}\" was given in the \\`location\\` prop.`\n    );\n\n    location = parsedLocationArg;\n  } else {\n    location = locationFromContext;\n  }\n\n  let pathname = location.pathname || \"/\";\n  let remainingPathname =\n    parentPathnameBase === \"/\"\n      ? pathname\n      : pathname.slice(parentPathnameBase.length) || \"/\";\n  let matches = matchRoutes(routes, { pathname: remainingPathname });\n\n  if (__DEV__) {\n    warning(\n      parentRoute || matches != null,\n      `No routes matched location \"${location.pathname}${location.search}${location.hash}\" `\n    );\n\n    warning(\n      matches == null ||\n        matches[matches.length - 1].route.element !== undefined,\n      `Matched leaf route at location \"${location.pathname}${location.search}${location.hash}\" does not have an element. ` +\n        `This means it will render an <Outlet /> with a null value by default resulting in an \"empty\" page.`\n    );\n  }\n\n  return _renderMatches(\n    matches &&\n      matches.map((match) =>\n        Object.assign({}, match, {\n          params: Object.assign({}, parentParams, match.params),\n          pathname: joinPaths([parentPathnameBase, match.pathname]),\n          pathnameBase:\n            match.pathnameBase === \"/\"\n              ? parentPathnameBase\n              : joinPaths([parentPathnameBase, match.pathnameBase]),\n        })\n      ),\n    parentMatches\n  );\n}\n\nexport function _renderMatches(\n  matches: RouteMatch[] | null,\n  parentMatches: RouteMatch[] = []\n): React.ReactElement | null {\n  if (matches == null) return null;\n\n  return matches.reduceRight((outlet, match, index) => {\n    return (\n      <RouteContext.Provider\n        children={\n          match.route.element !== undefined ? match.route.element : outlet\n        }\n        value={{\n          outlet,\n          matches: parentMatches.concat(matches.slice(0, index + 1)),\n        }}\n      />\n    );\n  }, null as React.ReactElement | null);\n}\n", "import * as React from \"react\";\nimport type { InitialEntry, Location, MemoryHistory, To } from \"history\";\nimport {\n  Action as NavigationType,\n  createMemoryHistory,\n  parsePath,\n} from \"history\";\n\nimport { LocationContext, NavigationContext, Navigator } from \"./context\";\nimport {\n  useInRouterContext,\n  useNavigate,\n  useOutlet,\n  useRoutes,\n  _renderMatches,\n} from \"./hooks\";\nimport type { RouteMatch, RouteObject } from \"./router\";\nimport { invariant, normalizePathname, stripBasename, warning } from \"./router\";\n\nexport interface MemoryRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  initialEntries?: InitialEntry[];\n  initialIndex?: number;\n}\n\n/**\n * A <Router> that stores all entries in memory.\n *\n * @see https://reactrouter.com/docs/en/v6/api#memoryrouter\n */\nexport function MemoryRouter({\n  basename,\n  children,\n  initialEntries,\n  initialIndex,\n}: MemoryRouterProps): React.ReactElement {\n  let historyRef = React.useRef<MemoryHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createMemoryHistory({ initialEntries, initialIndex });\n  }\n\n  let history = historyRef.current;\n  let [state, setState] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n    />\n  );\n}\n\nexport interface NavigateProps {\n  to: To;\n  replace?: boolean;\n  state?: any;\n}\n\n/**\n * Changes the current location.\n *\n * Note: This API is mostly useful in React.Component subclasses that are not\n * able to use hooks. In functional components, we recommend you use the\n * `useNavigate` hook instead.\n *\n * @see https://reactrouter.com/docs/en/v6/api#navigate\n */\nexport function Navigate({ to, replace, state }: NavigateProps): null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of\n    // the router loaded. We can help them understand how to avoid that.\n    `<Navigate> may be used only in the context of a <Router> component.`\n  );\n\n  warning(\n    !React.useContext(NavigationContext).static,\n    `<Navigate> must not be used on the initial render in a <StaticRouter>. ` +\n      `This is a no-op, but you should modify your code so the <Navigate> is ` +\n      `only ever rendered in response to some user interaction or state change.`\n  );\n\n  let navigate = useNavigate();\n  React.useEffect(() => {\n    navigate(to, { replace, state });\n  });\n\n  return null;\n}\n\nexport interface OutletProps {\n  context?: unknown;\n}\n\n/**\n * Renders the child route's element, if there is one.\n *\n * @see https://reactrouter.com/docs/en/v6/api#outlet\n */\nexport function Outlet(props: OutletProps): React.ReactElement | null {\n  return useOutlet(props.context);\n}\n\nexport interface RouteProps {\n  caseSensitive?: boolean;\n  children?: React.ReactNode;\n  element?: React.ReactNode | null;\n  index?: boolean;\n  path?: string;\n}\n\nexport interface PathRouteProps {\n  caseSensitive?: boolean;\n  children?: React.ReactNode;\n  element?: React.ReactNode | null;\n  index?: false;\n  path: string;\n}\n\nexport interface LayoutRouteProps {\n  children?: React.ReactNode;\n  element?: React.ReactNode | null;\n}\n\nexport interface IndexRouteProps {\n  element?: React.ReactNode | null;\n  index: true;\n}\n\n/**\n * Declares an element that should be rendered at a certain URL path.\n *\n * @see https://reactrouter.com/docs/en/v6/api#route\n */\nexport function Route(\n  _props: PathRouteProps | LayoutRouteProps | IndexRouteProps\n): React.ReactElement | null {\n  invariant(\n    false,\n    `A <Route> is only ever to be used as the child of <Routes> element, ` +\n      `never rendered directly. Please wrap your <Route> in a <Routes>.`\n  );\n}\n\nexport interface RouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  location: Partial<Location> | string;\n  navigationType?: NavigationType;\n  navigator: Navigator;\n  static?: boolean;\n}\n\n/**\n * Provides location context for the rest of the app.\n *\n * Note: You usually won't render a <Router> directly. Instead, you'll render a\n * router that is more specific to your environment such as a <BrowserRouter>\n * in web browsers or a <StaticRouter> for server rendering.\n *\n * @see https://reactrouter.com/docs/en/v6/api#router\n */\nexport function Router({\n  basename: basenameProp = \"/\",\n  children = null,\n  location: locationProp,\n  navigationType = NavigationType.Pop,\n  navigator,\n  static: staticProp = false,\n}: RouterProps): React.ReactElement | null {\n  invariant(\n    !useInRouterContext(),\n    `You cannot render a <Router> inside another <Router>.` +\n      ` You should never have more than one in your app.`\n  );\n\n  let basename = normalizePathname(basenameProp);\n  let navigationContext = React.useMemo(\n    () => ({ basename, navigator, static: staticProp }),\n    [basename, navigator, staticProp]\n  );\n\n  if (typeof locationProp === \"string\") {\n    locationProp = parsePath(locationProp);\n  }\n\n  let {\n    pathname = \"/\",\n    search = \"\",\n    hash = \"\",\n    state = null,\n    key = \"default\",\n  } = locationProp;\n\n  let location = React.useMemo(() => {\n    let trailingPathname = stripBasename(pathname, basename);\n\n    if (trailingPathname == null) {\n      return null;\n    }\n\n    return {\n      pathname: trailingPathname,\n      search,\n      hash,\n      state,\n      key,\n    };\n  }, [basename, pathname, search, hash, state, key]);\n\n  warning(\n    location != null,\n    `<Router basename=\"${basename}\"> is not able to match the URL ` +\n      `\"${pathname}${search}${hash}\" because it does not start with the ` +\n      `basename, so the <Router> won't render anything.`\n  );\n\n  if (location == null) {\n    return null;\n  }\n\n  return (\n    <NavigationContext.Provider value={navigationContext}>\n      <LocationContext.Provider\n        children={children}\n        value={{ location, navigationType }}\n      />\n    </NavigationContext.Provider>\n  );\n}\n\nexport interface RoutesProps {\n  children?: React.ReactNode;\n  location?: Partial<Location> | string;\n}\n\n/**\n * A container for a nested tree of <Route> elements that renders the branch\n * that best matches the current location.\n *\n * @see https://reactrouter.com/docs/en/v6/api#routes\n */\nexport function Routes({\n  children,\n  location,\n}: RoutesProps): React.ReactElement | null {\n  return useRoutes(createRoutesFromChildren(children), location);\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// UTILS\n///////////////////////////////////////////////////////////////////////////////\n\n/**\n * Creates a route config from a React \"children\" object, which is usually\n * either a `<Route>` element or an array of them. Used internally by\n * `<Routes>` to create a route config from its children.\n *\n * @see https://reactrouter.com/docs/en/v6/api#createroutesfromchildren\n */\nexport function createRoutesFromChildren(\n  children: React.ReactNode\n): RouteObject[] {\n  let routes: RouteObject[] = [];\n\n  React.Children.forEach(children, (element) => {\n    if (!React.isValidElement(element)) {\n      // Ignore non-elements. This allows people to more easily inline\n      // conditionals in their route config.\n      return;\n    }\n\n    if (element.type === React.Fragment) {\n      // Transparently support React.Fragment and its children.\n      routes.push.apply(\n        routes,\n        createRoutesFromChildren(element.props.children)\n      );\n      return;\n    }\n\n    invariant(\n      element.type === Route,\n      `[${\n        typeof element.type === \"string\" ? element.type : element.type.name\n      }] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`\n    );\n\n    let route: RouteObject = {\n      caseSensitive: element.props.caseSensitive,\n      element: element.props.element,\n      index: element.props.index,\n      path: element.props.path,\n    };\n\n    if (element.props.children) {\n      route.children = createRoutesFromChildren(element.props.children);\n    }\n\n    routes.push(route);\n  });\n\n  return routes;\n}\n\n/**\n * Renders the result of `matchRoutes()` into a React element.\n */\nexport function renderMatches(\n  matches: RouteMatch[] | null\n): React.ReactElement | null {\n  return _renderMatches(matches);\n}\n", "/**\n * NOTE: If you refactor this to split up the modules into separate files,\n * you'll need to update the rollup config for react-router-dom-v5-compat.\n */\nimport * as React from \"react\";\nimport type { BrowserHistory, HashHistory, History } from \"history\";\nimport { createBrowserHistory, createHashHistory } from \"history\";\nimport {\n  MemoryRouter,\n  Navigate,\n  Outlet,\n  Route,\n  Router,\n  Routes,\n  createRoutesFromChildren,\n  generatePath,\n  matchRoutes,\n  matchPath,\n  createPath,\n  parsePath,\n  resolvePath,\n  renderMatches,\n  useHref,\n  useInRouterContext,\n  useLocation,\n  useMatch,\n  useNavigate,\n  useNavigationType,\n  useOutlet,\n  useParams,\n  useResolvedPath,\n  useRoutes,\n  useOutletContext,\n} from \"react-router\";\nimport type { To } from \"react-router\";\n\nfunction warning(cond: boolean, message: string): void {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n\n    try {\n      // Welcome to debugging React Router!\n      //\n      // This error is thrown as a convenience so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message);\n      // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n}\n\n////////////////////////////////////////////////////////////////////////////////\n// RE-EXPORTS\n////////////////////////////////////////////////////////////////////////////////\n\n// Note: Keep in sync with react-router exports!\nexport {\n  MemoryRouter,\n  Navigate,\n  Outlet,\n  Route,\n  Router,\n  Routes,\n  createRoutesFromChildren,\n  generatePath,\n  matchRoutes,\n  matchPath,\n  createPath,\n  parsePath,\n  renderMatches,\n  resolvePath,\n  useHref,\n  useInRouterContext,\n  useLocation,\n  useMatch,\n  useNavigate,\n  useNavigationType,\n  useOutlet,\n  useParams,\n  useResolvedPath,\n  useRoutes,\n  useOutletContext,\n};\n\nexport { NavigationType } from \"react-router\";\nexport type {\n  Hash,\n  Location,\n  Path,\n  To,\n  MemoryRouterProps,\n  NavigateFunction,\n  NavigateOptions,\n  NavigateProps,\n  Navigator,\n  OutletProps,\n  Params,\n  PathMatch,\n  RouteMatch,\n  RouteObject,\n  RouteProps,\n  PathRouteProps,\n  LayoutRouteProps,\n  IndexRouteProps,\n  RouterProps,\n  Pathname,\n  Search,\n  RoutesProps,\n} from \"react-router\";\n\n///////////////////////////////////////////////////////////////////////////////\n// DANGER! PLEASE READ ME!\n// We provide these exports as an escape hatch in the event that you need any\n// routing data that we don't provide an explicit API for. With that said, we\n// want to cover your use case if we can, so if you feel the need to use these\n// we want to hear from you. Let us know what you're building and we'll do our\n// best to make sure we can support you!\n//\n// We consider these exports an implementation detail and do not guarantee\n// against any breaking changes, regardless of the semver release. Use with\n// extreme caution and only if you understand the consequences. Godspeed.\n///////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport {\n  UNSAFE_NavigationContext,\n  UNSAFE_LocationContext,\n  UNSAFE_RouteContext,\n} from \"react-router\";\n\n////////////////////////////////////////////////////////////////////////////////\n// COMPONENTS\n////////////////////////////////////////////////////////////////////////////////\n\nexport interface BrowserRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  window?: Window;\n}\n\n/**\n * A `<Router>` for use in web browsers. Provides the cleanest URLs.\n */\nexport function BrowserRouter({\n  basename,\n  children,\n  window,\n}: BrowserRouterProps) {\n  let historyRef = React.useRef<BrowserHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createBrowserHistory({ window });\n  }\n\n  let history = historyRef.current;\n  let [state, setState] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n    />\n  );\n}\n\nexport interface HashRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  window?: Window;\n}\n\n/**\n * A `<Router>` for use in web browsers. Stores the location in the hash\n * portion of the URL so it is not sent to the server.\n */\nexport function HashRouter({ basename, children, window }: HashRouterProps) {\n  let historyRef = React.useRef<HashHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createHashHistory({ window });\n  }\n\n  let history = historyRef.current;\n  let [state, setState] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n    />\n  );\n}\n\nexport interface HistoryRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  history: History;\n}\n\n/**\n * A `<Router>` that accepts a pre-instantiated history object. It's important\n * to note that using your own history object is highly discouraged and may add\n * two versions of the history library to your bundles unless you use the same\n * version of the history library that React Router uses internally.\n */\nfunction HistoryRouter({ basename, children, history }: HistoryRouterProps) {\n  const [state, setState] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n    />\n  );\n}\n\nif (__DEV__) {\n  HistoryRouter.displayName = \"unstable_HistoryRouter\";\n}\n\nexport { HistoryRouter as unstable_HistoryRouter };\n\nfunction isModifiedEvent(event: React.MouseEvent) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\n\nexport interface LinkProps\n  extends Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, \"href\"> {\n  reloadDocument?: boolean;\n  replace?: boolean;\n  state?: any;\n  to: To;\n}\n\n/**\n * The public API for rendering a history-aware <a>.\n */\nexport const Link = React.forwardRef<HTMLAnchorElement, LinkProps>(\n  function LinkWithRef(\n    { onClick, reloadDocument, replace = false, state, target, to, ...rest },\n    ref\n  ) {\n    let href = useHref(to);\n    let internalOnClick = useLinkClickHandler(to, { replace, state, target });\n    function handleClick(\n      event: React.MouseEvent<HTMLAnchorElement, MouseEvent>\n    ) {\n      if (onClick) onClick(event);\n      if (!event.defaultPrevented && !reloadDocument) {\n        internalOnClick(event);\n      }\n    }\n\n    return (\n      // eslint-disable-next-line jsx-a11y/anchor-has-content\n      <a\n        {...rest}\n        href={href}\n        onClick={handleClick}\n        ref={ref}\n        target={target}\n      />\n    );\n  }\n);\n\nif (__DEV__) {\n  Link.displayName = \"Link\";\n}\n\nexport interface NavLinkProps\n  extends Omit<LinkProps, \"className\" | \"style\" | \"children\"> {\n  children?:\n    | React.ReactNode\n    | ((props: { isActive: boolean }) => React.ReactNode);\n  caseSensitive?: boolean;\n  className?: string | ((props: { isActive: boolean }) => string | undefined);\n  end?: boolean;\n  style?:\n    | React.CSSProperties\n    | ((props: { isActive: boolean }) => React.CSSProperties);\n}\n\n/**\n * A <Link> wrapper that knows if it's \"active\" or not.\n */\nexport const NavLink = React.forwardRef<HTMLAnchorElement, NavLinkProps>(\n  function NavLinkWithRef(\n    {\n      \"aria-current\": ariaCurrentProp = \"page\",\n      caseSensitive = false,\n      className: classNameProp = \"\",\n      end = false,\n      style: styleProp,\n      to,\n      children,\n      ...rest\n    },\n    ref\n  ) {\n    let location = useLocation();\n    let path = useResolvedPath(to);\n\n    let locationPathname = location.pathname;\n    let toPathname = path.pathname;\n    if (!caseSensitive) {\n      locationPathname = locationPathname.toLowerCase();\n      toPathname = toPathname.toLowerCase();\n    }\n\n    let isActive =\n      locationPathname === toPathname ||\n      (!end &&\n        locationPathname.startsWith(toPathname) &&\n        locationPathname.charAt(toPathname.length) === \"/\");\n\n    let ariaCurrent = isActive ? ariaCurrentProp : undefined;\n\n    let className: string | undefined;\n    if (typeof classNameProp === \"function\") {\n      className = classNameProp({ isActive });\n    } else {\n      // If the className prop is not a function, we use a default `active`\n      // class for <NavLink />s that are active. In v5 `active` was the default\n      // value for `activeClassName`, but we are removing that API and can still\n      // use the old default behavior for a cleaner upgrade path and keep the\n      // simple styling rules working as they currently do.\n      className = [classNameProp, isActive ? \"active\" : null]\n        .filter(Boolean)\n        .join(\" \");\n    }\n\n    let style =\n      typeof styleProp === \"function\" ? styleProp({ isActive }) : styleProp;\n\n    return (\n      <Link\n        {...rest}\n        aria-current={ariaCurrent}\n        className={className}\n        ref={ref}\n        style={style}\n        to={to}\n      >\n        {typeof children === \"function\" ? children({ isActive }) : children}\n      </Link>\n    );\n  }\n);\n\nif (__DEV__) {\n  NavLink.displayName = \"NavLink\";\n}\n\n////////////////////////////////////////////////////////////////////////////////\n// HOOKS\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Handles the click behavior for router `<Link>` components. This is useful if\n * you need to create custom `<Link>` components with the same click behavior we\n * use in our exported `<Link>`.\n */\nexport function useLinkClickHandler<E extends Element = HTMLAnchorElement>(\n  to: To,\n  {\n    target,\n    replace: replaceProp,\n    state,\n  }: {\n    target?: React.HTMLAttributeAnchorTarget;\n    replace?: boolean;\n    state?: any;\n  } = {}\n): (event: React.MouseEvent<E, MouseEvent>) => void {\n  let navigate = useNavigate();\n  let location = useLocation();\n  let path = useResolvedPath(to);\n\n  return React.useCallback(\n    (event: React.MouseEvent<E, MouseEvent>) => {\n      if (\n        event.button === 0 && // Ignore everything but left clicks\n        (!target || target === \"_self\") && // Let browser handle \"target=_blank\" etc.\n        !isModifiedEvent(event) // Ignore clicks with modifier keys\n      ) {\n        event.preventDefault();\n\n        // If the URL hasn't changed, a regular <a> will do a replace instead of\n        // a push, so do the same here.\n        let replace =\n          !!replaceProp || createPath(location) === createPath(path);\n\n        navigate(to, { replace, state });\n      }\n    },\n    [location, navigate, path, replaceProp, state, target, to]\n  );\n}\n\n/**\n * A convenient wrapper for reading and writing search parameters via the\n * URLSearchParams interface.\n */\nexport function useSearchParams(defaultInit?: URLSearchParamsInit) {\n  warning(\n    typeof URLSearchParams !== \"undefined\",\n    `You cannot use the \\`useSearchParams\\` hook in a browser that does not ` +\n      `support the URLSearchParams API. If you need to support Internet ` +\n      `Explorer 11, we recommend you load a polyfill such as ` +\n      `https://github.com/ungap/url-search-params\\n\\n` +\n      `If you're unsure how to load polyfills, we recommend you check out ` +\n      `https://polyfill.io/v3/ which provides some recommendations about how ` +\n      `to load polyfills only for users that need them, instead of for every ` +\n      `user.`\n  );\n\n  let defaultSearchParamsRef = React.useRef(createSearchParams(defaultInit));\n\n  let location = useLocation();\n  let searchParams = React.useMemo(() => {\n    let searchParams = createSearchParams(location.search);\n\n    for (let key of defaultSearchParamsRef.current.keys()) {\n      if (!searchParams.has(key)) {\n        defaultSearchParamsRef.current.getAll(key).forEach((value) => {\n          searchParams.append(key, value);\n        });\n      }\n    }\n\n    return searchParams;\n  }, [location.search]);\n\n  let navigate = useNavigate();\n  let setSearchParams = React.useCallback(\n    (\n      nextInit: URLSearchParamsInit,\n      navigateOptions?: { replace?: boolean; state?: any }\n    ) => {\n      navigate(\"?\" + createSearchParams(nextInit), navigateOptions);\n    },\n    [navigate]\n  );\n\n  return [searchParams, setSearchParams] as const;\n}\n\nexport type ParamKeyValuePair = [string, string];\n\nexport type URLSearchParamsInit =\n  | string\n  | ParamKeyValuePair[]\n  | Record<string, string | string[]>\n  | URLSearchParams;\n\n/**\n * Creates a URLSearchParams object using the given initializer.\n *\n * This is identical to `new URLSearchParams(init)` except it also\n * supports arrays as values in the object form of the initializer\n * instead of just strings. This is convenient when you need multiple\n * values for a given key, but don't want to use an array initializer.\n *\n * For example, instead of:\n *\n *   let searchParams = new URLSearchParams([\n *     ['sort', 'name'],\n *     ['sort', 'price']\n *   ]);\n *\n * you can do:\n *\n *   let searchParams = createSearchParams({\n *     sort: ['name', 'price']\n *   });\n */\nexport function createSearchParams(\n  init: URLSearchParamsInit = \"\"\n): URLSearchParams {\n  return new URLSearchParams(\n    typeof init === \"string\" ||\n    Array.isArray(init) ||\n    init instanceof URLSearchParams\n      ? init\n      : Object.keys(init).reduce((memo, key) => {\n          let value = init[key];\n          return memo.concat(\n            Array.isArray(value) ? value.map((v) => [key, v]) : [[key, value]]\n          );\n        }, [] as ParamKeyValuePair[])\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;AAOA,IAAI;AAAA,CAEH,SAAUA,SAAQ;AAQjB,EAAAA,QAAO,KAAK,IAAI;AAOhB,EAAAA,QAAO,MAAM,IAAI;AAMjB,EAAAA,QAAO,SAAS,IAAI;AACtB,GAAG,WAAW,SAAS,CAAC,EAAE;AAE1B,IAAI,WAAW,OAAwC,SAAU,KAAK;AACpE,SAAO,OAAO,OAAO,GAAG;AAC1B,IAAI,SAAU,KAAK;AACjB,SAAO;AACT;AAEA,SAAS,QAAQ,MAAM,SAAS;AAC9B,MAAI,CAAC,MAAM;AAET,QAAI,OAAO,YAAY;AAAa,cAAQ,KAAK,OAAO;AAExD,QAAI;AAMF,YAAM,IAAI,MAAM,OAAO;AAAA,IACzB,SAAS,GAAG;AAAA,IAAC;AAAA,EACf;AACF;AAEA,IAAI,wBAAwB;AAC5B,IAAI,sBAAsB;AAC1B,IAAI,oBAAoB;AASxB,SAAS,qBAAqB,SAAS;AACrC,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AAEA,MAAI,WAAW,SACX,kBAAkB,SAAS,QAC3B,SAAS,oBAAoB,SAAS,SAAS,cAAc;AACjE,MAAI,gBAAgB,OAAO;AAE3B,WAAS,sBAAsB;AAC7B,QAAI,mBAAmB,OAAO,UAC1B,WAAW,iBAAiB,UAC5B,SAAS,iBAAiB,QAC1B,OAAO,iBAAiB;AAC5B,QAAI,QAAQ,cAAc,SAAS,CAAC;AACpC,WAAO,CAAC,MAAM,KAAK,SAAS;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAO,MAAM,OAAO;AAAA,MACpB,KAAK,MAAM,OAAO;AAAA,IACpB,CAAC,CAAC;AAAA,EACJ;AAEA,MAAI,eAAe;AAEnB,WAAS,YAAY;AACnB,QAAI,cAAc;AAChB,eAAS,KAAK,YAAY;AAC1B,qBAAe;AAAA,IACjB,OAAO;AACL,UAAI,aAAa,OAAO;AAExB,UAAI,uBAAuB,oBAAoB,GAC3C,YAAY,qBAAqB,CAAC,GAClC,eAAe,qBAAqB,CAAC;AAEzC,UAAI,SAAS,QAAQ;AACnB,YAAI,aAAa,MAAM;AACrB,cAAI,QAAQ,QAAQ;AAEpB,cAAI,OAAO;AAET,2BAAe;AAAA,cACb,QAAQ;AAAA,cACR,UAAU;AAAA,cACV,OAAO,SAAS,QAAQ;AACtB,mBAAG,QAAQ,EAAE;AAAA,cACf;AAAA,YACF;AACA,eAAG,KAAK;AAAA,UACV;AAAA,QACF,OAAO;AAGL,iBAAwC;AAAA,YAAQ;AAAA;AAAA;AAAA;AAAA,YAGhD;AAAA,UAAwT,IAAI;AAAA,QAC9T;AAAA,MACF,OAAO;AACL,gBAAQ,UAAU;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AAEA,SAAO,iBAAiB,mBAAmB,SAAS;AACpD,MAAI,SAAS,OAAO;AAEpB,MAAI,wBAAwB,oBAAoB,GAC5C,QAAQ,sBAAsB,CAAC,GAC/B,WAAW,sBAAsB,CAAC;AAEtC,MAAI,YAAY,aAAa;AAC7B,MAAI,WAAW,aAAa;AAE5B,MAAI,SAAS,MAAM;AACjB,YAAQ;AACR,kBAAc,aAAa,SAAS,CAAC,GAAG,cAAc,OAAO;AAAA,MAC3D,KAAK;AAAA,IACP,CAAC,GAAG,EAAE;AAAA,EACR;AAEA,WAAS,WAAW,IAAI;AACtB,WAAO,OAAO,OAAO,WAAW,KAAK,WAAW,EAAE;AAAA,EACpD;AAGA,WAAS,gBAAgB,IAAI,OAAO;AAClC,QAAI,UAAU,QAAQ;AACpB,cAAQ;AAAA,IACV;AAEA,WAAO,SAAS,SAAS;AAAA,MACvB,UAAU,SAAS;AAAA,MACnB,MAAM;AAAA,MACN,QAAQ;AAAA,IACV,GAAG,OAAO,OAAO,WAAW,UAAU,EAAE,IAAI,IAAI;AAAA,MAC9C;AAAA,MACA,KAAK,UAAU;AAAA,IACjB,CAAC,CAAC;AAAA,EACJ;AAEA,WAAS,sBAAsB,cAAcC,QAAO;AAClD,WAAO,CAAC;AAAA,MACN,KAAK,aAAa;AAAA,MAClB,KAAK,aAAa;AAAA,MAClB,KAAKA;AAAA,IACP,GAAG,WAAW,YAAY,CAAC;AAAA,EAC7B;AAEA,WAAS,QAAQC,SAAQC,WAAU,OAAO;AACxC,WAAO,CAAC,SAAS,WAAW,SAAS,KAAK;AAAA,MACxC,QAAQD;AAAA,MACR,UAAUC;AAAA,MACV;AAAA,IACF,CAAC,GAAG;AAAA,EACN;AAEA,WAAS,QAAQ,YAAY;AAC3B,aAAS;AAET,QAAI,wBAAwB,oBAAoB;AAEhD,YAAQ,sBAAsB,CAAC;AAC/B,eAAW,sBAAsB,CAAC;AAClC,cAAU,KAAK;AAAA,MACb;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAEA,WAAS,KAAK,IAAI,OAAO;AACvB,QAAI,aAAa,OAAO;AACxB,QAAI,eAAe,gBAAgB,IAAI,KAAK;AAE5C,aAAS,QAAQ;AACf,WAAK,IAAI,KAAK;AAAA,IAChB;AAEA,QAAI,QAAQ,YAAY,cAAc,KAAK,GAAG;AAC5C,UAAI,wBAAwB,sBAAsB,cAAc,QAAQ,CAAC,GACrE,eAAe,sBAAsB,CAAC,GACtC,MAAM,sBAAsB,CAAC;AAIjC,UAAI;AACF,sBAAc,UAAU,cAAc,IAAI,GAAG;AAAA,MAC/C,SAAS,OAAO;AAGd,eAAO,SAAS,OAAO,GAAG;AAAA,MAC5B;AAEA,cAAQ,UAAU;AAAA,IACpB;AAAA,EACF;AAEA,WAAS,QAAQ,IAAI,OAAO;AAC1B,QAAI,aAAa,OAAO;AACxB,QAAI,eAAe,gBAAgB,IAAI,KAAK;AAE5C,aAAS,QAAQ;AACf,cAAQ,IAAI,KAAK;AAAA,IACnB;AAEA,QAAI,QAAQ,YAAY,cAAc,KAAK,GAAG;AAC5C,UAAI,yBAAyB,sBAAsB,cAAc,KAAK,GAClE,eAAe,uBAAuB,CAAC,GACvC,MAAM,uBAAuB,CAAC;AAGlC,oBAAc,aAAa,cAAc,IAAI,GAAG;AAChD,cAAQ,UAAU;AAAA,IACpB;AAAA,EACF;AAEA,WAAS,GAAG,OAAO;AACjB,kBAAc,GAAG,KAAK;AAAA,EACxB;AAEA,MAAI,UAAU;AAAA,IACZ,IAAI,SAAS;AACX,aAAO;AAAA,IACT;AAAA,IAEA,IAAI,WAAW;AACb,aAAO;AAAA,IACT;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM,SAAS,OAAO;AACpB,SAAG,EAAE;AAAA,IACP;AAAA,IACA,SAAS,SAAS,UAAU;AAC1B,SAAG,CAAC;AAAA,IACN;AAAA,IACA,QAAQ,SAAS,OAAO,UAAU;AAChC,aAAO,UAAU,KAAK,QAAQ;AAAA,IAChC;AAAA,IACA,OAAO,SAAS,MAAM,SAAS;AAC7B,UAAI,UAAU,SAAS,KAAK,OAAO;AAEnC,UAAI,SAAS,WAAW,GAAG;AACzB,eAAO,iBAAiB,uBAAuB,kBAAkB;AAAA,MACnE;AAEA,aAAO,WAAY;AACjB,gBAAQ;AAIR,YAAI,CAAC,SAAS,QAAQ;AACpB,iBAAO,oBAAoB,uBAAuB,kBAAkB;AAAA,QACtE;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAUA,SAAS,kBAAkB,SAAS;AAClC,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AAEA,MAAI,YAAY,SACZ,mBAAmB,UAAU,QAC7B,SAAS,qBAAqB,SAAS,SAAS,cAAc;AAClE,MAAI,gBAAgB,OAAO;AAE3B,WAAS,sBAAsB;AAC7B,QAAI,aAAa,UAAU,OAAO,SAAS,KAAK,OAAO,CAAC,CAAC,GACrD,sBAAsB,WAAW,UACjC,WAAW,wBAAwB,SAAS,MAAM,qBAClD,oBAAoB,WAAW,QAC/B,SAAS,sBAAsB,SAAS,KAAK,mBAC7C,kBAAkB,WAAW,MAC7B,OAAO,oBAAoB,SAAS,KAAK;AAE7C,QAAI,QAAQ,cAAc,SAAS,CAAC;AACpC,WAAO,CAAC,MAAM,KAAK,SAAS;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAO,MAAM,OAAO;AAAA,MACpB,KAAK,MAAM,OAAO;AAAA,IACpB,CAAC,CAAC;AAAA,EACJ;AAEA,MAAI,eAAe;AAEnB,WAAS,YAAY;AACnB,QAAI,cAAc;AAChB,eAAS,KAAK,YAAY;AAC1B,qBAAe;AAAA,IACjB,OAAO;AACL,UAAI,aAAa,OAAO;AAExB,UAAI,wBAAwB,oBAAoB,GAC5C,YAAY,sBAAsB,CAAC,GACnC,eAAe,sBAAsB,CAAC;AAE1C,UAAI,SAAS,QAAQ;AACnB,YAAI,aAAa,MAAM;AACrB,cAAI,QAAQ,QAAQ;AAEpB,cAAI,OAAO;AAET,2BAAe;AAAA,cACb,QAAQ;AAAA,cACR,UAAU;AAAA,cACV,OAAO,SAAS,QAAQ;AACtB,mBAAG,QAAQ,EAAE;AAAA,cACf;AAAA,YACF;AACA,eAAG,KAAK;AAAA,UACV;AAAA,QACF,OAAO;AAGL,iBAAwC;AAAA,YAAQ;AAAA;AAAA;AAAA;AAAA,YAGhD;AAAA,UAAwT,IAAI;AAAA,QAC9T;AAAA,MACF,OAAO;AACL,gBAAQ,UAAU;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AAEA,SAAO,iBAAiB,mBAAmB,SAAS;AAGpD,SAAO,iBAAiB,qBAAqB,WAAY;AACvD,QAAI,wBAAwB,oBAAoB,GAC5C,eAAe,sBAAsB,CAAC;AAG1C,QAAI,WAAW,YAAY,MAAM,WAAW,QAAQ,GAAG;AACrD,gBAAU;AAAA,IACZ;AAAA,EACF,CAAC;AACD,MAAI,SAAS,OAAO;AAEpB,MAAI,wBAAwB,oBAAoB,GAC5C,QAAQ,sBAAsB,CAAC,GAC/B,WAAW,sBAAsB,CAAC;AAEtC,MAAI,YAAY,aAAa;AAC7B,MAAI,WAAW,aAAa;AAE5B,MAAI,SAAS,MAAM;AACjB,YAAQ;AACR,kBAAc,aAAa,SAAS,CAAC,GAAG,cAAc,OAAO;AAAA,MAC3D,KAAK;AAAA,IACP,CAAC,GAAG,EAAE;AAAA,EACR;AAEA,WAAS,cAAc;AACrB,QAAI,OAAO,SAAS,cAAc,MAAM;AACxC,QAAI,OAAO;AAEX,QAAI,QAAQ,KAAK,aAAa,MAAM,GAAG;AACrC,UAAI,MAAM,OAAO,SAAS;AAC1B,UAAI,YAAY,IAAI,QAAQ,GAAG;AAC/B,aAAO,cAAc,KAAK,MAAM,IAAI,MAAM,GAAG,SAAS;AAAA,IACxD;AAEA,WAAO;AAAA,EACT;AAEA,WAAS,WAAW,IAAI;AACtB,WAAO,YAAY,IAAI,OAAO,OAAO,OAAO,WAAW,KAAK,WAAW,EAAE;AAAA,EAC3E;AAEA,WAAS,gBAAgB,IAAI,OAAO;AAClC,QAAI,UAAU,QAAQ;AACpB,cAAQ;AAAA,IACV;AAEA,WAAO,SAAS,SAAS;AAAA,MACvB,UAAU,SAAS;AAAA,MACnB,MAAM;AAAA,MACN,QAAQ;AAAA,IACV,GAAG,OAAO,OAAO,WAAW,UAAU,EAAE,IAAI,IAAI;AAAA,MAC9C;AAAA,MACA,KAAK,UAAU;AAAA,IACjB,CAAC,CAAC;AAAA,EACJ;AAEA,WAAS,sBAAsB,cAAcF,QAAO;AAClD,WAAO,CAAC;AAAA,MACN,KAAK,aAAa;AAAA,MAClB,KAAK,aAAa;AAAA,MAClB,KAAKA;AAAA,IACP,GAAG,WAAW,YAAY,CAAC;AAAA,EAC7B;AAEA,WAAS,QAAQC,SAAQC,WAAU,OAAO;AACxC,WAAO,CAAC,SAAS,WAAW,SAAS,KAAK;AAAA,MACxC,QAAQD;AAAA,MACR,UAAUC;AAAA,MACV;AAAA,IACF,CAAC,GAAG;AAAA,EACN;AAEA,WAAS,QAAQ,YAAY;AAC3B,aAAS;AAET,QAAI,wBAAwB,oBAAoB;AAEhD,YAAQ,sBAAsB,CAAC;AAC/B,eAAW,sBAAsB,CAAC;AAClC,cAAU,KAAK;AAAA,MACb;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAEA,WAAS,KAAK,IAAI,OAAO;AACvB,QAAI,aAAa,OAAO;AACxB,QAAI,eAAe,gBAAgB,IAAI,KAAK;AAE5C,aAAS,QAAQ;AACf,WAAK,IAAI,KAAK;AAAA,IAChB;AAEA,WAAwC,QAAQ,aAAa,SAAS,OAAO,CAAC,MAAM,KAAK,+DAA+D,KAAK,UAAU,EAAE,IAAI,GAAG,IAAI;AAEpL,QAAI,QAAQ,YAAY,cAAc,KAAK,GAAG;AAC5C,UAAI,yBAAyB,sBAAsB,cAAc,QAAQ,CAAC,GACtE,eAAe,uBAAuB,CAAC,GACvC,MAAM,uBAAuB,CAAC;AAIlC,UAAI;AACF,sBAAc,UAAU,cAAc,IAAI,GAAG;AAAA,MAC/C,SAAS,OAAO;AAGd,eAAO,SAAS,OAAO,GAAG;AAAA,MAC5B;AAEA,cAAQ,UAAU;AAAA,IACpB;AAAA,EACF;AAEA,WAAS,QAAQ,IAAI,OAAO;AAC1B,QAAI,aAAa,OAAO;AACxB,QAAI,eAAe,gBAAgB,IAAI,KAAK;AAE5C,aAAS,QAAQ;AACf,cAAQ,IAAI,KAAK;AAAA,IACnB;AAEA,WAAwC,QAAQ,aAAa,SAAS,OAAO,CAAC,MAAM,KAAK,kEAAkE,KAAK,UAAU,EAAE,IAAI,GAAG,IAAI;AAEvL,QAAI,QAAQ,YAAY,cAAc,KAAK,GAAG;AAC5C,UAAI,yBAAyB,sBAAsB,cAAc,KAAK,GAClE,eAAe,uBAAuB,CAAC,GACvC,MAAM,uBAAuB,CAAC;AAGlC,oBAAc,aAAa,cAAc,IAAI,GAAG;AAChD,cAAQ,UAAU;AAAA,IACpB;AAAA,EACF;AAEA,WAAS,GAAG,OAAO;AACjB,kBAAc,GAAG,KAAK;AAAA,EACxB;AAEA,MAAI,UAAU;AAAA,IACZ,IAAI,SAAS;AACX,aAAO;AAAA,IACT;AAAA,IAEA,IAAI,WAAW;AACb,aAAO;AAAA,IACT;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM,SAAS,OAAO;AACpB,SAAG,EAAE;AAAA,IACP;AAAA,IACA,SAAS,SAAS,UAAU;AAC1B,SAAG,CAAC;AAAA,IACN;AAAA,IACA,QAAQ,SAAS,OAAO,UAAU;AAChC,aAAO,UAAU,KAAK,QAAQ;AAAA,IAChC;AAAA,IACA,OAAO,SAAS,MAAM,SAAS;AAC7B,UAAI,UAAU,SAAS,KAAK,OAAO;AAEnC,UAAI,SAAS,WAAW,GAAG;AACzB,eAAO,iBAAiB,uBAAuB,kBAAkB;AAAA,MACnE;AAEA,aAAO,WAAY;AACjB,gBAAQ;AAIR,YAAI,CAAC,SAAS,QAAQ;AACpB,iBAAO,oBAAoB,uBAAuB,kBAAkB;AAAA,QACtE;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAQA,SAAS,oBAAoB,SAAS;AACpC,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AAEA,MAAI,YAAY,SACZ,wBAAwB,UAAU,gBAClC,iBAAiB,0BAA0B,SAAS,CAAC,GAAG,IAAI,uBAC5D,eAAe,UAAU;AAC7B,MAAI,UAAU,eAAe,IAAI,SAAU,OAAO;AAChD,QAAIA,YAAW,SAAS,SAAS;AAAA,MAC/B,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,MACP,KAAK,UAAU;AAAA,IACjB,GAAG,OAAO,UAAU,WAAW,UAAU,KAAK,IAAI,KAAK,CAAC;AACxD,WAAwC,QAAQA,UAAS,SAAS,OAAO,CAAC,MAAM,KAAK,qGAAqG,KAAK,UAAU,KAAK,IAAI,GAAG,IAAI;AACzN,WAAOA;AAAA,EACT,CAAC;AACD,MAAI,QAAQ,MAAM,gBAAgB,OAAO,QAAQ,SAAS,IAAI,cAAc,GAAG,QAAQ,SAAS,CAAC;AACjG,MAAI,SAAS,OAAO;AACpB,MAAI,WAAW,QAAQ,KAAK;AAC5B,MAAI,YAAY,aAAa;AAC7B,MAAI,WAAW,aAAa;AAE5B,WAAS,WAAW,IAAI;AACtB,WAAO,OAAO,OAAO,WAAW,KAAK,WAAW,EAAE;AAAA,EACpD;AAEA,WAAS,gBAAgB,IAAI,OAAO;AAClC,QAAI,UAAU,QAAQ;AACpB,cAAQ;AAAA,IACV;AAEA,WAAO,SAAS,SAAS;AAAA,MACvB,UAAU,SAAS;AAAA,MACnB,QAAQ;AAAA,MACR,MAAM;AAAA,IACR,GAAG,OAAO,OAAO,WAAW,UAAU,EAAE,IAAI,IAAI;AAAA,MAC9C;AAAA,MACA,KAAK,UAAU;AAAA,IACjB,CAAC,CAAC;AAAA,EACJ;AAEA,WAAS,QAAQD,SAAQC,WAAU,OAAO;AACxC,WAAO,CAAC,SAAS,WAAW,SAAS,KAAK;AAAA,MACxC,QAAQD;AAAA,MACR,UAAUC;AAAA,MACV;AAAA,IACF,CAAC,GAAG;AAAA,EACN;AAEA,WAAS,QAAQ,YAAY,cAAc;AACzC,aAAS;AACT,eAAW;AACX,cAAU,KAAK;AAAA,MACb;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAEA,WAAS,KAAK,IAAI,OAAO;AACvB,QAAI,aAAa,OAAO;AACxB,QAAI,eAAe,gBAAgB,IAAI,KAAK;AAE5C,aAAS,QAAQ;AACf,WAAK,IAAI,KAAK;AAAA,IAChB;AAEA,WAAwC,QAAQ,SAAS,SAAS,OAAO,CAAC,MAAM,KAAK,iEAAiE,KAAK,UAAU,EAAE,IAAI,GAAG,IAAI;AAElL,QAAI,QAAQ,YAAY,cAAc,KAAK,GAAG;AAC5C,eAAS;AACT,cAAQ,OAAO,OAAO,QAAQ,QAAQ,YAAY;AAClD,cAAQ,YAAY,YAAY;AAAA,IAClC;AAAA,EACF;AAEA,WAAS,QAAQ,IAAI,OAAO;AAC1B,QAAI,aAAa,OAAO;AACxB,QAAI,eAAe,gBAAgB,IAAI,KAAK;AAE5C,aAAS,QAAQ;AACf,cAAQ,IAAI,KAAK;AAAA,IACnB;AAEA,WAAwC,QAAQ,SAAS,SAAS,OAAO,CAAC,MAAM,KAAK,oEAAoE,KAAK,UAAU,EAAE,IAAI,GAAG,IAAI;AAErL,QAAI,QAAQ,YAAY,cAAc,KAAK,GAAG;AAC5C,cAAQ,KAAK,IAAI;AACjB,cAAQ,YAAY,YAAY;AAAA,IAClC;AAAA,EACF;AAEA,WAAS,GAAG,OAAO;AACjB,QAAI,YAAY,MAAM,QAAQ,OAAO,GAAG,QAAQ,SAAS,CAAC;AAC1D,QAAI,aAAa,OAAO;AACxB,QAAI,eAAe,QAAQ,SAAS;AAEpC,aAAS,QAAQ;AACf,SAAG,KAAK;AAAA,IACV;AAEA,QAAI,QAAQ,YAAY,cAAc,KAAK,GAAG;AAC5C,cAAQ;AACR,cAAQ,YAAY,YAAY;AAAA,IAClC;AAAA,EACF;AAEA,MAAI,UAAU;AAAA,IACZ,IAAI,QAAQ;AACV,aAAO;AAAA,IACT;AAAA,IAEA,IAAI,SAAS;AACX,aAAO;AAAA,IACT;AAAA,IAEA,IAAI,WAAW;AACb,aAAO;AAAA,IACT;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM,SAAS,OAAO;AACpB,SAAG,EAAE;AAAA,IACP;AAAA,IACA,SAAS,SAAS,UAAU;AAC1B,SAAG,CAAC;AAAA,IACN;AAAA,IACA,QAAQ,SAAS,OAAO,UAAU;AAChC,aAAO,UAAU,KAAK,QAAQ;AAAA,IAChC;AAAA,IACA,OAAO,SAAS,MAAM,SAAS;AAC7B,aAAO,SAAS,KAAK,OAAO;AAAA,IAC9B;AAAA,EACF;AACA,SAAO;AACT;AAIA,SAAS,MAAM,GAAG,YAAY,YAAY;AACxC,SAAO,KAAK,IAAI,KAAK,IAAI,GAAG,UAAU,GAAG,UAAU;AACrD;AAEA,SAAS,mBAAmB,OAAO;AAEjC,QAAM,eAAe;AAErB,QAAM,cAAc;AACtB;AAEA,SAAS,eAAe;AACtB,MAAI,WAAW,CAAC;AAChB,SAAO;AAAA,IACL,IAAI,SAAS;AACX,aAAO,SAAS;AAAA,IAClB;AAAA,IAEA,MAAM,SAAS,KAAK,IAAI;AACtB,eAAS,KAAK,EAAE;AAChB,aAAO,WAAY;AACjB,mBAAW,SAAS,OAAO,SAAU,SAAS;AAC5C,iBAAO,YAAY;AAAA,QACrB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,MAAM,SAAS,KAAK,KAAK;AACvB,eAAS,QAAQ,SAAU,IAAI;AAC7B,eAAO,MAAM,GAAG,GAAG;AAAA,MACrB,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAEA,SAAS,YAAY;AACnB,SAAO,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,OAAO,GAAG,CAAC;AAC/C;AAQA,SAAS,WAAW,MAAM;AACxB,MAAI,gBAAgB,KAAK,UACrB,WAAW,kBAAkB,SAAS,MAAM,eAC5C,cAAc,KAAK,QACnB,SAAS,gBAAgB,SAAS,KAAK,aACvC,YAAY,KAAK,MACjB,OAAO,cAAc,SAAS,KAAK;AACvC,MAAI,UAAU,WAAW;AAAK,gBAAY,OAAO,OAAO,CAAC,MAAM,MAAM,SAAS,MAAM;AACpF,MAAI,QAAQ,SAAS;AAAK,gBAAY,KAAK,OAAO,CAAC,MAAM,MAAM,OAAO,MAAM;AAC5E,SAAO;AACT;AAOA,SAAS,UAAU,MAAM;AACvB,MAAI,aAAa,CAAC;AAElB,MAAI,MAAM;AACR,QAAI,YAAY,KAAK,QAAQ,GAAG;AAEhC,QAAI,aAAa,GAAG;AAClB,iBAAW,OAAO,KAAK,OAAO,SAAS;AACvC,aAAO,KAAK,OAAO,GAAG,SAAS;AAAA,IACjC;AAEA,QAAI,cAAc,KAAK,QAAQ,GAAG;AAElC,QAAI,eAAe,GAAG;AACpB,iBAAW,SAAS,KAAK,OAAO,WAAW;AAC3C,aAAO,KAAK,OAAO,GAAG,WAAW;AAAA,IACnC;AAEA,QAAI,MAAM;AACR,iBAAW,WAAW;AAAA,IACxB;AAAA,EACF;AAEA,SAAO;AACT;;;;IClwBaC,wBAAoBC,4BAC/B,IAD+B;AAIjC,IAAA,MAAa;AACXD,oBAAkBE,cAAc;AACjC;IAOYC,sBAAkBF,4BAC7B,IAD6B;AAI/B,IAAA,MAAa;AACXE,kBAAgBD,cAAc;AAC/B;IAOYE,mBAAeH,4BAAwC;EAClEI,QAAQ;EACRC,SAAS,CAAA;AAFyD,CAAxC;AAK5B,IAAA,MAAa;AACXF,eAAaF,cAAc;AAC5B;ACrDM,SAASK,UAAUC,MAAWC,SAA+B;AAClE,MAAI,CAACD;AAAM,UAAM,IAAIE,MAAMD,OAAV;AAClB;AAEM,SAASE,SAAQH,MAAWC,SAAuB;AACxD,MAAI,CAACD,MAAM;AAET,QAAI,OAAOI,YAAY;AAAaA,cAAQC,KAAKJ,OAAb;AAEpC,QAAI;AAMF,YAAM,IAAIC,MAAMD,OAAV;IAEP,SAAQK,GAAG;IAAA;EACb;AACF;AAED,IAAMC,gBAAyC,CAAA;AACxC,SAASC,YAAYC,KAAaT,MAAeC,SAAiB;AACvE,MAAI,CAACD,QAAQ,CAACO,cAAcE,GAAD,GAAO;AAChCF,kBAAcE,GAAD,IAAQ;AACrB,WAAAN,SAAQ,OAAOF,OAAR,IAAP;EACD;AACF;AAwEM,SAASS,aAAaC,MAAcC,QAA6B;AAAA,MAA7BA,WAA6B,QAAA;AAA7BA,aAAiB,CAAA;EAAY;AACtE,SAAOD,KACJE,QAAQ,WAAW,CAACC,GAAGL,QAAQ;AAC9B,MAAUG,OAAOH,GAAD,KAAS,QAAzB,OAAAV,UAAS,OAAA,eAAmCU,MAAnC,SAAA,IAATV,UAAS,KAAA,IAAT;AACA,WAAOa,OAAOH,GAAD;EACd,CAJI,EAKJI,QAAQ,UAAWC,OAClBF,OAAO,GAAD,KAAS,OAAO,KAAKA,OAAO,GAAD,EAAMC,QAAQ,QAAQ,GAA5B,CANxB;AAQR;AA6BM,SAASE,YACdC,QACAC,aACAC,UACqB;AAAA,MADrBA,aACqB,QAAA;AADrBA,eAAW;EACU;AACrB,MAAIC,WACF,OAAOF,gBAAgB,WAAWG,UAAUH,WAAD,IAAgBA;AAE7D,MAAII,WAAWC,cAAcH,SAASE,YAAY,KAAKH,QAA3B;AAE5B,MAAIG,YAAY,MAAM;AACpB,WAAO;EACR;AAED,MAAIE,WAAWC,cAAcR,MAAD;AAC5BS,oBAAkBF,QAAD;AAEjB,MAAIzB,UAAU;AACd,WAAS4B,IAAI,GAAG5B,WAAW,QAAQ4B,IAAIH,SAASI,QAAQ,EAAED,GAAG;AAC3D5B,cAAU8B,iBAAiBL,SAASG,CAAD,GAAKL,QAAd;EAC3B;AAED,SAAOvB;AACR;AAeD,SAAS0B,cACPR,QACAO,UACAM,aACAC,YACe;AAAA,MAHfP,aAGe,QAAA;AAHfA,eAA0B,CAAA;EAGX;AAAA,MAFfM,gBAEe,QAAA;AAFfA,kBAA2B,CAAA;EAEZ;AAAA,MADfC,eACe,QAAA;AADfA,iBAAa;EACE;AACfd,SAAOe,QAAQ,CAACC,OAAOC,UAAU;AAC/B,QAAIC,OAAkB;MACpBC,cAAcH,MAAMrB,QAAQ;MAC5ByB,eAAeJ,MAAMI,kBAAkB;MACvCC,eAAeJ;MACfD;IAJoB;AAOtB,QAAIE,KAAKC,aAAaG,WAAW,GAA7B,GAAmC;AACrC,OACEJ,KAAKC,aAAaG,WAAWR,UAA7B,IADF,OAAA/B,UAAS,OAEP,0BAAwBmC,KAAKC,eAA7B,0BAAA,MACML,aADN,mDAAA,6DAFO,IAAT/B,UAAS,KAAA,IAAT;AAOAmC,WAAKC,eAAeD,KAAKC,aAAaI,MAAMT,WAAWH,MAAnC;IACrB;AAED,QAAIhB,OAAO6B,UAAU,CAACV,YAAYI,KAAKC,YAAlB,CAAD;AACpB,QAAIM,aAAaZ,YAAYa,OAAOR,IAAnB;AAKjB,QAAIF,MAAMW,YAAYX,MAAMW,SAAShB,SAAS,GAAG;AAC/C,QACEK,MAAMC,UAAU,QADlB,OAAAlC,UAAS,OAEP,6DAAA,uCACuCY,OADvC,KAFO,IAATZ,UAAS,KAAA,IAAT;AAMAyB,oBAAcQ,MAAMW,UAAUpB,UAAUkB,YAAY9B,IAAvC;IACd;AAID,QAAIqB,MAAMrB,QAAQ,QAAQ,CAACqB,MAAMC,OAAO;AACtC;IACD;AAEDV,aAASqB,KAAK;MAAEjC;MAAMkC,OAAOC,aAAanC,MAAMqB,MAAMC,KAAb;MAAqBQ;IAAhD,CAAd;EACD,CA1CD;AA4CA,SAAOlB;AACR;AAED,SAASE,kBAAkBF,UAA+B;AACxDA,WAASwB,KAAK,CAACC,GAAGC,MAChBD,EAAEH,UAAUI,EAAEJ,QACVI,EAAEJ,QAAQG,EAAEH,QACZK,eACEF,EAAEP,WAAWU,IAAKjB,UAASA,KAAKG,aAAhC,GACAY,EAAER,WAAWU,IAAKjB,UAASA,KAAKG,aAAhC,CAFY,CAHpB;AAQD;AAED,IAAMe,UAAU;AAChB,IAAMC,sBAAsB;AAC5B,IAAMC,kBAAkB;AACxB,IAAMC,oBAAoB;AAC1B,IAAMC,qBAAqB;AAC3B,IAAMC,eAAe;AACrB,IAAMC,UAAWC,OAAcA,MAAM;AAErC,SAASb,aAAanC,MAAcsB,OAAoC;AACtE,MAAI2B,WAAWjD,KAAKkD,MAAM,GAAX;AACf,MAAIC,eAAeF,SAASjC;AAC5B,MAAIiC,SAASG,KAAKL,OAAd,GAAwB;AAC1BI,oBAAgBL;EACjB;AAED,MAAIxB,OAAO;AACT6B,oBAAgBR;EACjB;AAED,SAAOM,SACJI,OAAQL,OAAM,CAACD,QAAQC,CAAD,CADlB,EAEJM,OACC,CAACpB,OAAOqB,YACNrB,SACCO,QAAQe,KAAKD,OAAb,IACGb,sBACAa,YAAY,KACZX,oBACAC,qBACNM,YAVG;AAYR;AAED,SAASZ,eAAeF,GAAaC,GAAqB;AACxD,MAAImB,WACFpB,EAAErB,WAAWsB,EAAEtB,UAAUqB,EAAET,MAAM,GAAG,EAAX,EAAe8B,MAAM,CAACC,GAAG5C,MAAM4C,MAAMrB,EAAEvB,CAAD,CAAtC;AAE3B,SAAO0C;;;;;IAKHpB,EAAEA,EAAErB,SAAS,CAAZ,IAAiBsB,EAAEA,EAAEtB,SAAS,CAAZ;;;;IAGnB;;AACL;AAED,SAASC,iBACP2C,QACAlD,UAC+B;AAC/B,MAAI;IAAEoB;EAAF,IAAiB8B;AAErB,MAAIC,gBAAgB,CAAA;AACpB,MAAIC,kBAAkB;AACtB,MAAI3E,UAAwB,CAAA;AAC5B,WAAS4B,IAAI,GAAGA,IAAIe,WAAWd,QAAQ,EAAED,GAAG;AAC1C,QAAIQ,OAAOO,WAAWf,CAAD;AACrB,QAAIgD,MAAMhD,MAAMe,WAAWd,SAAS;AACpC,QAAIgD,oBACFF,oBAAoB,MAChBpD,WACAA,SAASkB,MAAMkC,gBAAgB9C,MAA/B,KAA0C;AAChD,QAAIiD,QAAQC,UACV;MAAElE,MAAMuB,KAAKC;MAAcC,eAAeF,KAAKE;MAAesC;IAA9D,GACAC,iBAFmB;AAKrB,QAAI,CAACC;AAAO,aAAO;AAEnBE,WAAOC,OAAOP,eAAeI,MAAMhE,MAAnC;AAEA,QAAIoB,QAAQE,KAAKF;AAEjBlC,YAAQ8C,KAAK;MACXhC,QAAQ4D;MACRnD,UAAUmB,UAAU,CAACiC,iBAAiBG,MAAMvD,QAAxB,CAAD;MACnB2D,cAAcC,kBACZzC,UAAU,CAACiC,iBAAiBG,MAAMI,YAAxB,CAAD,CADoB;MAG/BhD;IANW,CAAb;AASA,QAAI4C,MAAMI,iBAAiB,KAAK;AAC9BP,wBAAkBjC,UAAU,CAACiC,iBAAiBG,MAAMI,YAAxB,CAAD;IAC5B;EACF;AAED,SAAOlF;AACR;AAuDM,SAAS+E,UAIdK,SACA7D,UAC4B;AAC5B,MAAI,OAAO6D,YAAY,UAAU;AAC/BA,cAAU;MAAEvE,MAAMuE;MAAS9C,eAAe;MAAOsC,KAAK;IAA5C;EACX;AAED,MAAI,CAACS,SAASC,UAAV,IAAwBC,YAC1BH,QAAQvE,MACRuE,QAAQ9C,eACR8C,QAAQR,GAH6B;AAMvC,MAAIE,QAAQvD,SAASuD,MAAMO,OAAf;AACZ,MAAI,CAACP;AAAO,WAAO;AAEnB,MAAIH,kBAAkBG,MAAM,CAAD;AAC3B,MAAII,eAAeP,gBAAgB5D,QAAQ,WAAW,IAAnC;AACnB,MAAIyE,gBAAgBV,MAAMrC,MAAM,CAAZ;AACpB,MAAI3B,SAAiBwE,WAAWnB,OAC9B,CAACsB,MAAMC,WAAWvD,UAAU;AAG1B,QAAIuD,cAAc,KAAK;AACrB,UAAIC,aAAaH,cAAcrD,KAAD,KAAW;AACzC+C,qBAAeP,gBACZlC,MAAM,GAAGkC,gBAAgB9C,SAAS8D,WAAW9D,MADjC,EAEZd,QAAQ,WAAW,IAFP;IAGhB;AAED0E,SAAKC,SAAD,IAAcE,yBAChBJ,cAAcrD,KAAD,KAAW,IACxBuD,SAFwC;AAI1C,WAAOD;EACR,GACD,CAAA,CAjBmB;AAoBrB,SAAO;IACL3E;IACAS,UAAUoD;IACVO;IACAE;EAJK;AAMR;AAED,SAASG,YACP1E,MACAyB,eACAsC,KACoB;AAAA,MAFpBtC,kBAEoB,QAAA;AAFpBA,oBAAgB;EAEI;AAAA,MADpBsC,QACoB,QAAA;AADpBA,UAAM;EACc;AACpB,SAAAvE,SACEQ,SAAS,OAAO,CAACA,KAAKgF,SAAS,GAAd,KAAsBhF,KAAKgF,SAAS,IAAd,GACvC,iBAAehF,OAAf,sCAAA,MACMA,KAAKE,QAAQ,OAAO,IAApB,IADN,uCAAA,sEAAA,sCAGsCF,KAAKE,QAAQ,OAAO,IAApB,IAHtC,KAFK,IAAP;AAQA,MAAIuE,aAAuB,CAAA;AAC3B,MAAIQ,eACF,MACAjF,KACGE,QAAQ,WAAW,EADtB,EAEGA,QAAQ,QAAQ,GAFnB,EAGGA,QAAQ,uBAAuB,MAHlC,EAIGA,QAAQ,WAAW,CAACC,GAAW0E,cAAsB;AACpDJ,eAAWxC,KAAK4C,SAAhB;AACA,WAAO;EACR,CAPH;AASF,MAAI7E,KAAKgF,SAAS,GAAd,GAAoB;AACtBP,eAAWxC,KAAK,GAAhB;AACAgD,oBACEjF,SAAS,OAAOA,SAAS,OACrB,UACA;EACP,OAAM;AACLiF,oBAAgBlB,MACZ;;;;;;;MAOA;;EACL;AAED,MAAIS,UAAU,IAAIU,OAAOD,cAAcxD,gBAAgB0D,SAAY,GAArD;AAEd,SAAO,CAACX,SAASC,UAAV;AACR;AAED,SAASM,yBAAyBK,OAAeP,WAAmB;AAClE,MAAI;AACF,WAAOQ,mBAAmBD,KAAD;EAC1B,SAAQE,OAAO;AACd,WAAA9F,SACE,OACA,kCAAgCqF,YAAhC,mCAAA,kBACkBO,QADlB,qDAAA,qCAEqCE,QAFrC,KAFK,IAAP;AAOA,WAAOF;EACR;AACF;AAOM,SAASG,YAAYC,IAAQC,cAA0B;AAAA,MAA1BA,iBAA0B,QAAA;AAA1BA,mBAAe;EAAW;AAC5D,MAAI;IACF/E,UAAUgF;IACVC,SAAS;IACTC,OAAO;EAHL,IAIA,OAAOJ,OAAO,WAAW/E,UAAU+E,EAAD,IAAOA;AAE7C,MAAI9E,WAAWgF,aACXA,WAAW/D,WAAW,GAAtB,IACE+D,aACAG,gBAAgBH,YAAYD,YAAb,IACjBA;AAEJ,SAAO;IACL/E;IACAiF,QAAQG,gBAAgBH,MAAD;IACvBC,MAAMG,cAAcH,IAAD;EAHd;AAKR;AAED,SAASC,gBAAgBrE,cAAsBiE,cAA8B;AAC3E,MAAIxC,WAAWwC,aAAavF,QAAQ,QAAQ,EAA7B,EAAiCgD,MAAM,GAAvC;AACf,MAAI8C,mBAAmBxE,aAAa0B,MAAM,GAAnB;AAEvB8C,mBAAiB5E,QAASmC,aAAY;AACpC,QAAIA,YAAY,MAAM;AAEpB,UAAIN,SAASjC,SAAS;AAAGiC,iBAASgD,IAAT;IAC1B,WAAU1C,YAAY,KAAK;AAC1BN,eAAShB,KAAKsB,OAAd;IACD;EACF,CAPD;AASA,SAAON,SAASjC,SAAS,IAAIiC,SAASiD,KAAK,GAAd,IAAqB;AACnD;AAEM,SAASC,UACdC,OACAC,gBACAC,kBACM;AACN,MAAId,KAAK,OAAOY,UAAU,WAAW3F,UAAU2F,KAAD,IAAUA;AACxD,MAAIV,aAAaU,UAAU,MAAMZ,GAAG9E,aAAa,KAAK,MAAM8E,GAAG9E;AAS/D,MAAI6F;AACJ,MAAIb,cAAc,MAAM;AACtBa,WAAOD;EACR,OAAM;AACL,QAAIE,qBAAqBH,eAAerF,SAAS;AAEjD,QAAI0E,WAAW/D,WAAW,IAAtB,GAA6B;AAC/B,UAAI8E,aAAaf,WAAWxC,MAAM,GAAjB;AAKjB,aAAOuD,WAAW,CAAD,MAAQ,MAAM;AAC7BA,mBAAWC,MAAX;AACAF,8BAAsB;MACvB;AAEDhB,SAAG9E,WAAW+F,WAAWP,KAAK,GAAhB;IACf;AAIDK,WAAOC,sBAAsB,IAAIH,eAAeG,kBAAD,IAAuB;EACvE;AAED,MAAIxG,OAAOuF,YAAYC,IAAIe,IAAL;AAGtB,MACEb,cACAA,eAAe,OACfA,WAAWV,SAAS,GAApB,KACA,CAAChF,KAAKU,SAASsE,SAAS,GAAvB,GACD;AACAhF,SAAKU,YAAY;EAClB;AAED,SAAOV;AACR;AAEM,SAAS2G,cAAcnB,IAA4B;AAExD,SAAOA,OAAO,MAAOA,GAAY9E,aAAa,KAC1C,MACA,OAAO8E,OAAO,WACd/E,UAAU+E,EAAD,EAAK9E,WACd8E,GAAG9E;AACR;AAEM,SAASC,cACdD,UACAH,UACe;AACf,MAAIA,aAAa;AAAK,WAAOG;AAE7B,MAAI,CAACA,SAASkG,YAAT,EAAuBjF,WAAWpB,SAASqG,YAAT,CAAlC,GAA2D;AAC9D,WAAO;EACR;AAED,MAAIC,WAAWnG,SAASoG,OAAOvG,SAASS,MAAzB;AACf,MAAI6F,YAAYA,aAAa,KAAK;AAEhC,WAAO;EACR;AAED,SAAOnG,SAASkB,MAAMrB,SAASS,MAAxB,KAAmC;AAC3C;AAEM,IAAMa,YAAakF,WACxBA,MAAMb,KAAK,GAAX,EAAgBhG,QAAQ,UAAU,GAAlC;AAEK,IAAMoE,oBAAqB5D,cAChCA,SAASR,QAAQ,QAAQ,EAAzB,EAA6BA,QAAQ,QAAQ,GAA7C;AAEF,IAAM4F,kBAAmBH,YACvB,CAACA,UAAUA,WAAW,MAClB,KACAA,OAAOhE,WAAW,GAAlB,IACAgE,SACA,MAAMA;AAEZ,IAAMI,gBAAiBH,UACrB,CAACA,QAAQA,SAAS,MAAM,KAAKA,KAAKjE,WAAW,GAAhB,IAAuBiE,OAAO,MAAMA;ACjmB5D,SAASoB,QAAQxB,IAAgB;AACtC,GACEyB,mBAAkB,IADpB,OAAA7H;IAAS;;;IAAA;EAAA,IAATA,UAAS,KAAA,IAAT;AAOA,MAAI;IAAEmB;IAAU2G;EAAZ,QAA0BpI,yBAAiBD,iBAAjB;AAC9B,MAAI;IAAE+G;IAAMlF;IAAUiF;EAAlB,IAA6BwB,gBAAgB3B,EAAD;AAEhD,MAAI4B,iBAAiB1G;AACrB,MAAIH,aAAa,KAAK;AACpB,QAAImF,aAAaiB,cAAcnB,EAAD;AAC9B,QAAI6B,gBAAgB3B,cAAc,QAAQA,WAAWV,SAAS,GAApB;AAC1CoC,qBACE1G,aAAa,MACTH,YAAY8G,gBAAgB,MAAM,MAClCxF,UAAU,CAACtB,UAAUG,QAAX,CAAD;EAChB;AAED,SAAOwG,UAAUI,WAAW;IAAE5G,UAAU0G;IAAgBzB;IAAQC;EAApC,CAArB;AACR;AAOM,SAASqB,qBAA8B;AAC5C,aAAOnI,yBAAiBE,eAAjB,KAAqC;AAC7C;AAYM,SAASuI,cAAwB;AACtC,GACEN,mBAAkB,IADpB,OAAA7H;IAAS;;;IAAA;EAAA,IAATA,UAAS,KAAA,IAAT;AAOA,aAAON,yBAAiBE,eAAjB,EAAkCwB;AAC1C;AAQM,SAASgH,oBAAoC;AAClD,aAAO1I,yBAAiBE,eAAjB,EAAkCyI;AAC1C;AASM,SAASC,SAGdnD,SAA+D;AAC/D,GACE0C,mBAAkB,IADpB,OAAA7H;IAAS;;;IAAA;EAAA,IAATA,UAAS,KAAA,IAAT;AAOA,MAAI;IAAEsB;EAAF,IAAe6G,YAAW;AAC9B,aAAOzI,sBACL,MAAMoF,UAA0BK,SAAS7D,QAA1B,GACf,CAACA,UAAU6D,OAAX,CAFK;AAIR;AAqBM,SAASoD,cAAgC;AAC9C,GACEV,mBAAkB,IADpB,OAAA7H;IAAS;;;IAAA;EAAA,IAATA,UAAS,KAAA,IAAT;AAOA,MAAI;IAAEmB;IAAU2G;EAAZ,QAA0BpI,yBAAiBD,iBAAjB;AAC9B,MAAI;IAAEM;EAAF,QAAcL,yBAAiBG,YAAjB;AAClB,MAAI;IAAEyB,UAAU4F;EAAZ,IAAiCiB,YAAW;AAEhD,MAAIK,qBAAqBC,KAAKC,UAC5B3I,QAAQqD,IAAKyB,WAAUA,MAAMI,YAA7B,CADuB;AAIzB,MAAI0D,gBAAYjJ,qBAAa,KAAb;AAChBA,8BAAgB,MAAM;AACpBiJ,cAAUC,UAAU;EACrB,CAFD;AAIA,MAAIC,eAA6BnJ,0BAC/B,SAAC0G,IAAiB0C,SAAkC;AAAA,QAAlCA,YAAkC,QAAA;AAAlCA,gBAA2B,CAAA;IAAO;AAClD,WAAA1I,SACEuI,UAAUC,SACV,+FAFK,IAAP;AAMA,QAAI,CAACD,UAAUC;AAAS;AAExB,QAAI,OAAOxC,OAAO,UAAU;AAC1B0B,gBAAUiB,GAAG3C,EAAb;AACA;IACD;AAED,QAAIxF,OAAOmG,UACTX,IACAqC,KAAKO,MAAMR,kBAAX,GACAtB,gBAHkB;AAMpB,QAAI/F,aAAa,KAAK;AACpBP,WAAKU,WAAWmB,UAAU,CAACtB,UAAUP,KAAKU,QAAhB,CAAD;IAC1B;AAED,KAAC,CAAC,CAACwH,QAAQhI,UAAUgH,UAAUhH,UAAUgH,UAAUjF,MACjDjC,MACAkI,QAAQG,KAFV;EAID,GACD,CAAC9H,UAAU2G,WAAWU,oBAAoBtB,gBAA1C,CA9B+B;AAiCjC,SAAO2B;AACR;AAED,IAAMK,oBAAgBxJ,4BAA6B,IAA7B;AAOf,SAASyJ,mBAA+C;AAC7D,aAAOzJ,yBAAiBwJ,aAAjB;AACR;AAQM,SAASE,UAAUC,SAA8C;AACtE,MAAIvJ,aAASJ,yBAAiBG,YAAjB,EAA+BC;AAC5C,MAAIA,QAAQ;AACV,eACEwJ,4BAAC,cAAc,UAAf;MAAwB,OAAOD;IAA/B,GAAyCvJ,MAAzC;EAEH;AACD,SAAOA;AACR;AAQM,SAASyJ,YAId;AACA,MAAI;IAAExJ;EAAF,QAAcL,yBAAiBG,YAAjB;AAClB,MAAI2J,aAAazJ,QAAQA,QAAQ6B,SAAS,CAAlB;AACxB,SAAO4H,aAAcA,WAAW3I,SAAiB,CAAA;AAClD;AAOM,SAASkH,gBAAgB3B,IAAc;AAC5C,MAAI;IAAErG;EAAF,QAAcL,yBAAiBG,YAAjB;AAClB,MAAI;IAAEyB,UAAU4F;EAAZ,IAAiCiB,YAAW;AAEhD,MAAIK,qBAAqBC,KAAKC,UAC5B3I,QAAQqD,IAAKyB,WAAUA,MAAMI,YAA7B,CADuB;AAIzB,aAAOvF,sBACL,MAAMqH,UAAUX,IAAIqC,KAAKO,MAAMR,kBAAX,GAAgCtB,gBAArC,GACf,CAACd,IAAIoC,oBAAoBtB,gBAAzB,CAFK;AAIR;AAUM,SAASuC,UACdxI,QACAC,aAC2B;AAC3B,GACE2G,mBAAkB,IADpB,OAAA7H;IAAS;;;IAAA;EAAA,IAATA,UAAS,KAAA,IAAT;AAOA,MAAI;IAAED,SAAS2J;EAAX,QAA6BhK,yBAAiBG,YAAjB;AACjC,MAAI2J,aAAaE,cAAcA,cAAc9H,SAAS,CAAxB;AAC9B,MAAI+H,eAAeH,aAAaA,WAAW3I,SAAS,CAAA;AACpD,MAAI+I,iBAAiBJ,aAAaA,WAAWlI,WAAW;AACxD,MAAIuI,qBAAqBL,aAAaA,WAAWvE,eAAe;AAChE,MAAI6E,cAAcN,cAAcA,WAAWvH;AAE3C,MAAA,MAAa;AAqBX,QAAIF,aAAc+H,eAAeA,YAAYlJ,QAAS;AACtDH,gBACEmJ,gBACA,CAACE,eAAe/H,WAAW6D,SAAS,GAApB,GAChB,oEAAA,MACMgE,iBADN,2BAC6C7H,aAD7C,kBAAA;;KAAA,2CAK2CA,aAL3C,oBAAA,YAMWA,eAAe,MAAM,MAASA,aAA9B,QANX,MAHS;EAWZ;AAED,MAAIgI,sBAAsB5B,YAAW;AAErC,MAAI/G;AACJ,MAAIF,aAAa;AAAA,QAAA;AACf,QAAI8I,oBACF,OAAO9I,gBAAgB,WAAWG,UAAUH,WAAD,IAAgBA;AAE7D,MACE2I,uBAAuB,SAAvB,wBACEG,kBAAkB1I,aADpB,OAAA,SACE,sBAA4BiB,WAAWsH,kBAAvC,MAFJ,OAAA7J,UAAS,OAGP,8KAAA,iEAEiE6J,qBAFjE,SAAA,mBAGmBG,kBAAkB1I,WAHrC,sCAHO,IAATtB,UAAS,KAAA,IAAT;AASAoB,eAAW4I;EACZ,OAAM;AACL5I,eAAW2I;EACZ;AAED,MAAIzI,WAAWF,SAASE,YAAY;AACpC,MAAIsD,oBACFiF,uBAAuB,MACnBvI,WACAA,SAASkB,MAAMqH,mBAAmBjI,MAAlC,KAA6C;AACnD,MAAI7B,UAAUiB,YAAYC,QAAQ;IAAEK,UAAUsD;EAAZ,CAAT;AAEzB,MAAA,MAAa;AACX,WAAAxE,SACE0J,eAAe/J,WAAW,MADrB,iCAE0BqB,SAASE,WAAWF,SAASmF,SAASnF,SAASoF,OAFzE,IAAA,IAAP;AAKA,WAAApG,SACEL,WAAW,QACTA,QAAQA,QAAQ6B,SAAS,CAAlB,EAAqBK,MAAMgI,YAAYlE,QAChD,qCAAmC3E,SAASE,WAAWF,SAASmF,SAASnF,SAASoF,OAAlF,gIAHK,IAAP;EAMD;AAED,SAAO0D,eACLnK,WACEA,QAAQqD,IAAKyB,WACXE,OAAOC,OAAO,CAAA,GAAIH,OAAO;IACvBhE,QAAQkE,OAAOC,OAAO,CAAA,GAAI2E,cAAc9E,MAAMhE,MAAtC;IACRS,UAAUmB,UAAU,CAACoH,oBAAoBhF,MAAMvD,QAA3B,CAAD;IACnB2D,cACEJ,MAAMI,iBAAiB,MACnB4E,qBACApH,UAAU,CAACoH,oBAAoBhF,MAAMI,YAA3B,CAAD;EANQ,CAAzB,CADF,GAUFyE,aAZmB;AActB;AAEM,SAASQ,eACdnK,SACA2J,eAC2B;AAAA,MAD3BA,kBAC2B,QAAA;AAD3BA,oBAA8B,CAAA;EACH;AAC3B,MAAI3J,WAAW;AAAM,WAAO;AAE5B,SAAOA,QAAQoK,YAAY,CAACrK,QAAQ+E,OAAO3C,UAAU;AACnD,eACEoH,4BAAC,aAAa,UAAd;MACE,UACEzE,MAAM5C,MAAMgI,YAAYlE,SAAYlB,MAAM5C,MAAMgI,UAAUnK;MAE5D,OAAO;QACLA;QACAC,SAAS2J,cAAc/G,OAAO5C,QAAQyC,MAAM,GAAGN,QAAQ,CAAzB,CAArB;MAFJ;IAJT,CAAA;EAUH,GAAE,IAZI;AAaR;AC5WM,SAASkI,aAAT,MAKmC;AAAA,MALb;IAC3BjJ;IACAyB;IACAyH;IACAC;EAJ2B,IAKa;AACxC,MAAIC,iBAAa7K,qBAAA;AACjB,MAAI6K,WAAW3B,WAAW,MAAM;AAC9B2B,eAAW3B,UAAU4B,oBAAoB;MAAEH;MAAgBC;IAAlB,CAAD;EACzC;AAED,MAAIG,UAAUF,WAAW3B;AACzB,MAAI,CAACK,OAAOyB,QAAR,QAAoBhL,uBAAe;IACrCiL,QAAQF,QAAQE;IAChBvJ,UAAUqJ,QAAQrJ;EAFmB,CAAf;AAKxB1B,oCAAsB,MAAM+K,QAAQG,OAAOF,QAAf,GAA0B,CAACD,OAAD,CAAtD;AAEA,aACEnB,4BAAC,QAAD;IACE;IACA;IACA,UAAUL,MAAM7H;IAChB,gBAAgB6H,MAAM0B;IACtB,WAAWF;EALb,CAAA;AAQH;AAiBM,SAASI,SAAT,OAA+D;AAAA,MAA7C;IAAEzE;IAAItF;IAASmI;EAAf,IAA6C;AACpE,GACEpB,mBAAkB,IADpB,OAAA7H;IAAS;;;IAAA;EAAA,IAATA,UAAS,KAAA,IAAT;AAOA,SAAAI,SACE,KAACV,yBAAiBD,iBAAjB,EAAoCqL,QACrC,uNAFK,IAAP;AAOA,MAAIjC,WAAWN,YAAW;AAC1B7I,8BAAgB,MAAM;AACpBmJ,aAASzC,IAAI;MAAEtF;MAASmI;IAAX,CAAL;EACT,CAFD;AAIA,SAAO;AACR;AAWM,SAAS8B,OAAOC,OAA+C;AACpE,SAAO5B,UAAU4B,MAAM3B,OAAP;AACjB;AAiCM,SAAS4B,MACdC,QAC2B;AAC3B,SAAAlL,UAAS,OAEP,sIAFO,IAATA,UAAS,KAAA;AAKV;AAoBM,SAASmL,OAAT,OAOoC;AAAA,MAPpB;IACrBhK,UAAUiK,eAAe;IACzBxI,WAAW;IACXxB,UAAUiK;IACVhD,iBAAiBiD,OAAeC;IAChCzD;IACAgD,QAAQU,aAAa;EANA,IAOoB;AACzC,GACE,CAAC3D,mBAAkB,IADrB,OAAA7H,UAAS,OAEP,wGAFO,IAATA,UAAS,KAAA,IAAT;AAMA,MAAImB,WAAW+D,kBAAkBkG,YAAD;AAChC,MAAIK,wBAAoB/L,sBACtB,OAAO;IAAEyB;IAAU2G;IAAWgD,QAAQU;EAA/B,IACP,CAACrK,UAAU2G,WAAW0D,UAAtB,CAFsB;AAKxB,MAAI,OAAOH,iBAAiB,UAAU;AACpCA,mBAAehK,UAAUgK,YAAD;EACzB;AAED,MAAI;IACF/J,WAAW;IACXiF,SAAS;IACTC,OAAO;IACPyC,QAAQ;IACRvI,MAAM;EALJ,IAMA2K;AAEJ,MAAIjK,eAAW1B,sBAAc,MAAM;AACjC,QAAIgM,mBAAmBnK,cAAcD,UAAUH,QAAX;AAEpC,QAAIuK,oBAAoB,MAAM;AAC5B,aAAO;IACR;AAED,WAAO;MACLpK,UAAUoK;MACVnF;MACAC;MACAyC;MACAvI;IALK;EAOR,GAAE,CAACS,UAAUG,UAAUiF,QAAQC,MAAMyC,OAAOvI,GAA1C,CAdY;AAgBf,SAAAN,SACEgB,YAAY,MACZ,uBAAqBD,WAArB,sCAAA,MACMG,WAAWiF,SAASC,OAD1B,2CAAA,kDAFK,IAAP;AAOA,MAAIpF,YAAY,MAAM;AACpB,WAAO;EACR;AAED,aACEkI,4BAAC,kBAAkB,UAAnB;IAA4B,OAAOmC;EAAnC,OACEnC,4BAAC,gBAAgB,UAAjB;IACE;IACA,OAAO;MAAElI;MAAUiH;IAAZ;EAFT,CAAA,CADF;AAOH;AAaM,SAASsD,OAAT,OAGoC;AAAA,MAHpB;IACrB/I;IACAxB;EAFqB,IAGoB;AACzC,SAAOqI,UAAUmC,yBAAyBhJ,QAAD,GAAYxB,QAArC;AACjB;AAaM,SAASwK,yBACdhJ,UACe;AACf,MAAI3B,SAAwB,CAAA;AAE5BvB,wBAAesC,QAAQY,UAAWqH,aAAY;AAC5C,QAAI,KAACvK,6BAAqBuK,OAArB,GAA+B;AAGlC;IACD;AAED,QAAIA,QAAQ4B,SAASnM,uBAAgB;AAEnCuB,aAAO4B,KAAKiJ,MACV7K,QACA2K,yBAAyB3B,QAAQe,MAAMpI,QAAf,CAF1B;AAIA;IACD;AAED,MACEqH,QAAQ4B,SAASZ,SADnB,OAAAjL,UAAS,OAAA,OAGL,OAAOiK,QAAQ4B,SAAS,WAAW5B,QAAQ4B,OAAO5B,QAAQ4B,KAAKE,QAH1D,wGAAA,IAAT/L,UAAS,KAAA,IAAT;AAOA,QAAIiC,QAAqB;MACvBI,eAAe4H,QAAQe,MAAM3I;MAC7B4H,SAASA,QAAQe,MAAMf;MACvB/H,OAAO+H,QAAQe,MAAM9I;MACrBtB,MAAMqJ,QAAQe,MAAMpK;IAJG;AAOzB,QAAIqJ,QAAQe,MAAMpI,UAAU;AAC1BX,YAAMW,WAAWgJ,yBAAyB3B,QAAQe,MAAMpI,QAAf;IAC1C;AAED3B,WAAO4B,KAAKZ,KAAZ;EACD,CAnCD;AAqCA,SAAOhB;AACR;AAKM,SAAS+K,cACdjM,SAC2B;AAC3B,SAAOmK,eAAenK,OAAD;AACtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7RD,SAASkM,SAAQC,MAAeC,SAAuB;AACrD,MAAI,CAACD,MAAM;AAET,QAAI,OAAOE,YAAY;AAAaA,cAAQC,KAAKF,OAAb;AAEpC,QAAI;AAMF,YAAM,IAAIG,MAAMH,OAAV;IAEP,SAAQI,GAAG;IAAA;EACb;AACF;AA8FM,SAASC,cAAT,MAIgB;AAAA,MAJO;IAC5BC;IACAC;IACAC;EAH4B,IAIP;AACrB,MAAIC,iBAAaC,sBAAA;AACjB,MAAID,WAAWE,WAAW,MAAM;AAC9BF,eAAWE,UAAUC,qBAAqB;MAAEJ;IAAF,CAAD;EAC1C;AAED,MAAIK,UAAUJ,WAAWE;AACzB,MAAI,CAACG,OAAOC,QAAR,QAAoBL,wBAAe;IACrCM,QAAQH,QAAQG;IAChBC,UAAUJ,QAAQI;EAFmB,CAAf;AAKxBP,qCAAsB,MAAMG,QAAQK,OAAOH,QAAf,GAA0B,CAACF,OAAD,CAAtD;AAEA,aACEM,6BAAC,QAAD;IACE;IACA;IACA,UAAUL,MAAMG;IAChB,gBAAgBH,MAAME;IACtB,WAAWH;EALb,CAAA;AAQH;AAYM,SAASO,WAAT,OAAqE;AAAA,MAAjD;IAAEd;IAAUC;IAAUC;EAAtB,IAAiD;AAC1E,MAAIC,iBAAaC,sBAAA;AACjB,MAAID,WAAWE,WAAW,MAAM;AAC9BF,eAAWE,UAAUU,kBAAkB;MAAEb;IAAF,CAAD;EACvC;AAED,MAAIK,UAAUJ,WAAWE;AACzB,MAAI,CAACG,OAAOC,QAAR,QAAoBL,wBAAe;IACrCM,QAAQH,QAAQG;IAChBC,UAAUJ,QAAQI;EAFmB,CAAf;AAKxBP,qCAAsB,MAAMG,QAAQK,OAAOH,QAAf,GAA0B,CAACF,OAAD,CAAtD;AAEA,aACEM,6BAAC,QAAD;IACE;IACA;IACA,UAAUL,MAAMG;IAChB,gBAAgBH,MAAME;IACtB,WAAWH;EALb,CAAA;AAQH;AAcD,SAASS,cAAT,OAA4E;AAAA,MAArD;IAAEhB;IAAUC;IAAUM;EAAtB,IAAqD;AAC1E,QAAM,CAACC,OAAOC,QAAR,QAAoBL,wBAAe;IACvCM,QAAQH,QAAQG;IAChBC,UAAUJ,QAAQI;EAFqB,CAAf;AAK1BP,qCAAsB,MAAMG,QAAQK,OAAOH,QAAf,GAA0B,CAACF,OAAD,CAAtD;AAEA,aACEM,6BAAC,QAAD;IACE;IACA;IACA,UAAUL,MAAMG;IAChB,gBAAgBH,MAAME;IACtB,WAAWH;EALb,CAAA;AAQH;AAED,IAAA,MAAa;AACXS,gBAAcC,cAAc;AAC7B;AAID,SAASC,gBAAgBC,OAAyB;AAChD,SAAO,CAAC,EAAEA,MAAMC,WAAWD,MAAME,UAAUF,MAAMG,WAAWH,MAAMI;AACnE;IAaYC,WAAOpB,0BAClB,SAASqB,YAAT,OAEEC,KACA;AAAA,MAFA;IAAEC;IAASC;IAAgBC,UAAU;IAAOrB;IAAOsB;IAAQC;EAA3D,IAEA,OAFkEC,OAElE,8BAAA,OAAA,SAAA;AACA,MAAIC,OAAOC,QAAQH,EAAD;AAClB,MAAII,kBAAkBC,oBAAoBL,IAAI;IAAEF;IAASrB;IAAOsB;EAAlB,CAAL;AACzC,WAASO,YACPlB,OACA;AACA,QAAIQ;AAASA,cAAQR,KAAD;AACpB,QAAI,CAACA,MAAMmB,oBAAoB,CAACV,gBAAgB;AAC9CO,sBAAgBhB,KAAD;IAChB;EACF;AAED;;QAEE,6BAAA,KAAAoB,UAAA,CAAA,GACMP,MADN;MAEE;MACA,SAASK;MACT;MACA;IALF,CAAA,CAAA;;AAQH,CA1BiB;AA6BpB,IAAA,MAAa;AACXb,OAAKP,cAAc;AACpB;IAkBYuB,cAAUpC,0BACrB,SAASqC,eAAT,OAWEf,KACA;AAAA,MAXA;IACE,gBAAgBgB,kBAAkB;IAClCC,gBAAgB;IAChBC,WAAWC,gBAAgB;IAC3BC,MAAM;IACNC,OAAOC;IACPjB;IACA9B;EAPF,IAWA,OAHK+B,OAGL,8BAAA,OAAA,UAAA;AACA,MAAIrB,WAAWsC,YAAW;AAC1B,MAAIC,OAAOC,gBAAgBpB,EAAD;AAE1B,MAAIqB,mBAAmBzC,SAAS0C;AAChC,MAAIC,aAAaJ,KAAKG;AACtB,MAAI,CAACV,eAAe;AAClBS,uBAAmBA,iBAAiBG,YAAjB;AACnBD,iBAAaA,WAAWC,YAAX;EACd;AAED,MAAIC,WACFJ,qBAAqBE,cACpB,CAACR,OACAM,iBAAiBK,WAAWH,UAA5B,KACAF,iBAAiBM,OAAOJ,WAAWK,MAAnC,MAA+C;AAEnD,MAAIC,cAAcJ,WAAWd,kBAAkBmB;AAE/C,MAAIjB;AACJ,MAAI,OAAOC,kBAAkB,YAAY;AACvCD,gBAAYC,cAAc;MAAEW;IAAF,CAAD;EAC1B,OAAM;AAMLZ,gBAAY,CAACC,eAAeW,WAAW,WAAW,IAAtC,EACTM,OAAOC,OADE,EAETC,KAAK,GAFI;EAGb;AAED,MAAIjB,QACF,OAAOC,cAAc,aAAaA,UAAU;IAAEQ;EAAF,CAAD,IAAiBR;AAE9D,aACEnC,6BAAC,MAAD0B,UAAA,CAAA,GACMP,MADN;IAEE,gBAAc4B;IACd;IACA;IACA;IACA;EANF,CAAA,GAQG,OAAO3D,aAAa,aAAaA,SAAS;IAAEuD;EAAF,CAAD,IAAiBvD,QAR7D;AAWH,CA7DoB;AAgEvB,IAAA,MAAa;AACXuC,UAAQvB,cAAc;AACvB;AAWM,SAASmB,oBACdL,IADK,OAW6C;AAAA,MATlD;IACED;IACAD,SAASoC;IACTzD;EAHF,IASkD,UAAA,SAD9C,CAAA,IAC8C;AAClD,MAAI0D,WAAWC,YAAW;AAC1B,MAAIxD,WAAWsC,YAAW;AAC1B,MAAIC,OAAOC,gBAAgBpB,EAAD;AAE1B,aAAO3B,2BACJe,WAA2C;AAC1C,QACEA,MAAMiD,WAAW;KAChB,CAACtC,UAAUA,WAAW;IACvB,CAACZ,gBAAgBC,KAAD,GAChB;AACAA,YAAMkD,eAAN;AAIA,UAAIxC,UACF,CAAC,CAACoC,eAAeK,WAAW3D,QAAD,MAAe2D,WAAWpB,IAAD;AAEtDgB,eAASnC,IAAI;QAAEF;QAASrB;MAAX,CAAL;IACT;EACF,GACD,CAACG,UAAUuD,UAAUhB,MAAMe,aAAazD,OAAOsB,QAAQC,EAAvD,CAjBK;AAmBR;AAMM,SAASwC,gBAAgBC,aAAmC;AACjE,SAAAhF,SACE,OAAOiF,oBAAoB,aAC3B,gcAFK,IAAP;AAYA,MAAIC,6BAAyBtE,sBAAauE,mBAAmBH,WAAD,CAA/B;AAE7B,MAAI7D,WAAWsC,YAAW;AAC1B,MAAI2B,mBAAexE,uBAAc,MAAM;AACrC,QAAIwE,gBAAeD,mBAAmBhE,SAASkE,MAAV;AAErC,aAASC,OAAOJ,uBAAuBrE,QAAQ0E,KAA/B,GAAuC;AACrD,UAAI,CAACH,cAAaI,IAAIF,GAAjB,GAAuB;AAC1BJ,+BAAuBrE,QAAQ4E,OAAOH,GAAtC,EAA2CI,QAASC,WAAU;AAC5DP,UAAAA,cAAaQ,OAAON,KAAKK,KAAzB;QACD,CAFD;MAGD;IACF;AAED,WAAOP;EACR,GAAE,CAACjE,SAASkE,MAAV,CAZgB;AAcnB,MAAIX,WAAWC,YAAW;AAC1B,MAAIkB,sBAAkBjF,2BACpB,CACEkF,UACAC,oBACG;AACHrB,aAAS,MAAMS,mBAAmBW,QAAD,GAAYC,eAArC;EACT,GACD,CAACrB,QAAD,CAPoB;AAUtB,SAAO,CAACU,cAAcS,eAAf;AACR;AA+BM,SAASV,mBACda,MACiB;AAAA,MADjBA,SACiB,QAAA;AADjBA,WAA4B;EACX;AACjB,SAAO,IAAIf,gBACT,OAAOe,SAAS,YAChBC,MAAMC,QAAQF,IAAd,KACAA,gBAAgBf,kBACZe,OACAG,OAAOZ,KAAKS,IAAZ,EAAkBI,OAAO,CAACC,MAAMf,QAAQ;AACtC,QAAIK,QAAQK,KAAKV,GAAD;AAChB,WAAOe,KAAKC,OACVL,MAAMC,QAAQP,KAAd,IAAuBA,MAAMY,IAAKC,OAAM,CAAClB,KAAKkB,CAAN,CAAjB,IAA6B,CAAC,CAAClB,KAAKK,KAAN,CAAD,CAD/C;EAGR,GAAE,CAAA,CALH,CALC;AAYR;", "names": ["Action", "index", "action", "location", "NavigationContext", "React", "displayName", "LocationContext", "RouteContext", "outlet", "matches", "invariant", "cond", "message", "Error", "warning", "console", "warn", "e", "alreadyWarned", "warningOnce", "key", "generatePath", "path", "params", "replace", "_", "matchRoutes", "routes", "locationArg", "basename", "location", "parsePath", "pathname", "stripBasename", "branches", "flattenRoutes", "rankRouteBranches", "i", "length", "matchRouteBranch", "parents<PERSON>eta", "parentPath", "for<PERSON>ach", "route", "index", "meta", "relativePath", "caseSensitive", "childrenIndex", "startsWith", "slice", "joinPaths", "routesMeta", "concat", "children", "push", "score", "computeScore", "sort", "a", "b", "compareIndexes", "map", "paramRe", "dynamicSegmentValue", "indexRouteValue", "emptySegmentValue", "staticSegmentValue", "splatPenalty", "isSplat", "s", "segments", "split", "initialScore", "some", "filter", "reduce", "segment", "test", "siblings", "every", "n", "branch", "matchedParams", "matchedPathname", "end", "remainingPathname", "match", "matchPath", "Object", "assign", "pathnameBase", "normalizePathname", "pattern", "matcher", "paramNames", "compilePath", "captureGroups", "memo", "paramName", "splatValue", "safelyDecodeURIComponent", "endsWith", "regexpSource", "RegExp", "undefined", "value", "decodeURIComponent", "error", "<PERSON><PERSON><PERSON>", "to", "fromPathname", "toPathname", "search", "hash", "resolvePathname", "normalizeSearch", "normalizeHash", "relativeSegments", "pop", "join", "resolveTo", "to<PERSON><PERSON>", "routePathnames", "locationPathname", "from", "routePathnameIndex", "toSegments", "shift", "getToPathname", "toLowerCase", "nextChar", "char<PERSON>t", "paths", "useHref", "useInRouterContext", "navigator", "useResolvedPath", "joinedPathname", "endsWithSlash", "createHref", "useLocation", "useNavigationType", "navigationType", "useMatch", "useNavigate", "routePathnamesJson", "JSON", "stringify", "activeRef", "current", "navigate", "options", "go", "parse", "state", "OutletContext", "useOutletContext", "useOutlet", "context", "React.createElement", "useParams", "routeMatch", "useRoutes", "parentMatches", "parentParams", "parentPathname", "parentPathnameBase", "parentRoute", "locationFromContext", "parsedLocationArg", "element", "_renderMatches", "reduceRight", "MemoryRouter", "initialEntries", "initialIndex", "historyRef", "createMemoryHistory", "history", "setState", "action", "listen", "Navigate", "static", "Outlet", "props", "Route", "_props", "Router", "basenameProp", "locationProp", "NavigationType", "Pop", "staticProp", "navigationContext", "trailingPathname", "Routes", "createRoutesFromChildren", "type", "apply", "name", "renderMatches", "warning", "cond", "message", "console", "warn", "Error", "e", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "basename", "children", "window", "historyRef", "React", "current", "createBrowserHistory", "history", "state", "setState", "action", "location", "listen", "React.createElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createHashHistory", "HistoryRouter", "displayName", "isModifiedEvent", "event", "metaKey", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "Link", "LinkWithRef", "ref", "onClick", "reloadDocument", "replace", "target", "to", "rest", "href", "useHref", "internalOnClick", "useLinkClickHandler", "handleClick", "defaultPrevented", "_extends", "NavLink", "NavLinkWithRef", "ariaCurrentProp", "caseSensitive", "className", "classNameProp", "end", "style", "styleProp", "useLocation", "path", "useResolvedPath", "locationPathname", "pathname", "toPathname", "toLowerCase", "isActive", "startsWith", "char<PERSON>t", "length", "aria<PERSON>urrent", "undefined", "filter", "Boolean", "join", "replaceProp", "navigate", "useNavigate", "button", "preventDefault", "createPath", "useSearchParams", "defaultInit", "URLSearchParams", "defaultSearchParamsRef", "createSearchParams", "searchParams", "search", "key", "keys", "has", "getAll", "for<PERSON>ach", "value", "append", "setSearchParams", "nextInit", "navigateOptions", "init", "Array", "isArray", "Object", "reduce", "memo", "concat", "map", "v"]}