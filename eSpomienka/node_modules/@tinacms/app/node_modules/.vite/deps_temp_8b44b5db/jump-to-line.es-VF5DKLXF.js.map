{"version": 3, "sources": ["../../../../../node_modules/codemirror/addon/search/jump-to-line.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/LICENSE\n\n// Defines jumpToLine command. Uses dialog.js if present.\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"), require(\"../dialog/dialog\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\", \"../dialog/dialog\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n\n  // default search panel location\n  CodeMirror.defineOption(\"search\", {bottom: false});\n\n  function dialog(cm, text, shortText, deflt, f) {\n    if (cm.openDialog) cm.openDialog(text, f, {value: deflt, selectValueOnOpen: true, bottom: cm.options.search.bottom});\n    else f(prompt(shortTex<PERSON>, deflt));\n  }\n\n  function getJumpDialog(cm) {\n    return cm.phrase(\"Jump to line:\") + ' <input type=\"text\" style=\"width: 10em\" class=\"CodeMirror-search-field\"/> <span style=\"color: #888\" class=\"CodeMirror-search-hint\">' + cm.phrase(\"(Use line:column or scroll% syntax)\") + '</span>';\n  }\n\n  function interpretLine(cm, string) {\n    var num = Number(string)\n    if (/^[-+]/.test(string)) return cm.getCursor().line + num\n    else return num - 1\n  }\n\n  CodeMirror.commands.jumpToLine = function(cm) {\n    var cur = cm.getCursor();\n    dialog(cm, getJumpDialog(cm), cm.phrase(\"Jump to line:\"), (cur.line + 1) + \":\" + cur.ch, function(posStr) {\n      if (!posStr) return;\n\n      var match;\n      if (match = /^\\s*([\\+\\-]?\\d+)\\s*\\:\\s*(\\d+)\\s*$/.exec(posStr)) {\n        cm.setCursor(interpretLine(cm, match[1]), Number(match[2]))\n      } else if (match = /^\\s*([\\+\\-]?\\d+(\\.\\d+)?)\\%\\s*/.exec(posStr)) {\n        var line = Math.round(cm.lineCount() * Number(match[1]) / 100);\n        if (/^[-+]/.test(match[1])) line = cur.line + line + 1;\n        cm.setCursor(line - 1, cur.ch);\n      } else if (match = /^\\s*\\:?\\s*([\\+\\-]?\\d+)\\s*/.exec(posStr)) {\n        cm.setCursor(interpretLine(cm, match[1]), cur.ch);\n      }\n    });\n  };\n\n  CodeMirror.keyMap[\"default\"][\"Alt-G\"] = \"jumpToLine\";\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,GAAC,SAASA,GAAK;AAEXA,MAAIC,GAAA,GAAiCC,EAAA,CAA2B;EAKnE,GAAE,SAASC,GAAY;AAItBA,MAAW,aAAa,UAAU,EAAC,QAAQ,MAAK,CAAC;AAEjD,aAASC,EAAOC,GAAIC,GAAMC,GAAWC,GAAOC,GAAG;AACzCJ,QAAG,aAAYA,EAAG,WAAWC,GAAMG,GAAG,EAAC,OAAOD,GAAO,mBAAmB,MAAM,QAAQH,EAAG,QAAQ,OAAO,OAAM,CAAC,IAC9GI,EAAE,OAAOF,GAAWC,CAAK,CAAC;IAChC;AAHQE,MAAAN,GAAA,QAAA;AAKT,aAASO,EAAcN,GAAI;AACzB,aAAOA,EAAG,OAAO,eAAe,IAAI,wIAAwIA,EAAG,OAAO,qCAAqC,IAAI;IAChO;AAFQK,MAAAC,GAAA,eAAA;AAIT,aAASC,EAAcP,GAAIQ,GAAQ;AACjC,UAAIC,IAAM,OAAOD,CAAM;AACvB,aAAI,QAAQ,KAAKA,CAAM,IAAUR,EAAG,UAAS,EAAG,OAAOS,IAC3CA,IAAM;IACnB;AAJQJ,MAAAE,GAAA,eAAA,GAMTT,EAAW,SAAS,aAAa,SAASE,GAAI;AAC5C,UAAIU,IAAMV,EAAG,UAAA;AACbD,QAAOC,GAAIM,EAAcN,CAAE,GAAGA,EAAG,OAAO,eAAe,GAAIU,EAAI,OAAO,IAAK,MAAMA,EAAI,IAAI,SAASC,GAAQ;AACxG,YAAKA,GAEL;AAAA,cAAIC;AACJ,cAAIA,IAAQ,oCAAoC,KAAKD,CAAM;AACzDX,cAAG,UAAUO,EAAcP,GAAIY,EAAM,CAAC,CAAC,GAAG,OAAOA,EAAM,CAAC,CAAC,CAAC;mBACjDA,IAAQ,gCAAgC,KAAKD,CAAM,GAAG;AAC/D,gBAAIE,IAAO,KAAK,MAAMb,EAAG,UAAS,IAAK,OAAOY,EAAM,CAAC,CAAC,IAAI,GAAG;AACzD,oBAAQ,KAAKA,EAAM,CAAC,CAAC,MAAGC,IAAOH,EAAI,OAAOG,IAAO,IACrDb,EAAG,UAAUa,IAAO,GAAGH,EAAI,EAAE;UAAA;AACxB,aAAIE,IAAQ,4BAA4B,KAAKD,CAAM,MACxDX,EAAG,UAAUO,EAAcP,GAAIY,EAAM,CAAC,CAAC,GAAGF,EAAI,EAAE;QAAA;MAExD,CAAK;IACL,GAEEZ,EAAW,OAAO,QAAW,OAAO,IAAI;EAC1C,CAAC;;;;;;;;", "names": ["mod", "require$$0", "require$$1", "CodeMirror", "dialog", "cm", "text", "shortText", "deflt", "f", "__name", "getJumpDialog", "interpretLine", "string", "num", "cur", "posStr", "match", "line"]}