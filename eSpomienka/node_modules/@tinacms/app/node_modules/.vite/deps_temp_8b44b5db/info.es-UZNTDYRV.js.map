{"version": 3, "sources": ["../../../../../@graphiql/codemirror-graphql/esm/info.js"], "sourcesContent": ["import { Graph<PERSON><PERSON>ist, GraphQLNonNull, } from 'graphql';\nimport CodeMirror from 'codemirror';\nimport getTypeInfo from './utils/getTypeInfo';\nimport { getArgumentReference, getDirectiveReference, getEnumValueReference, getFieldReference, getTypeReference, } from './utils/SchemaReference';\nimport './utils/info-addon';\nCodeMirror.registerHelper('info', 'graphql', (token, options) => {\n    if (!options.schema || !token.state) {\n        return;\n    }\n    const { kind, step } = token.state;\n    const typeInfo = getTypeInfo(options.schema, token.state);\n    if ((kind === 'Field' && step === 0 && typeInfo.fieldDef) ||\n        (kind === 'AliasedField' && step === 2 && typeInfo.fieldDef)) {\n        const header = document.createElement('div');\n        header.className = 'CodeMirror-info-header';\n        renderField(header, typeInfo, options);\n        const into = document.createElement('div');\n        into.append(header);\n        renderDescription(into, options, typeInfo.fieldDef);\n        return into;\n    }\n    if (kind === 'Directive' && step === 1 && typeInfo.directiveDef) {\n        const header = document.createElement('div');\n        header.className = 'CodeMirror-info-header';\n        renderDirective(header, typeInfo, options);\n        const into = document.createElement('div');\n        into.append(header);\n        renderDescription(into, options, typeInfo.directiveDef);\n        return into;\n    }\n    if (kind === 'Argument' && step === 0 && typeInfo.argDef) {\n        const header = document.createElement('div');\n        header.className = 'CodeMirror-info-header';\n        renderArg(header, typeInfo, options);\n        const into = document.createElement('div');\n        into.append(header);\n        renderDescription(into, options, typeInfo.argDef);\n        return into;\n    }\n    if (kind === 'EnumValue' &&\n        typeInfo.enumValue &&\n        typeInfo.enumValue.description) {\n        const header = document.createElement('div');\n        header.className = 'CodeMirror-info-header';\n        renderEnumValue(header, typeInfo, options);\n        const into = document.createElement('div');\n        into.append(header);\n        renderDescription(into, options, typeInfo.enumValue);\n        return into;\n    }\n    if (kind === 'NamedType' &&\n        typeInfo.type &&\n        typeInfo.type.description) {\n        const header = document.createElement('div');\n        header.className = 'CodeMirror-info-header';\n        renderType(header, typeInfo, options, typeInfo.type);\n        const into = document.createElement('div');\n        into.append(header);\n        renderDescription(into, options, typeInfo.type);\n        return into;\n    }\n});\nfunction renderField(into, typeInfo, options) {\n    renderQualifiedField(into, typeInfo, options);\n    renderTypeAnnotation(into, typeInfo, options, typeInfo.type);\n}\nfunction renderQualifiedField(into, typeInfo, options) {\n    var _a;\n    const fieldName = ((_a = typeInfo.fieldDef) === null || _a === void 0 ? void 0 : _a.name) || '';\n    text(into, fieldName, 'field-name', options, getFieldReference(typeInfo));\n}\nfunction renderDirective(into, typeInfo, options) {\n    var _a;\n    const name = '@' + (((_a = typeInfo.directiveDef) === null || _a === void 0 ? void 0 : _a.name) || '');\n    text(into, name, 'directive-name', options, getDirectiveReference(typeInfo));\n}\nfunction renderArg(into, typeInfo, options) {\n    var _a;\n    const name = ((_a = typeInfo.argDef) === null || _a === void 0 ? void 0 : _a.name) || '';\n    text(into, name, 'arg-name', options, getArgumentReference(typeInfo));\n    renderTypeAnnotation(into, typeInfo, options, typeInfo.inputType);\n}\nfunction renderEnumValue(into, typeInfo, options) {\n    var _a;\n    const name = ((_a = typeInfo.enumValue) === null || _a === void 0 ? void 0 : _a.name) || '';\n    renderType(into, typeInfo, options, typeInfo.inputType);\n    text(into, '.');\n    text(into, name, 'enum-value', options, getEnumValueReference(typeInfo));\n}\nfunction renderTypeAnnotation(into, typeInfo, options, t) {\n    const typeSpan = document.createElement('span');\n    typeSpan.className = 'type-name-pill';\n    if (t instanceof GraphQLNonNull) {\n        renderType(typeSpan, typeInfo, options, t.ofType);\n        text(typeSpan, '!');\n    }\n    else if (t instanceof GraphQLList) {\n        text(typeSpan, '[');\n        renderType(typeSpan, typeInfo, options, t.ofType);\n        text(typeSpan, ']');\n    }\n    else {\n        text(typeSpan, (t === null || t === void 0 ? void 0 : t.name) || '', 'type-name', options, getTypeReference(typeInfo, t));\n    }\n    into.append(typeSpan);\n}\nfunction renderType(into, typeInfo, options, t) {\n    if (t instanceof GraphQLNonNull) {\n        renderType(into, typeInfo, options, t.ofType);\n        text(into, '!');\n    }\n    else if (t instanceof GraphQLList) {\n        text(into, '[');\n        renderType(into, typeInfo, options, t.ofType);\n        text(into, ']');\n    }\n    else {\n        text(into, (t === null || t === void 0 ? void 0 : t.name) || '', 'type-name', options, getTypeReference(typeInfo, t));\n    }\n}\nfunction renderDescription(into, options, def) {\n    const { description } = def;\n    if (description) {\n        const descriptionDiv = document.createElement('div');\n        descriptionDiv.className = 'info-description';\n        if (options.renderDescription) {\n            descriptionDiv.innerHTML = options.renderDescription(description);\n        }\n        else {\n            descriptionDiv.append(document.createTextNode(description));\n        }\n        into.append(descriptionDiv);\n    }\n    renderDeprecation(into, options, def);\n}\nfunction renderDeprecation(into, options, def) {\n    const reason = def.deprecationReason;\n    if (reason) {\n        const deprecationDiv = document.createElement('div');\n        deprecationDiv.className = 'info-deprecation';\n        into.append(deprecationDiv);\n        const label = document.createElement('span');\n        label.className = 'info-deprecation-label';\n        label.append(document.createTextNode('Deprecated'));\n        deprecationDiv.append(label);\n        const reasonDiv = document.createElement('div');\n        reasonDiv.className = 'info-deprecation-reason';\n        if (options.renderDescription) {\n            reasonDiv.innerHTML = options.renderDescription(reason);\n        }\n        else {\n            reasonDiv.append(document.createTextNode(reason));\n        }\n        deprecationDiv.append(reasonDiv);\n    }\n}\nfunction text(into, content, className = '', options = { onClick: null }, ref = null) {\n    if (className) {\n        const { onClick } = options;\n        let node;\n        if (onClick) {\n            node = document.createElement('a');\n            node.href = 'javascript:void 0';\n            node.addEventListener('click', (e) => {\n                onClick(ref, e);\n            });\n        }\n        else {\n            node = document.createElement('span');\n        }\n        node.className = className;\n        node.append(document.createTextNode(content));\n        into.append(node);\n    }\n    else {\n        into.append(document.createTextNode(content));\n    }\n}\n//# sourceMappingURL=info.js.map"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAKAA,EAAW,eAAe,QAAQ,WAAW,CAACC,GAAOC,MAAY;AAC7D,MAAI,CAACA,EAAQ,UAAU,CAACD,EAAM;AAC1B;AAEJ,QAAM,EAAE,MAAAE,IAAM,MAAAC,EAAAA,IAASH,EAAM,OACvBI,IAAWC,GAAYJ,EAAQ,QAAQD,EAAM,KAAK;AACxD,MAAKE,OAAS,WAAWC,MAAS,KAAKC,EAAS,YAC3CF,OAAS,kBAAkBC,MAAS,KAAKC,EAAS,UAAW;AAC9D,UAAME,IAAS,SAAS,cAAc,KAAK;AAC3CA,MAAO,YAAY,0BACnBC,EAAYD,GAAQF,GAAUH,CAAO;AACrC,UAAMO,IAAO,SAAS,cAAc,KAAK;AACzC,WAAAA,EAAK,OAAOF,CAAM,GAClBG,EAAkBD,GAAMP,GAASG,EAAS,QAAQ,GAC3CI;EAAA;AAEX,MAAIN,OAAS,eAAeC,MAAS,KAAKC,EAAS,cAAc;AAC7D,UAAME,IAAS,SAAS,cAAc,KAAK;AAC3CA,MAAO,YAAY,0BACnBI,EAAgBJ,GAAQF,GAAUH,CAAO;AACzC,UAAMO,IAAO,SAAS,cAAc,KAAK;AACzC,WAAAA,EAAK,OAAOF,CAAM,GAClBG,EAAkBD,GAAMP,GAASG,EAAS,YAAY,GAC/CI;EAAA;AAEX,MAAIN,OAAS,cAAcC,MAAS,KAAKC,EAAS,QAAQ;AACtD,UAAME,IAAS,SAAS,cAAc,KAAK;AAC3CA,MAAO,YAAY,0BACnBK,EAAUL,GAAQF,GAAUH,CAAO;AACnC,UAAMO,IAAO,SAAS,cAAc,KAAK;AACzC,WAAAA,EAAK,OAAOF,CAAM,GAClBG,EAAkBD,GAAMP,GAASG,EAAS,MAAM,GACzCI;EAAA;AAEX,MAAIN,OAAS,eACTE,EAAS,aACTA,EAAS,UAAU,aAAa;AAChC,UAAME,IAAS,SAAS,cAAc,KAAK;AAC3CA,MAAO,YAAY,0BACnBM,EAAgBN,GAAQF,GAAUH,CAAO;AACzC,UAAMO,IAAO,SAAS,cAAc,KAAK;AACzC,WAAAA,EAAK,OAAOF,CAAM,GAClBG,EAAkBD,GAAMP,GAASG,EAAS,SAAS,GAC5CI;EAAA;AAEX,MAAIN,OAAS,eACTE,EAAS,QACTA,EAAS,KAAK,aAAa;AAC3B,UAAME,IAAS,SAAS,cAAc,KAAK;AAC3CA,MAAO,YAAY,0BACnBO,EAAWP,GAAQF,GAAUH,GAASG,EAAS,IAAI;AACnD,UAAMI,IAAO,SAAS,cAAc,KAAK;AACzC,WAAAA,EAAK,OAAOF,CAAM,GAClBG,EAAkBD,GAAMP,GAASG,EAAS,IAAI,GACvCI;EAAA;AAEf,CAAC;AACD,SAASD,EAAYC,GAAMJ,GAAUH,IAAS;AAC1Ca,IAAqBN,GAAMJ,GAAUH,EAAO,GAC5Cc,EAAqBP,GAAMJ,GAAUH,IAASG,EAAS,IAAI;AAC/D;AAHSY,EAAAT,GAAA,aAAA;AAIT,SAASO,EAAqBN,GAAMJ,GAAUH,IAAS;AACnD,MAAIgB;AACJ,QAAMC,MAAcD,IAAKb,EAAS,cAAc,QAAQa,MAAO,SAAS,SAASA,EAAG,SAAS;AAC7FE,IAAKX,GAAMU,GAAW,cAAcjB,IAASmB,GAAkBhB,CAAQ,CAAC;AAC5E;AAJSY,EAAAF,GAAA,sBAAA;AAKT,SAASJ,EAAgBF,GAAMJ,GAAUH,IAAS;AAC9C,MAAIgB;AACJ,QAAMI,IAAO,SAASJ,IAAKb,EAAS,kBAAkB,QAAQa,MAAO,SAAS,SAASA,EAAG,SAAS;AACnGE,IAAKX,GAAMa,GAAM,kBAAkBpB,IAASqB,GAAsBlB,CAAQ,CAAC;AAC/E;AAJSY,EAAAN,GAAA,iBAAA;AAKT,SAASC,EAAUH,GAAMJ,GAAUH,IAAS;AACxC,MAAIgB;AACJ,QAAMI,MAASJ,IAAKb,EAAS,YAAY,QAAQa,MAAO,SAAS,SAASA,EAAG,SAAS;AACtFE,IAAKX,GAAMa,GAAM,YAAYpB,IAASsB,GAAqBnB,CAAQ,CAAC,GACpEW,EAAqBP,GAAMJ,GAAUH,IAASG,EAAS,SAAS;AACpE;AALSY,EAAAL,GAAA,WAAA;AAMT,SAASC,EAAgBJ,GAAMJ,GAAUH,IAAS;AAC9C,MAAIgB;AACJ,QAAMI,MAASJ,IAAKb,EAAS,eAAe,QAAQa,MAAO,SAAS,SAASA,EAAG,SAAS;AACzFJ,IAAWL,GAAMJ,GAAUH,IAASG,EAAS,SAAS,GACtDe,EAAKX,GAAM,GAAG,GACdW,EAAKX,GAAMa,GAAM,cAAcpB,IAASuB,GAAsBpB,CAAQ,CAAC;AAC3E;AANSY,EAAAJ,GAAA,iBAAA;AAOT,SAASG,EAAqBP,GAAMJ,GAAUH,IAASwB,GAAG;AACtD,QAAMC,IAAW,SAAS,cAAc,MAAM;AAC9CA,IAAS,YAAY,kBACjBD,aAAaE,kBACbd,EAAWa,GAAUtB,GAAUH,IAASwB,EAAE,MAAM,GAChDN,EAAKO,GAAU,GAAG,KAEbD,aAAaG,eAClBT,EAAKO,GAAU,GAAG,GAClBb,EAAWa,GAAUtB,GAAUH,IAASwB,EAAE,MAAM,GAChDN,EAAKO,GAAU,GAAG,KAGlBP,EAAKO,IAAWD,KAAM,OAAuB,SAASA,EAAE,SAAS,IAAI,aAAaxB,IAAS4B,GAAiBzB,GAAUqB,CAAC,CAAC,GAE5HjB,EAAK,OAAOkB,CAAQ;AACxB;AAhBSV,EAAAD,GAAA,sBAAA;AAiBT,SAASF,EAAWL,GAAMJ,GAAUH,IAASwB,GAAG;AACxCA,eAAaE,kBACbd,EAAWL,GAAMJ,GAAUH,IAASwB,EAAE,MAAM,GAC5CN,EAAKX,GAAM,GAAG,KAETiB,aAAaG,eAClBT,EAAKX,GAAM,GAAG,GACdK,EAAWL,GAAMJ,GAAUH,IAASwB,EAAE,MAAM,GAC5CN,EAAKX,GAAM,GAAG,KAGdW,EAAKX,IAAOiB,KAAM,OAAuB,SAASA,EAAE,SAAS,IAAI,aAAaxB,IAAS4B,GAAiBzB,GAAUqB,CAAC,CAAC;AAE5H;AAbST,EAAAH,GAAA,YAAA;AAcT,SAASJ,EAAkBD,GAAMP,GAAS6B,IAAK;AAC3C,QAAM,EAAE,aAAAC,EAAa,IAAGD;AACxB,MAAIC,GAAa;AACb,UAAMC,IAAiB,SAAS,cAAc,KAAK;AACnDA,MAAe,YAAY,oBACvB/B,EAAQ,oBACR+B,EAAe,YAAY/B,EAAQ,kBAAkB8B,CAAW,IAGhEC,EAAe,OAAO,SAAS,eAAeD,CAAW,CAAC,GAE9DvB,EAAK,OAAOwB,CAAc;EAAA;AAE9BC,IAAkBzB,GAAMP,GAAS6B,EAAG;AACxC;AAdSd,EAAAP,GAAA,mBAAA;AAeT,SAASwB,EAAkBzB,GAAMP,GAAS6B,IAAK;AAC3C,QAAMI,IAASJ,GAAI;AACnB,MAAII,GAAQ;AACR,UAAMC,IAAiB,SAAS,cAAc,KAAK;AACnDA,MAAe,YAAY,oBAC3B3B,EAAK,OAAO2B,CAAc;AAC1B,UAAMC,IAAQ,SAAS,cAAc,MAAM;AAC3CA,MAAM,YAAY,0BAClBA,EAAM,OAAO,SAAS,eAAe,YAAY,CAAC,GAClDD,EAAe,OAAOC,CAAK;AAC3B,UAAMC,IAAY,SAAS,cAAc,KAAK;AAC9CA,MAAU,YAAY,2BAClBpC,EAAQ,oBACRoC,EAAU,YAAYpC,EAAQ,kBAAkBiC,CAAM,IAGtDG,EAAU,OAAO,SAAS,eAAeH,CAAM,CAAC,GAEpDC,EAAe,OAAOE,CAAS;EAAA;AAEvC;AApBSrB,EAAAiB,GAAA,mBAAA;AAqBT,SAASd,EAAKX,GAAM8B,GAASC,KAAY,IAAItC,IAAU,EAAE,SAAS,KAAA,GAAQuC,IAAM,MAAM;AAClF,MAAID,IAAW;AACX,UAAM,EAAE,SAAAE,EAAS,IAAGxC;AACpB,QAAIyC;AACAD,SACAC,IAAO,SAAS,cAAc,GAAG,GACjCA,EAAK,OAAO,qBACZA,EAAK,iBAAiB,SAAS,CAACC,MAAM;AAClCF,QAAQD,GAAKG,CAAC;IAC9B,CAAa,KAGDD,IAAO,SAAS,cAAc,MAAM,GAExCA,EAAK,YAAYH,IACjBG,EAAK,OAAO,SAAS,eAAeJ,CAAO,CAAC,GAC5C9B,EAAK,OAAOkC,CAAI;EAAA;AAGhBlC,MAAK,OAAO,SAAS,eAAe8B,CAAO,CAAC;AAEpD;AArBStB,EAAAG,GAAA,MAAA;", "names": ["CodeMirror", "token", "options", "kind", "step", "typeInfo", "getTypeInfo", "header", "renderField", "into", "renderDescription", "renderDirective", "renderArg", "renderEnumValue", "renderType", "renderQualifiedField", "renderTypeAnnotation", "__name", "_a", "fieldName", "text", "getFieldReference", "name", "getDirectiveReference", "getArgumentReference", "getEnumValueReference", "t", "typeSpan", "GraphQLNonNull", "GraphQLList", "getTypeReference", "def", "description", "descriptionDiv", "renderDeprecation", "reason", "deprecationDiv", "label", "reasonDiv", "content", "className", "ref", "onClick", "node", "e"]}