import {
  K
} from "./chunk-4WPFA5CA.js";
import {
  hu
} from "./chunk-MEKKV4OY.js";
import "./chunk-AUZ3RYOM.js";

// node_modules/@graphiql/react/dist/searchcursor.es.js
var f = Object.defineProperty;
var a = (r, o) => f(r, "name", { value: o, configurable: true });
function p(r, o) {
  for (var s = 0; s < o.length; s++) {
    const e = o[s];
    if (typeof e != "string" && !Array.isArray(e)) {
      for (const t in e)
        if (t !== "default" && !(t in r)) {
          const c = Object.getOwnPropertyDescriptor(e, t);
          c && Object.defineProperty(r, t, c.get ? c : {
            enumerable: true,
            get: () => e[t]
          });
        }
    }
  }
  return Object.freeze(Object.defineProperty(r, Symbol.toStringTag, { value: "Module" }));
}
a(p, "_mergeNamespaces");
var n = K();
var g = hu(n);
var b = p({
  __proto__: null,
  default: g
}, [n]);
export {
  b as s
};
//# sourceMappingURL=searchcursor.es-RKXSKNER.js.map
