{"version": 3, "sources": ["../../../../../node_modules/codemirror/keymap/sublime.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/LICENSE\n\n// A rough approximation of Sublime Text's keybindings\n// Depends on addon/search/searchcursor.js and optionally addon/dialog/dialogs.js\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../lib/codemirror\"), require(\"../addon/search/searchcursor\"), require(\"../addon/edit/matchbrackets\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../lib/codemirror\", \"../addon/search/searchcursor\", \"../addon/edit/matchbrackets\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n\n  var cmds = CodeMirror.commands;\n  var Pos = CodeMirror.Pos;\n\n  // This is not exactly <PERSON>lime's algorithm. I couldn't make heads or tails of that.\n  function findPosSubword(doc, start, dir) {\n    if (dir < 0 && start.ch == 0) return doc.clipPos(Pos(start.line - 1));\n    var line = doc.getLine(start.line);\n    if (dir > 0 && start.ch >= line.length) return doc.clipPos(Pos(start.line + 1, 0));\n    var state = \"start\", type, startPos = start.ch;\n    for (var pos = startPos, e = dir < 0 ? 0 : line.length, i = 0; pos != e; pos += dir, i++) {\n      var next = line.charAt(dir < 0 ? pos - 1 : pos);\n      var cat = next != \"_\" && CodeMirror.isWordChar(next) ? \"w\" : \"o\";\n      if (cat == \"w\" && next.toUpperCase() == next) cat = \"W\";\n      if (state == \"start\") {\n        if (cat != \"o\") { state = \"in\"; type = cat; }\n        else startPos = pos + dir\n      } else if (state == \"in\") {\n        if (type != cat) {\n          if (type == \"w\" && cat == \"W\" && dir < 0) pos--;\n          if (type == \"W\" && cat == \"w\" && dir > 0) { // From uppercase to lowercase\n            if (pos == startPos + 1) { type = \"w\"; continue; }\n            else pos--;\n          }\n          break;\n        }\n      }\n    }\n    return Pos(start.line, pos);\n  }\n\n  function moveSubword(cm, dir) {\n    cm.extendSelectionsBy(function(range) {\n      if (cm.display.shift || cm.doc.extend || range.empty())\n        return findPosSubword(cm.doc, range.head, dir);\n      else\n        return dir < 0 ? range.from() : range.to();\n    });\n  }\n\n  cmds.goSubwordLeft = function(cm) { moveSubword(cm, -1); };\n  cmds.goSubwordRight = function(cm) { moveSubword(cm, 1); };\n\n  cmds.scrollLineUp = function(cm) {\n    var info = cm.getScrollInfo();\n    if (!cm.somethingSelected()) {\n      var visibleBottomLine = cm.lineAtHeight(info.top + info.clientHeight, \"local\");\n      if (cm.getCursor().line >= visibleBottomLine)\n        cm.execCommand(\"goLineUp\");\n    }\n    cm.scrollTo(null, info.top - cm.defaultTextHeight());\n  };\n  cmds.scrollLineDown = function(cm) {\n    var info = cm.getScrollInfo();\n    if (!cm.somethingSelected()) {\n      var visibleTopLine = cm.lineAtHeight(info.top, \"local\")+1;\n      if (cm.getCursor().line <= visibleTopLine)\n        cm.execCommand(\"goLineDown\");\n    }\n    cm.scrollTo(null, info.top + cm.defaultTextHeight());\n  };\n\n  cmds.splitSelectionByLine = function(cm) {\n    var ranges = cm.listSelections(), lineRanges = [];\n    for (var i = 0; i < ranges.length; i++) {\n      var from = ranges[i].from(), to = ranges[i].to();\n      for (var line = from.line; line <= to.line; ++line)\n        if (!(to.line > from.line && line == to.line && to.ch == 0))\n          lineRanges.push({anchor: line == from.line ? from : Pos(line, 0),\n                           head: line == to.line ? to : Pos(line)});\n    }\n    cm.setSelections(lineRanges, 0);\n  };\n\n  cmds.singleSelectionTop = function(cm) {\n    var range = cm.listSelections()[0];\n    cm.setSelection(range.anchor, range.head, {scroll: false});\n  };\n\n  cmds.selectLine = function(cm) {\n    var ranges = cm.listSelections(), extended = [];\n    for (var i = 0; i < ranges.length; i++) {\n      var range = ranges[i];\n      extended.push({anchor: Pos(range.from().line, 0),\n                     head: Pos(range.to().line + 1, 0)});\n    }\n    cm.setSelections(extended);\n  };\n\n  function insertLine(cm, above) {\n    if (cm.isReadOnly()) return CodeMirror.Pass\n    cm.operation(function() {\n      var len = cm.listSelections().length, newSelection = [], last = -1;\n      for (var i = 0; i < len; i++) {\n        var head = cm.listSelections()[i].head;\n        if (head.line <= last) continue;\n        var at = Pos(head.line + (above ? 0 : 1), 0);\n        cm.replaceRange(\"\\n\", at, null, \"+insertLine\");\n        cm.indentLine(at.line, null, true);\n        newSelection.push({head: at, anchor: at});\n        last = head.line + 1;\n      }\n      cm.setSelections(newSelection);\n    });\n    cm.execCommand(\"indentAuto\");\n  }\n\n  cmds.insertLineAfter = function(cm) { return insertLine(cm, false); };\n\n  cmds.insertLineBefore = function(cm) { return insertLine(cm, true); };\n\n  function wordAt(cm, pos) {\n    var start = pos.ch, end = start, line = cm.getLine(pos.line);\n    while (start && CodeMirror.isWordChar(line.charAt(start - 1))) --start;\n    while (end < line.length && CodeMirror.isWordChar(line.charAt(end))) ++end;\n    return {from: Pos(pos.line, start), to: Pos(pos.line, end), word: line.slice(start, end)};\n  }\n\n  cmds.selectNextOccurrence = function(cm) {\n    var from = cm.getCursor(\"from\"), to = cm.getCursor(\"to\");\n    var fullWord = cm.state.sublimeFindFullWord == cm.doc.sel;\n    if (CodeMirror.cmpPos(from, to) == 0) {\n      var word = wordAt(cm, from);\n      if (!word.word) return;\n      cm.setSelection(word.from, word.to);\n      fullWord = true;\n    } else {\n      var text = cm.getRange(from, to);\n      var query = fullWord ? new RegExp(\"\\\\b\" + text + \"\\\\b\") : text;\n      var cur = cm.getSearchCursor(query, to);\n      var found = cur.findNext();\n      if (!found) {\n        cur = cm.getSearchCursor(query, Pos(cm.firstLine(), 0));\n        found = cur.findNext();\n      }\n      if (!found || isSelectedRange(cm.listSelections(), cur.from(), cur.to())) return\n      cm.addSelection(cur.from(), cur.to());\n    }\n    if (fullWord)\n      cm.state.sublimeFindFullWord = cm.doc.sel;\n  };\n\n  cmds.skipAndSelectNextOccurrence = function(cm) {\n    var prevAnchor = cm.getCursor(\"anchor\"), prevHead = cm.getCursor(\"head\");\n    cmds.selectNextOccurrence(cm);\n    if (CodeMirror.cmpPos(prevAnchor, prevHead) != 0) {\n      cm.doc.setSelections(cm.doc.listSelections()\n          .filter(function (sel) {\n            return sel.anchor != prevAnchor || sel.head != prevHead;\n          }));\n    }\n  }\n\n  function addCursorToSelection(cm, dir) {\n    var ranges = cm.listSelections(), newRanges = [];\n    for (var i = 0; i < ranges.length; i++) {\n      var range = ranges[i];\n      var newAnchor = cm.findPosV(\n          range.anchor, dir, \"line\", range.anchor.goalColumn);\n      var newHead = cm.findPosV(\n          range.head, dir, \"line\", range.head.goalColumn);\n      newAnchor.goalColumn = range.anchor.goalColumn != null ?\n          range.anchor.goalColumn : cm.cursorCoords(range.anchor, \"div\").left;\n      newHead.goalColumn = range.head.goalColumn != null ?\n          range.head.goalColumn : cm.cursorCoords(range.head, \"div\").left;\n      var newRange = {anchor: newAnchor, head: newHead};\n      newRanges.push(range);\n      newRanges.push(newRange);\n    }\n    cm.setSelections(newRanges);\n  }\n  cmds.addCursorToPrevLine = function(cm) { addCursorToSelection(cm, -1); };\n  cmds.addCursorToNextLine = function(cm) { addCursorToSelection(cm, 1); };\n\n  function isSelectedRange(ranges, from, to) {\n    for (var i = 0; i < ranges.length; i++)\n      if (CodeMirror.cmpPos(ranges[i].from(), from) == 0 &&\n          CodeMirror.cmpPos(ranges[i].to(), to) == 0) return true\n    return false\n  }\n\n  var mirror = \"(){}[]\";\n  function selectBetweenBrackets(cm) {\n    var ranges = cm.listSelections(), newRanges = []\n    for (var i = 0; i < ranges.length; i++) {\n      var range = ranges[i], pos = range.head, opening = cm.scanForBracket(pos, -1);\n      if (!opening) return false;\n      for (;;) {\n        var closing = cm.scanForBracket(pos, 1);\n        if (!closing) return false;\n        if (closing.ch == mirror.charAt(mirror.indexOf(opening.ch) + 1)) {\n          var startPos = Pos(opening.pos.line, opening.pos.ch + 1);\n          if (CodeMirror.cmpPos(startPos, range.from()) == 0 &&\n              CodeMirror.cmpPos(closing.pos, range.to()) == 0) {\n            opening = cm.scanForBracket(opening.pos, -1);\n            if (!opening) return false;\n          } else {\n            newRanges.push({anchor: startPos, head: closing.pos});\n            break;\n          }\n        }\n        pos = Pos(closing.pos.line, closing.pos.ch + 1);\n      }\n    }\n    cm.setSelections(newRanges);\n    return true;\n  }\n\n  cmds.selectScope = function(cm) {\n    selectBetweenBrackets(cm) || cm.execCommand(\"selectAll\");\n  };\n  cmds.selectBetweenBrackets = function(cm) {\n    if (!selectBetweenBrackets(cm)) return CodeMirror.Pass;\n  };\n\n  function puncType(type) {\n    return !type ? null : /\\bpunctuation\\b/.test(type) ? type : undefined\n  }\n\n  cmds.goToBracket = function(cm) {\n    cm.extendSelectionsBy(function(range) {\n      var next = cm.scanForBracket(range.head, 1, puncType(cm.getTokenTypeAt(range.head)));\n      if (next && CodeMirror.cmpPos(next.pos, range.head) != 0) return next.pos;\n      var prev = cm.scanForBracket(range.head, -1, puncType(cm.getTokenTypeAt(Pos(range.head.line, range.head.ch + 1))));\n      return prev && Pos(prev.pos.line, prev.pos.ch + 1) || range.head;\n    });\n  };\n\n  cmds.swapLineUp = function(cm) {\n    if (cm.isReadOnly()) return CodeMirror.Pass\n    var ranges = cm.listSelections(), linesToMove = [], at = cm.firstLine() - 1, newSels = [];\n    for (var i = 0; i < ranges.length; i++) {\n      var range = ranges[i], from = range.from().line - 1, to = range.to().line;\n      newSels.push({anchor: Pos(range.anchor.line - 1, range.anchor.ch),\n                    head: Pos(range.head.line - 1, range.head.ch)});\n      if (range.to().ch == 0 && !range.empty()) --to;\n      if (from > at) linesToMove.push(from, to);\n      else if (linesToMove.length) linesToMove[linesToMove.length - 1] = to;\n      at = to;\n    }\n    cm.operation(function() {\n      for (var i = 0; i < linesToMove.length; i += 2) {\n        var from = linesToMove[i], to = linesToMove[i + 1];\n        var line = cm.getLine(from);\n        cm.replaceRange(\"\", Pos(from, 0), Pos(from + 1, 0), \"+swapLine\");\n        if (to > cm.lastLine())\n          cm.replaceRange(\"\\n\" + line, Pos(cm.lastLine()), null, \"+swapLine\");\n        else\n          cm.replaceRange(line + \"\\n\", Pos(to, 0), null, \"+swapLine\");\n      }\n      cm.setSelections(newSels);\n      cm.scrollIntoView();\n    });\n  };\n\n  cmds.swapLineDown = function(cm) {\n    if (cm.isReadOnly()) return CodeMirror.Pass\n    var ranges = cm.listSelections(), linesToMove = [], at = cm.lastLine() + 1;\n    for (var i = ranges.length - 1; i >= 0; i--) {\n      var range = ranges[i], from = range.to().line + 1, to = range.from().line;\n      if (range.to().ch == 0 && !range.empty()) from--;\n      if (from < at) linesToMove.push(from, to);\n      else if (linesToMove.length) linesToMove[linesToMove.length - 1] = to;\n      at = to;\n    }\n    cm.operation(function() {\n      for (var i = linesToMove.length - 2; i >= 0; i -= 2) {\n        var from = linesToMove[i], to = linesToMove[i + 1];\n        var line = cm.getLine(from);\n        if (from == cm.lastLine())\n          cm.replaceRange(\"\", Pos(from - 1), Pos(from), \"+swapLine\");\n        else\n          cm.replaceRange(\"\", Pos(from, 0), Pos(from + 1, 0), \"+swapLine\");\n        cm.replaceRange(line + \"\\n\", Pos(to, 0), null, \"+swapLine\");\n      }\n      cm.scrollIntoView();\n    });\n  };\n\n  cmds.toggleCommentIndented = function(cm) {\n    cm.toggleComment({ indent: true });\n  }\n\n  cmds.joinLines = function(cm) {\n    var ranges = cm.listSelections(), joined = [];\n    for (var i = 0; i < ranges.length; i++) {\n      var range = ranges[i], from = range.from();\n      var start = from.line, end = range.to().line;\n      while (i < ranges.length - 1 && ranges[i + 1].from().line == end)\n        end = ranges[++i].to().line;\n      joined.push({start: start, end: end, anchor: !range.empty() && from});\n    }\n    cm.operation(function() {\n      var offset = 0, ranges = [];\n      for (var i = 0; i < joined.length; i++) {\n        var obj = joined[i];\n        var anchor = obj.anchor && Pos(obj.anchor.line - offset, obj.anchor.ch), head;\n        for (var line = obj.start; line <= obj.end; line++) {\n          var actual = line - offset;\n          if (line == obj.end) head = Pos(actual, cm.getLine(actual).length + 1);\n          if (actual < cm.lastLine()) {\n            cm.replaceRange(\" \", Pos(actual), Pos(actual + 1, /^\\s*/.exec(cm.getLine(actual + 1))[0].length));\n            ++offset;\n          }\n        }\n        ranges.push({anchor: anchor || head, head: head});\n      }\n      cm.setSelections(ranges, 0);\n    });\n  };\n\n  cmds.duplicateLine = function(cm) {\n    cm.operation(function() {\n      var rangeCount = cm.listSelections().length;\n      for (var i = 0; i < rangeCount; i++) {\n        var range = cm.listSelections()[i];\n        if (range.empty())\n          cm.replaceRange(cm.getLine(range.head.line) + \"\\n\", Pos(range.head.line, 0));\n        else\n          cm.replaceRange(cm.getRange(range.from(), range.to()), range.from());\n      }\n      cm.scrollIntoView();\n    });\n  };\n\n\n  function sortLines(cm, caseSensitive, direction) {\n    if (cm.isReadOnly()) return CodeMirror.Pass\n    var ranges = cm.listSelections(), toSort = [], selected;\n    for (var i = 0; i < ranges.length; i++) {\n      var range = ranges[i];\n      if (range.empty()) continue;\n      var from = range.from().line, to = range.to().line;\n      while (i < ranges.length - 1 && ranges[i + 1].from().line == to)\n        to = ranges[++i].to().line;\n      if (!ranges[i].to().ch) to--;\n      toSort.push(from, to);\n    }\n    if (toSort.length) selected = true;\n    else toSort.push(cm.firstLine(), cm.lastLine());\n\n    cm.operation(function() {\n      var ranges = [];\n      for (var i = 0; i < toSort.length; i += 2) {\n        var from = toSort[i], to = toSort[i + 1];\n        var start = Pos(from, 0), end = Pos(to);\n        var lines = cm.getRange(start, end, false);\n        if (caseSensitive)\n          lines.sort(function(a, b) { return a < b ? -direction : a == b ? 0 : direction; });\n        else\n          lines.sort(function(a, b) {\n            var au = a.toUpperCase(), bu = b.toUpperCase();\n            if (au != bu) { a = au; b = bu; }\n            return a < b ? -direction : a == b ? 0 : direction;\n          });\n        cm.replaceRange(lines, start, end);\n        if (selected) ranges.push({anchor: start, head: Pos(to + 1, 0)});\n      }\n      if (selected) cm.setSelections(ranges, 0);\n    });\n  }\n\n  cmds.sortLines = function(cm) { sortLines(cm, true, 1); };\n  cmds.reverseSortLines = function(cm) { sortLines(cm, true, -1); };\n  cmds.sortLinesInsensitive = function(cm) { sortLines(cm, false, 1); };\n  cmds.reverseSortLinesInsensitive = function(cm) { sortLines(cm, false, -1); };\n\n  cmds.nextBookmark = function(cm) {\n    var marks = cm.state.sublimeBookmarks;\n    if (marks) while (marks.length) {\n      var current = marks.shift();\n      var found = current.find();\n      if (found) {\n        marks.push(current);\n        return cm.setSelection(found.from, found.to);\n      }\n    }\n  };\n\n  cmds.prevBookmark = function(cm) {\n    var marks = cm.state.sublimeBookmarks;\n    if (marks) while (marks.length) {\n      marks.unshift(marks.pop());\n      var found = marks[marks.length - 1].find();\n      if (!found)\n        marks.pop();\n      else\n        return cm.setSelection(found.from, found.to);\n    }\n  };\n\n  cmds.toggleBookmark = function(cm) {\n    var ranges = cm.listSelections();\n    var marks = cm.state.sublimeBookmarks || (cm.state.sublimeBookmarks = []);\n    for (var i = 0; i < ranges.length; i++) {\n      var from = ranges[i].from(), to = ranges[i].to();\n      var found = ranges[i].empty() ? cm.findMarksAt(from) : cm.findMarks(from, to);\n      for (var j = 0; j < found.length; j++) {\n        if (found[j].sublimeBookmark) {\n          found[j].clear();\n          for (var k = 0; k < marks.length; k++)\n            if (marks[k] == found[j])\n              marks.splice(k--, 1);\n          break;\n        }\n      }\n      if (j == found.length)\n        marks.push(cm.markText(from, to, {sublimeBookmark: true, clearWhenEmpty: false}));\n    }\n  };\n\n  cmds.clearBookmarks = function(cm) {\n    var marks = cm.state.sublimeBookmarks;\n    if (marks) for (var i = 0; i < marks.length; i++) marks[i].clear();\n    marks.length = 0;\n  };\n\n  cmds.selectBookmarks = function(cm) {\n    var marks = cm.state.sublimeBookmarks, ranges = [];\n    if (marks) for (var i = 0; i < marks.length; i++) {\n      var found = marks[i].find();\n      if (!found)\n        marks.splice(i--, 0);\n      else\n        ranges.push({anchor: found.from, head: found.to});\n    }\n    if (ranges.length)\n      cm.setSelections(ranges, 0);\n  };\n\n  function modifyWordOrSelection(cm, mod) {\n    cm.operation(function() {\n      var ranges = cm.listSelections(), indices = [], replacements = [];\n      for (var i = 0; i < ranges.length; i++) {\n        var range = ranges[i];\n        if (range.empty()) { indices.push(i); replacements.push(\"\"); }\n        else replacements.push(mod(cm.getRange(range.from(), range.to())));\n      }\n      cm.replaceSelections(replacements, \"around\", \"case\");\n      for (var i = indices.length - 1, at; i >= 0; i--) {\n        var range = ranges[indices[i]];\n        if (at && CodeMirror.cmpPos(range.head, at) > 0) continue;\n        var word = wordAt(cm, range.head);\n        at = word.from;\n        cm.replaceRange(mod(word.word), word.from, word.to);\n      }\n    });\n  }\n\n  cmds.smartBackspace = function(cm) {\n    if (cm.somethingSelected()) return CodeMirror.Pass;\n\n    cm.operation(function() {\n      var cursors = cm.listSelections();\n      var indentUnit = cm.getOption(\"indentUnit\");\n\n      for (var i = cursors.length - 1; i >= 0; i--) {\n        var cursor = cursors[i].head;\n        var toStartOfLine = cm.getRange({line: cursor.line, ch: 0}, cursor);\n        var column = CodeMirror.countColumn(toStartOfLine, null, cm.getOption(\"tabSize\"));\n\n        // Delete by one character by default\n        var deletePos = cm.findPosH(cursor, -1, \"char\", false);\n\n        if (toStartOfLine && !/\\S/.test(toStartOfLine) && column % indentUnit == 0) {\n          var prevIndent = new Pos(cursor.line,\n            CodeMirror.findColumn(toStartOfLine, column - indentUnit, indentUnit));\n\n          // Smart delete only if we found a valid prevIndent location\n          if (prevIndent.ch != cursor.ch) deletePos = prevIndent;\n        }\n\n        cm.replaceRange(\"\", deletePos, cursor, \"+delete\");\n      }\n    });\n  };\n\n  cmds.delLineRight = function(cm) {\n    cm.operation(function() {\n      var ranges = cm.listSelections();\n      for (var i = ranges.length - 1; i >= 0; i--)\n        cm.replaceRange(\"\", ranges[i].anchor, Pos(ranges[i].to().line), \"+delete\");\n      cm.scrollIntoView();\n    });\n  };\n\n  cmds.upcaseAtCursor = function(cm) {\n    modifyWordOrSelection(cm, function(str) { return str.toUpperCase(); });\n  };\n  cmds.downcaseAtCursor = function(cm) {\n    modifyWordOrSelection(cm, function(str) { return str.toLowerCase(); });\n  };\n\n  cmds.setSublimeMark = function(cm) {\n    if (cm.state.sublimeMark) cm.state.sublimeMark.clear();\n    cm.state.sublimeMark = cm.setBookmark(cm.getCursor());\n  };\n  cmds.selectToSublimeMark = function(cm) {\n    var found = cm.state.sublimeMark && cm.state.sublimeMark.find();\n    if (found) cm.setSelection(cm.getCursor(), found);\n  };\n  cmds.deleteToSublimeMark = function(cm) {\n    var found = cm.state.sublimeMark && cm.state.sublimeMark.find();\n    if (found) {\n      var from = cm.getCursor(), to = found;\n      if (CodeMirror.cmpPos(from, to) > 0) { var tmp = to; to = from; from = tmp; }\n      cm.state.sublimeKilled = cm.getRange(from, to);\n      cm.replaceRange(\"\", from, to);\n    }\n  };\n  cmds.swapWithSublimeMark = function(cm) {\n    var found = cm.state.sublimeMark && cm.state.sublimeMark.find();\n    if (found) {\n      cm.state.sublimeMark.clear();\n      cm.state.sublimeMark = cm.setBookmark(cm.getCursor());\n      cm.setCursor(found);\n    }\n  };\n  cmds.sublimeYank = function(cm) {\n    if (cm.state.sublimeKilled != null)\n      cm.replaceSelection(cm.state.sublimeKilled, null, \"paste\");\n  };\n\n  cmds.showInCenter = function(cm) {\n    var pos = cm.cursorCoords(null, \"local\");\n    cm.scrollTo(null, (pos.top + pos.bottom) / 2 - cm.getScrollInfo().clientHeight / 2);\n  };\n\n  function getTarget(cm) {\n    var from = cm.getCursor(\"from\"), to = cm.getCursor(\"to\");\n    if (CodeMirror.cmpPos(from, to) == 0) {\n      var word = wordAt(cm, from);\n      if (!word.word) return;\n      from = word.from;\n      to = word.to;\n    }\n    return {from: from, to: to, query: cm.getRange(from, to), word: word};\n  }\n\n  function findAndGoTo(cm, forward) {\n    var target = getTarget(cm);\n    if (!target) return;\n    var query = target.query;\n    var cur = cm.getSearchCursor(query, forward ? target.to : target.from);\n\n    if (forward ? cur.findNext() : cur.findPrevious()) {\n      cm.setSelection(cur.from(), cur.to());\n    } else {\n      cur = cm.getSearchCursor(query, forward ? Pos(cm.firstLine(), 0)\n                                              : cm.clipPos(Pos(cm.lastLine())));\n      if (forward ? cur.findNext() : cur.findPrevious())\n        cm.setSelection(cur.from(), cur.to());\n      else if (target.word)\n        cm.setSelection(target.from, target.to);\n    }\n  };\n  cmds.findUnder = function(cm) { findAndGoTo(cm, true); };\n  cmds.findUnderPrevious = function(cm) { findAndGoTo(cm,false); };\n  cmds.findAllUnder = function(cm) {\n    var target = getTarget(cm);\n    if (!target) return;\n    var cur = cm.getSearchCursor(target.query);\n    var matches = [];\n    var primaryIndex = -1;\n    while (cur.findNext()) {\n      matches.push({anchor: cur.from(), head: cur.to()});\n      if (cur.from().line <= target.from.line && cur.from().ch <= target.from.ch)\n        primaryIndex++;\n    }\n    cm.setSelections(matches, primaryIndex);\n  };\n\n\n  var keyMap = CodeMirror.keyMap;\n  keyMap.macSublime = {\n    \"Cmd-Left\": \"goLineStartSmart\",\n    \"Shift-Tab\": \"indentLess\",\n    \"Shift-Ctrl-K\": \"deleteLine\",\n    \"Alt-Q\": \"wrapLines\",\n    \"Ctrl-Left\": \"goSubwordLeft\",\n    \"Ctrl-Right\": \"goSubwordRight\",\n    \"Ctrl-Alt-Up\": \"scrollLineUp\",\n    \"Ctrl-Alt-Down\": \"scrollLineDown\",\n    \"Cmd-L\": \"selectLine\",\n    \"Shift-Cmd-L\": \"splitSelectionByLine\",\n    \"Esc\": \"singleSelectionTop\",\n    \"Cmd-Enter\": \"insertLineAfter\",\n    \"Shift-Cmd-Enter\": \"insertLineBefore\",\n    \"Cmd-D\": \"selectNextOccurrence\",\n    \"Shift-Cmd-Space\": \"selectScope\",\n    \"Shift-Cmd-M\": \"selectBetweenBrackets\",\n    \"Cmd-M\": \"goToBracket\",\n    \"Cmd-Ctrl-Up\": \"swapLineUp\",\n    \"Cmd-Ctrl-Down\": \"swapLineDown\",\n    \"Cmd-/\": \"toggleCommentIndented\",\n    \"Cmd-J\": \"joinLines\",\n    \"Shift-Cmd-D\": \"duplicateLine\",\n    \"F5\": \"sortLines\",\n    \"Shift-F5\": \"reverseSortLines\",\n    \"Cmd-F5\": \"sortLinesInsensitive\",\n    \"Shift-Cmd-F5\": \"reverseSortLinesInsensitive\",\n    \"F2\": \"nextBookmark\",\n    \"Shift-F2\": \"prevBookmark\",\n    \"Cmd-F2\": \"toggleBookmark\",\n    \"Shift-Cmd-F2\": \"clearBookmarks\",\n    \"Alt-F2\": \"selectBookmarks\",\n    \"Backspace\": \"smartBackspace\",\n    \"Cmd-K Cmd-D\": \"skipAndSelectNextOccurrence\",\n    \"Cmd-K Cmd-K\": \"delLineRight\",\n    \"Cmd-K Cmd-U\": \"upcaseAtCursor\",\n    \"Cmd-K Cmd-L\": \"downcaseAtCursor\",\n    \"Cmd-K Cmd-Space\": \"setSublimeMark\",\n    \"Cmd-K Cmd-A\": \"selectToSublimeMark\",\n    \"Cmd-K Cmd-W\": \"deleteToSublimeMark\",\n    \"Cmd-K Cmd-X\": \"swapWithSublimeMark\",\n    \"Cmd-K Cmd-Y\": \"sublimeYank\",\n    \"Cmd-K Cmd-C\": \"showInCenter\",\n    \"Cmd-K Cmd-G\": \"clearBookmarks\",\n    \"Cmd-K Cmd-Backspace\": \"delLineLeft\",\n    \"Cmd-K Cmd-1\": \"foldAll\",\n    \"Cmd-K Cmd-0\": \"unfoldAll\",\n    \"Cmd-K Cmd-J\": \"unfoldAll\",\n    \"Ctrl-Shift-Up\": \"addCursorToPrevLine\",\n    \"Ctrl-Shift-Down\": \"addCursorToNextLine\",\n    \"Cmd-F3\": \"findUnder\",\n    \"Shift-Cmd-F3\": \"findUnderPrevious\",\n    \"Alt-F3\": \"findAllUnder\",\n    \"Shift-Cmd-[\": \"fold\",\n    \"Shift-Cmd-]\": \"unfold\",\n    \"Cmd-I\": \"findIncremental\",\n    \"Shift-Cmd-I\": \"findIncrementalReverse\",\n    \"Cmd-H\": \"replace\",\n    \"F3\": \"findNext\",\n    \"Shift-F3\": \"findPrev\",\n    \"fallthrough\": \"macDefault\"\n  };\n  CodeMirror.normalizeKeyMap(keyMap.macSublime);\n\n  keyMap.pcSublime = {\n    \"Shift-Tab\": \"indentLess\",\n    \"Shift-Ctrl-K\": \"deleteLine\",\n    \"Alt-Q\": \"wrapLines\",\n    \"Ctrl-T\": \"transposeChars\",\n    \"Alt-Left\": \"goSubwordLeft\",\n    \"Alt-Right\": \"goSubwordRight\",\n    \"Ctrl-Up\": \"scrollLineUp\",\n    \"Ctrl-Down\": \"scrollLineDown\",\n    \"Ctrl-L\": \"selectLine\",\n    \"Shift-Ctrl-L\": \"splitSelectionByLine\",\n    \"Esc\": \"singleSelectionTop\",\n    \"Ctrl-Enter\": \"insertLineAfter\",\n    \"Shift-Ctrl-Enter\": \"insertLineBefore\",\n    \"Ctrl-D\": \"selectNextOccurrence\",\n    \"Shift-Ctrl-Space\": \"selectScope\",\n    \"Shift-Ctrl-M\": \"selectBetweenBrackets\",\n    \"Ctrl-M\": \"goToBracket\",\n    \"Shift-Ctrl-Up\": \"swapLineUp\",\n    \"Shift-Ctrl-Down\": \"swapLineDown\",\n    \"Ctrl-/\": \"toggleCommentIndented\",\n    \"Ctrl-J\": \"joinLines\",\n    \"Shift-Ctrl-D\": \"duplicateLine\",\n    \"F9\": \"sortLines\",\n    \"Shift-F9\": \"reverseSortLines\",\n    \"Ctrl-F9\": \"sortLinesInsensitive\",\n    \"Shift-Ctrl-F9\": \"reverseSortLinesInsensitive\",\n    \"F2\": \"nextBookmark\",\n    \"Shift-F2\": \"prevBookmark\",\n    \"Ctrl-F2\": \"toggleBookmark\",\n    \"Shift-Ctrl-F2\": \"clearBookmarks\",\n    \"Alt-F2\": \"selectBookmarks\",\n    \"Backspace\": \"smartBackspace\",\n    \"Ctrl-K Ctrl-D\": \"skipAndSelectNextOccurrence\",\n    \"Ctrl-K Ctrl-K\": \"delLineRight\",\n    \"Ctrl-K Ctrl-U\": \"upcaseAtCursor\",\n    \"Ctrl-K Ctrl-L\": \"downcaseAtCursor\",\n    \"Ctrl-K Ctrl-Space\": \"setSublimeMark\",\n    \"Ctrl-K Ctrl-A\": \"selectToSublimeMark\",\n    \"Ctrl-K Ctrl-W\": \"deleteToSublimeMark\",\n    \"Ctrl-K Ctrl-X\": \"swapWithSublimeMark\",\n    \"Ctrl-K Ctrl-Y\": \"sublimeYank\",\n    \"Ctrl-K Ctrl-C\": \"showInCenter\",\n    \"Ctrl-K Ctrl-G\": \"clearBookmarks\",\n    \"Ctrl-K Ctrl-Backspace\": \"delLineLeft\",\n    \"Ctrl-K Ctrl-1\": \"foldAll\",\n    \"Ctrl-K Ctrl-0\": \"unfoldAll\",\n    \"Ctrl-K Ctrl-J\": \"unfoldAll\",\n    \"Ctrl-Alt-Up\": \"addCursorToPrevLine\",\n    \"Ctrl-Alt-Down\": \"addCursorToNextLine\",\n    \"Ctrl-F3\": \"findUnder\",\n    \"Shift-Ctrl-F3\": \"findUnderPrevious\",\n    \"Alt-F3\": \"findAllUnder\",\n    \"Shift-Ctrl-[\": \"fold\",\n    \"Shift-Ctrl-]\": \"unfold\",\n    \"Ctrl-I\": \"findIncremental\",\n    \"Shift-Ctrl-I\": \"findIncrementalReverse\",\n    \"Ctrl-H\": \"replace\",\n    \"F3\": \"findNext\",\n    \"Shift-F3\": \"findPrev\",\n    \"fallthrough\": \"pcDefault\"\n  };\n  CodeMirror.normalizeKeyMap(keyMap.pcSublime);\n\n  var mac = keyMap.default == keyMap.macDefault;\n  keyMap.sublime = mac ? keyMap.macSublime : keyMap.pcSublime;\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,GAAC,SAASA,GAAK;AAEXA,MAAIC,GAA4B,GAAEC,EAAAA,GAAyCC,EAAsC,CAAA;EAKpH,GAAE,SAASC,GAAY;AAGtB,QAAIC,IAAOD,EAAW,UAClBE,IAAMF,EAAW;AAGrB,aAASG,EAAeC,GAAKC,GAAOC,GAAK;AACvC,UAAIA,IAAM,KAAKD,EAAM,MAAM;AAAG,eAAOD,EAAI,QAAQF,EAAIG,EAAM,OAAO,CAAC,CAAC;AACpE,UAAIE,IAAOH,EAAI,QAAQC,EAAM,IAAI;AACjC,UAAIC,IAAM,KAAKD,EAAM,MAAME,EAAK;AAAQ,eAAOH,EAAI,QAAQF,EAAIG,EAAM,OAAO,GAAG,CAAC,CAAC;AAEjF,eADIG,IAAQ,SAASC,GAAMC,IAAWL,EAAM,IACnCM,IAAMD,GAAUE,IAAIN,IAAM,IAAI,IAAIC,EAAK,QAAQM,IAAI,GAAGF,KAAOC,GAAGD,KAAOL,GAAKO,KAAK;AACxF,YAAIC,IAAOP,EAAK,OAAOD,IAAM,IAAIK,IAAM,IAAIA,CAAG,GAC1CI,IAAMD,KAAQ,OAAOd,EAAW,WAAWc,CAAI,IAAI,MAAM;AAE7D,YADIC,KAAO,OAAOD,EAAK,YAAW,KAAMA,MAAMC,IAAM,MAChDP,KAAS;AACPO,eAAO,OAAOP,IAAQ,MAAMC,IAAOM,KAClCL,IAAWC,IAAML;iBACbE,KAAS,QACdC,KAAQM,GAAK;AAEf,cADIN,KAAQ,OAAOM,KAAO,OAAOT,IAAM,KAAGK,KACtCF,KAAQ,OAAOM,KAAO,OAAOT,IAAM;AACrC,gBAAIK,KAAOD,IAAW,GAAG;AAAED,kBAAO;AAAK;YAAA;AAClCE;AAEP;QAAA;MAAA;AAIN,aAAOT,EAAIG,EAAM,MAAMM,CAAG;IAC3B;AAxBQK,MAAAb,GAAA,gBAAA;AA0BT,aAASc,EAAYC,GAAIZ,GAAK;AAC5BY,QAAG,mBAAmB,SAASC,GAAO;AACpC,eAAID,EAAG,QAAQ,SAASA,EAAG,IAAI,UAAUC,EAAM,MAAO,IAC7ChB,EAAee,EAAG,KAAKC,EAAM,MAAMb,CAAG,IAEtCA,IAAM,IAAIa,EAAM,KAAI,IAAKA,EAAM,GAAA;MAC9C,CAAK;IACF;AAPQH,MAAAC,GAAA,aAAA,GASThB,EAAK,gBAAgB,SAASiB,GAAI;AAAED,QAAYC,GAAI,EAAE;IAAA,GACtDjB,EAAK,iBAAiB,SAASiB,GAAI;AAAED,QAAYC,GAAI,CAAC;IAAA,GAEtDjB,EAAK,eAAe,SAASiB,GAAI;AAC/B,UAAIE,IAAOF,EAAG,cAAA;AACd,UAAI,CAACA,EAAG,kBAAA,GAAqB;AAC3B,YAAIG,IAAoBH,EAAG,aAAaE,EAAK,MAAMA,EAAK,cAAc,OAAO;AACzEF,UAAG,UAAA,EAAY,QAAQG,KACzBH,EAAG,YAAY,UAAU;MAAA;AAE7BA,QAAG,SAAS,MAAME,EAAK,MAAMF,EAAG,kBAAiB,CAAE;IACvD,GACEjB,EAAK,iBAAiB,SAASiB,GAAI;AACjC,UAAIE,IAAOF,EAAG,cAAA;AACd,UAAI,CAACA,EAAG,kBAAA,GAAqB;AAC3B,YAAII,IAAiBJ,EAAG,aAAaE,EAAK,KAAK,OAAO,IAAE;AACpDF,UAAG,UAAA,EAAY,QAAQI,KACzBJ,EAAG,YAAY,YAAY;MAAA;AAE/BA,QAAG,SAAS,MAAME,EAAK,MAAMF,EAAG,kBAAiB,CAAE;IACvD,GAEEjB,EAAK,uBAAuB,SAASiB,GAAI;AAEvC,eADIK,IAASL,EAAG,eAAc,GAAIM,IAAa,CAAA,GACtCX,IAAI,GAAGA,IAAIU,EAAO,QAAQV;AAEjC,iBADIY,IAAOF,EAAOV,CAAC,EAAE,KAAI,GAAIa,IAAKH,EAAOV,CAAC,EAAE,GAAA,GACnCN,IAAOkB,EAAK,MAAMlB,KAAQmB,EAAG,MAAM,EAAEnB;AACtCmB,YAAG,OAAOD,EAAK,QAAQlB,KAAQmB,EAAG,QAAQA,EAAG,MAAM,KACvDF,EAAW,KAAK;YAAC,QAAQjB,KAAQkB,EAAK,OAAOA,IAAOvB,EAAIK,GAAM,CAAC;YAC9C,MAAMA,KAAQmB,EAAG,OAAOA,IAAKxB,EAAIK,CAAI;UAAC,CAAC;AAE9DW,QAAG,cAAcM,GAAY,CAAC;IAClC,GAEEvB,EAAK,qBAAqB,SAASiB,GAAI;AACrC,UAAIC,IAAQD,EAAG,eAAgB,EAAC,CAAC;AACjCA,QAAG,aAAaC,EAAM,QAAQA,EAAM,MAAM,EAAC,QAAQ,MAAK,CAAC;IAC7D,GAEElB,EAAK,aAAa,SAASiB,GAAI;AAE7B,eADIK,IAASL,EAAG,eAAc,GAAIS,IAAW,CAAA,GACpCd,IAAI,GAAGA,IAAIU,EAAO,QAAQV,KAAK;AACtC,YAAIM,IAAQI,EAAOV,CAAC;AACpBc,UAAS,KAAK;UAAC,QAAQzB,EAAIiB,EAAM,KAAI,EAAG,MAAM,CAAC;UAChC,MAAMjB,EAAIiB,EAAM,GAAE,EAAG,OAAO,GAAG,CAAC;QAAC,CAAC;MAAA;AAEnDD,QAAG,cAAcS,CAAQ;IAC7B;AAEE,aAASC,EAAWV,GAAIW,GAAO;AAC7B,UAAIX,EAAG,WAAA;AAAc,eAAOlB,EAAW;AACvCkB,QAAG,UAAU,WAAW;AAEtB,iBADIY,IAAMZ,EAAG,eAAA,EAAiB,QAAQa,IAAe,CAAE,GAAEC,IAAO,IACvD,IAAI,GAAG,IAAIF,GAAK,KAAK;AAC5B,cAAIG,IAAOf,EAAG,eAAgB,EAAC,CAAC,EAAE;AAClC,cAAI,EAAAe,EAAK,QAAQD,IACjB;AAAA,gBAAIE,IAAKhC,EAAI+B,EAAK,QAAQJ,IAAQ,IAAI,IAAI,CAAC;AAC3CX,cAAG,aAAa;GAAMgB,GAAI,MAAM,aAAa,GAC7ChB,EAAG,WAAWgB,EAAG,MAAM,MAAM,IAAI,GACjCH,EAAa,KAAK,EAAC,MAAMG,GAAI,QAAQA,EAAE,CAAC,GACxCF,IAAOC,EAAK,OAAO;UAAA;QAAA;AAErBf,UAAG,cAAca,CAAY;MACnC,CAAK,GACDb,EAAG,YAAY,YAAY;IAC5B;AAhBQF,MAAAY,GAAA,YAAA,GAkBT3B,EAAK,kBAAkB,SAASiB,GAAI;AAAE,aAAOU,EAAWV,GAAI,KAAK;IAAA,GAEjEjB,EAAK,mBAAmB,SAASiB,GAAI;AAAE,aAAOU,EAAWV,GAAI,IAAI;IAAA;AAEjE,aAASiB,GAAOjB,GAAIP,GAAK;AAEvB,eADIN,IAAQM,EAAI,IAAIyB,IAAM/B,GAAOE,IAAOW,EAAG,QAAQP,EAAI,IAAI,GACpDN,KAASL,EAAW,WAAWO,EAAK,OAAOF,IAAQ,CAAC,CAAC;AAAG,UAAEA;AACjE,aAAO+B,IAAM7B,EAAK,UAAUP,EAAW,WAAWO,EAAK,OAAO6B,CAAG,CAAC;AAAG,UAAEA;AACvE,aAAO,EAAC,MAAMlC,EAAIS,EAAI,MAAMN,CAAK,GAAG,IAAIH,EAAIS,EAAI,MAAMyB,CAAG,GAAG,MAAM7B,EAAK,MAAMF,GAAO+B,CAAG,EAAC;IACzF;AALQpB,MAAAmB,IAAA,QAAA,GAOTlC,EAAK,uBAAuB,SAASiB,GAAI;AACvC,UAAIO,IAAOP,EAAG,UAAU,MAAM,GAAGQ,IAAKR,EAAG,UAAU,IAAI,GACnDmB,IAAWnB,EAAG,MAAM,uBAAuBA,EAAG,IAAI;AACtD,UAAIlB,EAAW,OAAOyB,GAAMC,CAAE,KAAK,GAAG;AACpC,YAAIY,IAAOH,GAAOjB,GAAIO,CAAI;AAC1B,YAAI,CAACa,EAAK;AAAM;AAChBpB,UAAG,aAAaoB,EAAK,MAAMA,EAAK,EAAE,GAClCD,IAAW;MAAA,OACN;AACL,YAAIE,IAAOrB,EAAG,SAASO,GAAMC,CAAE,GAC3Bc,IAAQH,IAAW,IAAI,OAAO,QAAQE,IAAO,KAAK,IAAIA,GACtDE,IAAMvB,EAAG,gBAAgBsB,GAAOd,CAAE,GAClCgB,IAAQD,EAAI,SAAA;AAKhB,YAJKC,MACHD,IAAMvB,EAAG,gBAAgBsB,GAAOtC,EAAIgB,EAAG,UAAS,GAAI,CAAC,CAAC,GACtDwB,IAAQD,EAAI,SAAA,IAEV,CAACC,KAASC,EAAgBzB,EAAG,eAAgB,GAAEuB,EAAI,KAAA,GAAQA,EAAI,GAAE,CAAE;AAAG;AAC1EvB,UAAG,aAAauB,EAAI,KAAM,GAAEA,EAAI,GAAE,CAAE;MAAA;AAElCJ,YACFnB,EAAG,MAAM,sBAAsBA,EAAG,IAAI;IAC5C,GAEEjB,EAAK,8BAA8B,SAASiB,GAAI;AAC9C,UAAI0B,IAAa1B,EAAG,UAAU,QAAQ,GAAG2B,IAAW3B,EAAG,UAAU,MAAM;AACvEjB,QAAK,qBAAqBiB,CAAE,GACxBlB,EAAW,OAAO4C,GAAYC,CAAQ,KAAK,KAC7C3B,EAAG,IAAI,cAAcA,EAAG,IAAI,eAAgB,EACvC,OAAO,SAAU4B,GAAK;AACrB,eAAOA,EAAI,UAAUF,KAAcE,EAAI,QAAQD;MAChD,CAAA,CAAC;IAET;AAED,aAASE,EAAqB7B,GAAIZ,GAAK;AAErC,eADIiB,IAASL,EAAG,eAAc,GAAI8B,IAAY,CAAA,GACrCnC,IAAI,GAAGA,IAAIU,EAAO,QAAQV,KAAK;AACtC,YAAIM,IAAQI,EAAOV,CAAC,GAChBoC,IAAY/B,EAAG;UACfC,EAAM;UAAQb;UAAK;UAAQa,EAAM,OAAO;QAAU,GAClD+B,IAAUhC,EAAG;UACbC,EAAM;UAAMb;UAAK;UAAQa,EAAM,KAAK;QAAU;AAClD8B,UAAU,aAAa9B,EAAM,OAAO,cAAc,OAC9CA,EAAM,OAAO,aAAaD,EAAG,aAAaC,EAAM,QAAQ,KAAK,EAAE,MACnE+B,EAAQ,aAAa/B,EAAM,KAAK,cAAc,OAC1CA,EAAM,KAAK,aAAaD,EAAG,aAAaC,EAAM,MAAM,KAAK,EAAE;AAC/D,YAAIgC,IAAW,EAAC,QAAQF,GAAW,MAAMC,EAAO;AAChDF,UAAU,KAAK7B,CAAK,GACpB6B,EAAU,KAAKG,CAAQ;MAAA;AAEzBjC,QAAG,cAAc8B,CAAS;IAC3B;AAjBQhC,MAAA+B,GAAA,sBAAA,GAkBT9C,EAAK,sBAAsB,SAASiB,GAAI;AAAE6B,QAAqB7B,GAAI,EAAE;IAAA,GACrEjB,EAAK,sBAAsB,SAASiB,GAAI;AAAE6B,QAAqB7B,GAAI,CAAC;IAAA;AAEpE,aAASyB,EAAgBpB,GAAQE,GAAMC,GAAI;AACzC,eAASb,IAAI,GAAGA,IAAIU,EAAO,QAAQV;AACjC,YAAIb,EAAW,OAAOuB,EAAOV,CAAC,EAAE,KAAM,GAAEY,CAAI,KAAK,KAC7CzB,EAAW,OAAOuB,EAAOV,CAAC,EAAE,GAAE,GAAIa,CAAE,KAAK;AAAG,iBAAO;AACzD,aAAO;IACR;AALQV,MAAA2B,GAAA,iBAAA;AAOT,QAAIS,IAAS;AACb,aAASC,EAAsBnC,GAAI;AAEjC,eADIK,IAASL,EAAG,eAAgB,GAAE8B,IAAY,CAAE,GACvCnC,IAAI,GAAGA,IAAIU,EAAO,QAAQV,KAAK;AACtC,YAAIM,IAAQI,EAAOV,CAAC,GAAGF,IAAMQ,EAAM,MAAMmC,IAAUpC,EAAG,eAAeP,GAAK,EAAE;AAC5E,YAAI,CAAC2C;AAAS,iBAAO;AACrB,mBAAS;AACP,cAAIC,IAAUrC,EAAG,eAAeP,GAAK,CAAC;AACtC,cAAI,CAAC4C;AAAS,mBAAO;AACrB,cAAIA,EAAQ,MAAMH,EAAO,OAAOA,EAAO,QAAQE,EAAQ,EAAE,IAAI,CAAC,GAAG;AAC/D,gBAAI5C,IAAWR,EAAIoD,EAAQ,IAAI,MAAMA,EAAQ,IAAI,KAAK,CAAC;AACvD,gBAAItD,EAAW,OAAOU,GAAUS,EAAM,KAAM,CAAA,KAAK,KAC7CnB,EAAW,OAAOuD,EAAQ,KAAKpC,EAAM,GAAE,CAAE,KAAK,GAAA;AAEhD,kBADAmC,IAAUpC,EAAG,eAAeoC,EAAQ,KAAK,EAAE,GACvC,CAACA;AAAS,uBAAO;YAAA,OAChB;AACLN,gBAAU,KAAK,EAAC,QAAQtC,GAAU,MAAM6C,EAAQ,IAAG,CAAC;AACpD;YAAA;UAAA;AAGJ5C,cAAMT,EAAIqD,EAAQ,IAAI,MAAMA,EAAQ,IAAI,KAAK,CAAC;QAAA;MAAA;AAGlD,aAAArC,EAAG,cAAc8B,CAAS,GACnB;IACR;AAxBQhC,MAAAqC,GAAA,uBAAA,GA0BTpD,EAAK,cAAc,SAASiB,GAAI;AAC9BmC,QAAsBnC,CAAE,KAAKA,EAAG,YAAY,WAAW;IAC3D,GACEjB,EAAK,wBAAwB,SAASiB,GAAI;AACxC,UAAI,CAACmC,EAAsBnC,CAAE;AAAG,eAAOlB,EAAW;IACtD;AAEE,aAASwD,EAAS/C,GAAM;AACtB,aAAQA,IAAc,kBAAkB,KAAKA,CAAI,IAAIA,IAAO,SAA7C;IAChB;AAFQO,MAAAwC,GAAA,UAAA,GAITvD,EAAK,cAAc,SAASiB,GAAI;AAC9BA,QAAG,mBAAmB,SAASC,GAAO;AACpC,YAAIL,IAAOI,EAAG,eAAeC,EAAM,MAAM,GAAGqC,EAAStC,EAAG,eAAeC,EAAM,IAAI,CAAC,CAAC;AACnF,YAAIL,KAAQd,EAAW,OAAOc,EAAK,KAAKK,EAAM,IAAI,KAAK;AAAG,iBAAOL,EAAK;AACtE,YAAI2C,IAAOvC,EAAG,eAAeC,EAAM,MAAM,IAAIqC,EAAStC,EAAG,eAAehB,EAAIiB,EAAM,KAAK,MAAMA,EAAM,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC;AACjH,eAAOsC,KAAQvD,EAAIuD,EAAK,IAAI,MAAMA,EAAK,IAAI,KAAK,CAAC,KAAKtC,EAAM;MAClE,CAAK;IACL,GAEElB,EAAK,aAAa,SAASiB,GAAI;AAC7B,UAAIA,EAAG,WAAA;AAAc,eAAOlB,EAAW;AAEvC,eADIuB,IAASL,EAAG,eAAgB,GAAEwC,IAAc,CAAE,GAAExB,IAAKhB,EAAG,UAAW,IAAG,GAAGyC,IAAU,CAAA,GAC9E,IAAI,GAAG,IAAIpC,EAAO,QAAQ,KAAK;AACtC,YAAIJ,IAAQI,EAAO,CAAC,GAAGE,IAAON,EAAM,KAAM,EAAC,OAAO,GAAGO,IAAKP,EAAM,GAAE,EAAG;AACrEwC,UAAQ,KAAK;UAAC,QAAQzD,EAAIiB,EAAM,OAAO,OAAO,GAAGA,EAAM,OAAO,EAAE;UAClD,MAAMjB,EAAIiB,EAAM,KAAK,OAAO,GAAGA,EAAM,KAAK,EAAE;QAAC,CAAC,GACxDA,EAAM,GAAI,EAAC,MAAM,KAAK,CAACA,EAAM,MAAA,KAAS,EAAEO,GACxCD,IAAOS,IAAIwB,EAAY,KAAKjC,GAAMC,CAAE,IAC/BgC,EAAY,WAAQA,EAAYA,EAAY,SAAS,CAAC,IAAIhC,IACnEQ,IAAKR;MAAA;AAEPR,QAAG,UAAU,WAAW;AACtB,iBAASL,IAAI,GAAGA,IAAI6C,EAAY,QAAQ7C,KAAK,GAAG;AAC9C,cAAIY,IAAOiC,EAAY7C,CAAC,GAAGa,IAAKgC,EAAY7C,IAAI,CAAC,GAC7CN,IAAOW,EAAG,QAAQO,CAAI;AAC1BP,YAAG,aAAa,IAAIhB,EAAIuB,GAAM,CAAC,GAAGvB,EAAIuB,IAAO,GAAG,CAAC,GAAG,WAAW,GAC3DC,IAAKR,EAAG,SAAU,IACpBA,EAAG,aAAa;IAAOX,GAAML,EAAIgB,EAAG,SAAA,CAAU,GAAG,MAAM,WAAW,IAElEA,EAAG,aAAaX,IAAO;GAAML,EAAIwB,GAAI,CAAC,GAAG,MAAM,WAAW;QAAA;AAE9DR,UAAG,cAAcyC,CAAO,GACxBzC,EAAG,eAAc;MACvB,CAAK;IACL,GAEEjB,EAAK,eAAe,SAASiB,GAAI;AAC/B,UAAIA,EAAG,WAAA;AAAc,eAAOlB,EAAW;AAEvC,eADIuB,IAASL,EAAG,eAAc,GAAIwC,IAAc,CAAA,GAAIxB,IAAKhB,EAAG,SAAU,IAAG,GAChEL,IAAIU,EAAO,SAAS,GAAGV,KAAK,GAAGA,KAAK;AAC3C,YAAIM,IAAQI,EAAOV,CAAC,GAAGY,IAAON,EAAM,GAAI,EAAC,OAAO,GAAGO,IAAKP,EAAM,KAAI,EAAG;AACjEA,UAAM,GAAI,EAAC,MAAM,KAAK,CAACA,EAAM,MAAA,KAASM,KACtCA,IAAOS,IAAIwB,EAAY,KAAKjC,GAAMC,CAAE,IAC/BgC,EAAY,WAAQA,EAAYA,EAAY,SAAS,CAAC,IAAIhC,IACnEQ,IAAKR;MAAA;AAEPR,QAAG,UAAU,WAAW;AACtB,iBAASL,IAAI6C,EAAY,SAAS,GAAG7C,KAAK,GAAGA,KAAK,GAAG;AACnD,cAAIY,IAAOiC,EAAY7C,CAAC,GAAGa,IAAKgC,EAAY7C,IAAI,CAAC,GAC7CN,IAAOW,EAAG,QAAQO,CAAI;AACtBA,eAAQP,EAAG,SAAU,IACvBA,EAAG,aAAa,IAAIhB,EAAIuB,IAAO,CAAC,GAAGvB,EAAIuB,CAAI,GAAG,WAAW,IAEzDP,EAAG,aAAa,IAAIhB,EAAIuB,GAAM,CAAC,GAAGvB,EAAIuB,IAAO,GAAG,CAAC,GAAG,WAAW,GACjEP,EAAG,aAAaX,IAAO;GAAML,EAAIwB,GAAI,CAAC,GAAG,MAAM,WAAW;QAAA;AAE5DR,UAAG,eAAc;MACvB,CAAK;IACL,GAEEjB,EAAK,wBAAwB,SAASiB,GAAI;AACxCA,QAAG,cAAc,EAAE,QAAQ,KAAM,CAAA;IAClC,GAEDjB,EAAK,YAAY,SAASiB,GAAI;AAE5B,eADIK,IAASL,EAAG,eAAc,GAAI0C,IAAS,CAAA,GAClC/C,IAAI,GAAGA,IAAIU,EAAO,QAAQV,KAAK;AAGtC,iBAFIM,IAAQI,EAAOV,CAAC,GAAGY,IAAON,EAAM,KAAA,GAChCd,IAAQoB,EAAK,MAAMW,IAAMjB,EAAM,GAAI,EAAC,MACjCN,IAAIU,EAAO,SAAS,KAAKA,EAAOV,IAAI,CAAC,EAAE,KAAM,EAAC,QAAQuB;AAC3DA,cAAMb,EAAO,EAAEV,CAAC,EAAE,GAAE,EAAG;AACzB+C,UAAO,KAAK,EAAC,OAAOvD,GAAO,KAAK+B,GAAK,QAAQ,CAACjB,EAAM,MAAA,KAAWM,EAAI,CAAC;MAAA;AAEtEP,QAAG,UAAU,WAAW;AAEtB,iBADI2C,IAAS,GAAGtC,IAAS,CAAA,GAChBV,IAAI,GAAGA,IAAI+C,EAAO,QAAQ/C,KAAK;AAGtC,mBAFIiD,IAAMF,EAAO/C,CAAC,GACdkD,IAASD,EAAI,UAAU5D,EAAI4D,EAAI,OAAO,OAAOD,GAAQC,EAAI,OAAO,EAAE,GAAG7B,GAChE1B,IAAOuD,EAAI,OAAOvD,KAAQuD,EAAI,KAAKvD,KAAQ;AAClD,gBAAIyD,IAASzD,IAAOsD;AAChBtD,iBAAQuD,EAAI,QAAK7B,IAAO/B,EAAI8D,GAAQ9C,EAAG,QAAQ8C,CAAM,EAAE,SAAS,CAAC,IACjEA,IAAS9C,EAAG,SAAA,MACdA,EAAG,aAAa,KAAKhB,EAAI8D,CAAM,GAAG9D,EAAI8D,IAAS,GAAG,OAAO,KAAK9C,EAAG,QAAQ8C,IAAS,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,GAChG,EAAEH;UAAA;AAGNtC,YAAO,KAAK,EAAC,QAAQwC,KAAU9B,GAAM,MAAMA,EAAI,CAAC;QAAA;AAElDf,UAAG,cAAcK,GAAQ,CAAC;MAChC,CAAK;IACL,GAEEtB,EAAK,gBAAgB,SAASiB,GAAI;AAChCA,QAAG,UAAU,WAAW;AAEtB,iBADI+C,IAAa/C,EAAG,eAAc,EAAG,QAC5BL,IAAI,GAAGA,IAAIoD,GAAYpD,KAAK;AACnC,cAAIM,IAAQD,EAAG,eAAgB,EAACL,CAAC;AAC7BM,YAAM,MAAO,IACfD,EAAG,aAAaA,EAAG,QAAQC,EAAM,KAAK,IAAI,IAAI;GAAMjB,EAAIiB,EAAM,KAAK,MAAM,CAAC,CAAC,IAE3ED,EAAG,aAAaA,EAAG,SAASC,EAAM,KAAI,GAAIA,EAAM,GAAE,CAAE,GAAGA,EAAM,KAAM,CAAA;QAAA;AAEvED,UAAG,eAAc;MACvB,CAAK;IACL;AAGE,aAASgD,EAAUhD,GAAIiD,GAAeC,GAAW;AAC/C,UAAIlD,EAAG,WAAA;AAAc,eAAOlB,EAAW;AAEvC,eADIuB,IAASL,EAAG,eAAgB,GAAEmD,IAAS,CAAE,GAAEC,GACtCzD,IAAI,GAAGA,IAAIU,EAAO,QAAQV,KAAK;AACtC,YAAIM,IAAQI,EAAOV,CAAC;AACpB,YAAI,CAAAM,EAAM,MAAK,GAEf;AAAA,mBADIM,IAAON,EAAM,KAAA,EAAO,MAAMO,IAAKP,EAAM,GAAI,EAAC,MACvCN,IAAIU,EAAO,SAAS,KAAKA,EAAOV,IAAI,CAAC,EAAE,KAAM,EAAC,QAAQa;AAC3DA,gBAAKH,EAAO,EAAEV,CAAC,EAAE,GAAE,EAAG;AACnBU,YAAOV,CAAC,EAAE,GAAE,EAAG,MAAIa,KACxB2C,EAAO,KAAK5C,GAAMC,CAAE;QAAA;MAAA;AAElB2C,QAAO,SAAQC,IAAW,OACzBD,EAAO,KAAKnD,EAAG,UAAW,GAAEA,EAAG,SAAQ,CAAE,GAE9CA,EAAG,UAAU,WAAW;AAEtB,iBADIK,IAAS,CAAA,GACJV,IAAI,GAAGA,IAAIwD,EAAO,QAAQxD,KAAK,GAAG;AACzC,cAAIY,IAAO4C,EAAOxD,CAAC,GAAGa,IAAK2C,EAAOxD,IAAI,CAAC,GACnCR,IAAQH,EAAIuB,GAAM,CAAC,GAAGW,IAAMlC,EAAIwB,CAAE,GAClC6C,IAAQrD,EAAG,SAASb,GAAO+B,GAAK,KAAK;AACrC+B,cACFI,EAAM,KAAK,SAASC,GAAGC,GAAG;AAAE,mBAAOD,IAAIC,IAAI,CAACL,IAAYI,KAAKC,IAAI,IAAIL;UAAY,CAAA,IAEjFG,EAAM,KAAK,SAASC,GAAGC,GAAG;AACxB,gBAAIC,IAAKF,EAAE,YAAW,GAAIG,IAAKF,EAAE,YAAA;AACjC,mBAAIC,KAAMC,MAAMH,IAAIE,GAAID,IAAIE,IACrBH,IAAIC,IAAI,CAACL,IAAYI,KAAKC,IAAI,IAAIL;UACrD,CAAW,GACHlD,EAAG,aAAaqD,GAAOlE,GAAO+B,CAAG,GAC7BkC,KAAU/C,EAAO,KAAK,EAAC,QAAQlB,GAAO,MAAMH,EAAIwB,IAAK,GAAG,CAAC,EAAC,CAAC;QAAA;AAE7D4C,aAAUpD,EAAG,cAAcK,GAAQ,CAAC;MAC9C,CAAK;IACF;AAlCQP,MAAAkD,GAAA,WAAA,GAoCTjE,EAAK,YAAY,SAASiB,GAAI;AAAEgD,QAAUhD,GAAI,MAAM,CAAC;IAAA,GACrDjB,EAAK,mBAAmB,SAASiB,GAAI;AAAEgD,QAAUhD,GAAI,MAAM,EAAE;IAAA,GAC7DjB,EAAK,uBAAuB,SAASiB,GAAI;AAAEgD,QAAUhD,GAAI,OAAO,CAAC;IAAA,GACjEjB,EAAK,8BAA8B,SAASiB,GAAI;AAAEgD,QAAUhD,GAAI,OAAO,EAAE;IAAA,GAEzEjB,EAAK,eAAe,SAASiB,GAAI;AAC/B,UAAI0D,IAAQ1D,EAAG,MAAM;AACrB,UAAI0D;AAAO,eAAOA,EAAM,UAAQ;AAC9B,cAAIC,IAAUD,EAAM,MAAA,GAChBlC,IAAQmC,EAAQ,KAAA;AACpB,cAAInC;AACF,mBAAAkC,EAAM,KAAKC,CAAO,GACX3D,EAAG,aAAawB,EAAM,MAAMA,EAAM,EAAE;QAAA;IAGnD,GAEEzC,EAAK,eAAe,SAASiB,GAAI;AAC/B,UAAI0D,IAAQ1D,EAAG,MAAM;AACrB,UAAI0D;AAAO,eAAOA,EAAM,UAAQ;AAC9BA,YAAM,QAAQA,EAAM,IAAK,CAAA;AACzB,cAAIlC,IAAQkC,EAAMA,EAAM,SAAS,CAAC,EAAE,KAAA;AACpC,cAAI,CAAClC;AACHkC,cAAM,IAAG;;AAET,mBAAO1D,EAAG,aAAawB,EAAM,MAAMA,EAAM,EAAE;QAAA;IAEnD,GAEEzC,EAAK,iBAAiB,SAASiB,GAAI;AAGjC,eAFIK,IAASL,EAAG,eAAA,GACZ0D,IAAQ1D,EAAG,MAAM,qBAAqBA,EAAG,MAAM,mBAAmB,CAAA,IAC7DL,IAAI,GAAGA,IAAIU,EAAO,QAAQV,KAAK;AAGtC,iBAFIY,IAAOF,EAAOV,CAAC,EAAE,KAAI,GAAIa,IAAKH,EAAOV,CAAC,EAAE,GAAA,GACxC6B,IAAQnB,EAAOV,CAAC,EAAE,MAAO,IAAGK,EAAG,YAAYO,CAAI,IAAIP,EAAG,UAAUO,GAAMC,CAAE,GACnEoD,IAAI,GAAGA,IAAIpC,EAAM,QAAQoC;AAChC,cAAIpC,EAAMoC,CAAC,EAAE,iBAAiB;AAC5BpC,cAAMoC,CAAC,EAAE,MAAA;AACT,qBAASC,IAAI,GAAGA,IAAIH,EAAM,QAAQG;AAC5BH,gBAAMG,CAAC,KAAKrC,EAAMoC,CAAC,KACrBF,EAAM,OAAOG,KAAK,CAAC;AACvB;UAAA;AAGAD,aAAKpC,EAAM,UACbkC,EAAM,KAAK1D,EAAG,SAASO,GAAMC,GAAI,EAAC,iBAAiB,MAAM,gBAAgB,MAAK,CAAC,CAAC;MAAA;IAExF,GAEEzB,EAAK,iBAAiB,SAASiB,GAAI;AACjC,UAAI0D,IAAQ1D,EAAG,MAAM;AACrB,UAAI0D;AAAO,iBAAS/D,IAAI,GAAGA,IAAI+D,EAAM,QAAQ/D;AAAK+D,YAAM/D,CAAC,EAAE,MAAK;AAChE+D,QAAM,SAAS;IACnB,GAEE3E,EAAK,kBAAkB,SAASiB,GAAI;AAClC,UAAI0D,IAAQ1D,EAAG,MAAM,kBAAkBK,IAAS,CAAA;AAChD,UAAIqD;AAAO,iBAAS/D,IAAI,GAAGA,IAAI+D,EAAM,QAAQ/D,KAAK;AAChD,cAAI6B,IAAQkC,EAAM/D,CAAC,EAAE,KAAI;AACpB6B,cAGHnB,EAAO,KAAK,EAAC,QAAQmB,EAAM,MAAM,MAAMA,EAAM,GAAE,CAAC,IAFhDkC,EAAM,OAAO/D,KAAK,CAAC;QAAA;AAInBU,QAAO,UACTL,EAAG,cAAcK,GAAQ,CAAC;IAChC;AAEE,aAASyD,EAAsB9D,GAAItB,GAAK;AACtCsB,QAAG,UAAU,WAAW;AAEtB,iBADIK,IAASL,EAAG,eAAc,GAAI+D,IAAU,CAAE,GAAEC,IAAe,CAAA,GACtD,IAAI,GAAG,IAAI3D,EAAO,QAAQ,KAAK;AACtC,cAAIJ,IAAQI,EAAO,CAAC;AAChBJ,YAAM,MAAA,KAAW8D,EAAQ,KAAK,CAAC,GAAGC,EAAa,KAAK,EAAE,KACrDA,EAAa,KAAKtF,EAAIsB,EAAG,SAASC,EAAM,KAAI,GAAIA,EAAM,GAAI,CAAA,CAAC,CAAC;QAAA;AAEnED,UAAG,kBAAkBgE,GAAc,UAAU,MAAM;AACnD,iBAAS,IAAID,EAAQ,SAAS,GAAG/C,GAAI,KAAK,GAAG,KAAK;AAChD,cAAIf,IAAQI,EAAO0D,EAAQ,CAAC,CAAC;AAC7B,cAAI,EAAA/C,KAAMlC,EAAW,OAAOmB,EAAM,MAAMe,CAAE,IAAI,IAC9C;AAAA,gBAAII,IAAOH,GAAOjB,GAAIC,EAAM,IAAI;AAChCe,gBAAKI,EAAK,MACVpB,EAAG,aAAatB,EAAI0C,EAAK,IAAI,GAAGA,EAAK,MAAMA,EAAK,EAAE;UAAA;QAAA;MAE1D,CAAK;IACF;AAjBQtB,MAAAgE,GAAA,uBAAA,GAmBT/E,EAAK,iBAAiB,SAASiB,GAAI;AACjC,UAAIA,EAAG,kBAAiB;AAAI,eAAOlB,EAAW;AAE9CkB,QAAG,UAAU,WAAW;AAItB,iBAHIiE,IAAUjE,EAAG,eAAA,GACbkE,IAAalE,EAAG,UAAU,YAAY,GAEjCL,IAAIsE,EAAQ,SAAS,GAAGtE,KAAK,GAAGA,KAAK;AAC5C,cAAIwE,IAASF,EAAQtE,CAAC,EAAE,MACpByE,IAAgBpE,EAAG,SAAS,EAAC,MAAMmE,EAAO,MAAM,IAAI,EAAC,GAAGA,CAAM,GAC9DE,IAASvF,EAAW,YAAYsF,GAAe,MAAMpE,EAAG,UAAU,SAAS,CAAC,GAG5EsE,IAAYtE,EAAG,SAASmE,GAAQ,IAAI,QAAQ,KAAK;AAErD,cAAIC,KAAiB,CAAC,KAAK,KAAKA,CAAa,KAAKC,IAASH,KAAc,GAAG;AAC1E,gBAAIK,IAAa,IAAIvF;cAAImF,EAAO;cAC9BrF,EAAW,WAAWsF,GAAeC,IAASH,GAAYA,CAAU;YAAC;AAGnEK,cAAW,MAAMJ,EAAO,OAAIG,IAAYC;UAAA;AAG9CvE,YAAG,aAAa,IAAIsE,GAAWH,GAAQ,SAAS;QAAA;MAExD,CAAK;IACL,GAEEpF,EAAK,eAAe,SAASiB,GAAI;AAC/BA,QAAG,UAAU,WAAW;AAEtB,iBADIK,IAASL,EAAG,eAAA,GACPL,IAAIU,EAAO,SAAS,GAAGV,KAAK,GAAGA;AACtCK,YAAG,aAAa,IAAIK,EAAOV,CAAC,EAAE,QAAQX,EAAIqB,EAAOV,CAAC,EAAE,GAAE,EAAG,IAAI,GAAG,SAAS;AAC3EK,UAAG,eAAc;MACvB,CAAK;IACL,GAEEjB,EAAK,iBAAiB,SAASiB,GAAI;AACjC8D,QAAsB9D,GAAI,SAASwE,GAAK;AAAE,eAAOA,EAAI,YAAW;MAAG,CAAE;IACzE,GACEzF,EAAK,mBAAmB,SAASiB,GAAI;AACnC8D,QAAsB9D,GAAI,SAASwE,GAAK;AAAE,eAAOA,EAAI,YAAW;MAAG,CAAE;IACzE,GAEEzF,EAAK,iBAAiB,SAASiB,GAAI;AAC7BA,QAAG,MAAM,eAAaA,EAAG,MAAM,YAAY,MAAA,GAC/CA,EAAG,MAAM,cAAcA,EAAG,YAAYA,EAAG,UAAS,CAAE;IACxD,GACEjB,EAAK,sBAAsB,SAASiB,GAAI;AACtC,UAAIwB,IAAQxB,EAAG,MAAM,eAAeA,EAAG,MAAM,YAAY,KAAA;AACrDwB,WAAOxB,EAAG,aAAaA,EAAG,UAAS,GAAIwB,CAAK;IACpD,GACEzC,EAAK,sBAAsB,SAASiB,GAAI;AACtC,UAAIwB,IAAQxB,EAAG,MAAM,eAAeA,EAAG,MAAM,YAAY,KAAA;AACzD,UAAIwB,GAAO;AACT,YAAIjB,IAAOP,EAAG,UAAS,GAAIQ,IAAKgB;AAChC,YAAI1C,EAAW,OAAOyB,GAAMC,CAAE,IAAI,GAAG;AAAE,cAAIiE,IAAMjE;AAAIA,cAAKD,GAAMA,IAAOkE;QAAA;AACvEzE,UAAG,MAAM,gBAAgBA,EAAG,SAASO,GAAMC,CAAE,GAC7CR,EAAG,aAAa,IAAIO,GAAMC,CAAE;MAAA;IAElC,GACEzB,EAAK,sBAAsB,SAASiB,GAAI;AACtC,UAAIwB,IAAQxB,EAAG,MAAM,eAAeA,EAAG,MAAM,YAAY,KAAA;AACrDwB,YACFxB,EAAG,MAAM,YAAY,MAAA,GACrBA,EAAG,MAAM,cAAcA,EAAG,YAAYA,EAAG,UAAS,CAAE,GACpDA,EAAG,UAAUwB,CAAK;IAExB,GACEzC,EAAK,cAAc,SAASiB,GAAI;AAC1BA,QAAG,MAAM,iBAAiB,QAC5BA,EAAG,iBAAiBA,EAAG,MAAM,eAAe,MAAM,OAAO;IAC/D,GAEEjB,EAAK,eAAe,SAASiB,GAAI;AAC/B,UAAIP,IAAMO,EAAG,aAAa,MAAM,OAAO;AACvCA,QAAG,SAAS,OAAOP,EAAI,MAAMA,EAAI,UAAU,IAAIO,EAAG,cAAe,EAAC,eAAe,CAAC;IACtF;AAEE,aAAS0E,EAAU1E,GAAI;AACrB,UAAIO,IAAOP,EAAG,UAAU,MAAM,GAAGQ,IAAKR,EAAG,UAAU,IAAI;AACvD,UAAIlB,EAAW,OAAOyB,GAAMC,CAAE,KAAK,GAAG;AACpC,YAAIY,IAAOH,GAAOjB,GAAIO,CAAI;AAC1B,YAAI,CAACa,EAAK;AAAM;AAChBb,YAAOa,EAAK,MACZZ,IAAKY,EAAK;MAAA;AAEZ,aAAO,EAAC,MAAMb,GAAM,IAAIC,GAAI,OAAOR,EAAG,SAASO,GAAMC,CAAE,GAAG,MAAMY,EAAI;IACrE;AATQtB,MAAA4E,GAAA,WAAA;AAWT,aAASC,EAAY3E,GAAI4E,GAAS;AAChC,UAAIC,IAASH,EAAU1E,CAAE;AACzB,UAAK6E,GACL;AAAA,YAAIvD,IAAQuD,EAAO,OACftD,IAAMvB,EAAG,gBAAgBsB,GAAOsD,IAAUC,EAAO,KAAKA,EAAO,IAAI;AAErE,SAAID,IAAUrD,EAAI,SAAU,IAAGA,EAAI,aAAY,KAC7CvB,EAAG,aAAauB,EAAI,KAAM,GAAEA,EAAI,GAAE,CAAE,KAEpCA,IAAMvB,EAAG,gBAAgBsB,GAAOsD,IAAU5F,EAAIgB,EAAG,UAAW,GAAE,CAAC,IACrBA,EAAG,QAAQhB,EAAIgB,EAAG,SAAQ,CAAE,CAAC,CAAC,IACpE4E,IAAUrD,EAAI,SAAQ,IAAKA,EAAI,aAAc,KAC/CvB,EAAG,aAAauB,EAAI,KAAM,GAAEA,EAAI,GAAE,CAAE,IAC7BsD,EAAO,QACd7E,EAAG,aAAa6E,EAAO,MAAMA,EAAO,EAAE;MAAA;IAE9C;AAhBW/E,MAAA6E,GAAA,aAAA,GAiBT5F,EAAK,YAAY,SAASiB,GAAI;AAAE2E,QAAY3E,GAAI,IAAI;IAAA,GACpDjB,EAAK,oBAAoB,SAASiB,GAAI;AAAE2E,QAAY3E,GAAG,KAAK;IAAA,GAC5DjB,EAAK,eAAe,SAASiB,GAAI;AAC/B,UAAI6E,IAASH,EAAU1E,CAAE;AACzB,UAAK6E,GAIL;AAAA,iBAHItD,IAAMvB,EAAG,gBAAgB6E,EAAO,KAAK,GACrCC,IAAU,CAAA,GACVC,IAAe,IACZxD,EAAI,SAAA;AACTuD,YAAQ,KAAK,EAAC,QAAQvD,EAAI,KAAA,GAAQ,MAAMA,EAAI,GAAI,EAAA,CAAC,GAC7CA,EAAI,KAAI,EAAG,QAAQsD,EAAO,KAAK,QAAQtD,EAAI,KAAM,EAAC,MAAMsD,EAAO,KAAK,MACtEE;AAEJ/E,UAAG,cAAc8E,GAASC,CAAY;MAAA;IAC1C;AAGE,QAAIC,IAASlG,EAAW;AACxBkG,MAAO,aAAa;MAClB,YAAY;MACZ,aAAa;MACb,gBAAgB;MAChB,SAAS;MACT,aAAa;MACb,cAAc;MACd,eAAe;MACf,iBAAiB;MACjB,SAAS;MACT,eAAe;MACf,KAAO;MACP,aAAa;MACb,mBAAmB;MACnB,SAAS;MACT,mBAAmB;MACnB,eAAe;MACf,SAAS;MACT,eAAe;MACf,iBAAiB;MACjB,SAAS;MACT,SAAS;MACT,eAAe;MACf,IAAM;MACN,YAAY;MACZ,UAAU;MACV,gBAAgB;MAChB,IAAM;MACN,YAAY;MACZ,UAAU;MACV,gBAAgB;MAChB,UAAU;MACV,WAAa;MACb,eAAe;MACf,eAAe;MACf,eAAe;MACf,eAAe;MACf,mBAAmB;MACnB,eAAe;MACf,eAAe;MACf,eAAe;MACf,eAAe;MACf,eAAe;MACf,eAAe;MACf,uBAAuB;MACvB,eAAe;MACf,eAAe;MACf,eAAe;MACf,iBAAiB;MACjB,mBAAmB;MACnB,UAAU;MACV,gBAAgB;MAChB,UAAU;MACV,eAAe;MACf,eAAe;MACf,SAAS;MACT,eAAe;MACf,SAAS;MACT,IAAM;MACN,YAAY;MACZ,aAAe;IACnB,GACElG,EAAW,gBAAgBkG,EAAO,UAAU,GAE5CA,EAAO,YAAY;MACjB,aAAa;MACb,gBAAgB;MAChB,SAAS;MACT,UAAU;MACV,YAAY;MACZ,aAAa;MACb,WAAW;MACX,aAAa;MACb,UAAU;MACV,gBAAgB;MAChB,KAAO;MACP,cAAc;MACd,oBAAoB;MACpB,UAAU;MACV,oBAAoB;MACpB,gBAAgB;MAChB,UAAU;MACV,iBAAiB;MACjB,mBAAmB;MACnB,UAAU;MACV,UAAU;MACV,gBAAgB;MAChB,IAAM;MACN,YAAY;MACZ,WAAW;MACX,iBAAiB;MACjB,IAAM;MACN,YAAY;MACZ,WAAW;MACX,iBAAiB;MACjB,UAAU;MACV,WAAa;MACb,iBAAiB;MACjB,iBAAiB;MACjB,iBAAiB;MACjB,iBAAiB;MACjB,qBAAqB;MACrB,iBAAiB;MACjB,iBAAiB;MACjB,iBAAiB;MACjB,iBAAiB;MACjB,iBAAiB;MACjB,iBAAiB;MACjB,yBAAyB;MACzB,iBAAiB;MACjB,iBAAiB;MACjB,iBAAiB;MACjB,eAAe;MACf,iBAAiB;MACjB,WAAW;MACX,iBAAiB;MACjB,UAAU;MACV,gBAAgB;MAChB,gBAAgB;MAChB,UAAU;MACV,gBAAgB;MAChB,UAAU;MACV,IAAM;MACN,YAAY;MACZ,aAAe;IACnB,GACElG,EAAW,gBAAgBkG,EAAO,SAAS;AAE3C,QAAIC,IAAMD,EAAO,WAAWA,EAAO;AACnCA,MAAO,UAAUC,IAAMD,EAAO,aAAaA,EAAO;EACpD,CAAC;;;;;;;;", "names": ["mod", "require$$0", "require$$1", "require$$2", "CodeMirror", "cmds", "Pos", "findPosSubword", "doc", "start", "dir", "line", "state", "type", "startPos", "pos", "e", "i", "next", "cat", "__name", "moveSubword", "cm", "range", "info", "visibleBottomLine", "visibleTopLine", "ranges", "lineRanges", "from", "to", "extended", "insertLine", "above", "len", "newSelection", "last", "head", "at", "wordAt", "end", "fullWord", "word", "text", "query", "cur", "found", "isSelectedRange", "prevAnchor", "prevHead", "sel", "addCursorToSelection", "newRang<PERSON>", "newAnchor", "newHead", "newRange", "mirror", "selectBetweenBrackets", "opening", "closing", "puncType", "prev", "linesToMove", "newSels", "joined", "offset", "obj", "anchor", "actual", "rangeCount", "sortLines", "caseSensitive", "direction", "to<PERSON>ort", "selected", "lines", "a", "b", "au", "bu", "marks", "current", "j", "k", "modifyWordOrSelection", "indices", "replacements", "cursors", "indentUnit", "cursor", "toStartOfLine", "column", "deletePos", "prevIndent", "str", "tmp", "get<PERSON><PERSON><PERSON>", "findAndGoTo", "forward", "target", "matches", "primaryIndex", "keyMap", "mac"]}