import {
  cu,
  hu
} from "./chunk-MEKKV4OY.js";
import "./chunk-AUZ3RYOM.js";

// node_modules/@graphiql/react/dist/foldgutter.es.js
var z = Object.defineProperty;
var u = (O, k) => z(O, "name", { value: k, configurable: true });
function j(O, k) {
  for (var i = 0; i < k.length; i++) {
    const s = k[i];
    if (typeof s != "string" && !Array.isArray(s)) {
      for (const p in s)
        if (p !== "default" && !(p in O)) {
          const w = Object.getOwnPropertyDescriptor(s, p);
          w && Object.defineProperty(O, p, w.get ? w : {
            enumerable: true,
            get: () => s[p]
          });
        }
    }
  }
  return Object.freeze(Object.defineProperty(O, Symbol.toStringTag, { value: "Module" }));
}
u(j, "_mergeNamespaces");
var D = { exports: {} };
var b = { exports: {} };
var U;
function V() {
  return U || (U = 1, function(O, k) {
    (function(i) {
      i(cu());
    })(function(i) {
      function s(e, o, f, a) {
        if (f && f.call) {
          var g = f;
          f = null;
        } else
          var g = v(e, f, "rangeFinder");
        typeof o == "number" && (o = i.Pos(o, 0));
        var y = v(e, f, "minFoldSize");
        function x(l) {
          var r = g(e, o);
          if (!r || r.to.line - r.from.line < y)
            return null;
          if (a === "fold")
            return r;
          for (var c = e.findMarksAt(r.from), h = 0; h < c.length; ++h)
            if (c[h].__isFold) {
              if (!l)
                return null;
              r.cleared = true, c[h].clear();
            }
          return r;
        }
        u(x, "getRange");
        var d = x(true);
        if (v(e, f, "scanUp"))
          for (; !d && o.line > e.firstLine(); )
            o = i.Pos(o.line - 1, 0), d = x(false);
        if (!(!d || d.cleared || a === "unfold")) {
          var t = p(e, f, d);
          i.on(t, "mousedown", function(l) {
            n.clear(), i.e_preventDefault(l);
          });
          var n = e.markText(d.from, d.to, {
            replacedWith: t,
            clearOnEnter: v(e, f, "clearOnEnter"),
            __isFold: true
          });
          n.on("clear", function(l, r) {
            i.signal(e, "unfold", e, l, r);
          }), i.signal(e, "fold", e, d.from, d.to);
        }
      }
      u(s, "doFold");
      function p(e, o, f) {
        var a = v(e, o, "widget");
        if (typeof a == "function" && (a = a(f.from, f.to)), typeof a == "string") {
          var g = document.createTextNode(a);
          a = document.createElement("span"), a.appendChild(g), a.className = "CodeMirror-foldmarker";
        } else
          a && (a = a.cloneNode(true));
        return a;
      }
      u(p, "makeWidget"), i.newFoldFunction = function(e, o) {
        return function(f, a) {
          s(f, a, { rangeFinder: e, widget: o });
        };
      }, i.defineExtension("foldCode", function(e, o, f) {
        s(this, e, o, f);
      }), i.defineExtension("isFolded", function(e) {
        for (var o = this.findMarksAt(e), f = 0; f < o.length; ++f)
          if (o[f].__isFold)
            return true;
      }), i.commands.toggleFold = function(e) {
        e.foldCode(e.getCursor());
      }, i.commands.fold = function(e) {
        e.foldCode(e.getCursor(), null, "fold");
      }, i.commands.unfold = function(e) {
        e.foldCode(e.getCursor(), { scanUp: false }, "unfold");
      }, i.commands.foldAll = function(e) {
        e.operation(function() {
          for (var o = e.firstLine(), f = e.lastLine(); o <= f; o++)
            e.foldCode(i.Pos(o, 0), { scanUp: false }, "fold");
        });
      }, i.commands.unfoldAll = function(e) {
        e.operation(function() {
          for (var o = e.firstLine(), f = e.lastLine(); o <= f; o++)
            e.foldCode(i.Pos(o, 0), { scanUp: false }, "unfold");
        });
      }, i.registerHelper("fold", "combine", function() {
        var e = Array.prototype.slice.call(arguments, 0);
        return function(o, f) {
          for (var a = 0; a < e.length; ++a) {
            var g = e[a](o, f);
            if (g)
              return g;
          }
        };
      }), i.registerHelper("fold", "auto", function(e, o) {
        for (var f = e.getHelpers(o, "fold"), a = 0; a < f.length; a++) {
          var g = f[a](e, o);
          if (g)
            return g;
        }
      });
      var w = {
        rangeFinder: i.fold.auto,
        widget: "↔",
        minFoldSize: 0,
        scanUp: false,
        clearOnEnter: true
      };
      i.defineOption("foldOptions", null);
      function v(e, o, f) {
        if (o && o[f] !== void 0)
          return o[f];
        var a = e.options.foldOptions;
        return a && a[f] !== void 0 ? a[f] : w[f];
      }
      u(v, "getOption"), i.defineExtension("foldOption", function(e, o) {
        return v(this, e, o);
      });
    });
  }()), b.exports;
}
u(V, "requireFoldcode");
(function(O, k) {
  (function(i) {
    i(cu(), V());
  })(function(i) {
    i.defineOption("foldGutter", false, function(t, n, l) {
      l && l != i.Init && (t.clearGutter(t.state.foldGutter.options.gutter), t.state.foldGutter = null, t.off("gutterClick", g), t.off("changes", y), t.off("viewportChange", x), t.off("fold", d), t.off("unfold", d), t.off("swapDoc", y)), n && (t.state.foldGutter = new p(w(n)), a(t), t.on("gutterClick", g), t.on("changes", y), t.on("viewportChange", x), t.on("fold", d), t.on("unfold", d), t.on("swapDoc", y));
    });
    var s = i.Pos;
    function p(t) {
      this.options = t, this.from = this.to = 0;
    }
    u(p, "State");
    function w(t) {
      return t === true && (t = {}), t.gutter == null && (t.gutter = "CodeMirror-foldgutter"), t.indicatorOpen == null && (t.indicatorOpen = "CodeMirror-foldgutter-open"), t.indicatorFolded == null && (t.indicatorFolded = "CodeMirror-foldgutter-folded"), t;
    }
    u(w, "parseOptions");
    function v(t, n) {
      for (var l = t.findMarks(s(n, 0), s(n + 1, 0)), r = 0; r < l.length; ++r)
        if (l[r].__isFold) {
          var c = l[r].find(-1);
          if (c && c.line === n)
            return l[r];
        }
    }
    u(v, "isFolded");
    function e(t) {
      if (typeof t == "string") {
        var n = document.createElement("div");
        return n.className = t + " CodeMirror-guttermarker-subtle", n;
      } else
        return t.cloneNode(true);
    }
    u(e, "marker");
    function o(t, n, l) {
      var r = t.state.foldGutter.options, c = n - 1, h = t.foldOption(r, "minFoldSize"), G = t.foldOption(r, "rangeFinder"), E = typeof r.indicatorFolded == "string" && f(r.indicatorFolded), S = typeof r.indicatorOpen == "string" && f(r.indicatorOpen);
      t.eachLine(n, l, function(T) {
        ++c;
        var _ = null, F = T.gutterMarkers;
        if (F && (F = F[r.gutter]), v(t, c)) {
          if (E && F && E.test(F.className))
            return;
          _ = e(r.indicatorFolded);
        } else {
          var A = s(c, 0), m = G && G(t, A);
          if (m && m.to.line - m.from.line >= h) {
            if (S && F && S.test(F.className))
              return;
            _ = e(r.indicatorOpen);
          }
        }
        !_ && !F || t.setGutterMarker(T, r.gutter, _);
      });
    }
    u(o, "updateFoldInfo");
    function f(t) {
      return new RegExp("(^|\\s)" + t + "(?:$|\\s)\\s*");
    }
    u(f, "classTest");
    function a(t) {
      var n = t.getViewport(), l = t.state.foldGutter;
      l && (t.operation(function() {
        o(t, n.from, n.to);
      }), l.from = n.from, l.to = n.to);
    }
    u(a, "updateInViewport");
    function g(t, n, l) {
      var r = t.state.foldGutter;
      if (r) {
        var c = r.options;
        if (l == c.gutter) {
          var h = v(t, n);
          h ? h.clear() : t.foldCode(s(n, 0), c);
        }
      }
    }
    u(g, "onGutterClick");
    function y(t) {
      var n = t.state.foldGutter;
      if (n) {
        var l = n.options;
        n.from = n.to = 0, clearTimeout(n.changeUpdate), n.changeUpdate = setTimeout(function() {
          a(t);
        }, l.foldOnChangeTimeSpan || 600);
      }
    }
    u(y, "onChange");
    function x(t) {
      var n = t.state.foldGutter;
      if (n) {
        var l = n.options;
        clearTimeout(n.changeUpdate), n.changeUpdate = setTimeout(function() {
          var r = t.getViewport();
          n.from == n.to || r.from - n.to > 20 || n.from - r.to > 20 ? a(t) : t.operation(function() {
            r.from < n.from && (o(t, r.from, n.from), n.from = r.from), r.to > n.to && (o(t, n.to, r.to), n.to = r.to);
          });
        }, l.updateViewportTimeSpan || 400);
      }
    }
    u(x, "onViewportChange");
    function d(t, n) {
      var l = t.state.foldGutter;
      if (l) {
        var r = n.line;
        r >= l.from && r < l.to && o(t, r, r + 1);
      }
    }
    u(d, "onFold");
  });
})();
var P = D.exports;
var C = hu(P);
var H = j({
  __proto__: null,
  default: C
}, [P]);
export {
  H as f
};
//# sourceMappingURL=foldgutter.es-NKOXVEC5.js.map
