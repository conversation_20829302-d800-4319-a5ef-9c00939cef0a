import {
  d
} from "./chunk-WIXYZC3K.js";
import "./chunk-KC5P2RGA.js";
import "./chunk-MEKKV4OY.js";
import {
  Position,
  getAutocompleteSuggestions
} from "./chunk-4LQY6QSN.js";
import "./chunk-HD22INE4.js";
import "./chunk-AUZ3RYOM.js";

// node_modules/@graphiql/react/dist/hint.es.js
d.registerHelper("hint", "graphql", (o, a) => {
  const { schema: i, externalFragments: c } = a;
  if (!i)
    return;
  const s = o.getCursor(), e = o.getTokenAt(s), l = e.type !== null && /"|\w/.test(e.string[0]) ? e.start : e.end, p = new Position(s.line, l), t = {
    list: getAutocompleteSuggestions(i, o.getValue(), p, e, c).map((n) => ({
      text: n.label,
      type: n.type,
      description: n.documentation,
      isDeprecated: n.isDeprecated,
      deprecationReason: n.deprecationReason
    })),
    from: { line: s.line, ch: l },
    to: { line: s.line, ch: e.end }
  };
  return t != null && t.list && t.list.length > 0 && (t.from = d.Pos(t.from.line, t.from.ch), t.to = d.Pos(t.to.line, t.to.ch), d.signal(o, "hasCompletion", o, t, e)), t;
});
//# sourceMappingURL=hint.es-36O6OW6O.js.map
