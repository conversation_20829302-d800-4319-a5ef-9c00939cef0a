{"version": 3, "sources": ["../../../../../node_modules/codemirror/addon/comment/comment.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n\n  var noOptions = {};\n  var nonWS = /[^\\s\\u00a0]/;\n  var Pos = CodeMirror.Pos, cmp = CodeMirror.cmpPos;\n\n  function firstNonWS(str) {\n    var found = str.search(nonWS);\n    return found == -1 ? 0 : found;\n  }\n\n  CodeMirror.commands.toggleComment = function(cm) {\n    cm.toggleComment();\n  };\n\n  CodeMirror.defineExtension(\"toggleComment\", function(options) {\n    if (!options) options = noOptions;\n    var cm = this;\n    var minLine = Infinity, ranges = this.listSelections(), mode = null;\n    for (var i = ranges.length - 1; i >= 0; i--) {\n      var from = ranges[i].from(), to = ranges[i].to();\n      if (from.line >= minLine) continue;\n      if (to.line >= minLine) to = Pos(minLine, 0);\n      minLine = from.line;\n      if (mode == null) {\n        if (cm.uncomment(from, to, options)) mode = \"un\";\n        else { cm.lineComment(from, to, options); mode = \"line\"; }\n      } else if (mode == \"un\") {\n        cm.uncomment(from, to, options);\n      } else {\n        cm.lineComment(from, to, options);\n      }\n    }\n  });\n\n  // Rough heuristic to try and detect lines that are part of multi-line string\n  function probablyInsideString(cm, pos, line) {\n    return /\\bstring\\b/.test(cm.getTokenTypeAt(Pos(pos.line, 0))) && !/^[\\'\\\"\\`]/.test(line)\n  }\n\n  function getMode(cm, pos) {\n    var mode = cm.getMode()\n    return mode.useInnerComments === false || !mode.innerMode ? mode : cm.getModeAt(pos)\n  }\n\n  CodeMirror.defineExtension(\"lineComment\", function(from, to, options) {\n    if (!options) options = noOptions;\n    var self = this, mode = getMode(self, from);\n    var firstLine = self.getLine(from.line);\n    if (firstLine == null || probablyInsideString(self, from, firstLine)) return;\n\n    var commentString = options.lineComment || mode.lineComment;\n    if (!commentString) {\n      if (options.blockCommentStart || mode.blockCommentStart) {\n        options.fullLines = true;\n        self.blockComment(from, to, options);\n      }\n      return;\n    }\n\n    var end = Math.min(to.ch != 0 || to.line == from.line ? to.line + 1 : to.line, self.lastLine() + 1);\n    var pad = options.padding == null ? \" \" : options.padding;\n    var blankLines = options.commentBlankLines || from.line == to.line;\n\n    self.operation(function() {\n      if (options.indent) {\n        var baseString = null;\n        for (var i = from.line; i < end; ++i) {\n          var line = self.getLine(i);\n          var whitespace = line.slice(0, firstNonWS(line));\n          if (baseString == null || baseString.length > whitespace.length) {\n            baseString = whitespace;\n          }\n        }\n        for (var i = from.line; i < end; ++i) {\n          var line = self.getLine(i), cut = baseString.length;\n          if (!blankLines && !nonWS.test(line)) continue;\n          if (line.slice(0, cut) != baseString) cut = firstNonWS(line);\n          self.replaceRange(baseString + commentString + pad, Pos(i, 0), Pos(i, cut));\n        }\n      } else {\n        for (var i = from.line; i < end; ++i) {\n          if (blankLines || nonWS.test(self.getLine(i)))\n            self.replaceRange(commentString + pad, Pos(i, 0));\n        }\n      }\n    });\n  });\n\n  CodeMirror.defineExtension(\"blockComment\", function(from, to, options) {\n    if (!options) options = noOptions;\n    var self = this, mode = getMode(self, from);\n    var startString = options.blockCommentStart || mode.blockCommentStart;\n    var endString = options.blockCommentEnd || mode.blockCommentEnd;\n    if (!startString || !endString) {\n      if ((options.lineComment || mode.lineComment) && options.fullLines != false)\n        self.lineComment(from, to, options);\n      return;\n    }\n    if (/\\bcomment\\b/.test(self.getTokenTypeAt(Pos(from.line, 0)))) return\n\n    var end = Math.min(to.line, self.lastLine());\n    if (end != from.line && to.ch == 0 && nonWS.test(self.getLine(end))) --end;\n\n    var pad = options.padding == null ? \" \" : options.padding;\n    if (from.line > end) return;\n\n    self.operation(function() {\n      if (options.fullLines != false) {\n        var lastLineHasText = nonWS.test(self.getLine(end));\n        self.replaceRange(pad + endString, Pos(end));\n        self.replaceRange(startString + pad, Pos(from.line, 0));\n        var lead = options.blockCommentLead || mode.blockCommentLead;\n        if (lead != null) for (var i = from.line + 1; i <= end; ++i)\n          if (i != end || lastLineHasText)\n            self.replaceRange(lead + pad, Pos(i, 0));\n      } else {\n        var atCursor = cmp(self.getCursor(\"to\"), to) == 0, empty = !self.somethingSelected()\n        self.replaceRange(endString, to);\n        if (atCursor) self.setSelection(empty ? to : self.getCursor(\"from\"), to)\n        self.replaceRange(startString, from);\n      }\n    });\n  });\n\n  CodeMirror.defineExtension(\"uncomment\", function(from, to, options) {\n    if (!options) options = noOptions;\n    var self = this, mode = getMode(self, from);\n    var end = Math.min(to.ch != 0 || to.line == from.line ? to.line : to.line - 1, self.lastLine()), start = Math.min(from.line, end);\n\n    // Try finding line comments\n    var lineString = options.lineComment || mode.lineComment, lines = [];\n    var pad = options.padding == null ? \" \" : options.padding, didSomething;\n    lineComment: {\n      if (!lineString) break lineComment;\n      for (var i = start; i <= end; ++i) {\n        var line = self.getLine(i);\n        var found = line.indexOf(lineString);\n        if (found > -1 && !/comment/.test(self.getTokenTypeAt(Pos(i, found + 1)))) found = -1;\n        if (found == -1 && nonWS.test(line)) break lineComment;\n        if (found > -1 && nonWS.test(line.slice(0, found))) break lineComment;\n        lines.push(line);\n      }\n      self.operation(function() {\n        for (var i = start; i <= end; ++i) {\n          var line = lines[i - start];\n          var pos = line.indexOf(lineString), endPos = pos + lineString.length;\n          if (pos < 0) continue;\n          if (line.slice(endPos, endPos + pad.length) == pad) endPos += pad.length;\n          didSomething = true;\n          self.replaceRange(\"\", Pos(i, pos), Pos(i, endPos));\n        }\n      });\n      if (didSomething) return true;\n    }\n\n    // Try block comments\n    var startString = options.blockCommentStart || mode.blockCommentStart;\n    var endString = options.blockCommentEnd || mode.blockCommentEnd;\n    if (!startString || !endString) return false;\n    var lead = options.blockCommentLead || mode.blockCommentLead;\n    var startLine = self.getLine(start), open = startLine.indexOf(startString)\n    if (open == -1) return false\n    var endLine = end == start ? startLine : self.getLine(end)\n    var close = endLine.indexOf(endString, end == start ? open + startString.length : 0);\n    var insideStart = Pos(start, open + 1), insideEnd = Pos(end, close + 1)\n    if (close == -1 ||\n        !/comment/.test(self.getTokenTypeAt(insideStart)) ||\n        !/comment/.test(self.getTokenTypeAt(insideEnd)) ||\n        self.getRange(insideStart, insideEnd, \"\\n\").indexOf(endString) > -1)\n      return false;\n\n    // Avoid killing block comments completely outside the selection.\n    // Positions of the last startString before the start of the selection, and the first endString after it.\n    var lastStart = startLine.lastIndexOf(startString, from.ch);\n    var firstEnd = lastStart == -1 ? -1 : startLine.slice(0, from.ch).indexOf(endString, lastStart + startString.length);\n    if (lastStart != -1 && firstEnd != -1 && firstEnd + endString.length != from.ch) return false;\n    // Positions of the first endString after the end of the selection, and the last startString before it.\n    firstEnd = endLine.indexOf(endString, to.ch);\n    var almostLastStart = endLine.slice(to.ch).lastIndexOf(startString, firstEnd - to.ch);\n    lastStart = (firstEnd == -1 || almostLastStart == -1) ? -1 : to.ch + almostLastStart;\n    if (firstEnd != -1 && lastStart != -1 && lastStart != to.ch) return false;\n\n    self.operation(function() {\n      self.replaceRange(\"\", Pos(end, close - (pad && endLine.slice(close - pad.length, close) == pad ? pad.length : 0)),\n                        Pos(end, close + endString.length));\n      var openEnd = open + startString.length;\n      if (pad && startLine.slice(openEnd, openEnd + pad.length) == pad) openEnd += pad.length;\n      self.replaceRange(\"\", Pos(start, open), Pos(start, openEnd));\n      if (lead) for (var i = start + 1; i <= end; ++i) {\n        var line = self.getLine(i), found = line.indexOf(lead);\n        if (found == -1 || nonWS.test(line.slice(0, found))) continue;\n        var foundEnd = found + lead.length;\n        if (pad && line.slice(foundEnd, foundEnd + pad.length) == pad) foundEnd += pad.length;\n        self.replaceRange(\"\", Pos(i, found), Pos(i, foundEnd));\n      }\n    });\n    return true;\n  });\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,GAAC,SAASA,GAAK;AAEXA,MAAIC,GAA+B,CAAA;EAKtC,GAAE,SAASC,GAAY;AAGtB,QAAIC,IAAY,CAAA,GACZC,IAAQ,eACRC,IAAMH,EAAW,KAAKI,IAAMJ,EAAW;AAE3C,aAASK,EAAWC,GAAK;AACvB,UAAIC,IAAQD,EAAI,OAAOJ,CAAK;AAC5B,aAAOK,KAAS,KAAK,IAAIA;IAC1B;AAHQC,MAAAH,GAAA,YAAA,GAKTL,EAAW,SAAS,gBAAgB,SAASS,GAAI;AAC/CA,QAAG,cAAa;IACpB,GAEET,EAAW,gBAAgB,iBAAiB,SAASU,GAAS;AACvDA,YAASA,IAAUT;AAGxB,eAFIQ,IAAK,MACLE,IAAU,IAAA,GAAUC,IAAS,KAAK,eAAgB,GAAEC,IAAO,MACtDC,IAAIF,EAAO,SAAS,GAAGE,KAAK,GAAGA,KAAK;AAC3C,YAAIC,IAAOH,EAAOE,CAAC,EAAE,KAAI,GAAIE,IAAKJ,EAAOE,CAAC,EAAE,GAAA;AACxCC,UAAK,QAAQJ,MACbK,EAAG,QAAQL,MAASK,IAAKb,EAAIQ,GAAS,CAAC,IAC3CA,IAAUI,EAAK,MACXF,KAAQ,OACNJ,EAAG,UAAUM,GAAMC,GAAIN,CAAO,IAAGG,IAAO,QACrCJ,EAAG,YAAYM,GAAMC,GAAIN,CAAO,GAAGG,IAAO,UACxCA,KAAQ,OACjBJ,EAAG,UAAUM,GAAMC,GAAIN,CAAO,IAE9BD,EAAG,YAAYM,GAAMC,GAAIN,CAAO;MAAA;IAGxC,CAAG;AAGD,aAASO,EAAqBR,GAAIS,GAAKC,GAAM;AAC3C,aAAO,aAAa,KAAKV,EAAG,eAAeN,EAAIe,EAAI,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,KAAKC,CAAI;IACxF;AAFQX,MAAAS,GAAA,sBAAA;AAIT,aAASG,EAAQX,GAAIS,GAAK;AACxB,UAAIL,IAAOJ,EAAG,QAAS;AACvB,aAAOI,EAAK,qBAAqB,SAAS,CAACA,EAAK,YAAYA,IAAOJ,EAAG,UAAUS,CAAG;IACpF;AAHQV,MAAAY,GAAA,SAAA,GAKTpB,EAAW,gBAAgB,eAAe,SAASe,GAAMC,GAAIN,GAAS;AAC/DA,YAASA,IAAUT;AACxB,UAAIoB,IAAO,MAAMR,IAAOO,EAAQC,GAAMN,CAAI,GACtCO,IAAYD,EAAK,QAAQN,EAAK,IAAI;AACtC,UAAI,EAAAO,KAAa,QAAQL,EAAqBI,GAAMN,GAAMO,CAAS,IAEnE;AAAA,YAAIC,IAAgBb,EAAQ,eAAeG,EAAK;AAChD,YAAI,CAACU,GAAe;AAClB,WAAIb,EAAQ,qBAAqBG,EAAK,uBACpCH,EAAQ,YAAY,MACpBW,EAAK,aAAaN,GAAMC,GAAIN,CAAO;AAErC;QAAA;AAGF,YAAIc,IAAM,KAAK,IAAIR,EAAG,MAAM,KAAKA,EAAG,QAAQD,EAAK,OAAOC,EAAG,OAAO,IAAIA,EAAG,MAAMK,EAAK,SAAA,IAAa,CAAC,GAC9FI,IAAMf,EAAQ,WAAW,OAAO,MAAMA,EAAQ,SAC9CgB,IAAahB,EAAQ,qBAAqBK,EAAK,QAAQC,EAAG;AAE9DK,UAAK,UAAU,WAAW;AACxB,cAAIX,EAAQ,QAAQ;AAElB,qBADIiB,IAAa,MACRb,IAAIC,EAAK,MAAMD,IAAIU,GAAK,EAAEV,GAAG;AACpC,kBAAIK,IAAOE,EAAK,QAAQP,CAAC,GACrBc,IAAaT,EAAK,MAAM,GAAGd,EAAWc,CAAI,CAAC;AAC/C,eAAIQ,KAAc,QAAQA,EAAW,SAASC,EAAW,YACvDD,IAAaC;YAAA;AAGjB,qBAASd,IAAIC,EAAK,MAAMD,IAAIU,GAAK,EAAEV,GAAG;AACpC,kBAAIK,IAAOE,EAAK,QAAQP,CAAC,GAAGe,IAAMF,EAAW;AACzC,eAACD,KAAc,CAACxB,EAAM,KAAKiB,CAAI,MAC/BA,EAAK,MAAM,GAAGU,CAAG,KAAKF,MAAYE,IAAMxB,EAAWc,CAAI,IAC3DE,EAAK,aAAaM,IAAaJ,IAAgBE,GAAKtB,EAAIW,GAAG,CAAC,GAAGX,EAAIW,GAAGe,CAAG,CAAC;YAAA;UAAA;AAG5E,qBAASf,IAAIC,EAAK,MAAMD,IAAIU,GAAK,EAAEV;AACjC,eAAIY,KAAcxB,EAAM,KAAKmB,EAAK,QAAQP,CAAC,CAAC,MAC1CO,EAAK,aAAaE,IAAgBE,GAAKtB,EAAIW,GAAG,CAAC,CAAC;QAG5D,CAAK;MAAA;IACL,CAAG,GAEDd,EAAW,gBAAgB,gBAAgB,SAASe,GAAMC,GAAIN,GAAS;AAChEA,YAASA,IAAUT;AACxB,UAAIoB,IAAO,MAAMR,IAAOO,EAAQC,GAAMN,CAAI,GACtCe,IAAcpB,EAAQ,qBAAqBG,EAAK,mBAChDkB,IAAYrB,EAAQ,mBAAmBG,EAAK;AAChD,UAAI,CAACiB,KAAe,CAACC,GAAW;AAC9B,SAAKrB,EAAQ,eAAeG,EAAK,gBAAgBH,EAAQ,aAAa,SACpEW,EAAK,YAAYN,GAAMC,GAAIN,CAAO;AACpC;MAAA;AAEF,UAAI,CAAA,cAAc,KAAKW,EAAK,eAAelB,EAAIY,EAAK,MAAM,CAAC,CAAC,CAAC,GAE7D;AAAA,YAAIS,IAAM,KAAK,IAAIR,EAAG,MAAMK,EAAK,SAAQ,CAAE;AACvCG,aAAOT,EAAK,QAAQC,EAAG,MAAM,KAAKd,EAAM,KAAKmB,EAAK,QAAQG,CAAG,CAAC,KAAG,EAAEA;AAEvE,YAAIC,IAAMf,EAAQ,WAAW,OAAO,MAAMA,EAAQ;AAC9CK,UAAK,OAAOS,KAEhBH,EAAK,UAAU,WAAW;AACxB,cAAIX,EAAQ,aAAa,OAAO;AAC9B,gBAAIsB,IAAkB9B,EAAM,KAAKmB,EAAK,QAAQG,CAAG,CAAC;AAClDH,cAAK,aAAaI,IAAMM,GAAW5B,EAAIqB,CAAG,CAAC,GAC3CH,EAAK,aAAaS,IAAcL,GAAKtB,EAAIY,EAAK,MAAM,CAAC,CAAC;AACtD,gBAAIkB,IAAOvB,EAAQ,oBAAoBG,EAAK;AAC5C,gBAAIoB,KAAQ;AAAM,uBAASnB,IAAIC,EAAK,OAAO,GAAGD,KAAKU,GAAK,EAAEV;AACxD,iBAAIA,KAAKU,KAAOQ,MACdX,EAAK,aAAaY,IAAOR,GAAKtB,EAAIW,GAAG,CAAC,CAAC;UAAA,OACtC;AACL,gBAAIoB,IAAW9B,EAAIiB,EAAK,UAAU,IAAI,GAAGL,CAAE,KAAK,GAAGmB,IAAQ,CAACd,EAAK,kBAAmB;AACpFA,cAAK,aAAaU,GAAWf,CAAE,GAC3BkB,KAAUb,EAAK,aAAac,IAAQnB,IAAKK,EAAK,UAAU,MAAM,GAAGL,CAAE,GACvEK,EAAK,aAAaS,GAAaf,CAAI;UAAA;QAE3C,CAAK;MAAA;IACL,CAAG,GAEDf,EAAW,gBAAgB,aAAa,SAASe,GAAMC,GAAIN,GAAS;AAC7DA,YAASA,IAAUT;AACxB,UAAIoB,IAAO,MAAMR,IAAOO,EAAQC,GAAMN,CAAI,GACtCS,IAAM,KAAK,IAAIR,EAAG,MAAM,KAAKA,EAAG,QAAQD,EAAK,OAAOC,EAAG,OAAOA,EAAG,OAAO,GAAGK,EAAK,SAAQ,CAAE,GAAGe,IAAQ,KAAK,IAAIrB,EAAK,MAAMS,CAAG,GAG5Ha,IAAa3B,EAAQ,eAAeG,EAAK,aAAayB,IAAQ,CAAA,GAC9Db,IAAMf,EAAQ,WAAW,OAAO,MAAMA,EAAQ,SAAS6B;AAC3DC,SAAa;AACX,YAAI,CAACH;AAAY,gBAAMG;AACvB,iBAAS1B,IAAIsB,GAAOtB,KAAKU,GAAK,EAAEV,GAAG;AACjC,cAAIK,IAAOE,EAAK,QAAQP,CAAC,GACrBP,IAAQY,EAAK,QAAQkB,CAAU;AAGnC,cAFI9B,IAAQ,MAAM,CAAC,UAAU,KAAKc,EAAK,eAAelB,EAAIW,GAAGP,IAAQ,CAAC,CAAC,CAAC,MAAGA,IAAQ,KAC/EA,KAAS,MAAML,EAAM,KAAKiB,CAAI,KAC9BZ,IAAQ,MAAML,EAAM,KAAKiB,EAAK,MAAM,GAAGZ,CAAK,CAAC;AAAG,kBAAMiC;AAC1DF,YAAM,KAAKnB,CAAI;QAAA;AAYjB,YAVAE,EAAK,UAAU,WAAW;AACxB,mBAASP,IAAIsB,GAAOtB,KAAKU,GAAK,EAAEV,GAAG;AACjC,gBAAIK,IAAOmB,EAAMxB,IAAIsB,CAAK,GACtBlB,IAAMC,EAAK,QAAQkB,CAAU,GAAGI,IAASvB,IAAMmB,EAAW;AAC1DnB,gBAAM,MACNC,EAAK,MAAMsB,GAAQA,IAAShB,EAAI,MAAM,KAAKA,MAAKgB,KAAUhB,EAAI,SAClEc,IAAe,MACflB,EAAK,aAAa,IAAIlB,EAAIW,GAAGI,CAAG,GAAGf,EAAIW,GAAG2B,CAAM,CAAC;UAAA;QAE3D,CAAO,GACGF;AAAc,iBAAO;MAAA;AAI3B,UAAIT,IAAcpB,EAAQ,qBAAqBG,EAAK,mBAChDkB,IAAYrB,EAAQ,mBAAmBG,EAAK;AAChD,UAAI,CAACiB,KAAe,CAACC;AAAW,eAAO;AACvC,UAAIE,IAAOvB,EAAQ,oBAAoBG,EAAK,kBACxC6B,IAAYrB,EAAK,QAAQe,CAAK,GAAGO,IAAOD,EAAU,QAAQZ,CAAW;AACzE,UAAIa,KAAQ;AAAI,eAAO;AACvB,UAAIC,IAAUpB,KAAOY,IAAQM,IAAYrB,EAAK,QAAQG,CAAG,GACrDqB,IAAQD,EAAQ,QAAQb,GAAWP,KAAOY,IAAQO,IAAOb,EAAY,SAAS,CAAC,GAC/EgB,IAAc3C,EAAIiC,GAAOO,IAAO,CAAC,GAAGI,IAAY5C,EAAIqB,GAAKqB,IAAQ,CAAC;AACtE,UAAIA,KAAS,MACT,CAAC,UAAU,KAAKxB,EAAK,eAAeyB,CAAW,CAAC,KAChD,CAAC,UAAU,KAAKzB,EAAK,eAAe0B,CAAS,CAAC,KAC9C1B,EAAK,SAASyB,GAAaC,GAAW;CAAI,EAAE,QAAQhB,CAAS,IAAI;AACnE,eAAO;AAIT,UAAIiB,IAAYN,EAAU,YAAYZ,GAAaf,EAAK,EAAE,GACtDkC,IAAWD,KAAa,KAAK,KAAKN,EAAU,MAAM,GAAG3B,EAAK,EAAE,EAAE,QAAQgB,GAAWiB,IAAYlB,EAAY,MAAM;AACnH,UAAIkB,KAAa,MAAMC,KAAY,MAAMA,IAAWlB,EAAU,UAAUhB,EAAK;AAAI,eAAO;AAExFkC,UAAWL,EAAQ,QAAQb,GAAWf,EAAG,EAAE;AAC3C,UAAIkC,IAAkBN,EAAQ,MAAM5B,EAAG,EAAE,EAAE,YAAYc,GAAamB,IAAWjC,EAAG,EAAE;AAEpF,aADAgC,IAAaC,KAAY,MAAMC,KAAmB,KAAM,KAAKlC,EAAG,KAAKkC,GACjED,KAAY,MAAMD,KAAa,MAAMA,KAAahC,EAAG,KAAW,SAEpEK,EAAK,UAAU,WAAW;AACxBA,UAAK;UAAa;UAAIlB,EAAIqB,GAAKqB,KAASpB,KAAOmB,EAAQ,MAAMC,IAAQpB,EAAI,QAAQoB,CAAK,KAAKpB,IAAMA,EAAI,SAAS,EAAE;UAC9FtB,EAAIqB,GAAKqB,IAAQd,EAAU,MAAM;QAAC;AACpD,YAAIoB,IAAUR,IAAOb,EAAY;AAGjC,YAFIL,KAAOiB,EAAU,MAAMS,GAASA,IAAU1B,EAAI,MAAM,KAAKA,MAAK0B,KAAW1B,EAAI,SACjFJ,EAAK,aAAa,IAAIlB,EAAIiC,GAAOO,CAAI,GAAGxC,EAAIiC,GAAOe,CAAO,CAAC,GACvDlB;AAAM,mBAASnB,IAAIsB,IAAQ,GAAGtB,KAAKU,GAAK,EAAEV,GAAG;AAC/C,gBAAIK,IAAOE,EAAK,QAAQP,CAAC,GAAGP,IAAQY,EAAK,QAAQc,CAAI;AACrD,gBAAI,EAAA1B,KAAS,MAAML,EAAM,KAAKiB,EAAK,MAAM,GAAGZ,CAAK,CAAC,IAClD;AAAA,kBAAI6C,IAAW7C,IAAQ0B,EAAK;AACxBR,mBAAON,EAAK,MAAMiC,GAAUA,IAAW3B,EAAI,MAAM,KAAKA,MAAK2B,KAAY3B,EAAI,SAC/EJ,EAAK,aAAa,IAAIlB,EAAIW,GAAGP,CAAK,GAAGJ,EAAIW,GAAGsC,CAAQ,CAAC;YAAA;UAAA;MAE7D,CAAK,GACM;IACX,CAAG;EACH,CAAC;;;;;;;;", "names": ["mod", "require$$0", "CodeMirror", "noOptions", "nonWS", "Pos", "cmp", "firstNonWS", "str", "found", "__name", "cm", "options", "minLine", "ranges", "mode", "i", "from", "to", "probablyInsideString", "pos", "line", "getMode", "self", "firstLine", "commentString", "end", "pad", "blankLines", "baseString", "whitespace", "cut", "startString", "endString", "lastLineHasText", "lead", "atCursor", "empty", "start", "lineString", "lines", "didSomething", "lineComment", "endPos", "startLine", "open", "endLine", "close", "insideStart", "insideEnd", "lastStart", "firstEnd", "almostLastStart", "openEnd", "foundEnd"]}