{"version": 3, "sources": ["../../../../../node_modules/codemirror/addon/dialog/dialog.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/LICENSE\n\n// Open simple dialogs on top of an editor. Relies on dialog.css.\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  function dialogDiv(cm, template, bottom) {\n    var wrap = cm.getWrapperElement();\n    var dialog;\n    dialog = wrap.appendChild(document.createElement(\"div\"));\n    if (bottom)\n      dialog.className = \"CodeMirror-dialog CodeMirror-dialog-bottom\";\n    else\n      dialog.className = \"CodeMirror-dialog CodeMirror-dialog-top\";\n\n    if (typeof template == \"string\") {\n      dialog.innerHTML = template;\n    } else { // Assuming it's a detached DOM element.\n      dialog.appendChild(template);\n    }\n    CodeMirror.addClass(wrap, 'dialog-opened');\n    return dialog;\n  }\n\n  function closeNotification(cm, newVal) {\n    if (cm.state.currentNotificationClose)\n      cm.state.currentNotificationClose();\n    cm.state.currentNotificationClose = newVal;\n  }\n\n  CodeMirror.defineExtension(\"openDialog\", function(template, callback, options) {\n    if (!options) options = {};\n\n    closeNotification(this, null);\n\n    var dialog = dialogDiv(this, template, options.bottom);\n    var closed = false, me = this;\n    function close(newVal) {\n      if (typeof newVal == 'string') {\n        inp.value = newVal;\n      } else {\n        if (closed) return;\n        closed = true;\n        CodeMirror.rmClass(dialog.parentNode, 'dialog-opened');\n        dialog.parentNode.removeChild(dialog);\n        me.focus();\n\n        if (options.onClose) options.onClose(dialog);\n      }\n    }\n\n    var inp = dialog.getElementsByTagName(\"input\")[0], button;\n    if (inp) {\n      inp.focus();\n\n      if (options.value) {\n        inp.value = options.value;\n        if (options.selectValueOnOpen !== false) {\n          inp.select();\n        }\n      }\n\n      if (options.onInput)\n        CodeMirror.on(inp, \"input\", function(e) { options.onInput(e, inp.value, close);});\n      if (options.onKeyUp)\n        CodeMirror.on(inp, \"keyup\", function(e) {options.onKeyUp(e, inp.value, close);});\n\n      CodeMirror.on(inp, \"keydown\", function(e) {\n        if (options && options.onKeyDown && options.onKeyDown(e, inp.value, close)) { return; }\n        if (e.keyCode == 27 || (options.closeOnEnter !== false && e.keyCode == 13)) {\n          inp.blur();\n          CodeMirror.e_stop(e);\n          close();\n        }\n        if (e.keyCode == 13) callback(inp.value, e);\n      });\n\n      if (options.closeOnBlur !== false) CodeMirror.on(dialog, \"focusout\", function (evt) {\n        if (evt.relatedTarget !== null) close();\n      });\n    } else if (button = dialog.getElementsByTagName(\"button\")[0]) {\n      CodeMirror.on(button, \"click\", function() {\n        close();\n        me.focus();\n      });\n\n      if (options.closeOnBlur !== false) CodeMirror.on(button, \"blur\", close);\n\n      button.focus();\n    }\n    return close;\n  });\n\n  CodeMirror.defineExtension(\"openConfirm\", function(template, callbacks, options) {\n    closeNotification(this, null);\n    var dialog = dialogDiv(this, template, options && options.bottom);\n    var buttons = dialog.getElementsByTagName(\"button\");\n    var closed = false, me = this, blurring = 1;\n    function close() {\n      if (closed) return;\n      closed = true;\n      CodeMirror.rmClass(dialog.parentNode, 'dialog-opened');\n      dialog.parentNode.removeChild(dialog);\n      me.focus();\n    }\n    buttons[0].focus();\n    for (var i = 0; i < buttons.length; ++i) {\n      var b = buttons[i];\n      (function(callback) {\n        CodeMirror.on(b, \"click\", function(e) {\n          CodeMirror.e_preventDefault(e);\n          close();\n          if (callback) callback(me);\n        });\n      })(callbacks[i]);\n      CodeMirror.on(b, \"blur\", function() {\n        --blurring;\n        setTimeout(function() { if (blurring <= 0) close(); }, 200);\n      });\n      CodeMirror.on(b, \"focus\", function() { ++blurring; });\n    }\n  });\n\n  /*\n   * openNotification\n   * Opens a notification, that can be closed with an optional timer\n   * (default 5000ms timer) and always closes on click.\n   *\n   * If a notification is opened while another is opened, it will close the\n   * currently opened one and open the new one immediately.\n   */\n  CodeMirror.defineExtension(\"openNotification\", function(template, options) {\n    closeNotification(this, close);\n    var dialog = dialogDiv(this, template, options && options.bottom);\n    var closed = false, doneTimer;\n    var duration = options && typeof options.duration !== \"undefined\" ? options.duration : 5000;\n\n    function close() {\n      if (closed) return;\n      closed = true;\n      clearTimeout(doneTimer);\n      CodeMirror.rmClass(dialog.parentNode, 'dialog-opened');\n      dialog.parentNode.removeChild(dialog);\n    }\n\n    CodeMirror.on(dialog, 'click', function(e) {\n      CodeMirror.e_preventDefault(e);\n      close();\n    });\n\n    if (duration)\n      doneTimer = setTimeout(close, duration);\n\n    return close;\n  });\n});\n"], "mappings": ";;;;;;;;;;;AAKA,KAAC,SAASA,GAAK;AAEXA,QAAIC,GAA+B,CAAA;IAKtC,GAAE,SAASC,GAAY;AACtB,eAASC,EAAUC,GAAIC,GAAUC,GAAQ;AACvC,YAAIC,IAAOH,EAAG,kBAAA,GACVI;AACJ,eAAAA,IAASD,EAAK,YAAY,SAAS,cAAc,KAAK,CAAC,GACnDD,IACFE,EAAO,YAAY,+CAEnBA,EAAO,YAAY,2CAEjB,OAAOH,KAAY,WACrBG,EAAO,YAAYH,IAEnBG,EAAO,YAAYH,CAAQ,GAE7BH,EAAW,SAASK,GAAM,eAAe,GAClCC;MACR;AAhBQC,QAAAN,GAAA,WAAA;AAkBT,eAASO,EAAkBN,GAAIO,GAAQ;AACjCP,UAAG,MAAM,4BACXA,EAAG,MAAM,yBAAA,GACXA,EAAG,MAAM,2BAA2BO;MACrC;AAJQF,QAAAC,GAAA,mBAAA,GAMTR,EAAW,gBAAgB,cAAc,SAASG,GAAUO,GAAUC,GAAS;AACxEA,cAASA,IAAU,CAAA,IAExBH,EAAkB,MAAM,IAAI;AAE5B,YAAIF,IAASL,EAAU,MAAME,GAAUQ,EAAQ,MAAM,GACjDC,IAAS,OAAOC,IAAK;AACzB,iBAASC,EAAML,GAAQ;AACrB,cAAI,OAAOA,KAAU;AACnBM,cAAI,QAAQN;eACP;AACL,gBAAIG;AAAQ;AACZA,gBAAS,MACTZ,EAAW,QAAQM,EAAO,YAAY,eAAe,GACrDA,EAAO,WAAW,YAAYA,CAAM,GACpCO,EAAG,MAAK,GAEJF,EAAQ,WAASA,EAAQ,QAAQL,CAAM;UAAA;QAE9C;AAZQC,UAAAO,GAAA,OAAA;AAcT,YAAIC,IAAMT,EAAO,qBAAqB,OAAO,EAAE,CAAC,GAAGU;AACnD,eAAID,KACFA,EAAI,MAAK,GAELJ,EAAQ,UACVI,EAAI,QAAQJ,EAAQ,OAChBA,EAAQ,sBAAsB,SAChCI,EAAI,OAAM,IAIVJ,EAAQ,WACVX,EAAW,GAAGe,GAAK,SAAS,SAASE,GAAG;AAAEN,YAAQ,QAAQM,GAAGF,EAAI,OAAOD,CAAK;QAAE,CAAC,GAC9EH,EAAQ,WACVX,EAAW,GAAGe,GAAK,SAAS,SAASE,GAAG;AAACN,YAAQ,QAAQM,GAAGF,EAAI,OAAOD,CAAK;QAAE,CAAC,GAEjFd,EAAW,GAAGe,GAAK,WAAW,SAASE,GAAG;AACpCN,eAAWA,EAAQ,aAAaA,EAAQ,UAAUM,GAAGF,EAAI,OAAOD,CAAK,OACrEG,EAAE,WAAW,MAAON,EAAQ,iBAAiB,SAASM,EAAE,WAAW,QACrEF,EAAI,KAAI,GACRf,EAAW,OAAOiB,CAAC,GACnBH,EAAAA,IAEEG,EAAE,WAAW,MAAIP,EAASK,EAAI,OAAOE,CAAC;QAClD,CAAO,GAEGN,EAAQ,gBAAgB,SAAOX,EAAW,GAAGM,GAAQ,YAAY,SAAUY,GAAK;AAC9EA,YAAI,kBAAkB,QAAMJ,EAAK;QAC7C,CAAO,MACQE,IAASV,EAAO,qBAAqB,QAAQ,EAAE,CAAC,OACzDN,EAAW,GAAGgB,GAAQ,SAAS,WAAW;AACxCF,YAAAA,GACAD,EAAG,MAAK;QAChB,CAAO,GAEGF,EAAQ,gBAAgB,SAAOX,EAAW,GAAGgB,GAAQ,QAAQF,CAAK,GAEtEE,EAAO,MAAK,IAEPF;MACX,CAAG,GAEDd,EAAW,gBAAgB,eAAe,SAASG,GAAUgB,GAAWR,GAAS;AAC/EH,UAAkB,MAAM,IAAI;AAC5B,YAAIF,IAASL,EAAU,MAAME,GAAUQ,KAAWA,EAAQ,MAAM,GAC5DS,IAAUd,EAAO,qBAAqB,QAAQ,GAC9CM,IAAS,OAAOC,IAAK,MAAMQ,IAAW;AAC1C,iBAASP,IAAQ;AACXF,gBACJA,IAAS,MACTZ,EAAW,QAAQM,EAAO,YAAY,eAAe,GACrDA,EAAO,WAAW,YAAYA,CAAM,GACpCO,EAAG,MAAK;QACT;AANQN,UAAAO,GAAA,OAAA,GAOTM,EAAQ,CAAC,EAAE,MAAA;AACX,iBAASE,IAAI,GAAGA,IAAIF,EAAQ,QAAQ,EAAEE,GAAG;AACvC,cAAIC,IAAIH,EAAQE,CAAC;AACjB,WAAC,SAASZ,GAAU;AAClBV,cAAW,GAAGuB,GAAG,SAAS,SAASN,GAAG;AACpCjB,gBAAW,iBAAiBiB,CAAC,GAC7BH,EAAAA,GACIJ,KAAUA,EAASG,CAAE;YACnC,CAAS;UACT,GAASM,EAAUG,CAAC,CAAC,GACftB,EAAW,GAAGuB,GAAG,QAAQ,WAAW;AAClC,cAAEF,GACF,WAAW,WAAW;AAAMA,mBAAY,KAAGP,EAAAA;YAAQ,GAAI,GAAG;UAClE,CAAO,GACDd,EAAW,GAAGuB,GAAG,SAAS,WAAW;AAAE,cAAEF;UAAS,CAAE;QAAA;MAE1D,CAAG,GAUDrB,EAAW,gBAAgB,oBAAoB,SAASG,GAAUQ,GAAS;AACzEH,UAAkB,MAAMM,CAAK;AAC7B,YAAIR,IAASL,EAAU,MAAME,GAAUQ,KAAWA,EAAQ,MAAM,GAC5DC,IAAS,OAAOY,GAChBC,IAAWd,KAAW,OAAOA,EAAQ,WAAa,MAAcA,EAAQ,WAAW;AAEvF,iBAASG,IAAQ;AACXF,gBACJA,IAAS,MACT,aAAaY,CAAS,GACtBxB,EAAW,QAAQM,EAAO,YAAY,eAAe,GACrDA,EAAO,WAAW,YAAYA,CAAM;QACrC;AANQ,eAAAC,EAAAO,GAAA,OAAA,GAQTd,EAAW,GAAGM,GAAQ,SAAS,SAASW,GAAG;AACzCjB,YAAW,iBAAiBiB,CAAC,GAC7BH,EAAAA;QACN,CAAK,GAEGW,MACFD,IAAY,WAAWV,GAAOW,CAAQ,IAEjCX;MACX,CAAG;IACH,CAAC;EAAA,EAAA,IAAA,EAAA;;;", "names": ["mod", "require$$0", "CodeMirror", "dialogDiv", "cm", "template", "bottom", "wrap", "dialog", "__name", "closeNotification", "newVal", "callback", "options", "closed", "me", "close", "inp", "button", "e", "evt", "callbacks", "buttons", "blurring", "i", "b", "doneTimer", "duration"]}