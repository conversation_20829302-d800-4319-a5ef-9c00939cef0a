{"version": 3, "sources": ["../../../../../@graphiql/codemirror-graphql/esm/utils/mode-factory.js", "../../../../../@graphiql/codemirror-graphql/esm/mode.js"], "sourcesContent": ["import { LexR<PERSON>, ParseRules, isIgnored, onlineParser, } from 'graphql-language-service';\nimport indent from './mode-indent';\nconst graphqlModeFactory = config => {\n    const parser = onlineParser({\n        eatWhitespace: stream => stream.eatWhile(isIgnored),\n        lexRules: LexRules,\n        parseRules: ParseRules,\n        editorConfig: { tabSize: config.tabSize },\n    });\n    return {\n        config,\n        startState: parser.startState,\n        token: parser.token,\n        indent,\n        electricInput: /^\\s*[})\\]]/,\n        fold: 'brace',\n        lineComment: '#',\n        closeBrackets: {\n            pairs: '()[]{}\"\"',\n            explode: '()[]{}',\n        },\n    };\n};\nexport default graphqlModeFactory;\n//# sourceMappingURL=mode-factory.js.map", "import CodeMirror from 'codemirror';\nimport modeFactory from './utils/mode-factory';\nCodeMirror.defineMode('graphql', modeFactory);\n//# sourceMappingURL=mode.js.map"], "mappings": ";;;;;;;;;;;;;;;;;;;AAEA,IAAMA,IAAqBC,EAAA,CAAAC,MAAU;AACjC,QAAMC,KAASC,aAAa;IACxB,eAAe,CAAAC,MAAUA,EAAO,SAASC,SAAS;IAClD,UAAUC;IACV,YAAYC;IACZ,cAAc,EAAE,SAASN,EAAO,QAAS;EACjD,CAAK;AACD,SAAO;IACH,QAAAA;IACA,YAAYC,GAAO;IACnB,OAAOA,GAAO;IACd,QAAAM;IACA,eAAe;IACf,MAAM;IACN,aAAa;IACb,eAAe;MACX,OAAO;MACP,SAAS;IACZ;EACT;AACA,GApB2B,oBAAA;ACA3BC,EAAW,WAAW,WAAWC,CAAW;", "names": ["graphqlModeFactory", "__name", "config", "parser", "onlineParser", "stream", "isIgnored", "LexRules", "ParseRules", "indent", "CodeMirror", "modeFactory"]}