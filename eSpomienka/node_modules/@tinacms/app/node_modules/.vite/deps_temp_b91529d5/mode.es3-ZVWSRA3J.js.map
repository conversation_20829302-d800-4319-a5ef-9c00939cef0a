{"version": 3, "sources": ["../../../../../@graphiql/codemirror-graphql/esm/results/mode.js"], "sourcesContent": ["import CodeMirror from 'codemirror';\nimport { list, t, onlineParser, p } from 'graphql-language-service';\nimport indent from '../utils/mode-indent';\nCodeMirror.defineMode('graphql-results', config => {\n    const parser = onlineParser({\n        eatWhitespace: stream => stream.eatSpace(),\n        lexRules: LexRules,\n        parseRules: ParseRules,\n        editorConfig: { tabSize: config.tabSize },\n    });\n    return {\n        config,\n        startState: parser.startState,\n        token: parser.token,\n        indent,\n        electricInput: /^\\s*[}\\]]/,\n        fold: 'brace',\n        closeBrackets: {\n            pairs: '[]{}\"\"',\n            explode: '[]{}',\n        },\n    };\n});\nconst LexRules = {\n    Punctuation: /^\\[|]|\\{|\\}|:|,/,\n    Number: /^-?(?:0|(?:[1-9][0-9]*))(?:\\.[0-9]*)?(?:[eE][+-]?[0-9]+)?/,\n    String: /^\"(?:[^\"\\\\]|\\\\(?:\"|\\/|\\\\|b|f|n|r|t|u[0-9a-fA-F]{4}))*\"?/,\n    Keyword: /^true|false|null/,\n};\nconst ParseRules = {\n    Document: [p('{'), list('Entry', p(',')), p('}')],\n    Entry: [t('String', 'def'), p(':'), 'Value'],\n    Value(token) {\n        switch (token.kind) {\n            case 'Number':\n                return 'NumberValue';\n            case 'String':\n                return 'StringValue';\n            case 'Punctuation':\n                switch (token.value) {\n                    case '[':\n                        return 'ListValue';\n                    case '{':\n                        return 'ObjectValue';\n                }\n                return null;\n            case 'Keyword':\n                switch (token.value) {\n                    case 'true':\n                    case 'false':\n                        return 'BooleanValue';\n                    case 'null':\n                        return 'NullValue';\n                }\n                return null;\n        }\n    },\n    NumberValue: [t('Number', 'number')],\n    StringValue: [t('String', 'string')],\n    BooleanValue: [t('Keyword', 'builtin')],\n    NullValue: [t('Keyword', 'keyword')],\n    ListValue: [p('['), list('Value', p(',')), p(']')],\n    ObjectValue: [p('{'), list('ObjectField', p(',')), p('}')],\n    ObjectField: [t('String', 'property'), p(':'), 'Value'],\n};\n//# sourceMappingURL=mode.js.map"], "mappings": ";;;;;;;;;;;;;;;;;AAGAA,EAAW,WAAW,mBAAmB,CAAAC,OAAU;AAC/C,QAAMC,IAASC,aAAa;IACxB,eAAe,CAAAC,MAAUA,EAAO,SAAU;IAC1C,UAAUC;IACV,YAAYC;IACZ,cAAc,EAAE,SAASL,GAAO,QAAS;EACjD,CAAK;AACD,SAAO;IACH,QAAAA;IACA,YAAYC,EAAO;IACnB,OAAOA,EAAO;IACd,QAAAK;IACA,eAAe;IACf,MAAM;IACN,eAAe;MACX,OAAO;MACP,SAAS;IACZ;EACT;AACA,CAAC;AACD,IAAMF,IAAW;EACb,aAAa;EACb,QAAQ;EACR,QAAQ;EACR,SAAS;AACb;AALA,IAMMC,IAAa;EACf,UAAU,CAACE,EAAE,GAAG,GAAGC,KAAK,SAASD,EAAE,GAAG,CAAC,GAAGA,EAAE,GAAG,CAAC;EAChD,OAAO,CAAC,EAAE,UAAU,KAAK,GAAGA,EAAE,GAAG,GAAG,OAAO;EAC3C,MAAME,IAAO;AACT,YAAQA,GAAM,MAAI;MACd,KAAK;AACD,eAAO;MACX,KAAK;AACD,eAAO;MACX,KAAK;AACD,gBAAQA,GAAM,OAAK;UACf,KAAK;AACD,mBAAO;UACX,KAAK;AACD,mBAAO;QACd;AACD,eAAO;MACX,KAAK;AACD,gBAAQA,GAAM,OAAK;UACf,KAAK;UACL,KAAK;AACD,mBAAO;UACX,KAAK;AACD,mBAAO;QACd;AACD,eAAO;IACd;EACJ;EACD,aAAa,CAAC,EAAE,UAAU,QAAQ,CAAC;EACnC,aAAa,CAAC,EAAE,UAAU,QAAQ,CAAC;EACnC,cAAc,CAAC,EAAE,WAAW,SAAS,CAAC;EACtC,WAAW,CAAC,EAAE,WAAW,SAAS,CAAC;EACnC,WAAW,CAACF,EAAE,GAAG,GAAGC,KAAK,SAASD,EAAE,GAAG,CAAC,GAAGA,EAAE,GAAG,CAAC;EACjD,aAAa,CAACA,EAAE,GAAG,GAAGC,KAAK,eAAeD,EAAE,GAAG,CAAC,GAAGA,EAAE,GAAG,CAAC;EACzD,aAAa,CAAC,EAAE,UAAU,UAAU,GAAGA,EAAE,GAAG,GAAG,OAAO;AAC1D;", "names": ["CodeMirror", "config", "parser", "onlineParser", "stream", "LexRules", "ParseRules", "indent", "p", "list", "token"]}