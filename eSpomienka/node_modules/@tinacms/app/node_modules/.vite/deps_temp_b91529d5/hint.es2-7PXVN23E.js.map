{"version": 3, "sources": ["../../../../../@graphiql/codemirror-graphql/esm/utils/hintList.js", "../../../../../@graphiql/codemirror-graphql/esm/variables/hint.js"], "sourcesContent": ["export default function hintList(cursor, token, list) {\n    const hints = filterAndSortList(list, normalizeText(token.string));\n    if (!hints) {\n        return;\n    }\n    const tokenStart = token.type !== null && /\"|\\w/.test(token.string[0])\n        ? token.start\n        : token.end;\n    return {\n        list: hints,\n        from: { line: cursor.line, ch: tokenStart },\n        to: { line: cursor.line, ch: token.end },\n    };\n}\nfunction filterAndSortList(list, text) {\n    if (!text) {\n        return filterNonEmpty(list, entry => !entry.isDeprecated);\n    }\n    const byProximity = list.map(entry => ({\n        proximity: getProximity(normalizeText(entry.text), text),\n        entry,\n    }));\n    const conciseMatches = filterNonEmpty(filterNonEmpty(byProximity, pair => pair.proximity <= 2), pair => !pair.entry.isDeprecated);\n    const sortedMatches = conciseMatches.sort((a, b) => (a.entry.isDeprecated ? 1 : 0) - (b.entry.isDeprecated ? 1 : 0) ||\n        a.proximity - b.proximity ||\n        a.entry.text.length - b.entry.text.length);\n    return sortedMatches.map(pair => pair.entry);\n}\nfunction filterNonEmpty(array, predicate) {\n    const filtered = array.filter(predicate);\n    return filtered.length === 0 ? array : filtered;\n}\nfunction normalizeText(text) {\n    return text.toLowerCase().replaceAll(/\\W/g, '');\n}\nfunction getProximity(suggestion, text) {\n    let proximity = lexicalDistance(text, suggestion);\n    if (suggestion.length > text.length) {\n        proximity -= suggestion.length - text.length - 1;\n        proximity += suggestion.indexOf(text) === 0 ? 0 : 0.5;\n    }\n    return proximity;\n}\nfunction lexicalDistance(a, b) {\n    let i;\n    let j;\n    const d = [];\n    const aLength = a.length;\n    const bLength = b.length;\n    for (i = 0; i <= aLength; i++) {\n        d[i] = [i];\n    }\n    for (j = 1; j <= bLength; j++) {\n        d[0][j] = j;\n    }\n    for (i = 1; i <= aLength; i++) {\n        for (j = 1; j <= bLength; j++) {\n            const cost = a[i - 1] === b[j - 1] ? 0 : 1;\n            d[i][j] = Math.min(d[i - 1][j] + 1, d[i][j - 1] + 1, d[i - 1][j - 1] + cost);\n            if (i > 1 && j > 1 && a[i - 1] === b[j - 2] && a[i - 2] === b[j - 1]) {\n                d[i][j] = Math.min(d[i][j], d[i - 2][j - 2] + cost);\n            }\n        }\n    }\n    return d[aLength][bLength];\n}\n//# sourceMappingURL=hintList.js.map", "import CodeMirror from 'codemirror';\nimport { getNullableType, getNamedType, GraphQLEnumType, GraphQLInputObjectType, GraphQLList, GraphQLBoolean, } from 'graphql';\nimport forEachState from '../utils/forEachState';\nimport hintList from '../utils/hintList';\nCodeMirror.registerHelper('hint', 'graphql-variables', (editor, options) => {\n    const cur = editor.getCursor();\n    const token = editor.getTokenAt(cur);\n    const results = getVariablesHint(cur, token, options);\n    if ((results === null || results === void 0 ? void 0 : results.list) && results.list.length > 0) {\n        results.from = CodeMirror.Pos(results.from.line, results.from.ch);\n        results.to = CodeMirror.Pos(results.to.line, results.to.ch);\n        CodeMirror.signal(editor, 'hasCompletion', editor, results, token);\n    }\n    return results;\n});\nfunction getVariablesHint(cur, token, options) {\n    const state = token.state.kind === 'Invalid' ? token.state.prevState : token.state;\n    const { kind, step } = state;\n    if (kind === 'Document' && step === 0) {\n        return hintList(cur, token, [{ text: '{' }]);\n    }\n    const { variableToType } = options;\n    if (!variableToType) {\n        return;\n    }\n    const typeInfo = getTypeInfo(variableToType, token.state);\n    if (kind === 'Document' || (kind === 'Variable' && step === 0)) {\n        const variableNames = Object.keys(variableToType);\n        return hintList(cur, token, variableNames.map(name => ({\n            text: `\"${name}\": `,\n            type: variableToType[name],\n        })));\n    }\n    if ((kind === 'ObjectValue' || (kind === 'ObjectField' && step === 0)) &&\n        typeInfo.fields) {\n        const inputFields = Object.keys(typeInfo.fields).map(fieldName => typeInfo.fields[fieldName]);\n        return hintList(cur, token, inputFields.map(field => ({\n            text: `\"${field.name}\": `,\n            type: field.type,\n            description: field.description,\n        })));\n    }\n    if (kind === 'StringValue' ||\n        kind === 'NumberValue' ||\n        kind === 'BooleanValue' ||\n        kind === 'NullValue' ||\n        (kind === 'ListValue' && step === 1) ||\n        (kind === 'ObjectField' && step === 2) ||\n        (kind === 'Variable' && step === 2)) {\n        const namedInputType = typeInfo.type\n            ? getNamedType(typeInfo.type)\n            : undefined;\n        if (namedInputType instanceof GraphQLInputObjectType) {\n            return hintList(cur, token, [{ text: '{' }]);\n        }\n        if (namedInputType instanceof GraphQLEnumType) {\n            const values = namedInputType.getValues();\n            return hintList(cur, token, values.map(value => ({\n                text: `\"${value.name}\"`,\n                type: namedInputType,\n                description: value.description,\n            })));\n        }\n        if (namedInputType === GraphQLBoolean) {\n            return hintList(cur, token, [\n                { text: 'true', type: GraphQLBoolean, description: 'Not false.' },\n                { text: 'false', type: GraphQLBoolean, description: 'Not true.' },\n            ]);\n        }\n    }\n}\nfunction getTypeInfo(variableToType, tokenState) {\n    const info = {\n        type: null,\n        fields: null,\n    };\n    forEachState(tokenState, state => {\n        switch (state.kind) {\n            case 'Variable': {\n                info.type = variableToType[state.name];\n                break;\n            }\n            case 'ListValue': {\n                const nullableType = info.type ? getNullableType(info.type) : undefined;\n                info.type =\n                    nullableType instanceof GraphQLList ? nullableType.ofType : null;\n                break;\n            }\n            case 'ObjectValue': {\n                const objectType = info.type ? getNamedType(info.type) : undefined;\n                info.fields =\n                    objectType instanceof GraphQLInputObjectType\n                        ? objectType.getFields()\n                        : null;\n                break;\n            }\n            case 'ObjectField': {\n                const objectField = state.name && info.fields ? info.fields[state.name] : null;\n                info.type = objectField === null || objectField === void 0 ? void 0 : objectField.type;\n                break;\n            }\n        }\n    });\n    return info;\n}\n//# sourceMappingURL=hint.js.map"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAe,SAASA,EAASC,GAAQC,GAAOC,GAAM;AAClD,QAAMC,IAAQC,EAAkBF,GAAMG,EAAcJ,EAAM,MAAM,CAAC;AACjE,MAAI,CAACE;AACD;AAEJ,QAAMG,IAAaL,EAAM,SAAS,QAAQ,OAAO,KAAKA,EAAM,OAAO,CAAC,CAAC,IAC/DA,EAAM,QACNA,EAAM;AACZ,SAAO;IACH,MAAME;IACN,MAAM,EAAE,MAAMH,EAAO,MAAM,IAAIM,EAAY;IAC3C,IAAI,EAAE,MAAMN,EAAO,MAAM,IAAIC,EAAM,IAAK;EAChD;AACA;AAbwBM,EAAAR,GAAA,UAAA;AAcxB,SAASK,EAAkBF,GAAMM,GAAM;AACnC,MAAI,CAACA;AACD,WAAOC,GAAeP,GAAM,CAAAQ,MAAS,CAACA,EAAM,YAAY;AAE5D,QAAMC,IAAcT,EAAK,IAAI,CAAAQ,OAAU;IACnC,WAAWE,EAAaP,EAAcK,EAAM,IAAI,GAAGF,CAAI;IACvD,OAAAE;EACH,EAAC;AAKF,SAJuBD,GAAeA,GAAeE,GAAa,CAAAE,MAAQA,EAAK,aAAa,CAAC,GAAG,CAAAA,MAAQ,CAACA,EAAK,MAAM,YAAY,EAC3F,KAAK,CAACC,GAAGC,QAAOD,EAAE,MAAM,eAAe,IAAI,MAAMC,GAAE,MAAM,eAAe,IAAI,MAC7GD,EAAE,YAAYC,GAAE,aAChBD,EAAE,MAAM,KAAK,SAASC,GAAE,MAAM,KAAK,MAAM,EACxB,IAAI,CAAAF,MAAQA,EAAK,KAAK;AAC/C;AAbSN,EAAAH,GAAA,mBAAA;AAcT,SAASK,GAAeO,GAAOC,GAAW;AACtC,QAAMC,IAAWF,EAAM,OAAOC,CAAS;AACvC,SAAOC,EAAS,WAAW,IAAIF,IAAQE;AAC3C;AAHSX,EAAAE,IAAA,gBAAA;AAIT,SAASJ,EAAcG,GAAM;AACzB,SAAOA,EAAK,YAAa,EAAC,WAAW,OAAO,EAAE;AAClD;AAFSD,EAAAF,GAAA,eAAA;AAGT,SAASO,EAAaO,GAAYX,GAAM;AACpC,MAAIY,IAAYC,EAAgBb,GAAMW,CAAU;AAChD,SAAIA,EAAW,SAASX,EAAK,WACzBY,KAAaD,EAAW,SAASX,EAAK,SAAS,GAC/CY,KAAaD,EAAW,QAAQX,CAAI,MAAM,IAAI,IAAI,MAE/CY;AACX;AAPSb,EAAAK,GAAA,cAAA;AAQT,SAASS,EAAgBP,GAAGC,GAAG;AAC3B,MAAIO,GACAC;AACJ,QAAMC,IAAI,CAAA,GACJC,IAAUX,EAAE,QACZY,KAAUX,EAAE;AAClB,OAAKO,IAAI,GAAGA,KAAKG,GAASH;AACtBE,MAAEF,CAAC,IAAI,CAACA,CAAC;AAEb,OAAKC,IAAI,GAAGA,KAAKG,IAASH;AACtBC,MAAE,CAAC,EAAED,CAAC,IAAIA;AAEd,OAAKD,IAAI,GAAGA,KAAKG,GAASH;AACtB,SAAKC,IAAI,GAAGA,KAAKG,IAASH,KAAK;AAC3B,YAAMI,IAAOb,EAAEQ,IAAI,CAAC,MAAMP,EAAEQ,IAAI,CAAC,IAAI,IAAI;AACzCC,QAAEF,CAAC,EAAEC,CAAC,IAAI,KAAK,IAAIC,EAAEF,IAAI,CAAC,EAAEC,CAAC,IAAI,GAAGC,EAAEF,CAAC,EAAEC,IAAI,CAAC,IAAI,GAAGC,EAAEF,IAAI,CAAC,EAAEC,IAAI,CAAC,IAAII,CAAI,GACvEL,IAAI,KAAKC,IAAI,KAAKT,EAAEQ,IAAI,CAAC,MAAMP,EAAEQ,IAAI,CAAC,KAAKT,EAAEQ,IAAI,CAAC,MAAMP,EAAEQ,IAAI,CAAC,MAC/DC,EAAEF,CAAC,EAAEC,CAAC,IAAI,KAAK,IAAIC,EAAEF,CAAC,EAAEC,CAAC,GAAGC,EAAEF,IAAI,CAAC,EAAEC,IAAI,CAAC,IAAII,CAAI;IAAA;AAI9D,SAAOH,EAAEC,CAAO,EAAEC,EAAO;AAC7B;AAtBSnB,EAAAc,GAAA,iBAAA;ACvCTO,EAAW,eAAe,QAAQ,qBAAqB,CAACC,GAAQC,MAAY;AACxE,QAAMC,IAAMF,EAAO,UAAA,GACb5B,IAAQ4B,EAAO,WAAWE,CAAG,GAC7BC,IAAUC,EAAiBF,GAAK9B,GAAO6B,CAAO;AACpD,SAAKE,KAAY,QAAsCA,EAAQ,QAASA,EAAQ,KAAK,SAAS,MAC1FA,EAAQ,OAAOJ,EAAW,IAAII,EAAQ,KAAK,MAAMA,EAAQ,KAAK,EAAE,GAChEA,EAAQ,KAAKJ,EAAW,IAAII,EAAQ,GAAG,MAAMA,EAAQ,GAAG,EAAE,GAC1DJ,EAAW,OAAOC,GAAQ,iBAAiBA,GAAQG,GAAS/B,CAAK,IAE9D+B;AACX,CAAC;AACD,SAASC,EAAiBF,GAAK9B,GAAO6B,GAAS;AAC3C,QAAMI,IAAQjC,EAAM,MAAM,SAAS,YAAYA,EAAM,MAAM,YAAYA,EAAM,OACvE,EAAE,MAAAkC,GAAM,MAAAC,EAAM,IAAGF;AACvB,MAAIC,MAAS,cAAcC,MAAS;AAChC,WAAOrC,EAASgC,GAAK9B,GAAO,CAAC,EAAE,MAAM,IAAK,CAAA,CAAC;AAE/C,QAAM,EAAE,gBAAAoC,GAAgB,IAAGP;AAC3B,MAAI,CAACO;AACD;AAEJ,QAAMC,IAAWC,EAAYF,IAAgBpC,EAAM,KAAK;AACxD,MAAIkC,MAAS,cAAeA,MAAS,cAAcC,MAAS,GAAI;AAC5D,UAAMI,IAAgB,OAAO,KAAKH,EAAc;AAChD,WAAOtC,EAASgC,GAAK9B,GAAOuC,EAAc,IAAI,CAAAC,OAAS;MACnD,MAAM,IAAIA,CAAAA;MACV,MAAMJ,GAAeI,CAAI;IAC5B,EAAC,CAAC;EAAA;AAEP,OAAKN,MAAS,iBAAkBA,MAAS,iBAAiBC,MAAS,MAC/DE,EAAS,QAAQ;AACjB,UAAMI,IAAc,OAAO,KAAKJ,EAAS,MAAM,EAAE,IAAI,CAAAK,MAAaL,EAAS,OAAOK,CAAS,CAAC;AAC5F,WAAO5C,EAASgC,GAAK9B,GAAOyC,EAAY,IAAI,CAAAE,OAAU;MAClD,MAAM,IAAIA,EAAM,IAAA;MAChB,MAAMA,EAAM;MACZ,aAAaA,EAAM;IACtB,EAAC,CAAC;EAAA;AAEP,MAAIT,MAAS,iBACTA,MAAS,iBACTA,MAAS,kBACTA,MAAS,eACRA,MAAS,eAAeC,MAAS,KACjCD,MAAS,iBAAiBC,MAAS,KACnCD,MAAS,cAAcC,MAAS,GAAI;AACrC,UAAMS,IAAiBP,EAAS,OAC1BQ,aAAaR,EAAS,IAAI,IAC1B;AACN,QAAIO,aAA0BE;AAC1B,aAAOhD,EAASgC,GAAK9B,GAAO,CAAC,EAAE,MAAM,IAAK,CAAA,CAAC;AAE/C,QAAI4C,aAA0BG,iBAAiB;AAC3C,YAAMC,IAASJ,EAAe,UAAA;AAC9B,aAAO9C,EAASgC,GAAK9B,GAAOgD,EAAO,IAAI,CAAAC,OAAU;QAC7C,MAAM,IAAIA,EAAM,IAAA;QAChB,MAAML;QACN,aAAaK,EAAM;MACtB,EAAC,CAAC;IAAA;AAEP,QAAIL,MAAmBM;AACnB,aAAOpD,EAASgC,GAAK9B,GAAO;QACxB,EAAE,MAAM,QAAQ,MAAMkD,gBAAgB,aAAa,aAAc;QACjE,EAAE,MAAM,SAAS,MAAMA,gBAAgB,aAAa,YAAa;MACjF,CAAa;EAAA;AAGb;AAvDS5C,EAAA0B,GAAA,kBAAA;AAwDT,SAASM,EAAYF,GAAgBe,GAAY;AAC7C,QAAMC,IAAO;IACT,MAAM;IACN,QAAQ;EAChB;AACI,SAAAC,EAAaF,GAAY,CAAAlB,MAAS;AAC9B,YAAQA,EAAM,MAAI;MACd,KAAK,YAAY;AACbmB,UAAK,OAAOhB,EAAeH,EAAM,IAAI;AACrC;MACH;MACD,KAAK,aAAa;AACd,cAAMqB,IAAeF,EAAK,OAAOG,gBAAgBH,EAAK,IAAI,IAAI;AAC9DA,UAAK,OACDE,aAAwBE,cAAcF,EAAa,SAAS;AAChE;MACH;MACD,KAAK,eAAe;AAChB,cAAMG,IAAaL,EAAK,OAAOP,aAAaO,EAAK,IAAI,IAAI;AACzDA,UAAK,SACDK,aAAsBX,yBAChBW,EAAW,UAAW,IACtB;AACV;MACH;MACD,KAAK,eAAe;AAChB,cAAMC,IAAczB,EAAM,QAAQmB,EAAK,SAASA,EAAK,OAAOnB,EAAM,IAAI,IAAI;AAC1EmB,UAAK,OAAOM,KAAgB,OAAiC,SAASA,EAAY;AAClF;MACH;IACJ;EACT,CAAK,GACMN;AACX;AAjCS9C,EAAAgC,GAAA,aAAA;", "names": ["hintList", "cursor", "token", "list", "hints", "filterAndSortList", "normalizeText", "tokenStart", "__name", "text", "filterNonEmpty", "entry", "byProximity", "getProximity", "pair", "a", "b", "array", "predicate", "filtered", "suggestion", "proximity", "lexicalDistance", "i", "j", "d", "a<PERSON><PERSON><PERSON>", "b<PERSON><PERSON><PERSON>", "cost", "CodeMirror", "editor", "options", "cur", "results", "getVariablesHint", "state", "kind", "step", "variableToType", "typeInfo", "getTypeInfo", "variableNames", "name", "inputFields", "fieldName", "field", "namedInputType", "getNamedType", "GraphQLInputObjectType", "GraphQLEnumType", "values", "value", "GraphQLBoolean", "tokenState", "info", "forEachState", "nullableType", "getNullableType", "GraphQLList", "objectType", "objectField"]}