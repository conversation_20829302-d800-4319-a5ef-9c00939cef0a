import {
  BREAK,
  DirectiveLocation,
  ExecutableDefinitionsRule,
  FragmentsOnCompositeTypesRule,
  GraphQLBoolean,
  GraphQLEnumType,
  GraphQLError,
  GraphQLFloat,
  GraphQLInputObjectType,
  GraphQLInterfaceType,
  GraphQLList,
  GraphQLObjectType,
  Kind,
  KnownDirectivesRule,
  KnownFragmentNamesRule,
  KnownTypeNamesRule,
  LoneSchemaDefinitionRule,
  NoDeprecatedCustomRule,
  NoUnusedFragmentsRule,
  PossibleTypeExtensionsRule,
  ProvidedRequiredArgumentsRule,
  SchemaMetaFieldDef,
  TypeMetaFieldDef,
  TypeNameMetaFieldDef,
  UniqueArgumentNamesRule,
  UniqueDirectiveNamesRule,
  UniqueDirectivesPerLocationRule,
  UniqueEnumValueNamesRule,
  UniqueFieldDefinitionNamesRule,
  UniqueInputFieldNamesRule,
  UniqueOperationTypesRule,
  UniqueTypeNamesRule,
  UniqueVariableNamesRule,
  assertAbstractType,
  doTypesOverlap,
  getNamedType,
  getNullableType,
  isAbstractType,
  isCompositeType,
  isEnumType,
  isInputObjectType,
  isInputType,
  isInterfaceType,
  isListType,
  isObjectType,
  isOutputType,
  isScalarType,
  isUnionType,
  parse,
  print,
  specifiedRules,
  typeFromAST,
  validate,
  visit
} from "./chunk-HD22INE4.js";
import {
  __commonJS,
  __toESM
} from "./chunk-AUZ3RYOM.js";

// node_modules/nullthrows/nullthrows.js
var require_nullthrows = __commonJS({
  "node_modules/nullthrows/nullthrows.js"(exports, module) {
    "use strict";
    function nullthrows2(x, message) {
      if (x != null) {
        return x;
      }
      var error = new Error(message !== void 0 ? message : "Got unexpected " + x);
      error.framesToPop = 1;
      throw error;
    }
    module.exports = nullthrows2;
    module.exports.default = nullthrows2;
    Object.defineProperty(module.exports, "__esModule", { value: true });
  }
});

// node_modules/graphql-language-service/esm/parser/RuleHelpers.js
function opt(ofRule) {
  return { ofRule };
}
function list(ofRule, separator) {
  return { ofRule, isList: true, separator };
}
function butNot(rule, exclusions) {
  const ruleMatch = rule.match;
  rule.match = (token) => {
    let check = false;
    if (ruleMatch) {
      check = ruleMatch(token);
    }
    return check && exclusions.every((exclusion) => exclusion.match && !exclusion.match(token));
  };
  return rule;
}
function t(kind, style) {
  return { style, match: (token) => token.kind === kind };
}
function p(value, style) {
  return {
    style: style || "punctuation",
    match: (token) => token.kind === "Punctuation" && token.value === value
  };
}

// node_modules/graphql-language-service/esm/parser/Rules.js
var isIgnored = (ch) => ch === " " || ch === "	" || ch === "," || ch === "\n" || ch === "\r" || ch === "\uFEFF" || ch === " ";
var LexRules = {
  Name: /^[_A-Za-z][_0-9A-Za-z]*/,
  Punctuation: /^(?:!|\$|\(|\)|\.\.\.|:|=|&|@|\[|]|\{|\||\})/,
  Number: /^-?(?:0|(?:[1-9][0-9]*))(?:\.[0-9]*)?(?:[eE][+-]?[0-9]+)?/,
  String: /^(?:"""(?:\\"""|[^"]|"[^"]|""[^"])*(?:""")?|"(?:[^"\\]|\\(?:"|\/|\\|b|f|n|r|t|u[0-9a-fA-F]{4}))*"?)/,
  Comment: /^#.*/
};
var ParseRules = {
  Document: [list("Definition")],
  Definition(token) {
    switch (token.value) {
      case "{":
        return "ShortQuery";
      case "query":
        return "Query";
      case "mutation":
        return "Mutation";
      case "subscription":
        return "Subscription";
      case "fragment":
        return Kind.FRAGMENT_DEFINITION;
      case "schema":
        return "SchemaDef";
      case "scalar":
        return "ScalarDef";
      case "type":
        return "ObjectTypeDef";
      case "interface":
        return "InterfaceDef";
      case "union":
        return "UnionDef";
      case "enum":
        return "EnumDef";
      case "input":
        return "InputDef";
      case "extend":
        return "ExtendDef";
      case "directive":
        return "DirectiveDef";
    }
  },
  ShortQuery: ["SelectionSet"],
  Query: [
    word("query"),
    opt(name("def")),
    opt("VariableDefinitions"),
    list("Directive"),
    "SelectionSet"
  ],
  Mutation: [
    word("mutation"),
    opt(name("def")),
    opt("VariableDefinitions"),
    list("Directive"),
    "SelectionSet"
  ],
  Subscription: [
    word("subscription"),
    opt(name("def")),
    opt("VariableDefinitions"),
    list("Directive"),
    "SelectionSet"
  ],
  VariableDefinitions: [p("("), list("VariableDefinition"), p(")")],
  VariableDefinition: ["Variable", p(":"), "Type", opt("DefaultValue")],
  Variable: [p("$", "variable"), name("variable")],
  DefaultValue: [p("="), "Value"],
  SelectionSet: [p("{"), list("Selection"), p("}")],
  Selection(token, stream) {
    return token.value === "..." ? stream.match(/[\s\u00a0,]*(on\b|@|{)/, false) ? "InlineFragment" : "FragmentSpread" : stream.match(/[\s\u00a0,]*:/, false) ? "AliasedField" : "Field";
  },
  AliasedField: [
    name("property"),
    p(":"),
    name("qualifier"),
    opt("Arguments"),
    list("Directive"),
    opt("SelectionSet")
  ],
  Field: [
    name("property"),
    opt("Arguments"),
    list("Directive"),
    opt("SelectionSet")
  ],
  Arguments: [p("("), list("Argument"), p(")")],
  Argument: [name("attribute"), p(":"), "Value"],
  FragmentSpread: [p("..."), name("def"), list("Directive")],
  InlineFragment: [
    p("..."),
    opt("TypeCondition"),
    list("Directive"),
    "SelectionSet"
  ],
  FragmentDefinition: [
    word("fragment"),
    opt(butNot(name("def"), [word("on")])),
    "TypeCondition",
    list("Directive"),
    "SelectionSet"
  ],
  TypeCondition: [word("on"), "NamedType"],
  Value(token) {
    switch (token.kind) {
      case "Number":
        return "NumberValue";
      case "String":
        return "StringValue";
      case "Punctuation":
        switch (token.value) {
          case "[":
            return "ListValue";
          case "{":
            return "ObjectValue";
          case "$":
            return "Variable";
          case "&":
            return "NamedType";
        }
        return null;
      case "Name":
        switch (token.value) {
          case "true":
          case "false":
            return "BooleanValue";
        }
        if (token.value === "null") {
          return "NullValue";
        }
        return "EnumValue";
    }
  },
  NumberValue: [t("Number", "number")],
  StringValue: [
    {
      style: "string",
      match: (token) => token.kind === "String",
      update(state, token) {
        if (token.value.startsWith('"""')) {
          state.inBlockstring = !token.value.slice(3).endsWith('"""');
        }
      }
    }
  ],
  BooleanValue: [t("Name", "builtin")],
  NullValue: [t("Name", "keyword")],
  EnumValue: [name("string-2")],
  ListValue: [p("["), list("Value"), p("]")],
  ObjectValue: [p("{"), list("ObjectField"), p("}")],
  ObjectField: [name("attribute"), p(":"), "Value"],
  Type(token) {
    return token.value === "[" ? "ListType" : "NonNullType";
  },
  ListType: [p("["), "Type", p("]"), opt(p("!"))],
  NonNullType: ["NamedType", opt(p("!"))],
  NamedType: [type("atom")],
  Directive: [p("@", "meta"), name("meta"), opt("Arguments")],
  DirectiveDef: [
    word("directive"),
    p("@", "meta"),
    name("meta"),
    opt("ArgumentsDef"),
    word("on"),
    list("DirectiveLocation", p("|"))
  ],
  InterfaceDef: [
    word("interface"),
    name("atom"),
    opt("Implements"),
    list("Directive"),
    p("{"),
    list("FieldDef"),
    p("}")
  ],
  Implements: [word("implements"), list("NamedType", p("&"))],
  DirectiveLocation: [name("string-2")],
  SchemaDef: [
    word("schema"),
    list("Directive"),
    p("{"),
    list("OperationTypeDef"),
    p("}")
  ],
  OperationTypeDef: [name("keyword"), p(":"), name("atom")],
  ScalarDef: [word("scalar"), name("atom"), list("Directive")],
  ObjectTypeDef: [
    word("type"),
    name("atom"),
    opt("Implements"),
    list("Directive"),
    p("{"),
    list("FieldDef"),
    p("}")
  ],
  FieldDef: [
    name("property"),
    opt("ArgumentsDef"),
    p(":"),
    "Type",
    list("Directive")
  ],
  ArgumentsDef: [p("("), list("InputValueDef"), p(")")],
  InputValueDef: [
    name("attribute"),
    p(":"),
    "Type",
    opt("DefaultValue"),
    list("Directive")
  ],
  UnionDef: [
    word("union"),
    name("atom"),
    list("Directive"),
    p("="),
    list("UnionMember", p("|"))
  ],
  UnionMember: ["NamedType"],
  EnumDef: [
    word("enum"),
    name("atom"),
    list("Directive"),
    p("{"),
    list("EnumValueDef"),
    p("}")
  ],
  EnumValueDef: [name("string-2"), list("Directive")],
  InputDef: [
    word("input"),
    name("atom"),
    list("Directive"),
    p("{"),
    list("InputValueDef"),
    p("}")
  ],
  ExtendDef: [word("extend"), "ExtensionDefinition"],
  ExtensionDefinition(token) {
    switch (token.value) {
      case "schema":
        return Kind.SCHEMA_EXTENSION;
      case "scalar":
        return Kind.SCALAR_TYPE_EXTENSION;
      case "type":
        return Kind.OBJECT_TYPE_EXTENSION;
      case "interface":
        return Kind.INTERFACE_TYPE_EXTENSION;
      case "union":
        return Kind.UNION_TYPE_EXTENSION;
      case "enum":
        return Kind.ENUM_TYPE_EXTENSION;
      case "input":
        return Kind.INPUT_OBJECT_TYPE_EXTENSION;
    }
  },
  [Kind.SCHEMA_EXTENSION]: ["SchemaDef"],
  [Kind.SCALAR_TYPE_EXTENSION]: ["ScalarDef"],
  [Kind.OBJECT_TYPE_EXTENSION]: ["ObjectTypeDef"],
  [Kind.INTERFACE_TYPE_EXTENSION]: ["InterfaceDef"],
  [Kind.UNION_TYPE_EXTENSION]: ["UnionDef"],
  [Kind.ENUM_TYPE_EXTENSION]: ["EnumDef"],
  [Kind.INPUT_OBJECT_TYPE_EXTENSION]: ["InputDef"]
};
function word(value) {
  return {
    style: "keyword",
    match: (token) => token.kind === "Name" && token.value === value
  };
}
function name(style) {
  return {
    style,
    match: (token) => token.kind === "Name",
    update(state, token) {
      state.name = token.value;
    }
  };
}
function type(style) {
  return {
    style,
    match: (token) => token.kind === "Name",
    update(state, token) {
      var _a;
      if ((_a = state.prevState) === null || _a === void 0 ? void 0 : _a.prevState) {
        state.name = token.value;
        state.prevState.prevState.type = token.value;
      }
    }
  };
}

// node_modules/graphql-language-service/esm/parser/onlineParser.js
function onlineParser(options = {
  eatWhitespace: (stream) => stream.eatWhile(isIgnored),
  lexRules: LexRules,
  parseRules: ParseRules,
  editorConfig: {}
}) {
  return {
    startState() {
      const initialState = {
        level: 0,
        step: 0,
        name: null,
        kind: null,
        type: null,
        rule: null,
        needsSeparator: false,
        prevState: null
      };
      pushRule(options.parseRules, initialState, Kind.DOCUMENT);
      return initialState;
    },
    token(stream, state) {
      return getToken(stream, state, options);
    }
  };
}
function getToken(stream, state, options) {
  var _a;
  if (state.inBlockstring) {
    if (stream.match(/.*"""/)) {
      state.inBlockstring = false;
      return "string";
    }
    stream.skipToEnd();
    return "string";
  }
  const { lexRules, parseRules, eatWhitespace, editorConfig } = options;
  if (state.rule && state.rule.length === 0) {
    popRule(state);
  } else if (state.needsAdvance) {
    state.needsAdvance = false;
    advanceRule(state, true);
  }
  if (stream.sol()) {
    const tabSize = (editorConfig === null || editorConfig === void 0 ? void 0 : editorConfig.tabSize) || 2;
    state.indentLevel = Math.floor(stream.indentation() / tabSize);
  }
  if (eatWhitespace(stream)) {
    return "ws";
  }
  const token = lex(lexRules, stream);
  if (!token) {
    const matchedSomething = stream.match(/\S+/);
    if (!matchedSomething) {
      stream.match(/\s/);
    }
    pushRule(SpecialParseRules, state, "Invalid");
    return "invalidchar";
  }
  if (token.kind === "Comment") {
    pushRule(SpecialParseRules, state, "Comment");
    return "comment";
  }
  const backupState = assign({}, state);
  if (token.kind === "Punctuation") {
    if (/^[{([]/.test(token.value)) {
      if (state.indentLevel !== void 0) {
        state.levels = (state.levels || []).concat(state.indentLevel + 1);
      }
    } else if (/^[})\]]/.test(token.value)) {
      const levels = state.levels = (state.levels || []).slice(0, -1);
      if (state.indentLevel && levels.length > 0 && levels.at(-1) < state.indentLevel) {
        state.indentLevel = levels.at(-1);
      }
    }
  }
  while (state.rule) {
    let expected = typeof state.rule === "function" ? state.step === 0 ? state.rule(token, stream) : null : state.rule[state.step];
    if (state.needsSeparator) {
      expected = expected === null || expected === void 0 ? void 0 : expected.separator;
    }
    if (expected) {
      if (expected.ofRule) {
        expected = expected.ofRule;
      }
      if (typeof expected === "string") {
        pushRule(parseRules, state, expected);
        continue;
      }
      if ((_a = expected.match) === null || _a === void 0 ? void 0 : _a.call(expected, token)) {
        if (expected.update) {
          expected.update(state, token);
        }
        if (token.kind === "Punctuation") {
          advanceRule(state, true);
        } else {
          state.needsAdvance = true;
        }
        return expected.style;
      }
    }
    unsuccessful(state);
  }
  assign(state, backupState);
  pushRule(SpecialParseRules, state, "Invalid");
  return "invalidchar";
}
function assign(to, from) {
  const keys = Object.keys(from);
  for (let i = 0; i < keys.length; i++) {
    to[keys[i]] = from[keys[i]];
  }
  return to;
}
var SpecialParseRules = {
  Invalid: [],
  Comment: []
};
function pushRule(rules, state, ruleKind) {
  if (!rules[ruleKind]) {
    throw new TypeError("Unknown rule: " + ruleKind);
  }
  state.prevState = Object.assign({}, state);
  state.kind = ruleKind;
  state.name = null;
  state.type = null;
  state.rule = rules[ruleKind];
  state.step = 0;
  state.needsSeparator = false;
}
function popRule(state) {
  if (!state.prevState) {
    return;
  }
  state.kind = state.prevState.kind;
  state.name = state.prevState.name;
  state.type = state.prevState.type;
  state.rule = state.prevState.rule;
  state.step = state.prevState.step;
  state.needsSeparator = state.prevState.needsSeparator;
  state.prevState = state.prevState.prevState;
}
function advanceRule(state, successful) {
  var _a;
  if (isList(state) && state.rule) {
    const step = state.rule[state.step];
    if (step.separator) {
      const { separator } = step;
      state.needsSeparator = !state.needsSeparator;
      if (!state.needsSeparator && separator.ofRule) {
        return;
      }
    }
    if (successful) {
      return;
    }
  }
  state.needsSeparator = false;
  state.step++;
  while (state.rule && !(Array.isArray(state.rule) && state.step < state.rule.length)) {
    popRule(state);
    if (state.rule) {
      if (isList(state)) {
        if ((_a = state.rule) === null || _a === void 0 ? void 0 : _a[state.step].separator) {
          state.needsSeparator = !state.needsSeparator;
        }
      } else {
        state.needsSeparator = false;
        state.step++;
      }
    }
  }
}
function isList(state) {
  const step = Array.isArray(state.rule) && typeof state.rule[state.step] !== "string" && state.rule[state.step];
  return step && step.isList;
}
function unsuccessful(state) {
  while (state.rule && !(Array.isArray(state.rule) && state.rule[state.step].ofRule)) {
    popRule(state);
  }
  if (state.rule) {
    advanceRule(state, false);
  }
}
function lex(lexRules, stream) {
  const kinds = Object.keys(lexRules);
  for (let i = 0; i < kinds.length; i++) {
    const match = stream.match(lexRules[kinds[i]]);
    if (match && match instanceof Array) {
      return { kind: kinds[i], value: match[0] };
    }
  }
}

// node_modules/graphql-language-service/esm/parser/CharacterStream.js
var CharacterStream = class {
  constructor(sourceText) {
    this._start = 0;
    this._pos = 0;
    this.getStartOfToken = () => this._start;
    this.getCurrentPosition = () => this._pos;
    this.eol = () => this._sourceText.length === this._pos;
    this.sol = () => this._pos === 0;
    this.peek = () => {
      return this._sourceText.charAt(this._pos) || null;
    };
    this.next = () => {
      const char = this._sourceText.charAt(this._pos);
      this._pos++;
      return char;
    };
    this.eat = (pattern) => {
      const isMatched = this._testNextCharacter(pattern);
      if (isMatched) {
        this._start = this._pos;
        this._pos++;
        return this._sourceText.charAt(this._pos - 1);
      }
      return void 0;
    };
    this.eatWhile = (match) => {
      let isMatched = this._testNextCharacter(match);
      let didEat = false;
      if (isMatched) {
        didEat = isMatched;
        this._start = this._pos;
      }
      while (isMatched) {
        this._pos++;
        isMatched = this._testNextCharacter(match);
        didEat = true;
      }
      return didEat;
    };
    this.eatSpace = () => this.eatWhile(/[\s\u00a0]/);
    this.skipToEnd = () => {
      this._pos = this._sourceText.length;
    };
    this.skipTo = (position) => {
      this._pos = position;
    };
    this.match = (pattern, consume = true, caseFold = false) => {
      let token = null;
      let match = null;
      if (typeof pattern === "string") {
        const regex = new RegExp(pattern, caseFold ? "i" : "g");
        match = regex.test(this._sourceText.slice(this._pos, this._pos + pattern.length));
        token = pattern;
      } else if (pattern instanceof RegExp) {
        match = this._sourceText.slice(this._pos).match(pattern);
        token = match === null || match === void 0 ? void 0 : match[0];
      }
      if (match != null && (typeof pattern === "string" || match instanceof Array && this._sourceText.startsWith(match[0], this._pos))) {
        if (consume) {
          this._start = this._pos;
          if (token && token.length) {
            this._pos += token.length;
          }
        }
        return match;
      }
      return false;
    };
    this.backUp = (num) => {
      this._pos -= num;
    };
    this.column = () => this._pos;
    this.indentation = () => {
      const match = this._sourceText.match(/\s*/);
      let indent = 0;
      if (match && match.length !== 0) {
        const whiteSpaces = match[0];
        let pos = 0;
        while (whiteSpaces.length > pos) {
          if (whiteSpaces.charCodeAt(pos) === 9) {
            indent += 2;
          } else {
            indent++;
          }
          pos++;
        }
      }
      return indent;
    };
    this.current = () => this._sourceText.slice(this._start, this._pos);
    this._sourceText = sourceText;
  }
  _testNextCharacter(pattern) {
    const character = this._sourceText.charAt(this._pos);
    let isMatched = false;
    if (typeof pattern === "string") {
      isMatched = character === pattern;
    } else {
      isMatched = pattern instanceof RegExp ? pattern.test(character) : pattern(character);
    }
    return isMatched;
  }
};

// node_modules/graphql-language-service/esm/parser/api.js
function runOnlineParser(queryText, callback) {
  const lines = queryText.split("\n");
  const parser = onlineParser();
  let state = parser.startState();
  let style = "";
  let stream = new CharacterStream("");
  for (let i = 0; i < lines.length; i++) {
    stream = new CharacterStream(lines[i]);
    while (!stream.eol()) {
      style = parser.token(stream, state);
      const code = callback(stream, state, style, i);
      if (code === "BREAK") {
        break;
      }
    }
    callback(stream, state, style, i);
    if (!state.kind) {
      state = parser.startState();
    }
  }
  return {
    start: stream.getStartOfToken(),
    end: stream.getCurrentPosition(),
    string: stream.current(),
    state,
    style
  };
}
var GraphQLDocumentMode;
(function(GraphQLDocumentMode2) {
  GraphQLDocumentMode2["TYPE_SYSTEM"] = "TYPE_SYSTEM";
  GraphQLDocumentMode2["EXECUTABLE"] = "EXECUTABLE";
  GraphQLDocumentMode2["UNKNOWN"] = "UNKNOWN";
})(GraphQLDocumentMode || (GraphQLDocumentMode = {}));
var TYPE_SYSTEM_KINDS = [
  Kind.SCHEMA_DEFINITION,
  Kind.OPERATION_TYPE_DEFINITION,
  Kind.SCALAR_TYPE_DEFINITION,
  Kind.OBJECT_TYPE_DEFINITION,
  Kind.INTERFACE_TYPE_DEFINITION,
  Kind.UNION_TYPE_DEFINITION,
  Kind.ENUM_TYPE_DEFINITION,
  Kind.INPUT_OBJECT_TYPE_DEFINITION,
  Kind.DIRECTIVE_DEFINITION,
  Kind.SCHEMA_EXTENSION,
  Kind.SCALAR_TYPE_EXTENSION,
  Kind.OBJECT_TYPE_EXTENSION,
  Kind.INTERFACE_TYPE_EXTENSION,
  Kind.UNION_TYPE_EXTENSION,
  Kind.ENUM_TYPE_EXTENSION,
  Kind.INPUT_OBJECT_TYPE_EXTENSION
];
var getParsedMode = (sdl) => {
  let mode = GraphQLDocumentMode.UNKNOWN;
  if (sdl) {
    try {
      visit(parse(sdl), {
        enter(node) {
          if (node.kind === "Document") {
            mode = GraphQLDocumentMode.EXECUTABLE;
            return;
          }
          if (TYPE_SYSTEM_KINDS.includes(node.kind)) {
            mode = GraphQLDocumentMode.TYPE_SYSTEM;
            return BREAK;
          }
          return false;
        }
      });
    } catch (_a) {
      return mode;
    }
  }
  return mode;
};
function getDocumentMode(documentText, uri) {
  if (uri === null || uri === void 0 ? void 0 : uri.endsWith(".graphqls")) {
    return GraphQLDocumentMode.TYPE_SYSTEM;
  }
  return getParsedMode(documentText);
}
function getTokenAtPosition(queryText, cursor, offset = 0) {
  let styleAtCursor = null;
  let stateAtCursor = null;
  let stringAtCursor = null;
  const token = runOnlineParser(queryText, (stream, state, style, index) => {
    if (index !== cursor.line || stream.getCurrentPosition() + offset < cursor.character + 1) {
      return;
    }
    styleAtCursor = style;
    stateAtCursor = Object.assign({}, state);
    stringAtCursor = stream.current();
    return "BREAK";
  });
  return {
    start: token.start,
    end: token.end,
    string: stringAtCursor || token.string,
    state: stateAtCursor || token.state,
    style: styleAtCursor || token.style
  };
}
function getContextAtPosition(queryText, cursor, schema, contextToken, options) {
  const token = contextToken || getTokenAtPosition(queryText, cursor, 1);
  if (!token) {
    return null;
  }
  const state = token.state.kind === "Invalid" ? token.state.prevState : token.state;
  if (!state) {
    return null;
  }
  const typeInfo = getTypeInfo(schema, token.state);
  const mode = (options === null || options === void 0 ? void 0 : options.mode) || getDocumentMode(queryText, options === null || options === void 0 ? void 0 : options.uri);
  return {
    token,
    state,
    typeInfo,
    mode
  };
}

// node_modules/graphql-language-service/esm/parser/getTypeInfo.js
function getFieldDef(schema, type2, fieldName) {
  if (fieldName === SchemaMetaFieldDef.name && schema.getQueryType() === type2) {
    return SchemaMetaFieldDef;
  }
  if (fieldName === TypeMetaFieldDef.name && schema.getQueryType() === type2) {
    return TypeMetaFieldDef;
  }
  if (fieldName === TypeNameMetaFieldDef.name && isCompositeType(type2)) {
    return TypeNameMetaFieldDef;
  }
  if ("getFields" in type2) {
    return type2.getFields()[fieldName];
  }
  return null;
}
function forEachState(stack, fn) {
  const reverseStateStack = [];
  let state = stack;
  while (state === null || state === void 0 ? void 0 : state.kind) {
    reverseStateStack.push(state);
    state = state.prevState;
  }
  for (let i = reverseStateStack.length - 1; i >= 0; i--) {
    fn(reverseStateStack[i]);
  }
}
function getDefinitionState(tokenState) {
  let definitionState;
  forEachState(tokenState, (state) => {
    switch (state.kind) {
      case "Query":
      case "ShortQuery":
      case "Mutation":
      case "Subscription":
      case "FragmentDefinition":
        definitionState = state;
        break;
    }
  });
  return definitionState;
}
function getTypeInfo(schema, tokenState) {
  let argDef;
  let argDefs;
  let directiveDef;
  let enumValue;
  let fieldDef;
  let inputType;
  let objectTypeDef;
  let objectFieldDefs;
  let parentType;
  let type2;
  let interfaceDef;
  forEachState(tokenState, (state) => {
    var _a;
    switch (state.kind) {
      case RuleKinds.QUERY:
      case "ShortQuery":
        type2 = schema.getQueryType();
        break;
      case RuleKinds.MUTATION:
        type2 = schema.getMutationType();
        break;
      case RuleKinds.SUBSCRIPTION:
        type2 = schema.getSubscriptionType();
        break;
      case RuleKinds.INLINE_FRAGMENT:
      case RuleKinds.FRAGMENT_DEFINITION:
        if (state.type) {
          type2 = schema.getType(state.type);
        }
        break;
      case RuleKinds.FIELD:
      case RuleKinds.ALIASED_FIELD: {
        if (!type2 || !state.name) {
          fieldDef = null;
        } else {
          fieldDef = parentType ? getFieldDef(schema, parentType, state.name) : null;
          type2 = fieldDef ? fieldDef.type : null;
        }
        break;
      }
      case RuleKinds.SELECTION_SET:
        parentType = getNamedType(type2);
        break;
      case RuleKinds.DIRECTIVE:
        directiveDef = state.name ? schema.getDirective(state.name) : null;
        break;
      case RuleKinds.INTERFACE_DEF:
        if (state.name) {
          objectTypeDef = null;
          interfaceDef = new GraphQLInterfaceType({
            name: state.name,
            interfaces: [],
            fields: {}
          });
        }
        break;
      case RuleKinds.OBJECT_TYPE_DEF:
        if (state.name) {
          interfaceDef = null;
          objectTypeDef = new GraphQLObjectType({
            name: state.name,
            interfaces: [],
            fields: {}
          });
        }
        break;
      case RuleKinds.ARGUMENTS: {
        if (state.prevState) {
          switch (state.prevState.kind) {
            case RuleKinds.FIELD:
              argDefs = fieldDef && fieldDef.args;
              break;
            case RuleKinds.DIRECTIVE:
              argDefs = directiveDef && directiveDef.args;
              break;
            case RuleKinds.ALIASED_FIELD: {
              const name2 = (_a = state.prevState) === null || _a === void 0 ? void 0 : _a.name;
              if (!name2) {
                argDefs = null;
                break;
              }
              const field = parentType ? getFieldDef(schema, parentType, name2) : null;
              if (!field) {
                argDefs = null;
                break;
              }
              argDefs = field.args;
              break;
            }
            default:
              argDefs = null;
              break;
          }
        } else {
          argDefs = null;
        }
        break;
      }
      case RuleKinds.ARGUMENT:
        if (argDefs) {
          for (let i = 0; i < argDefs.length; i++) {
            if (argDefs[i].name === state.name) {
              argDef = argDefs[i];
              break;
            }
          }
        }
        inputType = argDef === null || argDef === void 0 ? void 0 : argDef.type;
        break;
      case RuleKinds.VARIABLE_DEFINITION:
      case RuleKinds.VARIABLE:
        type2 = inputType;
        break;
      case RuleKinds.ENUM_VALUE:
        const enumType = getNamedType(inputType);
        enumValue = enumType instanceof GraphQLEnumType ? enumType.getValues().find((val) => val.value === state.name) : null;
        break;
      case RuleKinds.LIST_VALUE:
        const nullableType = getNullableType(inputType);
        inputType = nullableType instanceof GraphQLList ? nullableType.ofType : null;
        break;
      case RuleKinds.OBJECT_VALUE:
        const objectType = getNamedType(inputType);
        objectFieldDefs = objectType instanceof GraphQLInputObjectType ? objectType.getFields() : null;
        break;
      case RuleKinds.OBJECT_FIELD:
        const objectField = state.name && objectFieldDefs ? objectFieldDefs[state.name] : null;
        inputType = objectField === null || objectField === void 0 ? void 0 : objectField.type;
        fieldDef = objectField;
        type2 = fieldDef ? fieldDef.type : null;
        break;
      case RuleKinds.NAMED_TYPE:
        if (state.name) {
          type2 = schema.getType(state.name);
        }
        break;
    }
  });
  return {
    argDef,
    argDefs,
    directiveDef,
    enumValue,
    fieldDef,
    inputType,
    objectFieldDefs,
    parentType,
    type: type2,
    interfaceDef,
    objectTypeDef
  };
}

// node_modules/graphql-language-service/esm/parser/types.js
var AdditionalRuleKinds = {
  ALIASED_FIELD: "AliasedField",
  ARGUMENTS: "Arguments",
  SHORT_QUERY: "ShortQuery",
  QUERY: "Query",
  MUTATION: "Mutation",
  SUBSCRIPTION: "Subscription",
  TYPE_CONDITION: "TypeCondition",
  INVALID: "Invalid",
  COMMENT: "Comment",
  SCHEMA_DEF: "SchemaDef",
  SCALAR_DEF: "ScalarDef",
  OBJECT_TYPE_DEF: "ObjectTypeDef",
  OBJECT_VALUE: "ObjectValue",
  LIST_VALUE: "ListValue",
  INTERFACE_DEF: "InterfaceDef",
  UNION_DEF: "UnionDef",
  ENUM_DEF: "EnumDef",
  ENUM_VALUE: "EnumValue",
  FIELD_DEF: "FieldDef",
  INPUT_DEF: "InputDef",
  INPUT_VALUE_DEF: "InputValueDef",
  ARGUMENTS_DEF: "ArgumentsDef",
  EXTEND_DEF: "ExtendDef",
  EXTENSION_DEFINITION: "ExtensionDefinition",
  DIRECTIVE_DEF: "DirectiveDef",
  IMPLEMENTS: "Implements",
  VARIABLE_DEFINITIONS: "VariableDefinitions",
  TYPE: "Type",
  VARIABLE: "Variable"
};
var RuleKinds = Object.assign(Object.assign({}, Kind), AdditionalRuleKinds);

// node_modules/vscode-languageserver-types/lib/esm/main.js
var DocumentUri;
(function(DocumentUri2) {
  function is(value) {
    return typeof value === "string";
  }
  DocumentUri2.is = is;
})(DocumentUri || (DocumentUri = {}));
var URI;
(function(URI2) {
  function is(value) {
    return typeof value === "string";
  }
  URI2.is = is;
})(URI || (URI = {}));
var integer;
(function(integer2) {
  integer2.MIN_VALUE = -2147483648;
  integer2.MAX_VALUE = 2147483647;
  function is(value) {
    return typeof value === "number" && integer2.MIN_VALUE <= value && value <= integer2.MAX_VALUE;
  }
  integer2.is = is;
})(integer || (integer = {}));
var uinteger;
(function(uinteger2) {
  uinteger2.MIN_VALUE = 0;
  uinteger2.MAX_VALUE = 2147483647;
  function is(value) {
    return typeof value === "number" && uinteger2.MIN_VALUE <= value && value <= uinteger2.MAX_VALUE;
  }
  uinteger2.is = is;
})(uinteger || (uinteger = {}));
var Position;
(function(Position3) {
  function create(line, character) {
    if (line === Number.MAX_VALUE) {
      line = uinteger.MAX_VALUE;
    }
    if (character === Number.MAX_VALUE) {
      character = uinteger.MAX_VALUE;
    }
    return { line, character };
  }
  Position3.create = create;
  function is(value) {
    let candidate = value;
    return Is.objectLiteral(candidate) && Is.uinteger(candidate.line) && Is.uinteger(candidate.character);
  }
  Position3.is = is;
})(Position || (Position = {}));
var Range;
(function(Range3) {
  function create(one, two, three, four) {
    if (Is.uinteger(one) && Is.uinteger(two) && Is.uinteger(three) && Is.uinteger(four)) {
      return { start: Position.create(one, two), end: Position.create(three, four) };
    } else if (Position.is(one) && Position.is(two)) {
      return { start: one, end: two };
    } else {
      throw new Error(`Range#create called with invalid arguments[${one}, ${two}, ${three}, ${four}]`);
    }
  }
  Range3.create = create;
  function is(value) {
    let candidate = value;
    return Is.objectLiteral(candidate) && Position.is(candidate.start) && Position.is(candidate.end);
  }
  Range3.is = is;
})(Range || (Range = {}));
var Location;
(function(Location2) {
  function create(uri, range) {
    return { uri, range };
  }
  Location2.create = create;
  function is(value) {
    let candidate = value;
    return Is.objectLiteral(candidate) && Range.is(candidate.range) && (Is.string(candidate.uri) || Is.undefined(candidate.uri));
  }
  Location2.is = is;
})(Location || (Location = {}));
var LocationLink;
(function(LocationLink2) {
  function create(targetUri, targetRange, targetSelectionRange, originSelectionRange) {
    return { targetUri, targetRange, targetSelectionRange, originSelectionRange };
  }
  LocationLink2.create = create;
  function is(value) {
    let candidate = value;
    return Is.objectLiteral(candidate) && Range.is(candidate.targetRange) && Is.string(candidate.targetUri) && Range.is(candidate.targetSelectionRange) && (Range.is(candidate.originSelectionRange) || Is.undefined(candidate.originSelectionRange));
  }
  LocationLink2.is = is;
})(LocationLink || (LocationLink = {}));
var Color;
(function(Color2) {
  function create(red, green, blue, alpha) {
    return {
      red,
      green,
      blue,
      alpha
    };
  }
  Color2.create = create;
  function is(value) {
    const candidate = value;
    return Is.objectLiteral(candidate) && Is.numberRange(candidate.red, 0, 1) && Is.numberRange(candidate.green, 0, 1) && Is.numberRange(candidate.blue, 0, 1) && Is.numberRange(candidate.alpha, 0, 1);
  }
  Color2.is = is;
})(Color || (Color = {}));
var ColorInformation;
(function(ColorInformation2) {
  function create(range, color) {
    return {
      range,
      color
    };
  }
  ColorInformation2.create = create;
  function is(value) {
    const candidate = value;
    return Is.objectLiteral(candidate) && Range.is(candidate.range) && Color.is(candidate.color);
  }
  ColorInformation2.is = is;
})(ColorInformation || (ColorInformation = {}));
var ColorPresentation;
(function(ColorPresentation2) {
  function create(label, textEdit, additionalTextEdits) {
    return {
      label,
      textEdit,
      additionalTextEdits
    };
  }
  ColorPresentation2.create = create;
  function is(value) {
    const candidate = value;
    return Is.objectLiteral(candidate) && Is.string(candidate.label) && (Is.undefined(candidate.textEdit) || TextEdit.is(candidate)) && (Is.undefined(candidate.additionalTextEdits) || Is.typedArray(candidate.additionalTextEdits, TextEdit.is));
  }
  ColorPresentation2.is = is;
})(ColorPresentation || (ColorPresentation = {}));
var FoldingRangeKind;
(function(FoldingRangeKind2) {
  FoldingRangeKind2.Comment = "comment";
  FoldingRangeKind2.Imports = "imports";
  FoldingRangeKind2.Region = "region";
})(FoldingRangeKind || (FoldingRangeKind = {}));
var FoldingRange;
(function(FoldingRange2) {
  function create(startLine, endLine, startCharacter, endCharacter, kind, collapsedText) {
    const result = {
      startLine,
      endLine
    };
    if (Is.defined(startCharacter)) {
      result.startCharacter = startCharacter;
    }
    if (Is.defined(endCharacter)) {
      result.endCharacter = endCharacter;
    }
    if (Is.defined(kind)) {
      result.kind = kind;
    }
    if (Is.defined(collapsedText)) {
      result.collapsedText = collapsedText;
    }
    return result;
  }
  FoldingRange2.create = create;
  function is(value) {
    const candidate = value;
    return Is.objectLiteral(candidate) && Is.uinteger(candidate.startLine) && Is.uinteger(candidate.startLine) && (Is.undefined(candidate.startCharacter) || Is.uinteger(candidate.startCharacter)) && (Is.undefined(candidate.endCharacter) || Is.uinteger(candidate.endCharacter)) && (Is.undefined(candidate.kind) || Is.string(candidate.kind));
  }
  FoldingRange2.is = is;
})(FoldingRange || (FoldingRange = {}));
var DiagnosticRelatedInformation;
(function(DiagnosticRelatedInformation2) {
  function create(location, message) {
    return {
      location,
      message
    };
  }
  DiagnosticRelatedInformation2.create = create;
  function is(value) {
    let candidate = value;
    return Is.defined(candidate) && Location.is(candidate.location) && Is.string(candidate.message);
  }
  DiagnosticRelatedInformation2.is = is;
})(DiagnosticRelatedInformation || (DiagnosticRelatedInformation = {}));
var DiagnosticSeverity;
(function(DiagnosticSeverity2) {
  DiagnosticSeverity2.Error = 1;
  DiagnosticSeverity2.Warning = 2;
  DiagnosticSeverity2.Information = 3;
  DiagnosticSeverity2.Hint = 4;
})(DiagnosticSeverity || (DiagnosticSeverity = {}));
var DiagnosticTag;
(function(DiagnosticTag2) {
  DiagnosticTag2.Unnecessary = 1;
  DiagnosticTag2.Deprecated = 2;
})(DiagnosticTag || (DiagnosticTag = {}));
var CodeDescription;
(function(CodeDescription2) {
  function is(value) {
    const candidate = value;
    return Is.objectLiteral(candidate) && Is.string(candidate.href);
  }
  CodeDescription2.is = is;
})(CodeDescription || (CodeDescription = {}));
var Diagnostic;
(function(Diagnostic2) {
  function create(range, message, severity, code, source, relatedInformation) {
    let result = { range, message };
    if (Is.defined(severity)) {
      result.severity = severity;
    }
    if (Is.defined(code)) {
      result.code = code;
    }
    if (Is.defined(source)) {
      result.source = source;
    }
    if (Is.defined(relatedInformation)) {
      result.relatedInformation = relatedInformation;
    }
    return result;
  }
  Diagnostic2.create = create;
  function is(value) {
    var _a;
    let candidate = value;
    return Is.defined(candidate) && Range.is(candidate.range) && Is.string(candidate.message) && (Is.number(candidate.severity) || Is.undefined(candidate.severity)) && (Is.integer(candidate.code) || Is.string(candidate.code) || Is.undefined(candidate.code)) && (Is.undefined(candidate.codeDescription) || Is.string((_a = candidate.codeDescription) === null || _a === void 0 ? void 0 : _a.href)) && (Is.string(candidate.source) || Is.undefined(candidate.source)) && (Is.undefined(candidate.relatedInformation) || Is.typedArray(candidate.relatedInformation, DiagnosticRelatedInformation.is));
  }
  Diagnostic2.is = is;
})(Diagnostic || (Diagnostic = {}));
var Command;
(function(Command2) {
  function create(title, command, ...args) {
    let result = { title, command };
    if (Is.defined(args) && args.length > 0) {
      result.arguments = args;
    }
    return result;
  }
  Command2.create = create;
  function is(value) {
    let candidate = value;
    return Is.defined(candidate) && Is.string(candidate.title) && Is.string(candidate.command);
  }
  Command2.is = is;
})(Command || (Command = {}));
var TextEdit;
(function(TextEdit2) {
  function replace(range, newText) {
    return { range, newText };
  }
  TextEdit2.replace = replace;
  function insert(position, newText) {
    return { range: { start: position, end: position }, newText };
  }
  TextEdit2.insert = insert;
  function del(range) {
    return { range, newText: "" };
  }
  TextEdit2.del = del;
  function is(value) {
    const candidate = value;
    return Is.objectLiteral(candidate) && Is.string(candidate.newText) && Range.is(candidate.range);
  }
  TextEdit2.is = is;
})(TextEdit || (TextEdit = {}));
var ChangeAnnotation;
(function(ChangeAnnotation2) {
  function create(label, needsConfirmation, description) {
    const result = { label };
    if (needsConfirmation !== void 0) {
      result.needsConfirmation = needsConfirmation;
    }
    if (description !== void 0) {
      result.description = description;
    }
    return result;
  }
  ChangeAnnotation2.create = create;
  function is(value) {
    const candidate = value;
    return Is.objectLiteral(candidate) && Is.string(candidate.label) && (Is.boolean(candidate.needsConfirmation) || candidate.needsConfirmation === void 0) && (Is.string(candidate.description) || candidate.description === void 0);
  }
  ChangeAnnotation2.is = is;
})(ChangeAnnotation || (ChangeAnnotation = {}));
var ChangeAnnotationIdentifier;
(function(ChangeAnnotationIdentifier2) {
  function is(value) {
    const candidate = value;
    return Is.string(candidate);
  }
  ChangeAnnotationIdentifier2.is = is;
})(ChangeAnnotationIdentifier || (ChangeAnnotationIdentifier = {}));
var AnnotatedTextEdit;
(function(AnnotatedTextEdit2) {
  function replace(range, newText, annotation) {
    return { range, newText, annotationId: annotation };
  }
  AnnotatedTextEdit2.replace = replace;
  function insert(position, newText, annotation) {
    return { range: { start: position, end: position }, newText, annotationId: annotation };
  }
  AnnotatedTextEdit2.insert = insert;
  function del(range, annotation) {
    return { range, newText: "", annotationId: annotation };
  }
  AnnotatedTextEdit2.del = del;
  function is(value) {
    const candidate = value;
    return TextEdit.is(candidate) && (ChangeAnnotation.is(candidate.annotationId) || ChangeAnnotationIdentifier.is(candidate.annotationId));
  }
  AnnotatedTextEdit2.is = is;
})(AnnotatedTextEdit || (AnnotatedTextEdit = {}));
var TextDocumentEdit;
(function(TextDocumentEdit2) {
  function create(textDocument, edits) {
    return { textDocument, edits };
  }
  TextDocumentEdit2.create = create;
  function is(value) {
    let candidate = value;
    return Is.defined(candidate) && OptionalVersionedTextDocumentIdentifier.is(candidate.textDocument) && Array.isArray(candidate.edits);
  }
  TextDocumentEdit2.is = is;
})(TextDocumentEdit || (TextDocumentEdit = {}));
var CreateFile;
(function(CreateFile2) {
  function create(uri, options, annotation) {
    let result = {
      kind: "create",
      uri
    };
    if (options !== void 0 && (options.overwrite !== void 0 || options.ignoreIfExists !== void 0)) {
      result.options = options;
    }
    if (annotation !== void 0) {
      result.annotationId = annotation;
    }
    return result;
  }
  CreateFile2.create = create;
  function is(value) {
    let candidate = value;
    return candidate && candidate.kind === "create" && Is.string(candidate.uri) && (candidate.options === void 0 || (candidate.options.overwrite === void 0 || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === void 0 || Is.boolean(candidate.options.ignoreIfExists))) && (candidate.annotationId === void 0 || ChangeAnnotationIdentifier.is(candidate.annotationId));
  }
  CreateFile2.is = is;
})(CreateFile || (CreateFile = {}));
var RenameFile;
(function(RenameFile2) {
  function create(oldUri, newUri, options, annotation) {
    let result = {
      kind: "rename",
      oldUri,
      newUri
    };
    if (options !== void 0 && (options.overwrite !== void 0 || options.ignoreIfExists !== void 0)) {
      result.options = options;
    }
    if (annotation !== void 0) {
      result.annotationId = annotation;
    }
    return result;
  }
  RenameFile2.create = create;
  function is(value) {
    let candidate = value;
    return candidate && candidate.kind === "rename" && Is.string(candidate.oldUri) && Is.string(candidate.newUri) && (candidate.options === void 0 || (candidate.options.overwrite === void 0 || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === void 0 || Is.boolean(candidate.options.ignoreIfExists))) && (candidate.annotationId === void 0 || ChangeAnnotationIdentifier.is(candidate.annotationId));
  }
  RenameFile2.is = is;
})(RenameFile || (RenameFile = {}));
var DeleteFile;
(function(DeleteFile2) {
  function create(uri, options, annotation) {
    let result = {
      kind: "delete",
      uri
    };
    if (options !== void 0 && (options.recursive !== void 0 || options.ignoreIfNotExists !== void 0)) {
      result.options = options;
    }
    if (annotation !== void 0) {
      result.annotationId = annotation;
    }
    return result;
  }
  DeleteFile2.create = create;
  function is(value) {
    let candidate = value;
    return candidate && candidate.kind === "delete" && Is.string(candidate.uri) && (candidate.options === void 0 || (candidate.options.recursive === void 0 || Is.boolean(candidate.options.recursive)) && (candidate.options.ignoreIfNotExists === void 0 || Is.boolean(candidate.options.ignoreIfNotExists))) && (candidate.annotationId === void 0 || ChangeAnnotationIdentifier.is(candidate.annotationId));
  }
  DeleteFile2.is = is;
})(DeleteFile || (DeleteFile = {}));
var WorkspaceEdit;
(function(WorkspaceEdit2) {
  function is(value) {
    let candidate = value;
    return candidate && (candidate.changes !== void 0 || candidate.documentChanges !== void 0) && (candidate.documentChanges === void 0 || candidate.documentChanges.every((change) => {
      if (Is.string(change.kind)) {
        return CreateFile.is(change) || RenameFile.is(change) || DeleteFile.is(change);
      } else {
        return TextDocumentEdit.is(change);
      }
    }));
  }
  WorkspaceEdit2.is = is;
})(WorkspaceEdit || (WorkspaceEdit = {}));
var TextDocumentIdentifier;
(function(TextDocumentIdentifier2) {
  function create(uri) {
    return { uri };
  }
  TextDocumentIdentifier2.create = create;
  function is(value) {
    let candidate = value;
    return Is.defined(candidate) && Is.string(candidate.uri);
  }
  TextDocumentIdentifier2.is = is;
})(TextDocumentIdentifier || (TextDocumentIdentifier = {}));
var VersionedTextDocumentIdentifier;
(function(VersionedTextDocumentIdentifier2) {
  function create(uri, version) {
    return { uri, version };
  }
  VersionedTextDocumentIdentifier2.create = create;
  function is(value) {
    let candidate = value;
    return Is.defined(candidate) && Is.string(candidate.uri) && Is.integer(candidate.version);
  }
  VersionedTextDocumentIdentifier2.is = is;
})(VersionedTextDocumentIdentifier || (VersionedTextDocumentIdentifier = {}));
var OptionalVersionedTextDocumentIdentifier;
(function(OptionalVersionedTextDocumentIdentifier2) {
  function create(uri, version) {
    return { uri, version };
  }
  OptionalVersionedTextDocumentIdentifier2.create = create;
  function is(value) {
    let candidate = value;
    return Is.defined(candidate) && Is.string(candidate.uri) && (candidate.version === null || Is.integer(candidate.version));
  }
  OptionalVersionedTextDocumentIdentifier2.is = is;
})(OptionalVersionedTextDocumentIdentifier || (OptionalVersionedTextDocumentIdentifier = {}));
var TextDocumentItem;
(function(TextDocumentItem2) {
  function create(uri, languageId, version, text) {
    return { uri, languageId, version, text };
  }
  TextDocumentItem2.create = create;
  function is(value) {
    let candidate = value;
    return Is.defined(candidate) && Is.string(candidate.uri) && Is.string(candidate.languageId) && Is.integer(candidate.version) && Is.string(candidate.text);
  }
  TextDocumentItem2.is = is;
})(TextDocumentItem || (TextDocumentItem = {}));
var MarkupKind;
(function(MarkupKind2) {
  MarkupKind2.PlainText = "plaintext";
  MarkupKind2.Markdown = "markdown";
  function is(value) {
    const candidate = value;
    return candidate === MarkupKind2.PlainText || candidate === MarkupKind2.Markdown;
  }
  MarkupKind2.is = is;
})(MarkupKind || (MarkupKind = {}));
var MarkupContent;
(function(MarkupContent2) {
  function is(value) {
    const candidate = value;
    return Is.objectLiteral(value) && MarkupKind.is(candidate.kind) && Is.string(candidate.value);
  }
  MarkupContent2.is = is;
})(MarkupContent || (MarkupContent = {}));
var CompletionItemKind;
(function(CompletionItemKind3) {
  CompletionItemKind3.Text = 1;
  CompletionItemKind3.Method = 2;
  CompletionItemKind3.Function = 3;
  CompletionItemKind3.Constructor = 4;
  CompletionItemKind3.Field = 5;
  CompletionItemKind3.Variable = 6;
  CompletionItemKind3.Class = 7;
  CompletionItemKind3.Interface = 8;
  CompletionItemKind3.Module = 9;
  CompletionItemKind3.Property = 10;
  CompletionItemKind3.Unit = 11;
  CompletionItemKind3.Value = 12;
  CompletionItemKind3.Enum = 13;
  CompletionItemKind3.Keyword = 14;
  CompletionItemKind3.Snippet = 15;
  CompletionItemKind3.Color = 16;
  CompletionItemKind3.File = 17;
  CompletionItemKind3.Reference = 18;
  CompletionItemKind3.Folder = 19;
  CompletionItemKind3.EnumMember = 20;
  CompletionItemKind3.Constant = 21;
  CompletionItemKind3.Struct = 22;
  CompletionItemKind3.Event = 23;
  CompletionItemKind3.Operator = 24;
  CompletionItemKind3.TypeParameter = 25;
})(CompletionItemKind || (CompletionItemKind = {}));
var InsertTextFormat;
(function(InsertTextFormat2) {
  InsertTextFormat2.PlainText = 1;
  InsertTextFormat2.Snippet = 2;
})(InsertTextFormat || (InsertTextFormat = {}));
var CompletionItemTag;
(function(CompletionItemTag2) {
  CompletionItemTag2.Deprecated = 1;
})(CompletionItemTag || (CompletionItemTag = {}));
var InsertReplaceEdit;
(function(InsertReplaceEdit2) {
  function create(newText, insert, replace) {
    return { newText, insert, replace };
  }
  InsertReplaceEdit2.create = create;
  function is(value) {
    const candidate = value;
    return candidate && Is.string(candidate.newText) && Range.is(candidate.insert) && Range.is(candidate.replace);
  }
  InsertReplaceEdit2.is = is;
})(InsertReplaceEdit || (InsertReplaceEdit = {}));
var InsertTextMode;
(function(InsertTextMode2) {
  InsertTextMode2.asIs = 1;
  InsertTextMode2.adjustIndentation = 2;
})(InsertTextMode || (InsertTextMode = {}));
var CompletionItemLabelDetails;
(function(CompletionItemLabelDetails2) {
  function is(value) {
    const candidate = value;
    return candidate && (Is.string(candidate.detail) || candidate.detail === void 0) && (Is.string(candidate.description) || candidate.description === void 0);
  }
  CompletionItemLabelDetails2.is = is;
})(CompletionItemLabelDetails || (CompletionItemLabelDetails = {}));
var CompletionItem;
(function(CompletionItem2) {
  function create(label) {
    return { label };
  }
  CompletionItem2.create = create;
})(CompletionItem || (CompletionItem = {}));
var CompletionList;
(function(CompletionList2) {
  function create(items, isIncomplete) {
    return { items: items ? items : [], isIncomplete: !!isIncomplete };
  }
  CompletionList2.create = create;
})(CompletionList || (CompletionList = {}));
var MarkedString;
(function(MarkedString2) {
  function fromPlainText(plainText) {
    return plainText.replace(/[\\`*_{}[\]()#+\-.!]/g, "\\$&");
  }
  MarkedString2.fromPlainText = fromPlainText;
  function is(value) {
    const candidate = value;
    return Is.string(candidate) || Is.objectLiteral(candidate) && Is.string(candidate.language) && Is.string(candidate.value);
  }
  MarkedString2.is = is;
})(MarkedString || (MarkedString = {}));
var Hover;
(function(Hover2) {
  function is(value) {
    let candidate = value;
    return !!candidate && Is.objectLiteral(candidate) && (MarkupContent.is(candidate.contents) || MarkedString.is(candidate.contents) || Is.typedArray(candidate.contents, MarkedString.is)) && (value.range === void 0 || Range.is(value.range));
  }
  Hover2.is = is;
})(Hover || (Hover = {}));
var ParameterInformation;
(function(ParameterInformation2) {
  function create(label, documentation) {
    return documentation ? { label, documentation } : { label };
  }
  ParameterInformation2.create = create;
})(ParameterInformation || (ParameterInformation = {}));
var SignatureInformation;
(function(SignatureInformation2) {
  function create(label, documentation, ...parameters) {
    let result = { label };
    if (Is.defined(documentation)) {
      result.documentation = documentation;
    }
    if (Is.defined(parameters)) {
      result.parameters = parameters;
    } else {
      result.parameters = [];
    }
    return result;
  }
  SignatureInformation2.create = create;
})(SignatureInformation || (SignatureInformation = {}));
var DocumentHighlightKind;
(function(DocumentHighlightKind2) {
  DocumentHighlightKind2.Text = 1;
  DocumentHighlightKind2.Read = 2;
  DocumentHighlightKind2.Write = 3;
})(DocumentHighlightKind || (DocumentHighlightKind = {}));
var DocumentHighlight;
(function(DocumentHighlight2) {
  function create(range, kind) {
    let result = { range };
    if (Is.number(kind)) {
      result.kind = kind;
    }
    return result;
  }
  DocumentHighlight2.create = create;
})(DocumentHighlight || (DocumentHighlight = {}));
var SymbolKind;
(function(SymbolKind2) {
  SymbolKind2.File = 1;
  SymbolKind2.Module = 2;
  SymbolKind2.Namespace = 3;
  SymbolKind2.Package = 4;
  SymbolKind2.Class = 5;
  SymbolKind2.Method = 6;
  SymbolKind2.Property = 7;
  SymbolKind2.Field = 8;
  SymbolKind2.Constructor = 9;
  SymbolKind2.Enum = 10;
  SymbolKind2.Interface = 11;
  SymbolKind2.Function = 12;
  SymbolKind2.Variable = 13;
  SymbolKind2.Constant = 14;
  SymbolKind2.String = 15;
  SymbolKind2.Number = 16;
  SymbolKind2.Boolean = 17;
  SymbolKind2.Array = 18;
  SymbolKind2.Object = 19;
  SymbolKind2.Key = 20;
  SymbolKind2.Null = 21;
  SymbolKind2.EnumMember = 22;
  SymbolKind2.Struct = 23;
  SymbolKind2.Event = 24;
  SymbolKind2.Operator = 25;
  SymbolKind2.TypeParameter = 26;
})(SymbolKind || (SymbolKind = {}));
var SymbolTag;
(function(SymbolTag2) {
  SymbolTag2.Deprecated = 1;
})(SymbolTag || (SymbolTag = {}));
var SymbolInformation;
(function(SymbolInformation2) {
  function create(name2, kind, range, uri, containerName) {
    let result = {
      name: name2,
      kind,
      location: { uri, range }
    };
    if (containerName) {
      result.containerName = containerName;
    }
    return result;
  }
  SymbolInformation2.create = create;
})(SymbolInformation || (SymbolInformation = {}));
var WorkspaceSymbol;
(function(WorkspaceSymbol2) {
  function create(name2, kind, uri, range) {
    return range !== void 0 ? { name: name2, kind, location: { uri, range } } : { name: name2, kind, location: { uri } };
  }
  WorkspaceSymbol2.create = create;
})(WorkspaceSymbol || (WorkspaceSymbol = {}));
var DocumentSymbol;
(function(DocumentSymbol2) {
  function create(name2, detail, kind, range, selectionRange, children) {
    let result = {
      name: name2,
      detail,
      kind,
      range,
      selectionRange
    };
    if (children !== void 0) {
      result.children = children;
    }
    return result;
  }
  DocumentSymbol2.create = create;
  function is(value) {
    let candidate = value;
    return candidate && Is.string(candidate.name) && Is.number(candidate.kind) && Range.is(candidate.range) && Range.is(candidate.selectionRange) && (candidate.detail === void 0 || Is.string(candidate.detail)) && (candidate.deprecated === void 0 || Is.boolean(candidate.deprecated)) && (candidate.children === void 0 || Array.isArray(candidate.children)) && (candidate.tags === void 0 || Array.isArray(candidate.tags));
  }
  DocumentSymbol2.is = is;
})(DocumentSymbol || (DocumentSymbol = {}));
var CodeActionKind;
(function(CodeActionKind2) {
  CodeActionKind2.Empty = "";
  CodeActionKind2.QuickFix = "quickfix";
  CodeActionKind2.Refactor = "refactor";
  CodeActionKind2.RefactorExtract = "refactor.extract";
  CodeActionKind2.RefactorInline = "refactor.inline";
  CodeActionKind2.RefactorRewrite = "refactor.rewrite";
  CodeActionKind2.Source = "source";
  CodeActionKind2.SourceOrganizeImports = "source.organizeImports";
  CodeActionKind2.SourceFixAll = "source.fixAll";
})(CodeActionKind || (CodeActionKind = {}));
var CodeActionTriggerKind;
(function(CodeActionTriggerKind2) {
  CodeActionTriggerKind2.Invoked = 1;
  CodeActionTriggerKind2.Automatic = 2;
})(CodeActionTriggerKind || (CodeActionTriggerKind = {}));
var CodeActionContext;
(function(CodeActionContext2) {
  function create(diagnostics, only, triggerKind) {
    let result = { diagnostics };
    if (only !== void 0 && only !== null) {
      result.only = only;
    }
    if (triggerKind !== void 0 && triggerKind !== null) {
      result.triggerKind = triggerKind;
    }
    return result;
  }
  CodeActionContext2.create = create;
  function is(value) {
    let candidate = value;
    return Is.defined(candidate) && Is.typedArray(candidate.diagnostics, Diagnostic.is) && (candidate.only === void 0 || Is.typedArray(candidate.only, Is.string)) && (candidate.triggerKind === void 0 || candidate.triggerKind === CodeActionTriggerKind.Invoked || candidate.triggerKind === CodeActionTriggerKind.Automatic);
  }
  CodeActionContext2.is = is;
})(CodeActionContext || (CodeActionContext = {}));
var CodeAction;
(function(CodeAction2) {
  function create(title, kindOrCommandOrEdit, kind) {
    let result = { title };
    let checkKind = true;
    if (typeof kindOrCommandOrEdit === "string") {
      checkKind = false;
      result.kind = kindOrCommandOrEdit;
    } else if (Command.is(kindOrCommandOrEdit)) {
      result.command = kindOrCommandOrEdit;
    } else {
      result.edit = kindOrCommandOrEdit;
    }
    if (checkKind && kind !== void 0) {
      result.kind = kind;
    }
    return result;
  }
  CodeAction2.create = create;
  function is(value) {
    let candidate = value;
    return candidate && Is.string(candidate.title) && (candidate.diagnostics === void 0 || Is.typedArray(candidate.diagnostics, Diagnostic.is)) && (candidate.kind === void 0 || Is.string(candidate.kind)) && (candidate.edit !== void 0 || candidate.command !== void 0) && (candidate.command === void 0 || Command.is(candidate.command)) && (candidate.isPreferred === void 0 || Is.boolean(candidate.isPreferred)) && (candidate.edit === void 0 || WorkspaceEdit.is(candidate.edit));
  }
  CodeAction2.is = is;
})(CodeAction || (CodeAction = {}));
var CodeLens;
(function(CodeLens2) {
  function create(range, data) {
    let result = { range };
    if (Is.defined(data)) {
      result.data = data;
    }
    return result;
  }
  CodeLens2.create = create;
  function is(value) {
    let candidate = value;
    return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.command) || Command.is(candidate.command));
  }
  CodeLens2.is = is;
})(CodeLens || (CodeLens = {}));
var FormattingOptions;
(function(FormattingOptions2) {
  function create(tabSize, insertSpaces) {
    return { tabSize, insertSpaces };
  }
  FormattingOptions2.create = create;
  function is(value) {
    let candidate = value;
    return Is.defined(candidate) && Is.uinteger(candidate.tabSize) && Is.boolean(candidate.insertSpaces);
  }
  FormattingOptions2.is = is;
})(FormattingOptions || (FormattingOptions = {}));
var DocumentLink;
(function(DocumentLink2) {
  function create(range, target, data) {
    return { range, target, data };
  }
  DocumentLink2.create = create;
  function is(value) {
    let candidate = value;
    return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.target) || Is.string(candidate.target));
  }
  DocumentLink2.is = is;
})(DocumentLink || (DocumentLink = {}));
var SelectionRange;
(function(SelectionRange2) {
  function create(range, parent) {
    return { range, parent };
  }
  SelectionRange2.create = create;
  function is(value) {
    let candidate = value;
    return Is.objectLiteral(candidate) && Range.is(candidate.range) && (candidate.parent === void 0 || SelectionRange2.is(candidate.parent));
  }
  SelectionRange2.is = is;
})(SelectionRange || (SelectionRange = {}));
var SemanticTokenTypes;
(function(SemanticTokenTypes2) {
  SemanticTokenTypes2["namespace"] = "namespace";
  SemanticTokenTypes2["type"] = "type";
  SemanticTokenTypes2["class"] = "class";
  SemanticTokenTypes2["enum"] = "enum";
  SemanticTokenTypes2["interface"] = "interface";
  SemanticTokenTypes2["struct"] = "struct";
  SemanticTokenTypes2["typeParameter"] = "typeParameter";
  SemanticTokenTypes2["parameter"] = "parameter";
  SemanticTokenTypes2["variable"] = "variable";
  SemanticTokenTypes2["property"] = "property";
  SemanticTokenTypes2["enumMember"] = "enumMember";
  SemanticTokenTypes2["event"] = "event";
  SemanticTokenTypes2["function"] = "function";
  SemanticTokenTypes2["method"] = "method";
  SemanticTokenTypes2["macro"] = "macro";
  SemanticTokenTypes2["keyword"] = "keyword";
  SemanticTokenTypes2["modifier"] = "modifier";
  SemanticTokenTypes2["comment"] = "comment";
  SemanticTokenTypes2["string"] = "string";
  SemanticTokenTypes2["number"] = "number";
  SemanticTokenTypes2["regexp"] = "regexp";
  SemanticTokenTypes2["operator"] = "operator";
  SemanticTokenTypes2["decorator"] = "decorator";
})(SemanticTokenTypes || (SemanticTokenTypes = {}));
var SemanticTokenModifiers;
(function(SemanticTokenModifiers2) {
  SemanticTokenModifiers2["declaration"] = "declaration";
  SemanticTokenModifiers2["definition"] = "definition";
  SemanticTokenModifiers2["readonly"] = "readonly";
  SemanticTokenModifiers2["static"] = "static";
  SemanticTokenModifiers2["deprecated"] = "deprecated";
  SemanticTokenModifiers2["abstract"] = "abstract";
  SemanticTokenModifiers2["async"] = "async";
  SemanticTokenModifiers2["modification"] = "modification";
  SemanticTokenModifiers2["documentation"] = "documentation";
  SemanticTokenModifiers2["defaultLibrary"] = "defaultLibrary";
})(SemanticTokenModifiers || (SemanticTokenModifiers = {}));
var SemanticTokens;
(function(SemanticTokens2) {
  function is(value) {
    const candidate = value;
    return Is.objectLiteral(candidate) && (candidate.resultId === void 0 || typeof candidate.resultId === "string") && Array.isArray(candidate.data) && (candidate.data.length === 0 || typeof candidate.data[0] === "number");
  }
  SemanticTokens2.is = is;
})(SemanticTokens || (SemanticTokens = {}));
var InlineValueText;
(function(InlineValueText2) {
  function create(range, text) {
    return { range, text };
  }
  InlineValueText2.create = create;
  function is(value) {
    const candidate = value;
    return candidate !== void 0 && candidate !== null && Range.is(candidate.range) && Is.string(candidate.text);
  }
  InlineValueText2.is = is;
})(InlineValueText || (InlineValueText = {}));
var InlineValueVariableLookup;
(function(InlineValueVariableLookup2) {
  function create(range, variableName, caseSensitiveLookup) {
    return { range, variableName, caseSensitiveLookup };
  }
  InlineValueVariableLookup2.create = create;
  function is(value) {
    const candidate = value;
    return candidate !== void 0 && candidate !== null && Range.is(candidate.range) && Is.boolean(candidate.caseSensitiveLookup) && (Is.string(candidate.variableName) || candidate.variableName === void 0);
  }
  InlineValueVariableLookup2.is = is;
})(InlineValueVariableLookup || (InlineValueVariableLookup = {}));
var InlineValueEvaluatableExpression;
(function(InlineValueEvaluatableExpression2) {
  function create(range, expression) {
    return { range, expression };
  }
  InlineValueEvaluatableExpression2.create = create;
  function is(value) {
    const candidate = value;
    return candidate !== void 0 && candidate !== null && Range.is(candidate.range) && (Is.string(candidate.expression) || candidate.expression === void 0);
  }
  InlineValueEvaluatableExpression2.is = is;
})(InlineValueEvaluatableExpression || (InlineValueEvaluatableExpression = {}));
var InlineValueContext;
(function(InlineValueContext2) {
  function create(frameId, stoppedLocation) {
    return { frameId, stoppedLocation };
  }
  InlineValueContext2.create = create;
  function is(value) {
    const candidate = value;
    return Is.defined(candidate) && Range.is(value.stoppedLocation);
  }
  InlineValueContext2.is = is;
})(InlineValueContext || (InlineValueContext = {}));
var InlayHintKind;
(function(InlayHintKind2) {
  InlayHintKind2.Type = 1;
  InlayHintKind2.Parameter = 2;
  function is(value) {
    return value === 1 || value === 2;
  }
  InlayHintKind2.is = is;
})(InlayHintKind || (InlayHintKind = {}));
var InlayHintLabelPart;
(function(InlayHintLabelPart2) {
  function create(value) {
    return { value };
  }
  InlayHintLabelPart2.create = create;
  function is(value) {
    const candidate = value;
    return Is.objectLiteral(candidate) && (candidate.tooltip === void 0 || Is.string(candidate.tooltip) || MarkupContent.is(candidate.tooltip)) && (candidate.location === void 0 || Location.is(candidate.location)) && (candidate.command === void 0 || Command.is(candidate.command));
  }
  InlayHintLabelPart2.is = is;
})(InlayHintLabelPart || (InlayHintLabelPart = {}));
var InlayHint;
(function(InlayHint2) {
  function create(position, label, kind) {
    const result = { position, label };
    if (kind !== void 0) {
      result.kind = kind;
    }
    return result;
  }
  InlayHint2.create = create;
  function is(value) {
    const candidate = value;
    return Is.objectLiteral(candidate) && Position.is(candidate.position) && (Is.string(candidate.label) || Is.typedArray(candidate.label, InlayHintLabelPart.is)) && (candidate.kind === void 0 || InlayHintKind.is(candidate.kind)) && candidate.textEdits === void 0 || Is.typedArray(candidate.textEdits, TextEdit.is) && (candidate.tooltip === void 0 || Is.string(candidate.tooltip) || MarkupContent.is(candidate.tooltip)) && (candidate.paddingLeft === void 0 || Is.boolean(candidate.paddingLeft)) && (candidate.paddingRight === void 0 || Is.boolean(candidate.paddingRight));
  }
  InlayHint2.is = is;
})(InlayHint || (InlayHint = {}));
var StringValue;
(function(StringValue2) {
  function createSnippet(value) {
    return { kind: "snippet", value };
  }
  StringValue2.createSnippet = createSnippet;
})(StringValue || (StringValue = {}));
var InlineCompletionItem;
(function(InlineCompletionItem2) {
  function create(insertText, filterText, range, command) {
    return { insertText, filterText, range, command };
  }
  InlineCompletionItem2.create = create;
})(InlineCompletionItem || (InlineCompletionItem = {}));
var InlineCompletionList;
(function(InlineCompletionList2) {
  function create(items) {
    return { items };
  }
  InlineCompletionList2.create = create;
})(InlineCompletionList || (InlineCompletionList = {}));
var InlineCompletionTriggerKind;
(function(InlineCompletionTriggerKind2) {
  InlineCompletionTriggerKind2.Invoked = 0;
  InlineCompletionTriggerKind2.Automatic = 1;
})(InlineCompletionTriggerKind || (InlineCompletionTriggerKind = {}));
var SelectedCompletionInfo;
(function(SelectedCompletionInfo2) {
  function create(range, text) {
    return { range, text };
  }
  SelectedCompletionInfo2.create = create;
})(SelectedCompletionInfo || (SelectedCompletionInfo = {}));
var InlineCompletionContext;
(function(InlineCompletionContext2) {
  function create(triggerKind, selectedCompletionInfo) {
    return { triggerKind, selectedCompletionInfo };
  }
  InlineCompletionContext2.create = create;
})(InlineCompletionContext || (InlineCompletionContext = {}));
var WorkspaceFolder;
(function(WorkspaceFolder2) {
  function is(value) {
    const candidate = value;
    return Is.objectLiteral(candidate) && URI.is(candidate.uri) && Is.string(candidate.name);
  }
  WorkspaceFolder2.is = is;
})(WorkspaceFolder || (WorkspaceFolder = {}));
var TextDocument;
(function(TextDocument2) {
  function create(uri, languageId, version, content) {
    return new FullTextDocument(uri, languageId, version, content);
  }
  TextDocument2.create = create;
  function is(value) {
    let candidate = value;
    return Is.defined(candidate) && Is.string(candidate.uri) && (Is.undefined(candidate.languageId) || Is.string(candidate.languageId)) && Is.uinteger(candidate.lineCount) && Is.func(candidate.getText) && Is.func(candidate.positionAt) && Is.func(candidate.offsetAt) ? true : false;
  }
  TextDocument2.is = is;
  function applyEdits(document, edits) {
    let text = document.getText();
    let sortedEdits = mergeSort(edits, (a, b) => {
      let diff = a.range.start.line - b.range.start.line;
      if (diff === 0) {
        return a.range.start.character - b.range.start.character;
      }
      return diff;
    });
    let lastModifiedOffset = text.length;
    for (let i = sortedEdits.length - 1; i >= 0; i--) {
      let e = sortedEdits[i];
      let startOffset = document.offsetAt(e.range.start);
      let endOffset = document.offsetAt(e.range.end);
      if (endOffset <= lastModifiedOffset) {
        text = text.substring(0, startOffset) + e.newText + text.substring(endOffset, text.length);
      } else {
        throw new Error("Overlapping edit");
      }
      lastModifiedOffset = startOffset;
    }
    return text;
  }
  TextDocument2.applyEdits = applyEdits;
  function mergeSort(data, compare) {
    if (data.length <= 1) {
      return data;
    }
    const p2 = data.length / 2 | 0;
    const left = data.slice(0, p2);
    const right = data.slice(p2);
    mergeSort(left, compare);
    mergeSort(right, compare);
    let leftIdx = 0;
    let rightIdx = 0;
    let i = 0;
    while (leftIdx < left.length && rightIdx < right.length) {
      let ret = compare(left[leftIdx], right[rightIdx]);
      if (ret <= 0) {
        data[i++] = left[leftIdx++];
      } else {
        data[i++] = right[rightIdx++];
      }
    }
    while (leftIdx < left.length) {
      data[i++] = left[leftIdx++];
    }
    while (rightIdx < right.length) {
      data[i++] = right[rightIdx++];
    }
    return data;
  }
})(TextDocument || (TextDocument = {}));
var FullTextDocument = class {
  constructor(uri, languageId, version, content) {
    this._uri = uri;
    this._languageId = languageId;
    this._version = version;
    this._content = content;
    this._lineOffsets = void 0;
  }
  get uri() {
    return this._uri;
  }
  get languageId() {
    return this._languageId;
  }
  get version() {
    return this._version;
  }
  getText(range) {
    if (range) {
      let start = this.offsetAt(range.start);
      let end = this.offsetAt(range.end);
      return this._content.substring(start, end);
    }
    return this._content;
  }
  update(event, version) {
    this._content = event.text;
    this._version = version;
    this._lineOffsets = void 0;
  }
  getLineOffsets() {
    if (this._lineOffsets === void 0) {
      let lineOffsets = [];
      let text = this._content;
      let isLineStart = true;
      for (let i = 0; i < text.length; i++) {
        if (isLineStart) {
          lineOffsets.push(i);
          isLineStart = false;
        }
        let ch = text.charAt(i);
        isLineStart = ch === "\r" || ch === "\n";
        if (ch === "\r" && i + 1 < text.length && text.charAt(i + 1) === "\n") {
          i++;
        }
      }
      if (isLineStart && text.length > 0) {
        lineOffsets.push(text.length);
      }
      this._lineOffsets = lineOffsets;
    }
    return this._lineOffsets;
  }
  positionAt(offset) {
    offset = Math.max(Math.min(offset, this._content.length), 0);
    let lineOffsets = this.getLineOffsets();
    let low = 0, high = lineOffsets.length;
    if (high === 0) {
      return Position.create(0, offset);
    }
    while (low < high) {
      let mid = Math.floor((low + high) / 2);
      if (lineOffsets[mid] > offset) {
        high = mid;
      } else {
        low = mid + 1;
      }
    }
    let line = low - 1;
    return Position.create(line, offset - lineOffsets[line]);
  }
  offsetAt(position) {
    let lineOffsets = this.getLineOffsets();
    if (position.line >= lineOffsets.length) {
      return this._content.length;
    } else if (position.line < 0) {
      return 0;
    }
    let lineOffset = lineOffsets[position.line];
    let nextLineOffset = position.line + 1 < lineOffsets.length ? lineOffsets[position.line + 1] : this._content.length;
    return Math.max(Math.min(lineOffset + position.character, nextLineOffset), lineOffset);
  }
  get lineCount() {
    return this.getLineOffsets().length;
  }
};
var Is;
(function(Is2) {
  const toString = Object.prototype.toString;
  function defined(value) {
    return typeof value !== "undefined";
  }
  Is2.defined = defined;
  function undefined2(value) {
    return typeof value === "undefined";
  }
  Is2.undefined = undefined2;
  function boolean(value) {
    return value === true || value === false;
  }
  Is2.boolean = boolean;
  function string(value) {
    return toString.call(value) === "[object String]";
  }
  Is2.string = string;
  function number(value) {
    return toString.call(value) === "[object Number]";
  }
  Is2.number = number;
  function numberRange(value, min, max) {
    return toString.call(value) === "[object Number]" && min <= value && value <= max;
  }
  Is2.numberRange = numberRange;
  function integer2(value) {
    return toString.call(value) === "[object Number]" && -2147483648 <= value && value <= 2147483647;
  }
  Is2.integer = integer2;
  function uinteger2(value) {
    return toString.call(value) === "[object Number]" && 0 <= value && value <= 2147483647;
  }
  Is2.uinteger = uinteger2;
  function func(value) {
    return toString.call(value) === "[object Function]";
  }
  Is2.func = func;
  function objectLiteral(value) {
    return value !== null && typeof value === "object";
  }
  Is2.objectLiteral = objectLiteral;
  function typedArray(value, check) {
    return Array.isArray(value) && value.every(check);
  }
  Is2.typedArray = typedArray;
})(Is || (Is = {}));

// node_modules/graphql-language-service/esm/types.js
var CompletionItemKind2;
(function(CompletionItemKind3) {
  CompletionItemKind3.Text = 1;
  CompletionItemKind3.Method = 2;
  CompletionItemKind3.Function = 3;
  CompletionItemKind3.Constructor = 4;
  CompletionItemKind3.Field = 5;
  CompletionItemKind3.Variable = 6;
  CompletionItemKind3.Class = 7;
  CompletionItemKind3.Interface = 8;
  CompletionItemKind3.Module = 9;
  CompletionItemKind3.Property = 10;
  CompletionItemKind3.Unit = 11;
  CompletionItemKind3.Value = 12;
  CompletionItemKind3.Enum = 13;
  CompletionItemKind3.Keyword = 14;
  CompletionItemKind3.Snippet = 15;
  CompletionItemKind3.Color = 16;
  CompletionItemKind3.File = 17;
  CompletionItemKind3.Reference = 18;
  CompletionItemKind3.Folder = 19;
  CompletionItemKind3.EnumMember = 20;
  CompletionItemKind3.Constant = 21;
  CompletionItemKind3.Struct = 22;
  CompletionItemKind3.Event = 23;
  CompletionItemKind3.Operator = 24;
  CompletionItemKind3.TypeParameter = 25;
})(CompletionItemKind2 || (CompletionItemKind2 = {}));

// node_modules/graphql-language-service/esm/interface/autocompleteUtils.js
function objectValues(object) {
  const keys = Object.keys(object);
  const len = keys.length;
  const values = new Array(len);
  for (let i = 0; i < len; ++i) {
    values[i] = object[keys[i]];
  }
  return values;
}
function hintList(token, list2) {
  return filterAndSortList(list2, normalizeText(token.string));
}
function filterAndSortList(list2, text) {
  if (!text || text.trim() === "" || text.trim() === ":" || text.trim() === "{") {
    return filterNonEmpty(list2, (entry) => !entry.isDeprecated);
  }
  const byProximity = list2.map((entry) => ({
    proximity: getProximity(normalizeText(entry.label), text),
    entry
  }));
  return filterNonEmpty(filterNonEmpty(byProximity, (pair) => pair.proximity <= 2), (pair) => !pair.entry.isDeprecated).sort((a, b) => (a.entry.isDeprecated ? 1 : 0) - (b.entry.isDeprecated ? 1 : 0) || a.proximity - b.proximity || a.entry.label.length - b.entry.label.length).map((pair) => pair.entry);
}
function filterNonEmpty(array, predicate) {
  const filtered = array.filter(predicate);
  return filtered.length === 0 ? array : filtered;
}
function normalizeText(text) {
  return text.toLowerCase().replaceAll(/\W/g, "");
}
function getProximity(suggestion, text) {
  let proximity = lexicalDistance(text, suggestion);
  if (suggestion.length > text.length) {
    proximity -= suggestion.length - text.length - 1;
    proximity += suggestion.indexOf(text) === 0 ? 0 : 0.5;
  }
  return proximity;
}
function lexicalDistance(a, b) {
  let i;
  let j;
  const d = [];
  const aLength = a.length;
  const bLength = b.length;
  for (i = 0; i <= aLength; i++) {
    d[i] = [i];
  }
  for (j = 1; j <= bLength; j++) {
    d[0][j] = j;
  }
  for (i = 1; i <= aLength; i++) {
    for (j = 1; j <= bLength; j++) {
      const cost = a[i - 1] === b[j - 1] ? 0 : 1;
      d[i][j] = Math.min(d[i - 1][j] + 1, d[i][j - 1] + 1, d[i - 1][j - 1] + cost);
      if (i > 1 && j > 1 && a[i - 1] === b[j - 2] && a[i - 2] === b[j - 1]) {
        d[i][j] = Math.min(d[i][j], d[i - 2][j - 2] + cost);
      }
    }
  }
  return d[aLength][bLength];
}
var insertSuffix = (n) => ` {
   $${n !== null && n !== void 0 ? n : 1}
}`;
var getInsertText = (prefix, type2, fallback) => {
  if (!type2) {
    return fallback !== null && fallback !== void 0 ? fallback : prefix;
  }
  const namedType = getNamedType(type2);
  if (isObjectType(namedType) || isInputObjectType(namedType) || isListType(namedType) || isAbstractType(namedType)) {
    return prefix + insertSuffix();
  }
  return fallback !== null && fallback !== void 0 ? fallback : prefix;
};
var getInputInsertText = (prefix, type2, fallback) => {
  if (isListType(type2)) {
    const baseType = getNamedType(type2.ofType);
    return prefix + `[${getInsertText("", baseType, "$1")}]`;
  }
  return getInsertText(prefix, type2, fallback);
};
var getFieldInsertText = (field) => {
  const requiredArgs = field.args.filter((arg) => arg.type.toString().endsWith("!"));
  if (!requiredArgs.length) {
    return;
  }
  return field.name + `(${requiredArgs.map((arg, i) => `${arg.name}: $${i + 1}`)}) ${getInsertText("", field.type, "\n")}`;
};

// node_modules/graphql-language-service/esm/interface/getAutocompleteSuggestions.js
var SuggestionCommand = {
  command: "editor.action.triggerSuggest",
  title: "Suggestions"
};
var collectFragmentDefs = (op) => {
  const externalFragments = [];
  if (op) {
    try {
      visit(parse(op), {
        FragmentDefinition(def) {
          externalFragments.push(def);
        }
      });
    } catch (_a) {
      return [];
    }
  }
  return externalFragments;
};
function getAutocompleteSuggestions(schema, queryText, cursor, contextToken, fragmentDefs, options) {
  var _a;
  const opts = Object.assign(Object.assign({}, options), { schema });
  const context = getContextAtPosition(queryText, cursor, schema, contextToken, options);
  if (!context) {
    return [];
  }
  const { state, typeInfo, mode, token } = context;
  const { kind, step, prevState } = state;
  if (kind === RuleKinds.DOCUMENT) {
    if (mode === GraphQLDocumentMode.TYPE_SYSTEM) {
      return getSuggestionsForTypeSystemDefinitions(token);
    }
    if (mode === GraphQLDocumentMode.EXECUTABLE) {
      return getSuggestionsForExecutableDefinitions(token);
    }
    return getSuggestionsForUnknownDocumentMode(token);
  }
  if (kind === RuleKinds.EXTEND_DEF) {
    return getSuggestionsForExtensionDefinitions(token);
  }
  if (((_a = prevState === null || prevState === void 0 ? void 0 : prevState.prevState) === null || _a === void 0 ? void 0 : _a.kind) === RuleKinds.EXTENSION_DEFINITION && state.name) {
    return hintList(token, []);
  }
  if ((prevState === null || prevState === void 0 ? void 0 : prevState.kind) === Kind.SCALAR_TYPE_EXTENSION) {
    return hintList(token, Object.values(schema.getTypeMap()).filter(isScalarType).map((type2) => ({
      label: type2.name,
      kind: CompletionItemKind2.Function
    })));
  }
  if ((prevState === null || prevState === void 0 ? void 0 : prevState.kind) === Kind.OBJECT_TYPE_EXTENSION) {
    return hintList(token, Object.values(schema.getTypeMap()).filter((type2) => isObjectType(type2) && !type2.name.startsWith("__")).map((type2) => ({
      label: type2.name,
      kind: CompletionItemKind2.Function
    })));
  }
  if ((prevState === null || prevState === void 0 ? void 0 : prevState.kind) === Kind.INTERFACE_TYPE_EXTENSION) {
    return hintList(token, Object.values(schema.getTypeMap()).filter(isInterfaceType).map((type2) => ({
      label: type2.name,
      kind: CompletionItemKind2.Function
    })));
  }
  if ((prevState === null || prevState === void 0 ? void 0 : prevState.kind) === Kind.UNION_TYPE_EXTENSION) {
    return hintList(token, Object.values(schema.getTypeMap()).filter(isUnionType).map((type2) => ({
      label: type2.name,
      kind: CompletionItemKind2.Function
    })));
  }
  if ((prevState === null || prevState === void 0 ? void 0 : prevState.kind) === Kind.ENUM_TYPE_EXTENSION) {
    return hintList(token, Object.values(schema.getTypeMap()).filter((type2) => isEnumType(type2) && !type2.name.startsWith("__")).map((type2) => ({
      label: type2.name,
      kind: CompletionItemKind2.Function
    })));
  }
  if ((prevState === null || prevState === void 0 ? void 0 : prevState.kind) === Kind.INPUT_OBJECT_TYPE_EXTENSION) {
    return hintList(token, Object.values(schema.getTypeMap()).filter(isInputObjectType).map((type2) => ({
      label: type2.name,
      kind: CompletionItemKind2.Function
    })));
  }
  if (kind === RuleKinds.IMPLEMENTS || kind === RuleKinds.NAMED_TYPE && (prevState === null || prevState === void 0 ? void 0 : prevState.kind) === RuleKinds.IMPLEMENTS) {
    return getSuggestionsForImplements(token, state, schema, queryText, typeInfo);
  }
  if (kind === RuleKinds.SELECTION_SET || kind === RuleKinds.FIELD || kind === RuleKinds.ALIASED_FIELD) {
    return getSuggestionsForFieldNames(token, typeInfo, opts);
  }
  if (kind === RuleKinds.ARGUMENTS || kind === RuleKinds.ARGUMENT && step === 0) {
    const { argDefs } = typeInfo;
    if (argDefs) {
      return hintList(token, argDefs.map((argDef) => {
        var _a2;
        return {
          label: argDef.name,
          insertText: getInputInsertText(argDef.name + ": ", argDef.type),
          insertTextMode: InsertTextMode.adjustIndentation,
          insertTextFormat: InsertTextFormat.Snippet,
          command: SuggestionCommand,
          labelDetails: {
            detail: " " + String(argDef.type)
          },
          documentation: (_a2 = argDef.description) !== null && _a2 !== void 0 ? _a2 : void 0,
          kind: CompletionItemKind2.Variable,
          type: argDef.type
        };
      }));
    }
  }
  if ((kind === RuleKinds.OBJECT_VALUE || kind === RuleKinds.OBJECT_FIELD && step === 0) && typeInfo.objectFieldDefs) {
    const objectFields = objectValues(typeInfo.objectFieldDefs);
    const completionKind = kind === RuleKinds.OBJECT_VALUE ? CompletionItemKind2.Value : CompletionItemKind2.Field;
    return hintList(token, objectFields.map((field) => {
      var _a2;
      return {
        label: field.name,
        detail: String(field.type),
        documentation: (_a2 = field === null || field === void 0 ? void 0 : field.description) !== null && _a2 !== void 0 ? _a2 : void 0,
        kind: completionKind,
        type: field.type,
        insertText: getInputInsertText(field.name + ": ", field.type),
        insertTextMode: InsertTextMode.adjustIndentation,
        insertTextFormat: InsertTextFormat.Snippet,
        command: SuggestionCommand
      };
    }));
  }
  if (kind === RuleKinds.ENUM_VALUE || kind === RuleKinds.LIST_VALUE && step === 1 || kind === RuleKinds.OBJECT_FIELD && step === 2 || kind === RuleKinds.ARGUMENT && step === 2) {
    return getSuggestionsForInputValues(token, typeInfo, queryText, schema);
  }
  if (kind === RuleKinds.VARIABLE && step === 1) {
    const namedInputType = getNamedType(typeInfo.inputType);
    const variableDefinitions = getVariableCompletions(queryText, schema, token);
    return hintList(token, variableDefinitions.filter((v) => v.detail === (namedInputType === null || namedInputType === void 0 ? void 0 : namedInputType.name)));
  }
  if (kind === RuleKinds.TYPE_CONDITION && step === 1 || kind === RuleKinds.NAMED_TYPE && prevState != null && prevState.kind === RuleKinds.TYPE_CONDITION) {
    return getSuggestionsForFragmentTypeConditions(token, typeInfo, schema, kind);
  }
  if (kind === RuleKinds.FRAGMENT_SPREAD && step === 1) {
    return getSuggestionsForFragmentSpread(token, typeInfo, schema, queryText, Array.isArray(fragmentDefs) ? fragmentDefs : collectFragmentDefs(fragmentDefs));
  }
  const unwrappedState = unwrapType(state);
  if (unwrappedState.kind === RuleKinds.FIELD_DEF) {
    return hintList(token, Object.values(schema.getTypeMap()).filter((type2) => isOutputType(type2) && !type2.name.startsWith("__")).map((type2) => ({
      label: type2.name,
      kind: CompletionItemKind2.Function,
      insertText: (options === null || options === void 0 ? void 0 : options.fillLeafsOnComplete) ? type2.name + "\n" : type2.name,
      insertTextMode: InsertTextMode.adjustIndentation
    })));
  }
  if (unwrappedState.kind === RuleKinds.INPUT_VALUE_DEF && step === 2) {
    return hintList(token, Object.values(schema.getTypeMap()).filter((type2) => isInputType(type2) && !type2.name.startsWith("__")).map((type2) => ({
      label: type2.name,
      kind: CompletionItemKind2.Function,
      insertText: (options === null || options === void 0 ? void 0 : options.fillLeafsOnComplete) ? type2.name + "\n$1" : type2.name,
      insertTextMode: InsertTextMode.adjustIndentation,
      insertTextFormat: InsertTextFormat.Snippet
    })));
  }
  if (kind === RuleKinds.VARIABLE_DEFINITION && step === 2 || kind === RuleKinds.LIST_TYPE && step === 1 || kind === RuleKinds.NAMED_TYPE && prevState && (prevState.kind === RuleKinds.VARIABLE_DEFINITION || prevState.kind === RuleKinds.LIST_TYPE || prevState.kind === RuleKinds.NON_NULL_TYPE)) {
    return getSuggestionsForVariableDefinition(token, schema, kind);
  }
  if (kind === RuleKinds.DIRECTIVE) {
    return getSuggestionsForDirective(token, state, schema, kind);
  }
  if (kind === RuleKinds.DIRECTIVE_DEF) {
    return getSuggestionsForDirectiveArguments(token, state, schema, kind);
  }
  return [];
}
var typeSystemCompletionItems = [
  { label: "type", kind: CompletionItemKind2.Function },
  { label: "interface", kind: CompletionItemKind2.Function },
  { label: "union", kind: CompletionItemKind2.Function },
  { label: "input", kind: CompletionItemKind2.Function },
  { label: "scalar", kind: CompletionItemKind2.Function },
  { label: "schema", kind: CompletionItemKind2.Function }
];
var executableCompletionItems = [
  { label: "query", kind: CompletionItemKind2.Function },
  { label: "mutation", kind: CompletionItemKind2.Function },
  { label: "subscription", kind: CompletionItemKind2.Function },
  { label: "fragment", kind: CompletionItemKind2.Function },
  { label: "{", kind: CompletionItemKind2.Constructor }
];
function getSuggestionsForTypeSystemDefinitions(token) {
  return hintList(token, [
    { label: "extend", kind: CompletionItemKind2.Function },
    ...typeSystemCompletionItems
  ]);
}
function getSuggestionsForExecutableDefinitions(token) {
  return hintList(token, executableCompletionItems);
}
function getSuggestionsForUnknownDocumentMode(token) {
  return hintList(token, [
    { label: "extend", kind: CompletionItemKind2.Function },
    ...executableCompletionItems,
    ...typeSystemCompletionItems
  ]);
}
function getSuggestionsForExtensionDefinitions(token) {
  return hintList(token, typeSystemCompletionItems);
}
function getSuggestionsForFieldNames(token, typeInfo, options) {
  var _a;
  if (typeInfo.parentType) {
    const { parentType } = typeInfo;
    let fields = [];
    if ("getFields" in parentType) {
      fields = objectValues(parentType.getFields());
    }
    if (isCompositeType(parentType)) {
      fields.push(TypeNameMetaFieldDef);
    }
    if (parentType === ((_a = options === null || options === void 0 ? void 0 : options.schema) === null || _a === void 0 ? void 0 : _a.getQueryType())) {
      fields.push(SchemaMetaFieldDef, TypeMetaFieldDef);
    }
    return hintList(token, fields.map((field, index) => {
      var _a2;
      const suggestion = {
        sortText: String(index) + field.name,
        label: field.name,
        detail: String(field.type),
        documentation: (_a2 = field.description) !== null && _a2 !== void 0 ? _a2 : void 0,
        deprecated: Boolean(field.deprecationReason),
        isDeprecated: Boolean(field.deprecationReason),
        deprecationReason: field.deprecationReason,
        kind: CompletionItemKind2.Field,
        labelDetails: {
          detail: " " + field.type.toString()
        },
        type: field.type
      };
      if (options === null || options === void 0 ? void 0 : options.fillLeafsOnComplete) {
        suggestion.insertText = getFieldInsertText(field);
        if (!suggestion.insertText) {
          suggestion.insertText = getInsertText(field.name, field.type, field.name + (token.state.needsAdvance ? "" : "\n"));
        }
        if (suggestion.insertText) {
          suggestion.insertTextFormat = InsertTextFormat.Snippet;
          suggestion.insertTextMode = InsertTextMode.adjustIndentation;
          suggestion.command = SuggestionCommand;
        }
      }
      return suggestion;
    }));
  }
  return [];
}
function getSuggestionsForInputValues(token, typeInfo, queryText, schema) {
  const namedInputType = getNamedType(typeInfo.inputType);
  const queryVariables = getVariableCompletions(queryText, schema, token).filter((v) => v.detail === (namedInputType === null || namedInputType === void 0 ? void 0 : namedInputType.name));
  if (namedInputType instanceof GraphQLEnumType) {
    const values = namedInputType.getValues();
    return hintList(token, values.map((value) => {
      var _a;
      return {
        label: value.name,
        detail: String(namedInputType),
        documentation: (_a = value.description) !== null && _a !== void 0 ? _a : void 0,
        deprecated: Boolean(value.deprecationReason),
        isDeprecated: Boolean(value.deprecationReason),
        deprecationReason: value.deprecationReason,
        kind: CompletionItemKind2.EnumMember,
        type: namedInputType
      };
    }).concat(queryVariables));
  }
  if (namedInputType === GraphQLBoolean) {
    return hintList(token, queryVariables.concat([
      {
        label: "true",
        detail: String(GraphQLBoolean),
        documentation: "Not false.",
        kind: CompletionItemKind2.Variable,
        type: GraphQLBoolean
      },
      {
        label: "false",
        detail: String(GraphQLBoolean),
        documentation: "Not true.",
        kind: CompletionItemKind2.Variable,
        type: GraphQLBoolean
      }
    ]));
  }
  return queryVariables;
}
function getSuggestionsForImplements(token, tokenState, schema, documentText, typeInfo) {
  if (tokenState.needsSeparator) {
    return [];
  }
  const typeMap = schema.getTypeMap();
  const schemaInterfaces = objectValues(typeMap).filter(isInterfaceType);
  const schemaInterfaceNames = schemaInterfaces.map(({ name: name2 }) => name2);
  const inlineInterfaces = /* @__PURE__ */ new Set();
  runOnlineParser(documentText, (_, state) => {
    var _a, _b, _c, _d, _e;
    if (state.name) {
      if (state.kind === RuleKinds.INTERFACE_DEF && !schemaInterfaceNames.includes(state.name)) {
        inlineInterfaces.add(state.name);
      }
      if (state.kind === RuleKinds.NAMED_TYPE && ((_a = state.prevState) === null || _a === void 0 ? void 0 : _a.kind) === RuleKinds.IMPLEMENTS) {
        if (typeInfo.interfaceDef) {
          const existingType = (_b = typeInfo.interfaceDef) === null || _b === void 0 ? void 0 : _b.getInterfaces().find(({ name: name2 }) => name2 === state.name);
          if (existingType) {
            return;
          }
          const type2 = schema.getType(state.name);
          const interfaceConfig = (_c = typeInfo.interfaceDef) === null || _c === void 0 ? void 0 : _c.toConfig();
          typeInfo.interfaceDef = new GraphQLInterfaceType(Object.assign(Object.assign({}, interfaceConfig), { interfaces: [
            ...interfaceConfig.interfaces,
            type2 || new GraphQLInterfaceType({ name: state.name, fields: {} })
          ] }));
        } else if (typeInfo.objectTypeDef) {
          const existingType = (_d = typeInfo.objectTypeDef) === null || _d === void 0 ? void 0 : _d.getInterfaces().find(({ name: name2 }) => name2 === state.name);
          if (existingType) {
            return;
          }
          const type2 = schema.getType(state.name);
          const objectTypeConfig = (_e = typeInfo.objectTypeDef) === null || _e === void 0 ? void 0 : _e.toConfig();
          typeInfo.objectTypeDef = new GraphQLObjectType(Object.assign(Object.assign({}, objectTypeConfig), { interfaces: [
            ...objectTypeConfig.interfaces,
            type2 || new GraphQLInterfaceType({ name: state.name, fields: {} })
          ] }));
        }
      }
    }
  });
  const currentTypeToExtend = typeInfo.interfaceDef || typeInfo.objectTypeDef;
  const siblingInterfaces = (currentTypeToExtend === null || currentTypeToExtend === void 0 ? void 0 : currentTypeToExtend.getInterfaces()) || [];
  const siblingInterfaceNames = siblingInterfaces.map(({ name: name2 }) => name2);
  const possibleInterfaces = schemaInterfaces.concat([...inlineInterfaces].map((name2) => ({ name: name2 }))).filter(({ name: name2 }) => name2 !== (currentTypeToExtend === null || currentTypeToExtend === void 0 ? void 0 : currentTypeToExtend.name) && !siblingInterfaceNames.includes(name2));
  return hintList(token, possibleInterfaces.map((type2) => {
    const result = {
      label: type2.name,
      kind: CompletionItemKind2.Interface,
      type: type2
    };
    if (type2 === null || type2 === void 0 ? void 0 : type2.description) {
      result.documentation = type2.description;
    }
    return result;
  }));
}
function getSuggestionsForFragmentTypeConditions(token, typeInfo, schema, _kind) {
  let possibleTypes;
  if (typeInfo.parentType) {
    if (isAbstractType(typeInfo.parentType)) {
      const abstractType = assertAbstractType(typeInfo.parentType);
      const possibleObjTypes = schema.getPossibleTypes(abstractType);
      const possibleIfaceMap = /* @__PURE__ */ Object.create(null);
      for (const type2 of possibleObjTypes) {
        for (const iface of type2.getInterfaces()) {
          possibleIfaceMap[iface.name] = iface;
        }
      }
      possibleTypes = possibleObjTypes.concat(objectValues(possibleIfaceMap));
    } else {
      possibleTypes = [typeInfo.parentType];
    }
  } else {
    const typeMap = schema.getTypeMap();
    possibleTypes = objectValues(typeMap).filter((type2) => isCompositeType(type2) && !type2.name.startsWith("__"));
  }
  return hintList(token, possibleTypes.map((type2) => {
    const namedType = getNamedType(type2);
    return {
      label: String(type2),
      documentation: (namedType === null || namedType === void 0 ? void 0 : namedType.description) || "",
      kind: CompletionItemKind2.Field
    };
  }));
}
function getSuggestionsForFragmentSpread(token, typeInfo, schema, queryText, fragmentDefs) {
  if (!queryText) {
    return [];
  }
  const typeMap = schema.getTypeMap();
  const defState = getDefinitionState(token.state);
  const fragments = getFragmentDefinitions(queryText);
  if (fragmentDefs && fragmentDefs.length > 0) {
    fragments.push(...fragmentDefs);
  }
  const relevantFrags = fragments.filter((frag) => typeMap[frag.typeCondition.name.value] && !(defState && defState.kind === RuleKinds.FRAGMENT_DEFINITION && defState.name === frag.name.value) && isCompositeType(typeInfo.parentType) && isCompositeType(typeMap[frag.typeCondition.name.value]) && doTypesOverlap(schema, typeInfo.parentType, typeMap[frag.typeCondition.name.value]));
  return hintList(token, relevantFrags.map((frag) => ({
    label: frag.name.value,
    detail: String(typeMap[frag.typeCondition.name.value]),
    documentation: `fragment ${frag.name.value} on ${frag.typeCondition.name.value}`,
    labelDetails: {
      detail: `fragment ${frag.name.value} on ${frag.typeCondition.name.value}`
    },
    kind: CompletionItemKind2.Field,
    type: typeMap[frag.typeCondition.name.value]
  })));
}
var getParentDefinition = (state, kind) => {
  var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
  if (((_a = state.prevState) === null || _a === void 0 ? void 0 : _a.kind) === kind) {
    return state.prevState;
  }
  if (((_c = (_b = state.prevState) === null || _b === void 0 ? void 0 : _b.prevState) === null || _c === void 0 ? void 0 : _c.kind) === kind) {
    return state.prevState.prevState;
  }
  if (((_f = (_e = (_d = state.prevState) === null || _d === void 0 ? void 0 : _d.prevState) === null || _e === void 0 ? void 0 : _e.prevState) === null || _f === void 0 ? void 0 : _f.kind) === kind) {
    return state.prevState.prevState.prevState;
  }
  if (((_k = (_j = (_h = (_g = state.prevState) === null || _g === void 0 ? void 0 : _g.prevState) === null || _h === void 0 ? void 0 : _h.prevState) === null || _j === void 0 ? void 0 : _j.prevState) === null || _k === void 0 ? void 0 : _k.kind) === kind) {
    return state.prevState.prevState.prevState.prevState;
  }
};
function getVariableCompletions(queryText, schema, token) {
  let variableName = null;
  let variableType;
  const definitions = /* @__PURE__ */ Object.create({});
  runOnlineParser(queryText, (_, state) => {
    var _a;
    if ((state === null || state === void 0 ? void 0 : state.kind) === RuleKinds.VARIABLE && state.name) {
      variableName = state.name;
    }
    if ((state === null || state === void 0 ? void 0 : state.kind) === RuleKinds.NAMED_TYPE && variableName) {
      const parentDefinition = getParentDefinition(state, RuleKinds.TYPE);
      if (parentDefinition === null || parentDefinition === void 0 ? void 0 : parentDefinition.type) {
        variableType = schema.getType(parentDefinition === null || parentDefinition === void 0 ? void 0 : parentDefinition.type);
      }
    }
    if (variableName && variableType && !definitions[variableName]) {
      const replaceString = token.string === "$" || ((_a = token === null || token === void 0 ? void 0 : token.state) === null || _a === void 0 ? void 0 : _a.kind) === "Variable" ? variableName : "$" + variableName;
      definitions[variableName] = {
        detail: variableType.toString(),
        insertText: replaceString,
        label: "$" + variableName,
        rawInsert: replaceString,
        type: variableType,
        kind: CompletionItemKind2.Variable
      };
      variableName = null;
      variableType = null;
    }
  });
  return objectValues(definitions);
}
function getFragmentDefinitions(queryText) {
  const fragmentDefs = [];
  runOnlineParser(queryText, (_, state) => {
    if (state.kind === RuleKinds.FRAGMENT_DEFINITION && state.name && state.type) {
      fragmentDefs.push({
        kind: RuleKinds.FRAGMENT_DEFINITION,
        name: {
          kind: Kind.NAME,
          value: state.name
        },
        selectionSet: {
          kind: RuleKinds.SELECTION_SET,
          selections: []
        },
        typeCondition: {
          kind: RuleKinds.NAMED_TYPE,
          name: {
            kind: Kind.NAME,
            value: state.type
          }
        }
      });
    }
  });
  return fragmentDefs;
}
function getSuggestionsForVariableDefinition(token, schema, _kind) {
  const inputTypeMap = schema.getTypeMap();
  const inputTypes = objectValues(inputTypeMap).filter(isInputType);
  return hintList(token, inputTypes.map((type2) => ({
    label: type2.name,
    documentation: (type2 === null || type2 === void 0 ? void 0 : type2.description) || "",
    kind: CompletionItemKind2.Variable
  })));
}
function getSuggestionsForDirective(token, state, schema, _kind) {
  var _a;
  if ((_a = state.prevState) === null || _a === void 0 ? void 0 : _a.kind) {
    const directives = schema.getDirectives().filter((directive) => canUseDirective(state.prevState, directive));
    return hintList(token, directives.map((directive) => ({
      label: directive.name,
      documentation: (directive === null || directive === void 0 ? void 0 : directive.description) || "",
      kind: CompletionItemKind2.Function
    })));
  }
  return [];
}
function getSuggestionsForDirectiveArguments(token, state, schema, _kind) {
  const directive = schema.getDirectives().find((d) => d.name === state.name);
  return hintList(token, (directive === null || directive === void 0 ? void 0 : directive.args.map((arg) => ({
    label: arg.name,
    documentation: arg.description || "",
    kind: CompletionItemKind2.Field
  }))) || []);
}
function canUseDirective(state, directive) {
  if (!(state === null || state === void 0 ? void 0 : state.kind)) {
    return false;
  }
  const { kind, prevState } = state;
  const { locations } = directive;
  switch (kind) {
    case RuleKinds.QUERY:
      return locations.includes(DirectiveLocation.QUERY);
    case RuleKinds.MUTATION:
      return locations.includes(DirectiveLocation.MUTATION);
    case RuleKinds.SUBSCRIPTION:
      return locations.includes(DirectiveLocation.SUBSCRIPTION);
    case RuleKinds.FIELD:
    case RuleKinds.ALIASED_FIELD:
      return locations.includes(DirectiveLocation.FIELD);
    case RuleKinds.FRAGMENT_DEFINITION:
      return locations.includes(DirectiveLocation.FRAGMENT_DEFINITION);
    case RuleKinds.FRAGMENT_SPREAD:
      return locations.includes(DirectiveLocation.FRAGMENT_SPREAD);
    case RuleKinds.INLINE_FRAGMENT:
      return locations.includes(DirectiveLocation.INLINE_FRAGMENT);
    case RuleKinds.SCHEMA_DEF:
      return locations.includes(DirectiveLocation.SCHEMA);
    case RuleKinds.SCALAR_DEF:
      return locations.includes(DirectiveLocation.SCALAR);
    case RuleKinds.OBJECT_TYPE_DEF:
      return locations.includes(DirectiveLocation.OBJECT);
    case RuleKinds.FIELD_DEF:
      return locations.includes(DirectiveLocation.FIELD_DEFINITION);
    case RuleKinds.INTERFACE_DEF:
      return locations.includes(DirectiveLocation.INTERFACE);
    case RuleKinds.UNION_DEF:
      return locations.includes(DirectiveLocation.UNION);
    case RuleKinds.ENUM_DEF:
      return locations.includes(DirectiveLocation.ENUM);
    case RuleKinds.ENUM_VALUE:
      return locations.includes(DirectiveLocation.ENUM_VALUE);
    case RuleKinds.INPUT_DEF:
      return locations.includes(DirectiveLocation.INPUT_OBJECT);
    case RuleKinds.INPUT_VALUE_DEF:
      const prevStateKind = prevState === null || prevState === void 0 ? void 0 : prevState.kind;
      switch (prevStateKind) {
        case RuleKinds.ARGUMENTS_DEF:
          return locations.includes(DirectiveLocation.ARGUMENT_DEFINITION);
        case RuleKinds.INPUT_DEF:
          return locations.includes(DirectiveLocation.INPUT_FIELD_DEFINITION);
      }
  }
  return false;
}
function unwrapType(state) {
  if (state.prevState && state.kind && [
    RuleKinds.NAMED_TYPE,
    RuleKinds.LIST_TYPE,
    RuleKinds.TYPE,
    RuleKinds.NON_NULL_TYPE
  ].includes(state.kind)) {
    return unwrapType(state.prevState);
  }
  return state;
}

// node_modules/graphql-language-service/esm/utils/Range.js
var Range2 = class {
  constructor(start, end) {
    this.containsPosition = (position) => {
      if (this.start.line === position.line) {
        return this.start.character <= position.character;
      }
      if (this.end.line === position.line) {
        return this.end.character >= position.character;
      }
      return this.start.line <= position.line && this.end.line >= position.line;
    };
    this.start = start;
    this.end = end;
  }
  setStart(line, character) {
    this.start = new Position2(line, character);
  }
  setEnd(line, character) {
    this.end = new Position2(line, character);
  }
};
var Position2 = class {
  constructor(line, character) {
    this.lessThanOrEqualTo = (position) => this.line < position.line || this.line === position.line && this.character <= position.character;
    this.line = line;
    this.character = character;
  }
  setLine(line) {
    this.line = line;
  }
  setCharacter(character) {
    this.character = character;
  }
};

// node_modules/graphql-language-service/esm/utils/fragmentDependencies.js
var import_nullthrows = __toESM(require_nullthrows());
var getFragmentDependenciesForAST = (parsedOperation, fragmentDefinitions) => {
  if (!fragmentDefinitions) {
    return [];
  }
  const existingFrags = /* @__PURE__ */ new Map();
  const referencedFragNames = /* @__PURE__ */ new Set();
  visit(parsedOperation, {
    FragmentDefinition(node) {
      existingFrags.set(node.name.value, true);
    },
    FragmentSpread(node) {
      if (!referencedFragNames.has(node.name.value)) {
        referencedFragNames.add(node.name.value);
      }
    }
  });
  const asts = /* @__PURE__ */ new Set();
  for (const name2 of referencedFragNames) {
    if (!existingFrags.has(name2) && fragmentDefinitions.has(name2)) {
      asts.add((0, import_nullthrows.default)(fragmentDefinitions.get(name2)));
    }
  }
  const referencedFragments = [];
  for (const ast of asts) {
    visit(ast, {
      FragmentSpread(node) {
        if (!referencedFragNames.has(node.name.value) && fragmentDefinitions.get(node.name.value)) {
          asts.add((0, import_nullthrows.default)(fragmentDefinitions.get(node.name.value)));
          referencedFragNames.add(node.name.value);
        }
      }
    });
    if (!existingFrags.has(ast.name.value)) {
      referencedFragments.push(ast);
    }
  }
  return referencedFragments;
};

// node_modules/graphql-language-service/esm/utils/validateWithCustomRules.js
var specifiedSDLRules = [
  LoneSchemaDefinitionRule,
  UniqueOperationTypesRule,
  UniqueTypeNamesRule,
  UniqueEnumValueNamesRule,
  UniqueFieldDefinitionNamesRule,
  UniqueDirectiveNamesRule,
  KnownTypeNamesRule,
  KnownDirectivesRule,
  UniqueDirectivesPerLocationRule,
  PossibleTypeExtensionsRule,
  UniqueArgumentNamesRule,
  UniqueInputFieldNamesRule,
  UniqueVariableNamesRule,
  FragmentsOnCompositeTypesRule,
  ProvidedRequiredArgumentsRule
];
function validateWithCustomRules(schema, ast, customRules, isRelayCompatMode, isSchemaDocument) {
  const rules = specifiedRules.filter((rule) => {
    if (rule === NoUnusedFragmentsRule || rule === ExecutableDefinitionsRule) {
      return false;
    }
    if (isRelayCompatMode && rule === KnownFragmentNamesRule) {
      return false;
    }
    return true;
  });
  if (customRules) {
    Array.prototype.push.apply(rules, customRules);
  }
  if (isSchemaDocument) {
    Array.prototype.push.apply(rules, specifiedSDLRules);
  }
  const errors = validate(schema, ast, rules);
  return errors.filter((error) => {
    if (error.message.includes("Unknown directive") && error.nodes) {
      const node = error.nodes[0];
      if (node && node.kind === Kind.DIRECTIVE) {
        const name2 = node.name.value;
        if (name2 === "arguments" || name2 === "argumentDefinitions") {
          return false;
        }
      }
    }
    return true;
  });
}

// node_modules/graphql-language-service/esm/utils/collectVariables.js
function collectVariables(schema, documentAST) {
  const variableToType = /* @__PURE__ */ Object.create(null);
  for (const definition of documentAST.definitions) {
    if (definition.kind === "OperationDefinition") {
      const { variableDefinitions } = definition;
      if (variableDefinitions) {
        for (const { variable, type: type2 } of variableDefinitions) {
          const inputType = typeFromAST(schema, type2);
          if (inputType) {
            variableToType[variable.name.value] = inputType;
          } else if (type2.kind === Kind.NAMED_TYPE && type2.name.value === "Float") {
            variableToType[variable.name.value] = GraphQLFloat;
          }
        }
      }
    }
  }
  return variableToType;
}

// node_modules/graphql-language-service/esm/utils/getOperationFacts.js
function getOperationASTFacts(documentAST, schema) {
  const variableToType = schema ? collectVariables(schema, documentAST) : void 0;
  const operations = [];
  visit(documentAST, {
    OperationDefinition(node) {
      operations.push(node);
    }
  });
  return { variableToType, operations };
}
function getOperationFacts(schema, documentString) {
  if (!documentString) {
    return;
  }
  try {
    const documentAST = parse(documentString);
    return Object.assign(Object.assign({}, getOperationASTFacts(documentAST, schema)), { documentAST });
  } catch (_a) {
    return;
  }
}

// node_modules/graphql-language-service/esm/interface/getDiagnostics.js
var SEVERITY = {
  Error: "Error",
  Warning: "Warning",
  Information: "Information",
  Hint: "Hint"
};
var DIAGNOSTIC_SEVERITY = {
  [SEVERITY.Error]: 1,
  [SEVERITY.Warning]: 2,
  [SEVERITY.Information]: 3,
  [SEVERITY.Hint]: 4
};
var invariant = (condition, message) => {
  if (!condition) {
    throw new Error(message);
  }
};
function getDiagnostics(query, schema = null, customRules, isRelayCompatMode, externalFragments) {
  var _a, _b;
  let ast = null;
  let fragments = "";
  if (externalFragments) {
    fragments = typeof externalFragments === "string" ? externalFragments : externalFragments.reduce((acc, node) => acc + print(node) + "\n\n", "");
  }
  const enhancedQuery = fragments ? `${query}

${fragments}` : query;
  try {
    ast = parse(enhancedQuery);
  } catch (error) {
    if (error instanceof GraphQLError) {
      const range = getRange((_b = (_a = error.locations) === null || _a === void 0 ? void 0 : _a[0]) !== null && _b !== void 0 ? _b : { line: 0, column: 0 }, enhancedQuery);
      return [
        {
          severity: DIAGNOSTIC_SEVERITY.Error,
          message: error.message,
          source: "GraphQL: Syntax",
          range
        }
      ];
    }
    throw error;
  }
  return validateQuery(ast, schema, customRules, isRelayCompatMode);
}
function validateQuery(ast, schema = null, customRules, isRelayCompatMode) {
  if (!schema) {
    return [];
  }
  const validationErrorAnnotations = validateWithCustomRules(schema, ast, customRules, isRelayCompatMode).flatMap((error) => annotations(error, DIAGNOSTIC_SEVERITY.Error, "Validation"));
  const deprecationWarningAnnotations = validate(schema, ast, [
    NoDeprecatedCustomRule
  ]).flatMap((error) => annotations(error, DIAGNOSTIC_SEVERITY.Warning, "Deprecation"));
  return validationErrorAnnotations.concat(deprecationWarningAnnotations);
}
function annotations(error, severity, type2) {
  if (!error.nodes) {
    return [];
  }
  const highlightedNodes = [];
  for (const [i, node] of error.nodes.entries()) {
    const highlightNode = node.kind !== "Variable" && "name" in node && node.name !== void 0 ? node.name : "variable" in node && node.variable !== void 0 ? node.variable : node;
    if (highlightNode) {
      invariant(error.locations, "GraphQL validation error requires locations.");
      const loc = error.locations[i];
      const highlightLoc = getLocation(highlightNode);
      const end = loc.column + (highlightLoc.end - highlightLoc.start);
      highlightedNodes.push({
        source: `GraphQL: ${type2}`,
        message: error.message,
        severity,
        range: new Range2(new Position2(loc.line - 1, loc.column - 1), new Position2(loc.line - 1, end))
      });
    }
  }
  return highlightedNodes;
}
function getRange(location, queryText) {
  const parser = onlineParser();
  const state = parser.startState();
  const lines = queryText.split("\n");
  invariant(lines.length >= location.line, "Query text must have more lines than where the error happened");
  let stream = null;
  for (let i = 0; i < location.line; i++) {
    stream = new CharacterStream(lines[i]);
    while (!stream.eol()) {
      const style = parser.token(stream, state);
      if (style === "invalidchar") {
        break;
      }
    }
  }
  invariant(stream, "Expected Parser stream to be available.");
  const line = location.line - 1;
  const start = stream.getStartOfToken();
  const end = stream.getCurrentPosition();
  return new Range2(new Position2(line, start), new Position2(line, end));
}
function getLocation(node) {
  const typeCastedNode = node;
  const location = typeCastedNode.loc;
  invariant(location, "Expected ASTNode to have a location.");
  return location;
}

export {
  opt,
  list,
  t,
  p,
  isIgnored,
  LexRules,
  ParseRules,
  onlineParser,
  getAutocompleteSuggestions,
  getFragmentDependenciesForAST,
  Position2 as Position,
  getOperationFacts,
  getDiagnostics
};
//# sourceMappingURL=chunk-4LQY6QSN.js.map
