{"version": 3, "sources": ["../../../../../node_modules/codemirror/addon/edit/closebrackets.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  var defaults = {\n    pairs: \"()[]{}''\\\"\\\"\",\n    closeBefore: \")]}'\\\":;>\",\n    triples: \"\",\n    explode: \"[]{}\"\n  };\n\n  var Pos = CodeMirror.Pos;\n\n  CodeMirror.defineOption(\"autoCloseBrackets\", false, function(cm, val, old) {\n    if (old && old != CodeMirror.Init) {\n      cm.removeKeyMap(keyMap);\n      cm.state.closeBrackets = null;\n    }\n    if (val) {\n      ensureBound(getOption(val, \"pairs\"))\n      cm.state.closeBrackets = val;\n      cm.addKeyMap(keyMap);\n    }\n  });\n\n  function getOption(conf, name) {\n    if (name == \"pairs\" && typeof conf == \"string\") return conf;\n    if (typeof conf == \"object\" && conf[name] != null) return conf[name];\n    return defaults[name];\n  }\n\n  var keyMap = {Backspace: handleBackspace, Enter: handleEnter};\n  function ensureBound(chars) {\n    for (var i = 0; i < chars.length; i++) {\n      var ch = chars.charAt(i), key = \"'\" + ch + \"'\"\n      if (!keyMap[key]) keyMap[key] = handler(ch)\n    }\n  }\n  ensureBound(defaults.pairs + \"`\")\n\n  function handler(ch) {\n    return function(cm) { return handleChar(cm, ch); };\n  }\n\n  function getConfig(cm) {\n    var deflt = cm.state.closeBrackets;\n    if (!deflt || deflt.override) return deflt;\n    var mode = cm.getModeAt(cm.getCursor());\n    return mode.closeBrackets || deflt;\n  }\n\n  function handleBackspace(cm) {\n    var conf = getConfig(cm);\n    if (!conf || cm.getOption(\"disableInput\")) return CodeMirror.Pass;\n\n    var pairs = getOption(conf, \"pairs\");\n    var ranges = cm.listSelections();\n    for (var i = 0; i < ranges.length; i++) {\n      if (!ranges[i].empty()) return CodeMirror.Pass;\n      var around = charsAround(cm, ranges[i].head);\n      if (!around || pairs.indexOf(around) % 2 != 0) return CodeMirror.Pass;\n    }\n    for (var i = ranges.length - 1; i >= 0; i--) {\n      var cur = ranges[i].head;\n      cm.replaceRange(\"\", Pos(cur.line, cur.ch - 1), Pos(cur.line, cur.ch + 1), \"+delete\");\n    }\n  }\n\n  function handleEnter(cm) {\n    var conf = getConfig(cm);\n    var explode = conf && getOption(conf, \"explode\");\n    if (!explode || cm.getOption(\"disableInput\")) return CodeMirror.Pass;\n\n    var ranges = cm.listSelections();\n    for (var i = 0; i < ranges.length; i++) {\n      if (!ranges[i].empty()) return CodeMirror.Pass;\n      var around = charsAround(cm, ranges[i].head);\n      if (!around || explode.indexOf(around) % 2 != 0) return CodeMirror.Pass;\n    }\n    cm.operation(function() {\n      var linesep = cm.lineSeparator() || \"\\n\";\n      cm.replaceSelection(linesep + linesep, null);\n      moveSel(cm, -1)\n      ranges = cm.listSelections();\n      for (var i = 0; i < ranges.length; i++) {\n        var line = ranges[i].head.line;\n        cm.indentLine(line, null, true);\n        cm.indentLine(line + 1, null, true);\n      }\n    });\n  }\n\n  function moveSel(cm, dir) {\n    var newRanges = [], ranges = cm.listSelections(), primary = 0\n    for (var i = 0; i < ranges.length; i++) {\n      var range = ranges[i]\n      if (range.head == cm.getCursor()) primary = i\n      var pos = range.head.ch || dir > 0 ? {line: range.head.line, ch: range.head.ch + dir} : {line: range.head.line - 1}\n      newRanges.push({anchor: pos, head: pos})\n    }\n    cm.setSelections(newRanges, primary)\n  }\n\n  function contractSelection(sel) {\n    var inverted = CodeMirror.cmpPos(sel.anchor, sel.head) > 0;\n    return {anchor: new Pos(sel.anchor.line, sel.anchor.ch + (inverted ? -1 : 1)),\n            head: new Pos(sel.head.line, sel.head.ch + (inverted ? 1 : -1))};\n  }\n\n  function handleChar(cm, ch) {\n    var conf = getConfig(cm);\n    if (!conf || cm.getOption(\"disableInput\")) return CodeMirror.Pass;\n\n    var pairs = getOption(conf, \"pairs\");\n    var pos = pairs.indexOf(ch);\n    if (pos == -1) return CodeMirror.Pass;\n\n    var closeBefore = getOption(conf,\"closeBefore\");\n\n    var triples = getOption(conf, \"triples\");\n\n    var identical = pairs.charAt(pos + 1) == ch;\n    var ranges = cm.listSelections();\n    var opening = pos % 2 == 0;\n\n    var type;\n    for (var i = 0; i < ranges.length; i++) {\n      var range = ranges[i], cur = range.head, curType;\n      var next = cm.getRange(cur, Pos(cur.line, cur.ch + 1));\n      if (opening && !range.empty()) {\n        curType = \"surround\";\n      } else if ((identical || !opening) && next == ch) {\n        if (identical && stringStartsAfter(cm, cur))\n          curType = \"both\";\n        else if (triples.indexOf(ch) >= 0 && cm.getRange(cur, Pos(cur.line, cur.ch + 3)) == ch + ch + ch)\n          curType = \"skipThree\";\n        else\n          curType = \"skip\";\n      } else if (identical && cur.ch > 1 && triples.indexOf(ch) >= 0 &&\n                 cm.getRange(Pos(cur.line, cur.ch - 2), cur) == ch + ch) {\n        if (cur.ch > 2 && /\\bstring/.test(cm.getTokenTypeAt(Pos(cur.line, cur.ch - 2)))) return CodeMirror.Pass;\n        curType = \"addFour\";\n      } else if (identical) {\n        var prev = cur.ch == 0 ? \" \" : cm.getRange(Pos(cur.line, cur.ch - 1), cur)\n        if (!CodeMirror.isWordChar(next) && prev != ch && !CodeMirror.isWordChar(prev)) curType = \"both\";\n        else return CodeMirror.Pass;\n      } else if (opening && (next.length === 0 || /\\s/.test(next) || closeBefore.indexOf(next) > -1)) {\n        curType = \"both\";\n      } else {\n        return CodeMirror.Pass;\n      }\n      if (!type) type = curType;\n      else if (type != curType) return CodeMirror.Pass;\n    }\n\n    var left = pos % 2 ? pairs.charAt(pos - 1) : ch;\n    var right = pos % 2 ? ch : pairs.charAt(pos + 1);\n    cm.operation(function() {\n      if (type == \"skip\") {\n        moveSel(cm, 1)\n      } else if (type == \"skipThree\") {\n        moveSel(cm, 3)\n      } else if (type == \"surround\") {\n        var sels = cm.getSelections();\n        for (var i = 0; i < sels.length; i++)\n          sels[i] = left + sels[i] + right;\n        cm.replaceSelections(sels, \"around\");\n        sels = cm.listSelections().slice();\n        for (var i = 0; i < sels.length; i++)\n          sels[i] = contractSelection(sels[i]);\n        cm.setSelections(sels);\n      } else if (type == \"both\") {\n        cm.replaceSelection(left + right, null);\n        cm.triggerElectric(left + right);\n        moveSel(cm, -1)\n      } else if (type == \"addFour\") {\n        cm.replaceSelection(left + left + left + left, \"before\");\n        moveSel(cm, 1)\n      }\n    });\n  }\n\n  function charsAround(cm, pos) {\n    var str = cm.getRange(Pos(pos.line, pos.ch - 1),\n                          Pos(pos.line, pos.ch + 1));\n    return str.length == 2 ? str : null;\n  }\n\n  function stringStartsAfter(cm, pos) {\n    var token = cm.getTokenAt(Pos(pos.line, pos.ch + 1))\n    return /\\bstring/.test(token.type) && token.start == pos.ch &&\n      (pos.ch == 0 || !/\\bstring/.test(cm.getTokenTypeAt(pos)))\n  }\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,GAAC,SAASA,GAAK;AAEXA,MAAIC,GAA+B,CAAA;EAKtC,GAAE,SAASC,GAAY;AACtB,QAAIC,IAAW;MACb,OAAO;MACP,aAAa;MACb,SAAS;MACT,SAAS;IACb,GAEMC,IAAMF,EAAW;AAErBA,MAAW,aAAa,qBAAqB,OAAO,SAASG,GAAIC,GAAKC,GAAK;AACrEA,WAAOA,KAAOL,EAAW,SAC3BG,EAAG,aAAaG,CAAM,GACtBH,EAAG,MAAM,gBAAgB,OAEvBC,MACFG,EAAYC,EAAUJ,GAAK,OAAO,CAAC,GACnCD,EAAG,MAAM,gBAAgBC,GACzBD,EAAG,UAAUG,CAAM;IAEzB,CAAG;AAED,aAASE,EAAUC,GAAMC,GAAM;AAC7B,aAAIA,KAAQ,WAAW,OAAOD,KAAQ,WAAiBA,IACnD,OAAOA,KAAQ,YAAYA,EAAKC,CAAI,KAAK,OAAaD,EAAKC,CAAI,IAC5DT,EAASS,CAAI;IACrB;AAJQC,MAAAH,GAAA,WAAA;AAMT,QAAIF,IAAS,EAAC,WAAWM,GAAiB,OAAOC,EAAW;AAC5D,aAASN,EAAYO,GAAO;AAC1B,eAASC,IAAI,GAAGA,IAAID,EAAM,QAAQC,KAAK;AACrC,YAAIC,IAAKF,EAAM,OAAOC,CAAC,GAAGE,IAAM,MAAMD,IAAK;AACtCV,UAAOW,CAAG,MAAGX,EAAOW,CAAG,IAAIC,EAAQF,CAAE;MAAA;IAE7C;AALQL,MAAAJ,GAAA,aAAA,GAMTA,EAAYN,EAAS,QAAQ,GAAG;AAEhC,aAASiB,EAAQF,GAAI;AACnB,aAAO,SAASb,GAAI;AAAE,eAAOgB,EAAWhB,GAAIa,CAAE;MAAA;IAC/C;AAFQL,MAAAO,GAAA,SAAA;AAIT,aAASE,EAAUjB,GAAI;AACrB,UAAIkB,IAAQlB,EAAG,MAAM;AACrB,UAAI,CAACkB,KAASA,EAAM;AAAU,eAAOA;AACrC,UAAIC,IAAOnB,EAAG,UAAUA,EAAG,UAAW,CAAA;AACtC,aAAOmB,EAAK,iBAAiBD;IAC9B;AALQV,MAAAS,GAAA,WAAA;AAOT,aAASR,EAAgBT,GAAI;AAC3B,UAAIM,IAAOW,EAAUjB,CAAE;AACvB,UAAI,CAACM,KAAQN,EAAG,UAAU,cAAc;AAAG,eAAOH,EAAW;AAI7D,eAFIuB,IAAQf,EAAUC,GAAM,OAAO,GAC/Be,IAASrB,EAAG,eAAA,GACPY,IAAI,GAAGA,IAAIS,EAAO,QAAQT,KAAK;AACtC,YAAI,CAACS,EAAOT,CAAC,EAAE,MAAK;AAAI,iBAAOf,EAAW;AAC1C,YAAIyB,IAASC,EAAYvB,GAAIqB,EAAOT,CAAC,EAAE,IAAI;AAC3C,YAAI,CAACU,KAAUF,EAAM,QAAQE,CAAM,IAAI,KAAK;AAAG,iBAAOzB,EAAW;MAAA;AAEnE,eAASe,IAAIS,EAAO,SAAS,GAAGT,KAAK,GAAGA,KAAK;AAC3C,YAAIY,IAAMH,EAAOT,CAAC,EAAE;AACpBZ,UAAG,aAAa,IAAID,EAAIyB,EAAI,MAAMA,EAAI,KAAK,CAAC,GAAGzB,EAAIyB,EAAI,MAAMA,EAAI,KAAK,CAAC,GAAG,SAAS;MAAA;IAEtF;AAfQhB,MAAAC,GAAA,iBAAA;AAiBT,aAASC,EAAYV,GAAI;AACvB,UAAIM,IAAOW,EAAUjB,CAAE,GACnByB,IAAUnB,KAAQD,EAAUC,GAAM,SAAS;AAC/C,UAAI,CAACmB,KAAWzB,EAAG,UAAU,cAAc;AAAG,eAAOH,EAAW;AAGhE,eADIwB,IAASrB,EAAG,eAAA,GACPY,IAAI,GAAGA,IAAIS,EAAO,QAAQT,KAAK;AACtC,YAAI,CAACS,EAAOT,CAAC,EAAE,MAAK;AAAI,iBAAOf,EAAW;AAC1C,YAAIyB,IAASC,EAAYvB,GAAIqB,EAAOT,CAAC,EAAE,IAAI;AAC3C,YAAI,CAACU,KAAUG,EAAQ,QAAQH,CAAM,IAAI,KAAK;AAAG,iBAAOzB,EAAW;MAAA;AAErEG,QAAG,UAAU,WAAW;AACtB,YAAI0B,IAAU1B,EAAG,cAAa,KAAM;;AACpCA,UAAG,iBAAiB0B,IAAUA,GAAS,IAAI,GAC3CC,EAAQ3B,GAAI,EAAE,GACdqB,IAASrB,EAAG,eAAA;AACZ,iBAASY,IAAI,GAAGA,IAAIS,EAAO,QAAQT,KAAK;AACtC,cAAIgB,IAAOP,EAAOT,CAAC,EAAE,KAAK;AAC1BZ,YAAG,WAAW4B,GAAM,MAAM,IAAI,GAC9B5B,EAAG,WAAW4B,IAAO,GAAG,MAAM,IAAI;QAAA;MAE1C,CAAK;IACF;AAtBQpB,MAAAE,GAAA,aAAA;AAwBT,aAASiB,EAAQ3B,GAAI6B,GAAK;AAExB,eADIC,IAAY,CAAA,GAAIT,IAASrB,EAAG,eAAc,GAAI+B,IAAU,GACnDnB,IAAI,GAAGA,IAAIS,EAAO,QAAQT,KAAK;AACtC,YAAIoB,IAAQX,EAAOT,CAAC;AAChBoB,UAAM,QAAQhC,EAAG,UAAW,MAAE+B,IAAUnB;AAC5C,YAAIqB,IAAMD,EAAM,KAAK,MAAMH,IAAM,IAAI,EAAC,MAAMG,EAAM,KAAK,MAAM,IAAIA,EAAM,KAAK,KAAKH,EAAG,IAAI,EAAC,MAAMG,EAAM,KAAK,OAAO,EAAC;AAClHF,UAAU,KAAK,EAAC,QAAQG,GAAK,MAAMA,EAAG,CAAC;MAAA;AAEzCjC,QAAG,cAAc8B,GAAWC,CAAO;IACpC;AATQvB,MAAAmB,GAAA,SAAA;AAWT,aAASO,EAAkBC,GAAK;AAC9B,UAAIC,IAAWvC,EAAW,OAAOsC,EAAI,QAAQA,EAAI,IAAI,IAAI;AACzD,aAAO;QAAC,QAAQ,IAAIpC,EAAIoC,EAAI,OAAO,MAAMA,EAAI,OAAO,MAAMC,IAAW,KAAK,EAAE;QACpE,MAAM,IAAIrC,EAAIoC,EAAI,KAAK,MAAMA,EAAI,KAAK,MAAMC,IAAW,IAAI,GAAG;MAAC;IACxE;AAJQ5B,MAAA0B,GAAA,mBAAA;AAMT,aAASlB,EAAWhB,GAAIa,GAAI;AAC1B,UAAIP,IAAOW,EAAUjB,CAAE;AACvB,UAAI,CAACM,KAAQN,EAAG,UAAU,cAAc;AAAG,eAAOH,EAAW;AAE7D,UAAIuB,IAAQf,EAAUC,GAAM,OAAO,GAC/B2B,IAAMb,EAAM,QAAQP,CAAE;AAC1B,UAAIoB,KAAO;AAAI,eAAOpC,EAAW;AAWjC,eATIwC,IAAchC,EAAUC,GAAK,aAAa,GAE1CgC,IAAUjC,EAAUC,GAAM,SAAS,GAEnCiC,IAAYnB,EAAM,OAAOa,IAAM,CAAC,KAAKpB,GACrCQ,IAASrB,EAAG,eAAA,GACZwC,IAAUP,IAAM,KAAK,GAErBQ,GACK7B,IAAI,GAAGA,IAAIS,EAAO,QAAQT,KAAK;AACtC,YAAIoB,IAAQX,EAAOT,CAAC,GAAGY,IAAMQ,EAAM,MAAMU,GACrCC,IAAO3C,EAAG,SAASwB,GAAKzB,EAAIyB,EAAI,MAAMA,EAAI,KAAK,CAAC,CAAC;AACrD,YAAIgB,KAAW,CAACR,EAAM,MAAA;AACpBU,cAAU;kBACAH,KAAa,CAACC,MAAYG,KAAQ9B;AACxC0B,eAAaK,EAAkB5C,GAAIwB,CAAG,IACxCkB,IAAU,SACHJ,EAAQ,QAAQzB,CAAE,KAAK,KAAKb,EAAG,SAASwB,GAAKzB,EAAIyB,EAAI,MAAMA,EAAI,KAAK,CAAC,CAAC,KAAKX,IAAKA,IAAKA,IAC5F6B,IAAU,cAEVA,IAAU;iBACHH,KAAaf,EAAI,KAAK,KAAKc,EAAQ,QAAQzB,CAAE,KAAK,KAClDb,EAAG,SAASD,EAAIyB,EAAI,MAAMA,EAAI,KAAK,CAAC,GAAGA,CAAG,KAAKX,IAAKA,GAAI;AACjE,cAAIW,EAAI,KAAK,KAAK,WAAW,KAAKxB,EAAG,eAAeD,EAAIyB,EAAI,MAAMA,EAAI,KAAK,CAAC,CAAC,CAAC;AAAG,mBAAO3B,EAAW;AACnG6C,cAAU;QAAA,WACDH,GAAW;AACpB,cAAIM,IAAOrB,EAAI,MAAM,IAAI,MAAMxB,EAAG,SAASD,EAAIyB,EAAI,MAAMA,EAAI,KAAK,CAAC,GAAGA,CAAG;AACzE,cAAI,CAAC3B,EAAW,WAAW8C,CAAI,KAAKE,KAAQhC,KAAM,CAAChB,EAAW,WAAWgD,CAAI;AAAGH,gBAAU;;AACrF,mBAAO7C,EAAW;QAAA,WACd2C,MAAYG,EAAK,WAAW,KAAK,KAAK,KAAKA,CAAI,KAAKN,EAAY,QAAQM,CAAI,IAAI;AACzFD,cAAU;;AAEV,iBAAO7C,EAAW;AAEpB,YAAI,CAAC4C;AAAMA,cAAOC;iBACTD,KAAQC;AAAS,iBAAO7C,EAAW;MAAA;AAG9C,UAAIiD,IAAOb,IAAM,IAAIb,EAAM,OAAOa,IAAM,CAAC,IAAIpB,GACzCkC,IAAQd,IAAM,IAAIpB,IAAKO,EAAM,OAAOa,IAAM,CAAC;AAC/CjC,QAAG,UAAU,WAAW;AACtB,YAAIyC,KAAQ;AACVd,YAAQ3B,GAAI,CAAC;iBACJyC,KAAQ;AACjBd,YAAQ3B,GAAI,CAAC;iBACJyC,KAAQ,YAAY;AAE7B,mBADIO,IAAOhD,EAAG,cAAA,GACLY,IAAI,GAAGA,IAAIoC,EAAK,QAAQpC;AAC/BoC,cAAKpC,CAAC,IAAIkC,IAAOE,EAAKpC,CAAC,IAAImC;AAC7B/C,YAAG,kBAAkBgD,GAAM,QAAQ,GACnCA,IAAOhD,EAAG,eAAgB,EAAC,MAAK;AAChC,mBAASY,IAAI,GAAGA,IAAIoC,EAAK,QAAQpC;AAC/BoC,cAAKpC,CAAC,IAAIsB,EAAkBc,EAAKpC,CAAC,CAAC;AACrCZ,YAAG,cAAcgD,CAAI;QAAA;AACZP,eAAQ,UACjBzC,EAAG,iBAAiB8C,IAAOC,GAAO,IAAI,GACtC/C,EAAG,gBAAgB8C,IAAOC,CAAK,GAC/BpB,EAAQ3B,GAAI,EAAE,KACLyC,KAAQ,cACjBzC,EAAG,iBAAiB8C,IAAOA,IAAOA,IAAOA,GAAM,QAAQ,GACvDnB,EAAQ3B,GAAI,CAAC;MAErB,CAAK;IACF;AAvEQQ,MAAAQ,GAAA,YAAA;AAyET,aAASO,EAAYvB,GAAIiC,GAAK;AAC5B,UAAIgB,IAAMjD,EAAG;QAASD,EAAIkC,EAAI,MAAMA,EAAI,KAAK,CAAC;QACxBlC,EAAIkC,EAAI,MAAMA,EAAI,KAAK,CAAC;MAAC;AAC/C,aAAOgB,EAAI,UAAU,IAAIA,IAAM;IAChC;AAJQzC,MAAAe,GAAA,aAAA;AAMT,aAASqB,EAAkB5C,GAAIiC,GAAK;AAClC,UAAIiB,IAAQlD,EAAG,WAAWD,EAAIkC,EAAI,MAAMA,EAAI,KAAK,CAAC,CAAC;AACnD,aAAO,WAAW,KAAKiB,EAAM,IAAI,KAAKA,EAAM,SAASjB,EAAI,OACtDA,EAAI,MAAM,KAAK,CAAC,WAAW,KAAKjC,EAAG,eAAeiC,CAAG,CAAC;IAC1D;AAJQzB,MAAAoC,GAAA,mBAAA;EAKX,CAAC;;;;;;;;", "names": ["mod", "require$$0", "CodeMirror", "defaults", "Pos", "cm", "val", "old", "keyMap", "ensureBound", "getOption", "conf", "name", "__name", "handleBackspace", "handleEnter", "chars", "i", "ch", "key", "handler", "handleChar", "getConfig", "deflt", "mode", "pairs", "ranges", "around", "charsAround", "cur", "explode", "linesep", "moveSel", "line", "dir", "newRang<PERSON>", "primary", "range", "pos", "contractSelection", "sel", "inverted", "closeBefore", "triples", "identical", "opening", "type", "curType", "next", "stringStartsAfter", "prev", "left", "right", "sels", "str", "token"]}