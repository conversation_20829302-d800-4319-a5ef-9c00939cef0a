{"version": 3, "sources": ["../../../../../node_modules/codemirror/addon/fold/brace-fold.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n\"use strict\";\n\nfunction bracketFolding(pairs) {\n  return function(cm, start) {\n    var line = start.line, lineText = cm.getLine(line);\n\n    function findOpening(pair) {\n      var tokenType;\n      for (var at = start.ch, pass = 0;;) {\n        var found = at <= 0 ? -1 : lineText.lastIndexOf(pair[0], at - 1);\n        if (found == -1) {\n          if (pass == 1) break;\n          pass = 1;\n          at = lineText.length;\n          continue;\n        }\n        if (pass == 1 && found < start.ch) break;\n        tokenType = cm.getTokenTypeAt(CodeMirror.Pos(line, found + 1));\n        if (!/^(comment|string)/.test(tokenType)) return {ch: found + 1, tokenType: tokenType, pair: pair};\n        at = found - 1;\n      }\n    }\n\n    function findRange(found) {\n      var count = 1, lastLine = cm.lastLine(), end, startCh = found.ch, endCh\n      outer: for (var i = line; i <= lastLine; ++i) {\n        var text = cm.getLine(i), pos = i == line ? startCh : 0;\n        for (;;) {\n          var nextOpen = text.indexOf(found.pair[0], pos), nextClose = text.indexOf(found.pair[1], pos);\n          if (nextOpen < 0) nextOpen = text.length;\n          if (nextClose < 0) nextClose = text.length;\n          pos = Math.min(nextOpen, nextClose);\n          if (pos == text.length) break;\n          if (cm.getTokenTypeAt(CodeMirror.Pos(i, pos + 1)) == found.tokenType) {\n            if (pos == nextOpen) ++count;\n            else if (!--count) { end = i; endCh = pos; break outer; }\n          }\n          ++pos;\n        }\n      }\n\n      if (end == null || line == end) return null\n      return {from: CodeMirror.Pos(line, startCh),\n              to: CodeMirror.Pos(end, endCh)};\n    }\n\n    var found = []\n    for (var i = 0; i < pairs.length; i++) {\n      var open = findOpening(pairs[i])\n      if (open) found.push(open)\n    }\n    found.sort(function(a, b) { return a.ch - b.ch })\n    for (var i = 0; i < found.length; i++) {\n      var range = findRange(found[i])\n      if (range) return range\n    }\n    return null\n  }\n}\n\nCodeMirror.registerHelper(\"fold\", \"brace\", bracketFolding([[\"{\", \"}\"], [\"[\", \"]\"]]));\n\nCodeMirror.registerHelper(\"fold\", \"brace-paren\", bracketFolding([[\"{\", \"}\"], [\"[\", \"]\"], [\"(\", \")\"]]));\n\nCodeMirror.registerHelper(\"fold\", \"import\", function(cm, start) {\n  function hasImport(line) {\n    if (line < cm.firstLine() || line > cm.lastLine()) return null;\n    var start = cm.getTokenAt(CodeMirror.Pos(line, 1));\n    if (!/\\S/.test(start.string)) start = cm.getTokenAt(CodeMirror.Pos(line, start.end + 1));\n    if (start.type != \"keyword\" || start.string != \"import\") return null;\n    // Now find closing semicolon, return its position\n    for (var i = line, e = Math.min(cm.lastLine(), line + 10); i <= e; ++i) {\n      var text = cm.getLine(i), semi = text.indexOf(\";\");\n      if (semi != -1) return {startCh: start.end, end: CodeMirror.Pos(i, semi)};\n    }\n  }\n\n  var startLine = start.line, has = hasImport(startLine), prev;\n  if (!has || hasImport(startLine - 1) || ((prev = hasImport(startLine - 2)) && prev.end.line == startLine - 1))\n    return null;\n  for (var end = has.end;;) {\n    var next = hasImport(end.line + 1);\n    if (next == null) break;\n    end = next.end;\n  }\n  return {from: cm.clipPos(CodeMirror.Pos(startLine, has.startCh + 1)), to: end};\n});\n\nCodeMirror.registerHelper(\"fold\", \"include\", function(cm, start) {\n  function hasInclude(line) {\n    if (line < cm.firstLine() || line > cm.lastLine()) return null;\n    var start = cm.getTokenAt(CodeMirror.Pos(line, 1));\n    if (!/\\S/.test(start.string)) start = cm.getTokenAt(CodeMirror.Pos(line, start.end + 1));\n    if (start.type == \"meta\" && start.string.slice(0, 8) == \"#include\") return start.start + 8;\n  }\n\n  var startLine = start.line, has = hasInclude(startLine);\n  if (has == null || hasInclude(startLine - 1) != null) return null;\n  for (var end = startLine;;) {\n    var next = hasInclude(end + 1);\n    if (next == null) break;\n    ++end;\n  }\n  return {from: CodeMirror.Pos(startLine, has + 1),\n          to: cm.clipPos(CodeMirror.Pos(end))};\n});\n\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,GAAC,SAASA,GAAK;AAEXA,MAAIC,GAA+B,CAAA;EAKtC,GAAE,SAASC,GAAY;AAGxB,aAASC,EAAeC,GAAO;AAC7B,aAAO,SAASC,GAAIC,GAAO;AACzB,YAAIC,IAAOD,EAAM,MAAME,IAAWH,EAAG,QAAQE,CAAI;AAEjD,iBAASE,EAAYC,GAAM;AAEzB,mBADIC,GACKC,IAAKN,EAAM,IAAIO,IAAO,OAAK;AAClC,gBAAIC,IAAQF,KAAM,IAAI,KAAKJ,EAAS,YAAYE,EAAK,CAAC,GAAGE,IAAK,CAAC;AAC/D,gBAAIE,KAAS,IAAI;AACf,kBAAID,KAAQ;AAAG;AACfA,kBAAO,GACPD,IAAKJ,EAAS;AACd;YAAA;AAEF,gBAAIK,KAAQ,KAAKC,IAAQR,EAAM;AAAI;AAEnC,gBADAK,IAAYN,EAAG,eAAeH,EAAW,IAAIK,GAAMO,IAAQ,CAAC,CAAC,GACzD,CAAC,oBAAoB,KAAKH,CAAS;AAAG,qBAAO,EAAC,IAAIG,IAAQ,GAAG,WAAWH,GAAW,MAAMD,EAAI;AACjGE,gBAAKE,IAAQ;UAAA;QAEhB;AAfQC,UAAAN,GAAA,aAAA;AAiBT,iBAASO,EAAUF,GAAO;AACxB,cAAIG,IAAQ,GAAGC,IAAWb,EAAG,SAAA,GAAYc,GAAKC,IAAUN,EAAM,IAAIO;AAClEC;AAAO,qBAASC,IAAIhB,GAAMgB,KAAKL,GAAU,EAAEK;AAEzC,uBADIC,IAAOnB,EAAG,QAAQkB,CAAC,GAAGE,IAAMF,KAAKhB,IAAOa,IAAU,OAC7C;AACP,oBAAIM,IAAWF,EAAK,QAAQV,EAAM,KAAK,CAAC,GAAGW,CAAG,GAAGE,IAAYH,EAAK,QAAQV,EAAM,KAAK,CAAC,GAAGW,CAAG;AAI5F,oBAHIC,IAAW,MAAGA,IAAWF,EAAK,SAC9BG,IAAY,MAAGA,IAAYH,EAAK,SACpCC,IAAM,KAAK,IAAIC,GAAUC,CAAS,GAC9BF,KAAOD,EAAK;AAAQ;AACxB,oBAAInB,EAAG,eAAeH,EAAW,IAAIqB,GAAGE,IAAM,CAAC,CAAC,KAAKX,EAAM,WAAA;AACzD,sBAAIW,KAAOC;AAAU,sBAAET;2BACd,CAAC,EAAEA,GAAO;AAAEE,wBAAMI,GAAGF,IAAQI;AAAK,0BAAMH;kBAAA;gBAAA;AAEnD,kBAAEG;cAAA;AAIN,iBAAIN,KAAO,QAAQZ,KAAQY,IAAY,OAChC;YAAC,MAAMjB,EAAW,IAAIK,GAAMa,CAAO;YAClC,IAAIlB,EAAW,IAAIiB,GAAKE,CAAK;UAAC;QACvC;AArBQN,UAAAC,GAAA,WAAA;AAwBT,iBADIF,IAAQ,CAAE,GACLS,IAAI,GAAGA,IAAInB,EAAM,QAAQmB,KAAK;AACrC,cAAIK,IAAOnB,EAAYL,EAAMmB,CAAC,CAAC;AAC3BK,eAAMd,EAAM,KAAKc,CAAI;QAAA;AAE3Bd,UAAM,KAAK,SAASe,GAAGC,GAAG;AAAE,iBAAOD,EAAE,KAAKC,EAAE;QAAA,CAAI;AAChD,iBAASP,IAAI,GAAGA,IAAIT,EAAM,QAAQS,KAAK;AACrC,cAAIQ,IAAQf,EAAUF,EAAMS,CAAC,CAAC;AAC9B,cAAIQ;AAAO,mBAAOA;QAAA;AAEpB,eAAO;MACR;IACF;AAxDQhB,MAAAZ,GAAA,gBAAA,GA0DTD,EAAW,eAAe,QAAQ,SAASC,EAAe,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,GAEnFD,EAAW,eAAe,QAAQ,eAAeC,EAAe,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,GAErGD,EAAW,eAAe,QAAQ,UAAU,SAASG,GAAIC,GAAO;AAC9D,eAAS0B,EAAUzB,GAAM;AACvB,YAAIA,IAAOF,EAAG,UAAW,KAAIE,IAAOF,EAAG,SAAA;AAAY,iBAAO;AAC1D,YAAIC,IAAQD,EAAG,WAAWH,EAAW,IAAIK,GAAM,CAAC,CAAC;AAEjD,YADK,KAAK,KAAKD,EAAM,MAAM,MAAGA,IAAQD,EAAG,WAAWH,EAAW,IAAIK,GAAMD,EAAM,MAAM,CAAC,CAAC,IACnFA,EAAM,QAAQ,aAAaA,EAAM,UAAU;AAAU,iBAAO;AAEhE,iBAASiB,IAAIhB,GAAM0B,IAAI,KAAK,IAAI5B,EAAG,SAAQ,GAAIE,IAAO,EAAE,GAAGgB,KAAKU,GAAG,EAAEV,GAAG;AACtE,cAAIC,IAAOnB,EAAG,QAAQkB,CAAC,GAAGW,IAAOV,EAAK,QAAQ,GAAG;AACjD,cAAIU,KAAQ;AAAI,mBAAO,EAAC,SAAS5B,EAAM,KAAK,KAAKJ,EAAW,IAAIqB,GAAGW,CAAI,EAAC;QAAA;MAE3E;AAVQnB,QAAAiB,GAAA,WAAA;AAYT,UAAIG,IAAY7B,EAAM,MAAM8B,IAAMJ,EAAUG,CAAS,GAAGE;AACxD,UAAI,CAACD,KAAOJ,EAAUG,IAAY,CAAC,MAAOE,IAAOL,EAAUG,IAAY,CAAC,MAAME,EAAK,IAAI,QAAQF,IAAY;AACzG,eAAO;AACT,eAAShB,IAAMiB,EAAI,SAAO;AACxB,YAAIE,IAAON,EAAUb,EAAI,OAAO,CAAC;AACjC,YAAImB,KAAQ;AAAM;AAClBnB,YAAMmB,EAAK;MAAA;AAEb,aAAO,EAAC,MAAMjC,EAAG,QAAQH,EAAW,IAAIiC,GAAWC,EAAI,UAAU,CAAC,CAAC,GAAG,IAAIjB,EAAG;IAC/E,CAAC,GAEDjB,EAAW,eAAe,QAAQ,WAAW,SAASG,GAAIC,GAAO;AAC/D,eAASiC,EAAWhC,GAAM;AACxB,YAAIA,IAAOF,EAAG,UAAW,KAAIE,IAAOF,EAAG,SAAA;AAAY,iBAAO;AAC1D,YAAIC,IAAQD,EAAG,WAAWH,EAAW,IAAIK,GAAM,CAAC,CAAC;AAEjD,YADK,KAAK,KAAKD,EAAM,MAAM,MAAGA,IAAQD,EAAG,WAAWH,EAAW,IAAIK,GAAMD,EAAM,MAAM,CAAC,CAAC,IACnFA,EAAM,QAAQ,UAAUA,EAAM,OAAO,MAAM,GAAG,CAAC,KAAK;AAAY,iBAAOA,EAAM,QAAQ;MAC1F;AALQS,QAAAwB,GAAA,YAAA;AAOT,UAAIJ,IAAY7B,EAAM,MAAM8B,IAAMG,EAAWJ,CAAS;AACtD,UAAIC,KAAO,QAAQG,EAAWJ,IAAY,CAAC,KAAK;AAAM,eAAO;AAC7D,eAAShB,IAAMgB,OAAa;AAC1B,YAAIG,IAAOC,EAAWpB,IAAM,CAAC;AAC7B,YAAImB,KAAQ;AAAM;AAClB,UAAEnB;MAAA;AAEJ,aAAO;QAAC,MAAMjB,EAAW,IAAIiC,GAAWC,IAAM,CAAC;QACvC,IAAI/B,EAAG,QAAQH,EAAW,IAAIiB,CAAG,CAAC;MAAC;IAC7C,CAAC;EAED,CAAC;;;;;;;;", "names": ["mod", "require$$0", "CodeMirror", "bracketFolding", "pairs", "cm", "start", "line", "lineText", "findOpening", "pair", "tokenType", "at", "pass", "found", "__name", "find<PERSON><PERSON><PERSON>", "count", "lastLine", "end", "startCh", "endCh", "outer", "i", "text", "pos", "nextOpen", "nextClose", "open", "a", "b", "range", "hasImport", "e", "semi", "startLine", "has", "prev", "next", "hasInclude"]}