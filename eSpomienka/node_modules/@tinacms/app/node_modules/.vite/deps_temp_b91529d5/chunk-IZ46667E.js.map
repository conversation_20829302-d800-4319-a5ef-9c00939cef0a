{"version": 3, "sources": ["../../../../../@graphiql/codemirror-graphql/esm/utils/info-addon.js"], "sourcesContent": ["import CodeMirror from 'codemirror';\nCodeMirror.defineOption('info', false, (cm, options, old) => {\n    if (old && old !== CodeMirror.Init) {\n        const oldOnMouseOver = cm.state.info.onMouseOver;\n        CodeMirror.off(cm.getWrapperElement(), 'mouseover', oldOnMouseOver);\n        clearTimeout(cm.state.info.hoverTimeout);\n        delete cm.state.info;\n    }\n    if (options) {\n        const state = (cm.state.info = createState(options));\n        state.onMouseOver = onMouseOver.bind(null, cm);\n        CodeMirror.on(cm.getWrapperElement(), 'mouseover', state.onMouseOver);\n    }\n});\nfunction createState(options) {\n    return {\n        options: options instanceof Function\n            ? { render: options }\n            : options === true\n                ? {}\n                : options,\n    };\n}\nfunction getHoverTime(cm) {\n    const { options } = cm.state.info;\n    return (options === null || options === void 0 ? void 0 : options.hoverTime) || 500;\n}\nfunction onMouseOver(cm, e) {\n    const state = cm.state.info;\n    const target = e.target || e.srcElement;\n    if (!(target instanceof HTMLElement)) {\n        return;\n    }\n    if (target.nodeName !== 'SPAN' || state.hoverTimeout !== undefined) {\n        return;\n    }\n    const box = target.getBoundingClientRect();\n    const onMouseMove = function () {\n        clearTimeout(state.hoverTimeout);\n        state.hoverTimeout = setTimeout(onHover, hoverTime);\n    };\n    const onMouseOut = function () {\n        CodeMirror.off(document, 'mousemove', onMouseMove);\n        CodeMirror.off(cm.getWrapperElement(), 'mouseout', onMouseOut);\n        clearTimeout(state.hoverTimeout);\n        state.hoverTimeout = undefined;\n    };\n    const onHover = function () {\n        CodeMirror.off(document, 'mousemove', onMouseMove);\n        CodeMirror.off(cm.getWrapperElement(), 'mouseout', onMouseOut);\n        state.hoverTimeout = undefined;\n        onMouseHover(cm, box);\n    };\n    const hoverTime = getHoverTime(cm);\n    state.hoverTimeout = setTimeout(onHover, hoverTime);\n    CodeMirror.on(document, 'mousemove', onMouseMove);\n    CodeMirror.on(cm.getWrapperElement(), 'mouseout', onMouseOut);\n}\nfunction onMouseHover(cm, box) {\n    const pos = cm.coordsChar({\n        left: (box.left + box.right) / 2,\n        top: (box.top + box.bottom) / 2,\n    }, 'window');\n    const state = cm.state.info;\n    const { options } = state;\n    const render = options.render || cm.getHelper(pos, 'info');\n    if (render) {\n        const token = cm.getTokenAt(pos, true);\n        if (token) {\n            const info = render(token, options, cm, pos);\n            if (info) {\n                showPopup(cm, box, info);\n            }\n        }\n    }\n}\nfunction showPopup(cm, box, info) {\n    const popup = document.createElement('div');\n    popup.className = 'CodeMirror-info';\n    popup.append(info);\n    document.body.append(popup);\n    const popupBox = popup.getBoundingClientRect();\n    const popupStyle = window.getComputedStyle(popup);\n    const popupWidth = popupBox.right -\n        popupBox.left +\n        parseFloat(popupStyle.marginLeft) +\n        parseFloat(popupStyle.marginRight);\n    const popupHeight = popupBox.bottom -\n        popupBox.top +\n        parseFloat(popupStyle.marginTop) +\n        parseFloat(popupStyle.marginBottom);\n    let topPos = box.bottom;\n    if (popupHeight > window.innerHeight - box.bottom - 15 &&\n        box.top > window.innerHeight - box.bottom) {\n        topPos = box.top - popupHeight;\n    }\n    if (topPos < 0) {\n        topPos = box.bottom;\n    }\n    let leftPos = Math.max(0, window.innerWidth - popupWidth - 15);\n    if (leftPos > box.left) {\n        leftPos = box.left;\n    }\n    popup.style.opacity = '1';\n    popup.style.top = topPos + 'px';\n    popup.style.left = leftPos + 'px';\n    let popupTimeout;\n    const onMouseOverPopup = function () {\n        clearTimeout(popupTimeout);\n    };\n    const onMouseOut = function () {\n        clearTimeout(popupTimeout);\n        popupTimeout = setTimeout(hidePopup, 200);\n    };\n    const hidePopup = function () {\n        CodeMirror.off(popup, 'mouseover', onMouseOverPopup);\n        CodeMirror.off(popup, 'mouseout', onMouseOut);\n        CodeMirror.off(cm.getWrapperElement(), 'mouseout', onMouseOut);\n        if (popup.style.opacity) {\n            popup.style.opacity = '0';\n            setTimeout(() => {\n                if (popup.parentNode) {\n                    popup.remove();\n                }\n            }, 600);\n        }\n        else if (popup.parentNode) {\n            popup.remove();\n        }\n    };\n    CodeMirror.on(popup, 'mouseover', onMouseOverPopup);\n    CodeMirror.on(popup, 'mouseout', onMouseOut);\n    CodeMirror.on(cm.getWrapperElement(), 'mouseout', onMouseOut);\n}\n//# sourceMappingURL=info-addon.js.map"], "mappings": ";;;;;;;AACAA,EAAW,aAAa,QAAQ,OAAO,CAACC,GAAIC,GAASC,MAAQ;AACzD,MAAIA,KAAOA,MAAQH,EAAW,MAAM;AAChC,UAAMI,IAAiBH,EAAG,MAAM,KAAK;AACrCD,MAAW,IAAIC,EAAG,kBAAiB,GAAI,aAAaG,CAAc,GAClE,aAAaH,EAAG,MAAM,KAAK,YAAY,GACvC,OAAOA,EAAG,MAAM;EAAA;AAEpB,MAAIC,GAAS;AACT,UAAMG,IAASJ,EAAG,MAAM,OAAOK,EAAYJ,CAAO;AAClDG,MAAM,cAAcE,EAAY,KAAK,MAAMN,CAAE,GAC7CD,EAAW,GAAGC,EAAG,kBAAmB,GAAE,aAAaI,EAAM,WAAW;EAAA;AAE5E,CAAC;AACD,SAASC,EAAYJ,GAAS;AAC1B,SAAO;IACH,SAASA,aAAmB,WACtB,EAAE,QAAQA,EAAS,IACnBA,MAAY,OACR,CAAE,IACFA;EAClB;AACA;AARSM,EAAAF,GAAA,aAAA;AAST,SAASG,EAAaR,GAAI;AACtB,QAAM,EAAE,SAAAC,EAAS,IAAGD,EAAG,MAAM;AAC7B,UAAQC,KAAY,OAA6B,SAASA,EAAQ,cAAc;AACpF;AAHSM,EAAAC,GAAA,cAAA;AAIT,SAASF,EAAYN,GAAIS,GAAG;AACxB,QAAML,IAAQJ,EAAG,MAAM,MACjBU,IAASD,EAAE,UAAUA,EAAE;AAI7B,MAHI,EAAEC,aAAkB,gBAGpBA,EAAO,aAAa,UAAUN,EAAM,iBAAiB;AACrD;AAEJ,QAAMO,IAAMD,EAAO,sBAAA,GACbE,IAAcL,EAAA,WAAY;AAC5B,iBAAaH,EAAM,YAAY,GAC/BA,EAAM,eAAe,WAAWS,GAASC,CAAS;EAC1D,GAHwB,aAAA,GAIdC,IAAaR,EAAA,WAAY;AAC3BR,MAAW,IAAI,UAAU,aAAaa,CAAW,GACjDb,EAAW,IAAIC,EAAG,kBAAiB,GAAI,YAAYe,CAAU,GAC7D,aAAaX,EAAM,YAAY,GAC/BA,EAAM,eAAe;EAC7B,GALuB,YAAA,GAMbS,IAAUN,EAAA,WAAY;AACxBR,MAAW,IAAI,UAAU,aAAaa,CAAW,GACjDb,EAAW,IAAIC,EAAG,kBAAiB,GAAI,YAAYe,CAAU,GAC7DX,EAAM,eAAe,QACrBY,EAAahB,GAAIW,CAAG;EAC5B,GALoB,SAAA,GAMVG,IAAYN,EAAaR,CAAE;AACjCI,IAAM,eAAe,WAAWS,GAASC,CAAS,GAClDf,EAAW,GAAG,UAAU,aAAaa,CAAW,GAChDb,EAAW,GAAGC,EAAG,kBAAiB,GAAI,YAAYe,CAAU;AAChE;AA9BSR,EAAAD,GAAA,aAAA;AA+BT,SAASU,EAAahB,GAAIW,GAAK;AAC3B,QAAMM,IAAMjB,EAAG,WAAW;IACtB,OAAOW,EAAI,OAAOA,EAAI,SAAS;IAC/B,MAAMA,EAAI,MAAMA,EAAI,UAAU;EACjC,GAAE,QAAQ,GACLP,IAAQJ,EAAG,MAAM,MACjB,EAAE,SAAAC,EAAS,IAAGG,GACdc,IAASjB,EAAQ,UAAUD,EAAG,UAAUiB,GAAK,MAAM;AACzD,MAAIC,GAAQ;AACR,UAAMC,IAAQnB,EAAG,WAAWiB,GAAK,IAAI;AACrC,QAAIE,GAAO;AACP,YAAMC,IAAOF,EAAOC,GAAOlB,GAASD,GAAIiB,CAAG;AACvCG,WACAC,EAAUrB,GAAIW,GAAKS,CAAI;IAAA;EAAA;AAIvC;AAjBSb,EAAAS,GAAA,cAAA;AAkBT,SAASK,EAAUrB,GAAIW,GAAKS,GAAM;AAC9B,QAAME,IAAQ,SAAS,cAAc,KAAK;AAC1CA,IAAM,YAAY,mBAClBA,EAAM,OAAOF,CAAI,GACjB,SAAS,KAAK,OAAOE,CAAK;AAC1B,QAAMC,IAAWD,EAAM,sBAAA,GACjBE,IAAa,OAAO,iBAAiBF,CAAK,GAC1CG,IAAaF,EAAS,QACxBA,EAAS,OACT,WAAWC,EAAW,UAAU,IAChC,WAAWA,EAAW,WAAW,GAC/BE,IAAcH,EAAS,SACzBA,EAAS,MACT,WAAWC,EAAW,SAAS,IAC/B,WAAWA,EAAW,YAAY;AACtC,MAAIG,IAAShB,EAAI;AACbe,MAAc,OAAO,cAAcf,EAAI,SAAS,MAChDA,EAAI,MAAM,OAAO,cAAcA,EAAI,WACnCgB,IAAShB,EAAI,MAAMe,IAEnBC,IAAS,MACTA,IAAShB,EAAI;AAEjB,MAAIiB,IAAU,KAAK,IAAI,GAAG,OAAO,aAAaH,IAAa,EAAE;AACzDG,MAAUjB,EAAI,SACdiB,IAAUjB,EAAI,OAElBW,EAAM,MAAM,UAAU,KACtBA,EAAM,MAAM,MAAMK,IAAS,MAC3BL,EAAM,MAAM,OAAOM,IAAU;AAC7B,MAAIC;AACJ,QAAMC,KAAmBvB,EAAA,WAAY;AACjC,iBAAasB,CAAY;EACjC,GAF6B,kBAAA,GAGnBd,IAAaR,EAAA,WAAY;AAC3B,iBAAasB,CAAY,GACzBA,IAAe,WAAWE,GAAW,GAAG;EAChD,GAHuB,YAAA,GAIbA,IAAYxB,EAAA,WAAY;AAC1BR,MAAW,IAAIuB,GAAO,aAAaQ,EAAgB,GACnD/B,EAAW,IAAIuB,GAAO,YAAYP,CAAU,GAC5ChB,EAAW,IAAIC,EAAG,kBAAiB,GAAI,YAAYe,CAAU,GACzDO,EAAM,MAAM,WACZA,EAAM,MAAM,UAAU,KACtB,WAAW,MAAM;AACTA,QAAM,cACNA,EAAM,OAAM;IAEnB,GAAE,GAAG,KAEDA,EAAM,cACXA,EAAM,OAAM;EAExB,GAfsB,WAAA;AAgBlBvB,IAAW,GAAGuB,GAAO,aAAaQ,EAAgB,GAClD/B,EAAW,GAAGuB,GAAO,YAAYP,CAAU,GAC3ChB,EAAW,GAAGC,EAAG,kBAAiB,GAAI,YAAYe,CAAU;AAChE;AAzDSR,EAAAc,GAAA,WAAA;", "names": ["CodeMirror", "cm", "options", "old", "oldOnMouseOver", "state", "createState", "onMouseOver", "__name", "getHoverTime", "e", "target", "box", "onMouseMove", "onHover", "hoverTime", "onMouseOut", "onMouseHover", "pos", "render", "token", "info", "showPopup", "popup", "popupBox", "popupStyle", "popup<PERSON><PERSON><PERSON>", "popupHeight", "topPos", "leftPos", "popupTimeout", "onMouseOverPopup", "hidePopup"]}