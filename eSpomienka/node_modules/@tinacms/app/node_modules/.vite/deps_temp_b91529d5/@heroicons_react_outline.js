import {
  require_react
} from "./chunk-AWTNXPUB.js";
import {
  __toESM
} from "./chunk-AUZ3RYOM.js";

// node_modules/@heroicons/react/outline/esm/AcademicCapIcon.js
var React = __toESM(require_react(), 1);
function AcademicCapIcon(props, svgRef) {
  return React.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React.createElement("path", {
    d: "M12 14l9-5-9-5-9 5 9 5z"
  }), React.createElement("path", {
    d: "M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"
  }), React.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222"
  }));
}
var ForwardRef = React.forwardRef(AcademicCapIcon);
var AcademicCapIcon_default = ForwardRef;

// node_modules/@heroicons/react/outline/esm/AdjustmentsIcon.js
var React2 = __toESM(require_react(), 1);
function AdjustmentsIcon(props, svgRef) {
  return React2.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React2.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"
  }));
}
var ForwardRef2 = React2.forwardRef(AdjustmentsIcon);
var AdjustmentsIcon_default = ForwardRef2;

// node_modules/@heroicons/react/outline/esm/AnnotationIcon.js
var React3 = __toESM(require_react(), 1);
function AnnotationIcon(props, svgRef) {
  return React3.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React3.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"
  }));
}
var ForwardRef3 = React3.forwardRef(AnnotationIcon);
var AnnotationIcon_default = ForwardRef3;

// node_modules/@heroicons/react/outline/esm/ArchiveIcon.js
var React4 = __toESM(require_react(), 1);
function ArchiveIcon(props, svgRef) {
  return React4.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React4.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"
  }));
}
var ForwardRef4 = React4.forwardRef(ArchiveIcon);
var ArchiveIcon_default = ForwardRef4;

// node_modules/@heroicons/react/outline/esm/ArrowCircleDownIcon.js
var React5 = __toESM(require_react(), 1);
function ArrowCircleDownIcon(props, svgRef) {
  return React5.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React5.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M15 13l-3 3m0 0l-3-3m3 3V8m0 13a9 9 0 110-18 9 9 0 010 18z"
  }));
}
var ForwardRef5 = React5.forwardRef(ArrowCircleDownIcon);
var ArrowCircleDownIcon_default = ForwardRef5;

// node_modules/@heroicons/react/outline/esm/ArrowCircleLeftIcon.js
var React6 = __toESM(require_react(), 1);
function ArrowCircleLeftIcon(props, svgRef) {
  return React6.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React6.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M11 15l-3-3m0 0l3-3m-3 3h8M3 12a9 9 0 1118 0 9 9 0 01-18 0z"
  }));
}
var ForwardRef6 = React6.forwardRef(ArrowCircleLeftIcon);
var ArrowCircleLeftIcon_default = ForwardRef6;

// node_modules/@heroicons/react/outline/esm/ArrowCircleRightIcon.js
var React7 = __toESM(require_react(), 1);
function ArrowCircleRightIcon(props, svgRef) {
  return React7.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React7.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M13 9l3 3m0 0l-3 3m3-3H8m13 0a9 9 0 11-18 0 9 9 0 0118 0z"
  }));
}
var ForwardRef7 = React7.forwardRef(ArrowCircleRightIcon);
var ArrowCircleRightIcon_default = ForwardRef7;

// node_modules/@heroicons/react/outline/esm/ArrowCircleUpIcon.js
var React8 = __toESM(require_react(), 1);
function ArrowCircleUpIcon(props, svgRef) {
  return React8.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React8.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M9 11l3-3m0 0l3 3m-3-3v8m0-13a9 9 0 110 18 9 9 0 010-18z"
  }));
}
var ForwardRef8 = React8.forwardRef(ArrowCircleUpIcon);
var ArrowCircleUpIcon_default = ForwardRef8;

// node_modules/@heroicons/react/outline/esm/ArrowDownIcon.js
var React9 = __toESM(require_react(), 1);
function ArrowDownIcon(props, svgRef) {
  return React9.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React9.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M19 14l-7 7m0 0l-7-7m7 7V3"
  }));
}
var ForwardRef9 = React9.forwardRef(ArrowDownIcon);
var ArrowDownIcon_default = ForwardRef9;

// node_modules/@heroicons/react/outline/esm/ArrowLeftIcon.js
var React10 = __toESM(require_react(), 1);
function ArrowLeftIcon(props, svgRef) {
  return React10.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React10.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M10 19l-7-7m0 0l7-7m-7 7h18"
  }));
}
var ForwardRef10 = React10.forwardRef(ArrowLeftIcon);
var ArrowLeftIcon_default = ForwardRef10;

// node_modules/@heroicons/react/outline/esm/ArrowNarrowDownIcon.js
var React11 = __toESM(require_react(), 1);
function ArrowNarrowDownIcon(props, svgRef) {
  return React11.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React11.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M16 17l-4 4m0 0l-4-4m4 4V3"
  }));
}
var ForwardRef11 = React11.forwardRef(ArrowNarrowDownIcon);
var ArrowNarrowDownIcon_default = ForwardRef11;

// node_modules/@heroicons/react/outline/esm/ArrowNarrowLeftIcon.js
var React12 = __toESM(require_react(), 1);
function ArrowNarrowLeftIcon(props, svgRef) {
  return React12.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React12.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M7 16l-4-4m0 0l4-4m-4 4h18"
  }));
}
var ForwardRef12 = React12.forwardRef(ArrowNarrowLeftIcon);
var ArrowNarrowLeftIcon_default = ForwardRef12;

// node_modules/@heroicons/react/outline/esm/ArrowNarrowRightIcon.js
var React13 = __toESM(require_react(), 1);
function ArrowNarrowRightIcon(props, svgRef) {
  return React13.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React13.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M17 8l4 4m0 0l-4 4m4-4H3"
  }));
}
var ForwardRef13 = React13.forwardRef(ArrowNarrowRightIcon);
var ArrowNarrowRightIcon_default = ForwardRef13;

// node_modules/@heroicons/react/outline/esm/ArrowNarrowUpIcon.js
var React14 = __toESM(require_react(), 1);
function ArrowNarrowUpIcon(props, svgRef) {
  return React14.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React14.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M8 7l4-4m0 0l4 4m-4-4v18"
  }));
}
var ForwardRef14 = React14.forwardRef(ArrowNarrowUpIcon);
var ArrowNarrowUpIcon_default = ForwardRef14;

// node_modules/@heroicons/react/outline/esm/ArrowRightIcon.js
var React15 = __toESM(require_react(), 1);
function ArrowRightIcon(props, svgRef) {
  return React15.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React15.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M14 5l7 7m0 0l-7 7m7-7H3"
  }));
}
var ForwardRef15 = React15.forwardRef(ArrowRightIcon);
var ArrowRightIcon_default = ForwardRef15;

// node_modules/@heroicons/react/outline/esm/ArrowSmDownIcon.js
var React16 = __toESM(require_react(), 1);
function ArrowSmDownIcon(props, svgRef) {
  return React16.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React16.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M17 13l-5 5m0 0l-5-5m5 5V6"
  }));
}
var ForwardRef16 = React16.forwardRef(ArrowSmDownIcon);
var ArrowSmDownIcon_default = ForwardRef16;

// node_modules/@heroicons/react/outline/esm/ArrowSmLeftIcon.js
var React17 = __toESM(require_react(), 1);
function ArrowSmLeftIcon(props, svgRef) {
  return React17.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React17.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M11 17l-5-5m0 0l5-5m-5 5h12"
  }));
}
var ForwardRef17 = React17.forwardRef(ArrowSmLeftIcon);
var ArrowSmLeftIcon_default = ForwardRef17;

// node_modules/@heroicons/react/outline/esm/ArrowSmRightIcon.js
var React18 = __toESM(require_react(), 1);
function ArrowSmRightIcon(props, svgRef) {
  return React18.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React18.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M13 7l5 5m0 0l-5 5m5-5H6"
  }));
}
var ForwardRef18 = React18.forwardRef(ArrowSmRightIcon);
var ArrowSmRightIcon_default = ForwardRef18;

// node_modules/@heroicons/react/outline/esm/ArrowSmUpIcon.js
var React19 = __toESM(require_react(), 1);
function ArrowSmUpIcon(props, svgRef) {
  return React19.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React19.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M7 11l5-5m0 0l5 5m-5-5v12"
  }));
}
var ForwardRef19 = React19.forwardRef(ArrowSmUpIcon);
var ArrowSmUpIcon_default = ForwardRef19;

// node_modules/@heroicons/react/outline/esm/ArrowUpIcon.js
var React20 = __toESM(require_react(), 1);
function ArrowUpIcon(props, svgRef) {
  return React20.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React20.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M5 10l7-7m0 0l7 7m-7-7v18"
  }));
}
var ForwardRef20 = React20.forwardRef(ArrowUpIcon);
var ArrowUpIcon_default = ForwardRef20;

// node_modules/@heroicons/react/outline/esm/ArrowsExpandIcon.js
var React21 = __toESM(require_react(), 1);
function ArrowsExpandIcon(props, svgRef) {
  return React21.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React21.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"
  }));
}
var ForwardRef21 = React21.forwardRef(ArrowsExpandIcon);
var ArrowsExpandIcon_default = ForwardRef21;

// node_modules/@heroicons/react/outline/esm/AtSymbolIcon.js
var React22 = __toESM(require_react(), 1);
function AtSymbolIcon(props, svgRef) {
  return React22.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React22.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"
  }));
}
var ForwardRef22 = React22.forwardRef(AtSymbolIcon);
var AtSymbolIcon_default = ForwardRef22;

// node_modules/@heroicons/react/outline/esm/BackspaceIcon.js
var React23 = __toESM(require_react(), 1);
function BackspaceIcon(props, svgRef) {
  return React23.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React23.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M12 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2M3 12l6.414 6.414a2 2 0 001.414.586H19a2 2 0 002-2V7a2 2 0 00-2-2h-8.172a2 2 0 00-1.414.586L3 12z"
  }));
}
var ForwardRef23 = React23.forwardRef(BackspaceIcon);
var BackspaceIcon_default = ForwardRef23;

// node_modules/@heroicons/react/outline/esm/BadgeCheckIcon.js
var React24 = __toESM(require_react(), 1);
function BadgeCheckIcon(props, svgRef) {
  return React24.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React24.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"
  }));
}
var ForwardRef24 = React24.forwardRef(BadgeCheckIcon);
var BadgeCheckIcon_default = ForwardRef24;

// node_modules/@heroicons/react/outline/esm/BanIcon.js
var React25 = __toESM(require_react(), 1);
function BanIcon(props, svgRef) {
  return React25.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React25.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636"
  }));
}
var ForwardRef25 = React25.forwardRef(BanIcon);
var BanIcon_default = ForwardRef25;

// node_modules/@heroicons/react/outline/esm/BeakerIcon.js
var React26 = __toESM(require_react(), 1);
function BeakerIcon(props, svgRef) {
  return React26.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React26.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"
  }));
}
var ForwardRef26 = React26.forwardRef(BeakerIcon);
var BeakerIcon_default = ForwardRef26;

// node_modules/@heroicons/react/outline/esm/BellIcon.js
var React27 = __toESM(require_react(), 1);
function BellIcon(props, svgRef) {
  return React27.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React27.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
  }));
}
var ForwardRef27 = React27.forwardRef(BellIcon);
var BellIcon_default = ForwardRef27;

// node_modules/@heroicons/react/outline/esm/BookOpenIcon.js
var React28 = __toESM(require_react(), 1);
function BookOpenIcon(props, svgRef) {
  return React28.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React28.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
  }));
}
var ForwardRef28 = React28.forwardRef(BookOpenIcon);
var BookOpenIcon_default = ForwardRef28;

// node_modules/@heroicons/react/outline/esm/BookmarkAltIcon.js
var React29 = __toESM(require_react(), 1);
function BookmarkAltIcon(props, svgRef) {
  return React29.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React29.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M16 4v12l-4-2-4 2V4M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
  }));
}
var ForwardRef29 = React29.forwardRef(BookmarkAltIcon);
var BookmarkAltIcon_default = ForwardRef29;

// node_modules/@heroicons/react/outline/esm/BookmarkIcon.js
var React30 = __toESM(require_react(), 1);
function BookmarkIcon(props, svgRef) {
  return React30.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React30.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"
  }));
}
var ForwardRef30 = React30.forwardRef(BookmarkIcon);
var BookmarkIcon_default = ForwardRef30;

// node_modules/@heroicons/react/outline/esm/BriefcaseIcon.js
var React31 = __toESM(require_react(), 1);
function BriefcaseIcon(props, svgRef) {
  return React31.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React31.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
  }));
}
var ForwardRef31 = React31.forwardRef(BriefcaseIcon);
var BriefcaseIcon_default = ForwardRef31;

// node_modules/@heroicons/react/outline/esm/CakeIcon.js
var React32 = __toESM(require_react(), 1);
function CakeIcon(props, svgRef) {
  return React32.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React32.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M21 15.546c-.523 0-1.046.151-1.5.454a2.704 2.704 0 01-3 0 2.704 2.704 0 00-3 0 2.704 2.704 0 01-3 0 2.704 2.704 0 00-3 0 2.704 2.704 0 01-3 0 2.701 2.701 0 00-1.5-.454M9 6v2m3-2v2m3-2v2M9 3h.01M12 3h.01M15 3h.01M21 21v-7a2 2 0 00-2-2H5a2 2 0 00-2 2v7h18zm-3-9v-2a2 2 0 00-2-2H8a2 2 0 00-2 2v2h12z"
  }));
}
var ForwardRef32 = React32.forwardRef(CakeIcon);
var CakeIcon_default = ForwardRef32;

// node_modules/@heroicons/react/outline/esm/CalculatorIcon.js
var React33 = __toESM(require_react(), 1);
function CalculatorIcon(props, svgRef) {
  return React33.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React33.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"
  }));
}
var ForwardRef33 = React33.forwardRef(CalculatorIcon);
var CalculatorIcon_default = ForwardRef33;

// node_modules/@heroicons/react/outline/esm/CalendarIcon.js
var React34 = __toESM(require_react(), 1);
function CalendarIcon(props, svgRef) {
  return React34.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React34.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
  }));
}
var ForwardRef34 = React34.forwardRef(CalendarIcon);
var CalendarIcon_default = ForwardRef34;

// node_modules/@heroicons/react/outline/esm/CameraIcon.js
var React35 = __toESM(require_react(), 1);
function CameraIcon(props, svgRef) {
  return React35.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React35.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"
  }), React35.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M15 13a3 3 0 11-6 0 3 3 0 016 0z"
  }));
}
var ForwardRef35 = React35.forwardRef(CameraIcon);
var CameraIcon_default = ForwardRef35;

// node_modules/@heroicons/react/outline/esm/CashIcon.js
var React36 = __toESM(require_react(), 1);
function CashIcon(props, svgRef) {
  return React36.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React36.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"
  }));
}
var ForwardRef36 = React36.forwardRef(CashIcon);
var CashIcon_default = ForwardRef36;

// node_modules/@heroicons/react/outline/esm/ChartBarIcon.js
var React37 = __toESM(require_react(), 1);
function ChartBarIcon(props, svgRef) {
  return React37.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React37.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
  }));
}
var ForwardRef37 = React37.forwardRef(ChartBarIcon);
var ChartBarIcon_default = ForwardRef37;

// node_modules/@heroicons/react/outline/esm/ChartPieIcon.js
var React38 = __toESM(require_react(), 1);
function ChartPieIcon(props, svgRef) {
  return React38.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React38.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"
  }), React38.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"
  }));
}
var ForwardRef38 = React38.forwardRef(ChartPieIcon);
var ChartPieIcon_default = ForwardRef38;

// node_modules/@heroicons/react/outline/esm/ChartSquareBarIcon.js
var React39 = __toESM(require_react(), 1);
function ChartSquareBarIcon(props, svgRef) {
  return React39.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React39.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
  }));
}
var ForwardRef39 = React39.forwardRef(ChartSquareBarIcon);
var ChartSquareBarIcon_default = ForwardRef39;

// node_modules/@heroicons/react/outline/esm/ChatAlt2Icon.js
var React40 = __toESM(require_react(), 1);
function ChatAlt2Icon(props, svgRef) {
  return React40.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React40.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z"
  }));
}
var ForwardRef40 = React40.forwardRef(ChatAlt2Icon);
var ChatAlt2Icon_default = ForwardRef40;

// node_modules/@heroicons/react/outline/esm/ChatAltIcon.js
var React41 = __toESM(require_react(), 1);
function ChatAltIcon(props, svgRef) {
  return React41.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React41.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"
  }));
}
var ForwardRef41 = React41.forwardRef(ChatAltIcon);
var ChatAltIcon_default = ForwardRef41;

// node_modules/@heroicons/react/outline/esm/ChatIcon.js
var React42 = __toESM(require_react(), 1);
function ChatIcon(props, svgRef) {
  return React42.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React42.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
  }));
}
var ForwardRef42 = React42.forwardRef(ChatIcon);
var ChatIcon_default = ForwardRef42;

// node_modules/@heroicons/react/outline/esm/CheckCircleIcon.js
var React43 = __toESM(require_react(), 1);
function CheckCircleIcon(props, svgRef) {
  return React43.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React43.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
  }));
}
var ForwardRef43 = React43.forwardRef(CheckCircleIcon);
var CheckCircleIcon_default = ForwardRef43;

// node_modules/@heroicons/react/outline/esm/CheckIcon.js
var React44 = __toESM(require_react(), 1);
function CheckIcon(props, svgRef) {
  return React44.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React44.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M5 13l4 4L19 7"
  }));
}
var ForwardRef44 = React44.forwardRef(CheckIcon);
var CheckIcon_default = ForwardRef44;

// node_modules/@heroicons/react/outline/esm/ChevronDoubleDownIcon.js
var React45 = __toESM(require_react(), 1);
function ChevronDoubleDownIcon(props, svgRef) {
  return React45.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React45.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M19 13l-7 7-7-7m14-8l-7 7-7-7"
  }));
}
var ForwardRef45 = React45.forwardRef(ChevronDoubleDownIcon);
var ChevronDoubleDownIcon_default = ForwardRef45;

// node_modules/@heroicons/react/outline/esm/ChevronDoubleLeftIcon.js
var React46 = __toESM(require_react(), 1);
function ChevronDoubleLeftIcon(props, svgRef) {
  return React46.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React46.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M11 19l-7-7 7-7m8 14l-7-7 7-7"
  }));
}
var ForwardRef46 = React46.forwardRef(ChevronDoubleLeftIcon);
var ChevronDoubleLeftIcon_default = ForwardRef46;

// node_modules/@heroicons/react/outline/esm/ChevronDoubleRightIcon.js
var React47 = __toESM(require_react(), 1);
function ChevronDoubleRightIcon(props, svgRef) {
  return React47.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React47.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M13 5l7 7-7 7M5 5l7 7-7 7"
  }));
}
var ForwardRef47 = React47.forwardRef(ChevronDoubleRightIcon);
var ChevronDoubleRightIcon_default = ForwardRef47;

// node_modules/@heroicons/react/outline/esm/ChevronDoubleUpIcon.js
var React48 = __toESM(require_react(), 1);
function ChevronDoubleUpIcon(props, svgRef) {
  return React48.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React48.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M5 11l7-7 7 7M5 19l7-7 7 7"
  }));
}
var ForwardRef48 = React48.forwardRef(ChevronDoubleUpIcon);
var ChevronDoubleUpIcon_default = ForwardRef48;

// node_modules/@heroicons/react/outline/esm/ChevronDownIcon.js
var React49 = __toESM(require_react(), 1);
function ChevronDownIcon(props, svgRef) {
  return React49.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React49.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M19 9l-7 7-7-7"
  }));
}
var ForwardRef49 = React49.forwardRef(ChevronDownIcon);
var ChevronDownIcon_default = ForwardRef49;

// node_modules/@heroicons/react/outline/esm/ChevronLeftIcon.js
var React50 = __toESM(require_react(), 1);
function ChevronLeftIcon(props, svgRef) {
  return React50.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React50.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M15 19l-7-7 7-7"
  }));
}
var ForwardRef50 = React50.forwardRef(ChevronLeftIcon);
var ChevronLeftIcon_default = ForwardRef50;

// node_modules/@heroicons/react/outline/esm/ChevronRightIcon.js
var React51 = __toESM(require_react(), 1);
function ChevronRightIcon(props, svgRef) {
  return React51.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React51.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M9 5l7 7-7 7"
  }));
}
var ForwardRef51 = React51.forwardRef(ChevronRightIcon);
var ChevronRightIcon_default = ForwardRef51;

// node_modules/@heroicons/react/outline/esm/ChevronUpIcon.js
var React52 = __toESM(require_react(), 1);
function ChevronUpIcon(props, svgRef) {
  return React52.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React52.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M5 15l7-7 7 7"
  }));
}
var ForwardRef52 = React52.forwardRef(ChevronUpIcon);
var ChevronUpIcon_default = ForwardRef52;

// node_modules/@heroicons/react/outline/esm/ChipIcon.js
var React53 = __toESM(require_react(), 1);
function ChipIcon(props, svgRef) {
  return React53.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React53.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"
  }));
}
var ForwardRef53 = React53.forwardRef(ChipIcon);
var ChipIcon_default = ForwardRef53;

// node_modules/@heroicons/react/outline/esm/ClipboardCheckIcon.js
var React54 = __toESM(require_react(), 1);
function ClipboardCheckIcon(props, svgRef) {
  return React54.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React54.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"
  }));
}
var ForwardRef54 = React54.forwardRef(ClipboardCheckIcon);
var ClipboardCheckIcon_default = ForwardRef54;

// node_modules/@heroicons/react/outline/esm/ClipboardCopyIcon.js
var React55 = __toESM(require_react(), 1);
function ClipboardCopyIcon(props, svgRef) {
  return React55.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React55.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"
  }));
}
var ForwardRef55 = React55.forwardRef(ClipboardCopyIcon);
var ClipboardCopyIcon_default = ForwardRef55;

// node_modules/@heroicons/react/outline/esm/ClipboardListIcon.js
var React56 = __toESM(require_react(), 1);
function ClipboardListIcon(props, svgRef) {
  return React56.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React56.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"
  }));
}
var ForwardRef56 = React56.forwardRef(ClipboardListIcon);
var ClipboardListIcon_default = ForwardRef56;

// node_modules/@heroicons/react/outline/esm/ClipboardIcon.js
var React57 = __toESM(require_react(), 1);
function ClipboardIcon(props, svgRef) {
  return React57.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React57.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
  }));
}
var ForwardRef57 = React57.forwardRef(ClipboardIcon);
var ClipboardIcon_default = ForwardRef57;

// node_modules/@heroicons/react/outline/esm/ClockIcon.js
var React58 = __toESM(require_react(), 1);
function ClockIcon(props, svgRef) {
  return React58.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React58.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
  }));
}
var ForwardRef58 = React58.forwardRef(ClockIcon);
var ClockIcon_default = ForwardRef58;

// node_modules/@heroicons/react/outline/esm/CloudDownloadIcon.js
var React59 = __toESM(require_react(), 1);
function CloudDownloadIcon(props, svgRef) {
  return React59.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React59.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"
  }));
}
var ForwardRef59 = React59.forwardRef(CloudDownloadIcon);
var CloudDownloadIcon_default = ForwardRef59;

// node_modules/@heroicons/react/outline/esm/CloudUploadIcon.js
var React60 = __toESM(require_react(), 1);
function CloudUploadIcon(props, svgRef) {
  return React60.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React60.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
  }));
}
var ForwardRef60 = React60.forwardRef(CloudUploadIcon);
var CloudUploadIcon_default = ForwardRef60;

// node_modules/@heroicons/react/outline/esm/CloudIcon.js
var React61 = __toESM(require_react(), 1);
function CloudIcon(props, svgRef) {
  return React61.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React61.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z"
  }));
}
var ForwardRef61 = React61.forwardRef(CloudIcon);
var CloudIcon_default = ForwardRef61;

// node_modules/@heroicons/react/outline/esm/CodeIcon.js
var React62 = __toESM(require_react(), 1);
function CodeIcon(props, svgRef) {
  return React62.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React62.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"
  }));
}
var ForwardRef62 = React62.forwardRef(CodeIcon);
var CodeIcon_default = ForwardRef62;

// node_modules/@heroicons/react/outline/esm/CogIcon.js
var React63 = __toESM(require_react(), 1);
function CogIcon(props, svgRef) {
  return React63.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React63.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
  }), React63.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M15 12a3 3 0 11-6 0 3 3 0 016 0z"
  }));
}
var ForwardRef63 = React63.forwardRef(CogIcon);
var CogIcon_default = ForwardRef63;

// node_modules/@heroicons/react/outline/esm/CollectionIcon.js
var React64 = __toESM(require_react(), 1);
function CollectionIcon(props, svgRef) {
  return React64.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React64.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
  }));
}
var ForwardRef64 = React64.forwardRef(CollectionIcon);
var CollectionIcon_default = ForwardRef64;

// node_modules/@heroicons/react/outline/esm/ColorSwatchIcon.js
var React65 = __toESM(require_react(), 1);
function ColorSwatchIcon(props, svgRef) {
  return React65.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React65.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01"
  }));
}
var ForwardRef65 = React65.forwardRef(ColorSwatchIcon);
var ColorSwatchIcon_default = ForwardRef65;

// node_modules/@heroicons/react/outline/esm/CreditCardIcon.js
var React66 = __toESM(require_react(), 1);
function CreditCardIcon(props, svgRef) {
  return React66.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React66.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
  }));
}
var ForwardRef66 = React66.forwardRef(CreditCardIcon);
var CreditCardIcon_default = ForwardRef66;

// node_modules/@heroicons/react/outline/esm/CubeTransparentIcon.js
var React67 = __toESM(require_react(), 1);
function CubeTransparentIcon(props, svgRef) {
  return React67.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React67.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M14 10l-2 1m0 0l-2-1m2 1v2.5M20 7l-2 1m2-1l-2-1m2 1v2.5M14 4l-2-1-2 1M4 7l2-1M4 7l2 1M4 7v2.5M12 21l-2-1m2 1l2-1m-2 1v-2.5M6 18l-2-1v-2.5M18 18l2-1v-2.5"
  }));
}
var ForwardRef67 = React67.forwardRef(CubeTransparentIcon);
var CubeTransparentIcon_default = ForwardRef67;

// node_modules/@heroicons/react/outline/esm/CubeIcon.js
var React68 = __toESM(require_react(), 1);
function CubeIcon(props, svgRef) {
  return React68.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React68.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"
  }));
}
var ForwardRef68 = React68.forwardRef(CubeIcon);
var CubeIcon_default = ForwardRef68;

// node_modules/@heroicons/react/outline/esm/CurrencyBangladeshiIcon.js
var React69 = __toESM(require_react(), 1);
function CurrencyBangladeshiIcon(props, svgRef) {
  return React69.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React69.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M11 11V9a2 2 0 00-2-2m2 4v4a2 2 0 104 0v-1m-4-3H9m2 0h4m6 1a9 9 0 11-18 0 9 9 0 0118 0z"
  }));
}
var ForwardRef69 = React69.forwardRef(CurrencyBangladeshiIcon);
var CurrencyBangladeshiIcon_default = ForwardRef69;

// node_modules/@heroicons/react/outline/esm/CurrencyDollarIcon.js
var React70 = __toESM(require_react(), 1);
function CurrencyDollarIcon(props, svgRef) {
  return React70.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React70.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
  }));
}
var ForwardRef70 = React70.forwardRef(CurrencyDollarIcon);
var CurrencyDollarIcon_default = ForwardRef70;

// node_modules/@heroicons/react/outline/esm/CurrencyEuroIcon.js
var React71 = __toESM(require_react(), 1);
function CurrencyEuroIcon(props, svgRef) {
  return React71.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React71.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M14.121 15.536c-1.171 1.952-3.07 1.952-4.242 0-1.172-1.953-1.172-5.119 0-7.072 1.171-1.952 3.07-1.952 4.242 0M8 10.5h4m-4 3h4m9-1.5a9 9 0 11-18 0 9 9 0 0118 0z"
  }));
}
var ForwardRef71 = React71.forwardRef(CurrencyEuroIcon);
var CurrencyEuroIcon_default = ForwardRef71;

// node_modules/@heroicons/react/outline/esm/CurrencyPoundIcon.js
var React72 = __toESM(require_react(), 1);
function CurrencyPoundIcon(props, svgRef) {
  return React72.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React72.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M15 9a2 2 0 10-4 0v5a2 2 0 01-2 2h6m-6-4h4m8 0a9 9 0 11-18 0 9 9 0 0118 0z"
  }));
}
var ForwardRef72 = React72.forwardRef(CurrencyPoundIcon);
var CurrencyPoundIcon_default = ForwardRef72;

// node_modules/@heroicons/react/outline/esm/CurrencyRupeeIcon.js
var React73 = __toESM(require_react(), 1);
function CurrencyRupeeIcon(props, svgRef) {
  return React73.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React73.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M9 8h6m-5 0a3 3 0 110 6H9l3 3m-3-6h6m6 1a9 9 0 11-18 0 9 9 0 0118 0z"
  }));
}
var ForwardRef73 = React73.forwardRef(CurrencyRupeeIcon);
var CurrencyRupeeIcon_default = ForwardRef73;

// node_modules/@heroicons/react/outline/esm/CurrencyYenIcon.js
var React74 = __toESM(require_react(), 1);
function CurrencyYenIcon(props, svgRef) {
  return React74.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React74.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M9 8l3 5m0 0l3-5m-3 5v4m-3-5h6m-6 3h6m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
  }));
}
var ForwardRef74 = React74.forwardRef(CurrencyYenIcon);
var CurrencyYenIcon_default = ForwardRef74;

// node_modules/@heroicons/react/outline/esm/CursorClickIcon.js
var React75 = __toESM(require_react(), 1);
function CursorClickIcon(props, svgRef) {
  return React75.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React75.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122"
  }));
}
var ForwardRef75 = React75.forwardRef(CursorClickIcon);
var CursorClickIcon_default = ForwardRef75;

// node_modules/@heroicons/react/outline/esm/DatabaseIcon.js
var React76 = __toESM(require_react(), 1);
function DatabaseIcon(props, svgRef) {
  return React76.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React76.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4"
  }));
}
var ForwardRef76 = React76.forwardRef(DatabaseIcon);
var DatabaseIcon_default = ForwardRef76;

// node_modules/@heroicons/react/outline/esm/DesktopComputerIcon.js
var React77 = __toESM(require_react(), 1);
function DesktopComputerIcon(props, svgRef) {
  return React77.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React77.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
  }));
}
var ForwardRef77 = React77.forwardRef(DesktopComputerIcon);
var DesktopComputerIcon_default = ForwardRef77;

// node_modules/@heroicons/react/outline/esm/DeviceMobileIcon.js
var React78 = __toESM(require_react(), 1);
function DeviceMobileIcon(props, svgRef) {
  return React78.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React78.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"
  }));
}
var ForwardRef78 = React78.forwardRef(DeviceMobileIcon);
var DeviceMobileIcon_default = ForwardRef78;

// node_modules/@heroicons/react/outline/esm/DeviceTabletIcon.js
var React79 = __toESM(require_react(), 1);
function DeviceTabletIcon(props, svgRef) {
  return React79.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React79.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M12 18h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"
  }));
}
var ForwardRef79 = React79.forwardRef(DeviceTabletIcon);
var DeviceTabletIcon_default = ForwardRef79;

// node_modules/@heroicons/react/outline/esm/DocumentAddIcon.js
var React80 = __toESM(require_react(), 1);
function DocumentAddIcon(props, svgRef) {
  return React80.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React80.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
  }));
}
var ForwardRef80 = React80.forwardRef(DocumentAddIcon);
var DocumentAddIcon_default = ForwardRef80;

// node_modules/@heroicons/react/outline/esm/DocumentDownloadIcon.js
var React81 = __toESM(require_react(), 1);
function DocumentDownloadIcon(props, svgRef) {
  return React81.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React81.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
  }));
}
var ForwardRef81 = React81.forwardRef(DocumentDownloadIcon);
var DocumentDownloadIcon_default = ForwardRef81;

// node_modules/@heroicons/react/outline/esm/DocumentDuplicateIcon.js
var React82 = __toESM(require_react(), 1);
function DocumentDuplicateIcon(props, svgRef) {
  return React82.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React82.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2"
  }));
}
var ForwardRef82 = React82.forwardRef(DocumentDuplicateIcon);
var DocumentDuplicateIcon_default = ForwardRef82;

// node_modules/@heroicons/react/outline/esm/DocumentRemoveIcon.js
var React83 = __toESM(require_react(), 1);
function DocumentRemoveIcon(props, svgRef) {
  return React83.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React83.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M9 13h6m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
  }));
}
var ForwardRef83 = React83.forwardRef(DocumentRemoveIcon);
var DocumentRemoveIcon_default = ForwardRef83;

// node_modules/@heroicons/react/outline/esm/DocumentReportIcon.js
var React84 = __toESM(require_react(), 1);
function DocumentReportIcon(props, svgRef) {
  return React84.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React84.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
  }));
}
var ForwardRef84 = React84.forwardRef(DocumentReportIcon);
var DocumentReportIcon_default = ForwardRef84;

// node_modules/@heroicons/react/outline/esm/DocumentSearchIcon.js
var React85 = __toESM(require_react(), 1);
function DocumentSearchIcon(props, svgRef) {
  return React85.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React85.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M10 21h7a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v11m0 5l4.879-4.879m0 0a3 3 0 104.243-4.242 3 3 0 00-4.243 4.242z"
  }));
}
var ForwardRef85 = React85.forwardRef(DocumentSearchIcon);
var DocumentSearchIcon_default = ForwardRef85;

// node_modules/@heroicons/react/outline/esm/DocumentTextIcon.js
var React86 = __toESM(require_react(), 1);
function DocumentTextIcon(props, svgRef) {
  return React86.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React86.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
  }));
}
var ForwardRef86 = React86.forwardRef(DocumentTextIcon);
var DocumentTextIcon_default = ForwardRef86;

// node_modules/@heroicons/react/outline/esm/DocumentIcon.js
var React87 = __toESM(require_react(), 1);
function DocumentIcon(props, svgRef) {
  return React87.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React87.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
  }));
}
var ForwardRef87 = React87.forwardRef(DocumentIcon);
var DocumentIcon_default = ForwardRef87;

// node_modules/@heroicons/react/outline/esm/DotsCircleHorizontalIcon.js
var React88 = __toESM(require_react(), 1);
function DotsCircleHorizontalIcon(props, svgRef) {
  return React88.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React88.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M8 12h.01M12 12h.01M16 12h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
  }));
}
var ForwardRef88 = React88.forwardRef(DotsCircleHorizontalIcon);
var DotsCircleHorizontalIcon_default = ForwardRef88;

// node_modules/@heroicons/react/outline/esm/DotsHorizontalIcon.js
var React89 = __toESM(require_react(), 1);
function DotsHorizontalIcon(props, svgRef) {
  return React89.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React89.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z"
  }));
}
var ForwardRef89 = React89.forwardRef(DotsHorizontalIcon);
var DotsHorizontalIcon_default = ForwardRef89;

// node_modules/@heroicons/react/outline/esm/DotsVerticalIcon.js
var React90 = __toESM(require_react(), 1);
function DotsVerticalIcon(props, svgRef) {
  return React90.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React90.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
  }));
}
var ForwardRef90 = React90.forwardRef(DotsVerticalIcon);
var DotsVerticalIcon_default = ForwardRef90;

// node_modules/@heroicons/react/outline/esm/DownloadIcon.js
var React91 = __toESM(require_react(), 1);
function DownloadIcon(props, svgRef) {
  return React91.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React91.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
  }));
}
var ForwardRef91 = React91.forwardRef(DownloadIcon);
var DownloadIcon_default = ForwardRef91;

// node_modules/@heroicons/react/outline/esm/DuplicateIcon.js
var React92 = __toESM(require_react(), 1);
function DuplicateIcon(props, svgRef) {
  return React92.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React92.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
  }));
}
var ForwardRef92 = React92.forwardRef(DuplicateIcon);
var DuplicateIcon_default = ForwardRef92;

// node_modules/@heroicons/react/outline/esm/EmojiHappyIcon.js
var React93 = __toESM(require_react(), 1);
function EmojiHappyIcon(props, svgRef) {
  return React93.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React93.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
  }));
}
var ForwardRef93 = React93.forwardRef(EmojiHappyIcon);
var EmojiHappyIcon_default = ForwardRef93;

// node_modules/@heroicons/react/outline/esm/EmojiSadIcon.js
var React94 = __toESM(require_react(), 1);
function EmojiSadIcon(props, svgRef) {
  return React94.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React94.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
  }));
}
var ForwardRef94 = React94.forwardRef(EmojiSadIcon);
var EmojiSadIcon_default = ForwardRef94;

// node_modules/@heroicons/react/outline/esm/ExclamationCircleIcon.js
var React95 = __toESM(require_react(), 1);
function ExclamationCircleIcon(props, svgRef) {
  return React95.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React95.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
  }));
}
var ForwardRef95 = React95.forwardRef(ExclamationCircleIcon);
var ExclamationCircleIcon_default = ForwardRef95;

// node_modules/@heroicons/react/outline/esm/ExclamationIcon.js
var React96 = __toESM(require_react(), 1);
function ExclamationIcon(props, svgRef) {
  return React96.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React96.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
  }));
}
var ForwardRef96 = React96.forwardRef(ExclamationIcon);
var ExclamationIcon_default = ForwardRef96;

// node_modules/@heroicons/react/outline/esm/ExternalLinkIcon.js
var React97 = __toESM(require_react(), 1);
function ExternalLinkIcon(props, svgRef) {
  return React97.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React97.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
  }));
}
var ForwardRef97 = React97.forwardRef(ExternalLinkIcon);
var ExternalLinkIcon_default = ForwardRef97;

// node_modules/@heroicons/react/outline/esm/EyeOffIcon.js
var React98 = __toESM(require_react(), 1);
function EyeOffIcon(props, svgRef) {
  return React98.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React98.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21"
  }));
}
var ForwardRef98 = React98.forwardRef(EyeOffIcon);
var EyeOffIcon_default = ForwardRef98;

// node_modules/@heroicons/react/outline/esm/EyeIcon.js
var React99 = __toESM(require_react(), 1);
function EyeIcon(props, svgRef) {
  return React99.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React99.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M15 12a3 3 0 11-6 0 3 3 0 016 0z"
  }), React99.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
  }));
}
var ForwardRef99 = React99.forwardRef(EyeIcon);
var EyeIcon_default = ForwardRef99;

// node_modules/@heroicons/react/outline/esm/FastForwardIcon.js
var React100 = __toESM(require_react(), 1);
function FastForwardIcon(props, svgRef) {
  return React100.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React100.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M11.933 12.8a1 1 0 000-1.6L6.6 7.2A1 1 0 005 8v8a1 1 0 001.6.8l5.333-4zM19.933 12.8a1 1 0 000-1.6l-5.333-4A1 1 0 0013 8v8a1 1 0 001.6.8l5.333-4z"
  }));
}
var ForwardRef100 = React100.forwardRef(FastForwardIcon);
var FastForwardIcon_default = ForwardRef100;

// node_modules/@heroicons/react/outline/esm/FilmIcon.js
var React101 = __toESM(require_react(), 1);
function FilmIcon(props, svgRef) {
  return React101.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React101.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M7 4v16M17 4v16M3 8h4m10 0h4M3 12h18M3 16h4m10 0h4M4 20h16a1 1 0 001-1V5a1 1 0 00-1-1H4a1 1 0 00-1 1v14a1 1 0 001 1z"
  }));
}
var ForwardRef101 = React101.forwardRef(FilmIcon);
var FilmIcon_default = ForwardRef101;

// node_modules/@heroicons/react/outline/esm/FilterIcon.js
var React102 = __toESM(require_react(), 1);
function FilterIcon(props, svgRef) {
  return React102.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React102.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"
  }));
}
var ForwardRef102 = React102.forwardRef(FilterIcon);
var FilterIcon_default = ForwardRef102;

// node_modules/@heroicons/react/outline/esm/FingerPrintIcon.js
var React103 = __toESM(require_react(), 1);
function FingerPrintIcon(props, svgRef) {
  return React103.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React103.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M12 11c0 3.517-1.009 6.799-2.753 9.571m-3.44-2.04l.054-.09A13.916 13.916 0 008 11a4 4 0 118 0c0 1.017-.07 2.019-.203 3m-2.118 6.844A21.88 21.88 0 0015.171 17m3.839 1.132c.645-2.266.99-4.659.99-7.132A8 8 0 008 4.07M3 15.364c.64-1.319 1-2.8 1-4.364 0-1.457.39-2.823 1.07-4"
  }));
}
var ForwardRef103 = React103.forwardRef(FingerPrintIcon);
var FingerPrintIcon_default = ForwardRef103;

// node_modules/@heroicons/react/outline/esm/FireIcon.js
var React104 = __toESM(require_react(), 1);
function FireIcon(props, svgRef) {
  return React104.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React104.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z"
  }), React104.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M9.879 16.121A3 3 0 1012.015 11L11 14H9c0 .768.293 1.536.879 2.121z"
  }));
}
var ForwardRef104 = React104.forwardRef(FireIcon);
var FireIcon_default = ForwardRef104;

// node_modules/@heroicons/react/outline/esm/FlagIcon.js
var React105 = __toESM(require_react(), 1);
function FlagIcon(props, svgRef) {
  return React105.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React105.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M3 21v-4m0 0V5a2 2 0 012-2h6.5l1 1H21l-3 6 3 6h-8.5l-1-1H5a2 2 0 00-2 2zm9-13.5V9"
  }));
}
var ForwardRef105 = React105.forwardRef(FlagIcon);
var FlagIcon_default = ForwardRef105;

// node_modules/@heroicons/react/outline/esm/FolderAddIcon.js
var React106 = __toESM(require_react(), 1);
function FolderAddIcon(props, svgRef) {
  return React106.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React106.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z"
  }));
}
var ForwardRef106 = React106.forwardRef(FolderAddIcon);
var FolderAddIcon_default = ForwardRef106;

// node_modules/@heroicons/react/outline/esm/FolderDownloadIcon.js
var React107 = __toESM(require_react(), 1);
function FolderDownloadIcon(props, svgRef) {
  return React107.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React107.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M12 10v6m0 0l-3-3m3 3l3-3M3 17V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z"
  }));
}
var ForwardRef107 = React107.forwardRef(FolderDownloadIcon);
var FolderDownloadIcon_default = ForwardRef107;

// node_modules/@heroicons/react/outline/esm/FolderOpenIcon.js
var React108 = __toESM(require_react(), 1);
function FolderOpenIcon(props, svgRef) {
  return React108.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React108.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M5 19a2 2 0 01-2-2V7a2 2 0 012-2h4l2 2h4a2 2 0 012 2v1M5 19h14a2 2 0 002-2v-5a2 2 0 00-2-2H9a2 2 0 00-2 2v5a2 2 0 01-2 2z"
  }));
}
var ForwardRef108 = React108.forwardRef(FolderOpenIcon);
var FolderOpenIcon_default = ForwardRef108;

// node_modules/@heroicons/react/outline/esm/FolderRemoveIcon.js
var React109 = __toESM(require_react(), 1);
function FolderRemoveIcon(props, svgRef) {
  return React109.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React109.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M9 13h6M3 17V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z"
  }));
}
var ForwardRef109 = React109.forwardRef(FolderRemoveIcon);
var FolderRemoveIcon_default = ForwardRef109;

// node_modules/@heroicons/react/outline/esm/FolderIcon.js
var React110 = __toESM(require_react(), 1);
function FolderIcon(props, svgRef) {
  return React110.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React110.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"
  }));
}
var ForwardRef110 = React110.forwardRef(FolderIcon);
var FolderIcon_default = ForwardRef110;

// node_modules/@heroicons/react/outline/esm/GiftIcon.js
var React111 = __toESM(require_react(), 1);
function GiftIcon(props, svgRef) {
  return React111.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React111.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"
  }));
}
var ForwardRef111 = React111.forwardRef(GiftIcon);
var GiftIcon_default = ForwardRef111;

// node_modules/@heroicons/react/outline/esm/GlobeAltIcon.js
var React112 = __toESM(require_react(), 1);
function GlobeAltIcon(props, svgRef) {
  return React112.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React112.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"
  }));
}
var ForwardRef112 = React112.forwardRef(GlobeAltIcon);
var GlobeAltIcon_default = ForwardRef112;

// node_modules/@heroicons/react/outline/esm/GlobeIcon.js
var React113 = __toESM(require_react(), 1);
function GlobeIcon(props, svgRef) {
  return React113.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React113.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
  }));
}
var ForwardRef113 = React113.forwardRef(GlobeIcon);
var GlobeIcon_default = ForwardRef113;

// node_modules/@heroicons/react/outline/esm/HandIcon.js
var React114 = __toESM(require_react(), 1);
function HandIcon(props, svgRef) {
  return React114.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React114.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M7 11.5V14m0-2.5v-6a1.5 1.5 0 113 0m-3 6a1.5 1.5 0 00-3 0v2a7.5 7.5 0 0015 0v-5a1.5 1.5 0 00-3 0m-6-3V11m0-5.5v-1a1.5 1.5 0 013 0v1m0 0V11m0-5.5a1.5 1.5 0 013 0v3m0 0V11"
  }));
}
var ForwardRef114 = React114.forwardRef(HandIcon);
var HandIcon_default = ForwardRef114;

// node_modules/@heroicons/react/outline/esm/HashtagIcon.js
var React115 = __toESM(require_react(), 1);
function HashtagIcon(props, svgRef) {
  return React115.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React115.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M7 20l4-16m2 16l4-16M6 9h14M4 15h14"
  }));
}
var ForwardRef115 = React115.forwardRef(HashtagIcon);
var HashtagIcon_default = ForwardRef115;

// node_modules/@heroicons/react/outline/esm/HeartIcon.js
var React116 = __toESM(require_react(), 1);
function HeartIcon(props, svgRef) {
  return React116.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React116.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
  }));
}
var ForwardRef116 = React116.forwardRef(HeartIcon);
var HeartIcon_default = ForwardRef116;

// node_modules/@heroicons/react/outline/esm/HomeIcon.js
var React117 = __toESM(require_react(), 1);
function HomeIcon(props, svgRef) {
  return React117.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React117.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
  }));
}
var ForwardRef117 = React117.forwardRef(HomeIcon);
var HomeIcon_default = ForwardRef117;

// node_modules/@heroicons/react/outline/esm/IdentificationIcon.js
var React118 = __toESM(require_react(), 1);
function IdentificationIcon(props, svgRef) {
  return React118.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React118.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V5a2 2 0 114 0v1m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2"
  }));
}
var ForwardRef118 = React118.forwardRef(IdentificationIcon);
var IdentificationIcon_default = ForwardRef118;

// node_modules/@heroicons/react/outline/esm/InboxInIcon.js
var React119 = __toESM(require_react(), 1);
function InboxInIcon(props, svgRef) {
  return React119.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React119.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M8 4H6a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-2m-4-1v8m0 0l3-3m-3 3L9 8m-5 5h2.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293h3.172a1 1 0 00.707-.293l2.414-2.414a1 1 0 01.707-.293H20"
  }));
}
var ForwardRef119 = React119.forwardRef(InboxInIcon);
var InboxInIcon_default = ForwardRef119;

// node_modules/@heroicons/react/outline/esm/InboxIcon.js
var React120 = __toESM(require_react(), 1);
function InboxIcon(props, svgRef) {
  return React120.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React120.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"
  }));
}
var ForwardRef120 = React120.forwardRef(InboxIcon);
var InboxIcon_default = ForwardRef120;

// node_modules/@heroicons/react/outline/esm/InformationCircleIcon.js
var React121 = __toESM(require_react(), 1);
function InformationCircleIcon(props, svgRef) {
  return React121.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React121.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
  }));
}
var ForwardRef121 = React121.forwardRef(InformationCircleIcon);
var InformationCircleIcon_default = ForwardRef121;

// node_modules/@heroicons/react/outline/esm/KeyIcon.js
var React122 = __toESM(require_react(), 1);
function KeyIcon(props, svgRef) {
  return React122.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React122.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"
  }));
}
var ForwardRef122 = React122.forwardRef(KeyIcon);
var KeyIcon_default = ForwardRef122;

// node_modules/@heroicons/react/outline/esm/LibraryIcon.js
var React123 = __toESM(require_react(), 1);
function LibraryIcon(props, svgRef) {
  return React123.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React123.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z"
  }));
}
var ForwardRef123 = React123.forwardRef(LibraryIcon);
var LibraryIcon_default = ForwardRef123;

// node_modules/@heroicons/react/outline/esm/LightBulbIcon.js
var React124 = __toESM(require_react(), 1);
function LightBulbIcon(props, svgRef) {
  return React124.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React124.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
  }));
}
var ForwardRef124 = React124.forwardRef(LightBulbIcon);
var LightBulbIcon_default = ForwardRef124;

// node_modules/@heroicons/react/outline/esm/LightningBoltIcon.js
var React125 = __toESM(require_react(), 1);
function LightningBoltIcon(props, svgRef) {
  return React125.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React125.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M13 10V3L4 14h7v7l9-11h-7z"
  }));
}
var ForwardRef125 = React125.forwardRef(LightningBoltIcon);
var LightningBoltIcon_default = ForwardRef125;

// node_modules/@heroicons/react/outline/esm/LinkIcon.js
var React126 = __toESM(require_react(), 1);
function LinkIcon(props, svgRef) {
  return React126.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React126.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
  }));
}
var ForwardRef126 = React126.forwardRef(LinkIcon);
var LinkIcon_default = ForwardRef126;

// node_modules/@heroicons/react/outline/esm/LocationMarkerIcon.js
var React127 = __toESM(require_react(), 1);
function LocationMarkerIcon(props, svgRef) {
  return React127.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React127.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
  }), React127.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M15 11a3 3 0 11-6 0 3 3 0 016 0z"
  }));
}
var ForwardRef127 = React127.forwardRef(LocationMarkerIcon);
var LocationMarkerIcon_default = ForwardRef127;

// node_modules/@heroicons/react/outline/esm/LockClosedIcon.js
var React128 = __toESM(require_react(), 1);
function LockClosedIcon(props, svgRef) {
  return React128.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React128.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
  }));
}
var ForwardRef128 = React128.forwardRef(LockClosedIcon);
var LockClosedIcon_default = ForwardRef128;

// node_modules/@heroicons/react/outline/esm/LockOpenIcon.js
var React129 = __toESM(require_react(), 1);
function LockOpenIcon(props, svgRef) {
  return React129.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React129.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"
  }));
}
var ForwardRef129 = React129.forwardRef(LockOpenIcon);
var LockOpenIcon_default = ForwardRef129;

// node_modules/@heroicons/react/outline/esm/LoginIcon.js
var React130 = __toESM(require_react(), 1);
function LoginIcon(props, svgRef) {
  return React130.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React130.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"
  }));
}
var ForwardRef130 = React130.forwardRef(LoginIcon);
var LoginIcon_default = ForwardRef130;

// node_modules/@heroicons/react/outline/esm/LogoutIcon.js
var React131 = __toESM(require_react(), 1);
function LogoutIcon(props, svgRef) {
  return React131.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React131.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
  }));
}
var ForwardRef131 = React131.forwardRef(LogoutIcon);
var LogoutIcon_default = ForwardRef131;

// node_modules/@heroicons/react/outline/esm/MailOpenIcon.js
var React132 = __toESM(require_react(), 1);
function MailOpenIcon(props, svgRef) {
  return React132.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React132.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M3 19v-8.93a2 2 0 01.89-1.664l7-4.666a2 2 0 012.22 0l7 4.666A2 2 0 0121 10.07V19M3 19a2 2 0 002 2h14a2 2 0 002-2M3 19l6.75-4.5M21 19l-6.75-4.5M3 10l6.75 4.5M21 10l-6.75 4.5m0 0l-1.14.76a2 2 0 01-2.22 0l-1.14-.76"
  }));
}
var ForwardRef132 = React132.forwardRef(MailOpenIcon);
var MailOpenIcon_default = ForwardRef132;

// node_modules/@heroicons/react/outline/esm/MailIcon.js
var React133 = __toESM(require_react(), 1);
function MailIcon(props, svgRef) {
  return React133.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React133.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
  }));
}
var ForwardRef133 = React133.forwardRef(MailIcon);
var MailIcon_default = ForwardRef133;

// node_modules/@heroicons/react/outline/esm/MapIcon.js
var React134 = __toESM(require_react(), 1);
function MapIcon(props, svgRef) {
  return React134.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React134.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"
  }));
}
var ForwardRef134 = React134.forwardRef(MapIcon);
var MapIcon_default = ForwardRef134;

// node_modules/@heroicons/react/outline/esm/MenuAlt1Icon.js
var React135 = __toESM(require_react(), 1);
function MenuAlt1Icon(props, svgRef) {
  return React135.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React135.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M4 6h16M4 12h8m-8 6h16"
  }));
}
var ForwardRef135 = React135.forwardRef(MenuAlt1Icon);
var MenuAlt1Icon_default = ForwardRef135;

// node_modules/@heroicons/react/outline/esm/MenuAlt2Icon.js
var React136 = __toESM(require_react(), 1);
function MenuAlt2Icon(props, svgRef) {
  return React136.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React136.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M4 6h16M4 12h16M4 18h7"
  }));
}
var ForwardRef136 = React136.forwardRef(MenuAlt2Icon);
var MenuAlt2Icon_default = ForwardRef136;

// node_modules/@heroicons/react/outline/esm/MenuAlt3Icon.js
var React137 = __toESM(require_react(), 1);
function MenuAlt3Icon(props, svgRef) {
  return React137.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React137.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M4 6h16M4 12h16m-7 6h7"
  }));
}
var ForwardRef137 = React137.forwardRef(MenuAlt3Icon);
var MenuAlt3Icon_default = ForwardRef137;

// node_modules/@heroicons/react/outline/esm/MenuAlt4Icon.js
var React138 = __toESM(require_react(), 1);
function MenuAlt4Icon(props, svgRef) {
  return React138.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React138.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M4 8h16M4 16h16"
  }));
}
var ForwardRef138 = React138.forwardRef(MenuAlt4Icon);
var MenuAlt4Icon_default = ForwardRef138;

// node_modules/@heroicons/react/outline/esm/MenuIcon.js
var React139 = __toESM(require_react(), 1);
function MenuIcon(props, svgRef) {
  return React139.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React139.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M4 6h16M4 12h16M4 18h16"
  }));
}
var ForwardRef139 = React139.forwardRef(MenuIcon);
var MenuIcon_default = ForwardRef139;

// node_modules/@heroicons/react/outline/esm/MicrophoneIcon.js
var React140 = __toESM(require_react(), 1);
function MicrophoneIcon(props, svgRef) {
  return React140.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React140.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"
  }));
}
var ForwardRef140 = React140.forwardRef(MicrophoneIcon);
var MicrophoneIcon_default = ForwardRef140;

// node_modules/@heroicons/react/outline/esm/MinusCircleIcon.js
var React141 = __toESM(require_react(), 1);
function MinusCircleIcon(props, svgRef) {
  return React141.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React141.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M15 12H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"
  }));
}
var ForwardRef141 = React141.forwardRef(MinusCircleIcon);
var MinusCircleIcon_default = ForwardRef141;

// node_modules/@heroicons/react/outline/esm/MinusSmIcon.js
var React142 = __toESM(require_react(), 1);
function MinusSmIcon(props, svgRef) {
  return React142.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React142.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M18 12H6"
  }));
}
var ForwardRef142 = React142.forwardRef(MinusSmIcon);
var MinusSmIcon_default = ForwardRef142;

// node_modules/@heroicons/react/outline/esm/MinusIcon.js
var React143 = __toESM(require_react(), 1);
function MinusIcon(props, svgRef) {
  return React143.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React143.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M20 12H4"
  }));
}
var ForwardRef143 = React143.forwardRef(MinusIcon);
var MinusIcon_default = ForwardRef143;

// node_modules/@heroicons/react/outline/esm/MoonIcon.js
var React144 = __toESM(require_react(), 1);
function MoonIcon(props, svgRef) {
  return React144.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React144.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
  }));
}
var ForwardRef144 = React144.forwardRef(MoonIcon);
var MoonIcon_default = ForwardRef144;

// node_modules/@heroicons/react/outline/esm/MusicNoteIcon.js
var React145 = __toESM(require_react(), 1);
function MusicNoteIcon(props, svgRef) {
  return React145.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React145.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"
  }));
}
var ForwardRef145 = React145.forwardRef(MusicNoteIcon);
var MusicNoteIcon_default = ForwardRef145;

// node_modules/@heroicons/react/outline/esm/NewspaperIcon.js
var React146 = __toESM(require_react(), 1);
function NewspaperIcon(props, svgRef) {
  return React146.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React146.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"
  }));
}
var ForwardRef146 = React146.forwardRef(NewspaperIcon);
var NewspaperIcon_default = ForwardRef146;

// node_modules/@heroicons/react/outline/esm/OfficeBuildingIcon.js
var React147 = __toESM(require_react(), 1);
function OfficeBuildingIcon(props, svgRef) {
  return React147.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React147.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
  }));
}
var ForwardRef147 = React147.forwardRef(OfficeBuildingIcon);
var OfficeBuildingIcon_default = ForwardRef147;

// node_modules/@heroicons/react/outline/esm/PaperAirplaneIcon.js
var React148 = __toESM(require_react(), 1);
function PaperAirplaneIcon(props, svgRef) {
  return React148.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React148.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
  }));
}
var ForwardRef148 = React148.forwardRef(PaperAirplaneIcon);
var PaperAirplaneIcon_default = ForwardRef148;

// node_modules/@heroicons/react/outline/esm/PaperClipIcon.js
var React149 = __toESM(require_react(), 1);
function PaperClipIcon(props, svgRef) {
  return React149.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React149.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"
  }));
}
var ForwardRef149 = React149.forwardRef(PaperClipIcon);
var PaperClipIcon_default = ForwardRef149;

// node_modules/@heroicons/react/outline/esm/PauseIcon.js
var React150 = __toESM(require_react(), 1);
function PauseIcon(props, svgRef) {
  return React150.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React150.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"
  }));
}
var ForwardRef150 = React150.forwardRef(PauseIcon);
var PauseIcon_default = ForwardRef150;

// node_modules/@heroicons/react/outline/esm/PencilAltIcon.js
var React151 = __toESM(require_react(), 1);
function PencilAltIcon(props, svgRef) {
  return React151.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React151.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
  }));
}
var ForwardRef151 = React151.forwardRef(PencilAltIcon);
var PencilAltIcon_default = ForwardRef151;

// node_modules/@heroicons/react/outline/esm/PencilIcon.js
var React152 = __toESM(require_react(), 1);
function PencilIcon(props, svgRef) {
  return React152.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React152.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
  }));
}
var ForwardRef152 = React152.forwardRef(PencilIcon);
var PencilIcon_default = ForwardRef152;

// node_modules/@heroicons/react/outline/esm/PhoneIncomingIcon.js
var React153 = __toESM(require_react(), 1);
function PhoneIncomingIcon(props, svgRef) {
  return React153.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React153.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M21 3l-6 6m0 0V4m0 5h5M5 3a2 2 0 00-2 2v1c0 8.284 6.716 15 15 15h1a2 2 0 002-2v-3.28a1 1 0 00-.684-.948l-4.493-1.498a1 1 0 00-1.21.502l-1.13 2.257a11.042 11.042 0 01-5.516-5.517l2.257-1.128a1 1 0 00.502-1.21L9.228 3.683A1 1 0 008.279 3H5z"
  }));
}
var ForwardRef153 = React153.forwardRef(PhoneIncomingIcon);
var PhoneIncomingIcon_default = ForwardRef153;

// node_modules/@heroicons/react/outline/esm/PhoneMissedCallIcon.js
var React154 = __toESM(require_react(), 1);
function PhoneMissedCallIcon(props, svgRef) {
  return React154.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React154.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M16 8l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2M5 3a2 2 0 00-2 2v1c0 8.284 6.716 15 15 15h1a2 2 0 002-2v-3.28a1 1 0 00-.684-.948l-4.493-1.498a1 1 0 00-1.21.502l-1.13 2.257a11.042 11.042 0 01-5.516-5.517l2.257-1.128a1 1 0 00.502-1.21L9.228 3.683A1 1 0 008.279 3H5z"
  }));
}
var ForwardRef154 = React154.forwardRef(PhoneMissedCallIcon);
var PhoneMissedCallIcon_default = ForwardRef154;

// node_modules/@heroicons/react/outline/esm/PhoneOutgoingIcon.js
var React155 = __toESM(require_react(), 1);
function PhoneOutgoingIcon(props, svgRef) {
  return React155.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React155.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M16 3h5m0 0v5m0-5l-6 6M5 3a2 2 0 00-2 2v1c0 8.284 6.716 15 15 15h1a2 2 0 002-2v-3.28a1 1 0 00-.684-.948l-4.493-1.498a1 1 0 00-1.21.502l-1.13 2.257a11.042 11.042 0 01-5.516-5.517l2.257-1.128a1 1 0 00.502-1.21L9.228 3.683A1 1 0 008.279 3H5z"
  }));
}
var ForwardRef155 = React155.forwardRef(PhoneOutgoingIcon);
var PhoneOutgoingIcon_default = ForwardRef155;

// node_modules/@heroicons/react/outline/esm/PhoneIcon.js
var React156 = __toESM(require_react(), 1);
function PhoneIcon(props, svgRef) {
  return React156.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React156.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
  }));
}
var ForwardRef156 = React156.forwardRef(PhoneIcon);
var PhoneIcon_default = ForwardRef156;

// node_modules/@heroicons/react/outline/esm/PhotographIcon.js
var React157 = __toESM(require_react(), 1);
function PhotographIcon(props, svgRef) {
  return React157.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React157.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
  }));
}
var ForwardRef157 = React157.forwardRef(PhotographIcon);
var PhotographIcon_default = ForwardRef157;

// node_modules/@heroicons/react/outline/esm/PlayIcon.js
var React158 = __toESM(require_react(), 1);
function PlayIcon(props, svgRef) {
  return React158.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React158.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"
  }), React158.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
  }));
}
var ForwardRef158 = React158.forwardRef(PlayIcon);
var PlayIcon_default = ForwardRef158;

// node_modules/@heroicons/react/outline/esm/PlusCircleIcon.js
var React159 = __toESM(require_react(), 1);
function PlusCircleIcon(props, svgRef) {
  return React159.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React159.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"
  }));
}
var ForwardRef159 = React159.forwardRef(PlusCircleIcon);
var PlusCircleIcon_default = ForwardRef159;

// node_modules/@heroicons/react/outline/esm/PlusSmIcon.js
var React160 = __toESM(require_react(), 1);
function PlusSmIcon(props, svgRef) {
  return React160.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React160.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M12 6v6m0 0v6m0-6h6m-6 0H6"
  }));
}
var ForwardRef160 = React160.forwardRef(PlusSmIcon);
var PlusSmIcon_default = ForwardRef160;

// node_modules/@heroicons/react/outline/esm/PlusIcon.js
var React161 = __toESM(require_react(), 1);
function PlusIcon(props, svgRef) {
  return React161.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React161.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M12 4v16m8-8H4"
  }));
}
var ForwardRef161 = React161.forwardRef(PlusIcon);
var PlusIcon_default = ForwardRef161;

// node_modules/@heroicons/react/outline/esm/PresentationChartBarIcon.js
var React162 = __toESM(require_react(), 1);
function PresentationChartBarIcon(props, svgRef) {
  return React162.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React162.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M8 13v-1m4 1v-3m4 3V8M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"
  }));
}
var ForwardRef162 = React162.forwardRef(PresentationChartBarIcon);
var PresentationChartBarIcon_default = ForwardRef162;

// node_modules/@heroicons/react/outline/esm/PresentationChartLineIcon.js
var React163 = __toESM(require_react(), 1);
function PresentationChartLineIcon(props, svgRef) {
  return React163.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React163.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"
  }));
}
var ForwardRef163 = React163.forwardRef(PresentationChartLineIcon);
var PresentationChartLineIcon_default = ForwardRef163;

// node_modules/@heroicons/react/outline/esm/PrinterIcon.js
var React164 = __toESM(require_react(), 1);
function PrinterIcon(props, svgRef) {
  return React164.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React164.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"
  }));
}
var ForwardRef164 = React164.forwardRef(PrinterIcon);
var PrinterIcon_default = ForwardRef164;

// node_modules/@heroicons/react/outline/esm/PuzzleIcon.js
var React165 = __toESM(require_react(), 1);
function PuzzleIcon(props, svgRef) {
  return React165.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React165.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z"
  }));
}
var ForwardRef165 = React165.forwardRef(PuzzleIcon);
var PuzzleIcon_default = ForwardRef165;

// node_modules/@heroicons/react/outline/esm/QrcodeIcon.js
var React166 = __toESM(require_react(), 1);
function QrcodeIcon(props, svgRef) {
  return React166.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React166.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"
  }));
}
var ForwardRef166 = React166.forwardRef(QrcodeIcon);
var QrcodeIcon_default = ForwardRef166;

// node_modules/@heroicons/react/outline/esm/QuestionMarkCircleIcon.js
var React167 = __toESM(require_react(), 1);
function QuestionMarkCircleIcon(props, svgRef) {
  return React167.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React167.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
  }));
}
var ForwardRef167 = React167.forwardRef(QuestionMarkCircleIcon);
var QuestionMarkCircleIcon_default = ForwardRef167;

// node_modules/@heroicons/react/outline/esm/ReceiptRefundIcon.js
var React168 = __toESM(require_react(), 1);
function ReceiptRefundIcon(props, svgRef) {
  return React168.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React168.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"
  }));
}
var ForwardRef168 = React168.forwardRef(ReceiptRefundIcon);
var ReceiptRefundIcon_default = ForwardRef168;

// node_modules/@heroicons/react/outline/esm/ReceiptTaxIcon.js
var React169 = __toESM(require_react(), 1);
function ReceiptTaxIcon(props, svgRef) {
  return React169.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React169.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M9 14l6-6m-5.5.5h.01m4.99 5h.01M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16l3.5-2 3.5 2 3.5-2 3.5 2zM10 8.5a.5.5 0 11-1 0 .5.5 0 011 0zm5 5a.5.5 0 11-1 0 .5.5 0 011 0z"
  }));
}
var ForwardRef169 = React169.forwardRef(ReceiptTaxIcon);
var ReceiptTaxIcon_default = ForwardRef169;

// node_modules/@heroicons/react/outline/esm/RefreshIcon.js
var React170 = __toESM(require_react(), 1);
function RefreshIcon(props, svgRef) {
  return React170.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React170.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
  }));
}
var ForwardRef170 = React170.forwardRef(RefreshIcon);
var RefreshIcon_default = ForwardRef170;

// node_modules/@heroicons/react/outline/esm/ReplyIcon.js
var React171 = __toESM(require_react(), 1);
function ReplyIcon(props, svgRef) {
  return React171.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React171.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"
  }));
}
var ForwardRef171 = React171.forwardRef(ReplyIcon);
var ReplyIcon_default = ForwardRef171;

// node_modules/@heroicons/react/outline/esm/RewindIcon.js
var React172 = __toESM(require_react(), 1);
function RewindIcon(props, svgRef) {
  return React172.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React172.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M12.066 11.2a1 1 0 000 1.6l5.334 4A1 1 0 0019 16V8a1 1 0 00-1.6-.8l-5.333 4zM4.066 11.2a1 1 0 000 1.6l5.334 4A1 1 0 0011 16V8a1 1 0 00-1.6-.8l-5.334 4z"
  }));
}
var ForwardRef172 = React172.forwardRef(RewindIcon);
var RewindIcon_default = ForwardRef172;

// node_modules/@heroicons/react/outline/esm/RssIcon.js
var React173 = __toESM(require_react(), 1);
function RssIcon(props, svgRef) {
  return React173.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React173.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M6 5c7.18 0 13 5.82 13 13M6 11a7 7 0 017 7m-6 0a1 1 0 11-2 0 1 1 0 012 0z"
  }));
}
var ForwardRef173 = React173.forwardRef(RssIcon);
var RssIcon_default = ForwardRef173;

// node_modules/@heroicons/react/outline/esm/SaveAsIcon.js
var React174 = __toESM(require_react(), 1);
function SaveAsIcon(props, svgRef) {
  return React174.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React174.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M17 16v2a2 2 0 01-2 2H5a2 2 0 01-2-2v-7a2 2 0 012-2h2m3-4H9a2 2 0 00-2 2v7a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-1m-1 4l-3 3m0 0l-3-3m3 3V3"
  }));
}
var ForwardRef174 = React174.forwardRef(SaveAsIcon);
var SaveAsIcon_default = ForwardRef174;

// node_modules/@heroicons/react/outline/esm/SaveIcon.js
var React175 = __toESM(require_react(), 1);
function SaveIcon(props, svgRef) {
  return React175.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React175.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"
  }));
}
var ForwardRef175 = React175.forwardRef(SaveIcon);
var SaveIcon_default = ForwardRef175;

// node_modules/@heroicons/react/outline/esm/ScaleIcon.js
var React176 = __toESM(require_react(), 1);
function ScaleIcon(props, svgRef) {
  return React176.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React176.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3"
  }));
}
var ForwardRef176 = React176.forwardRef(ScaleIcon);
var ScaleIcon_default = ForwardRef176;

// node_modules/@heroicons/react/outline/esm/ScissorsIcon.js
var React177 = __toESM(require_react(), 1);
function ScissorsIcon(props, svgRef) {
  return React177.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React177.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M14.121 14.121L19 19m-7-7l7-7m-7 7l-2.879 2.879M12 12L9.121 9.121m0 5.758a3 3 0 10-4.243 4.243 3 3 0 004.243-4.243zm0-5.758a3 3 0 10-4.243-4.243 3 3 0 004.243 4.243z"
  }));
}
var ForwardRef177 = React177.forwardRef(ScissorsIcon);
var ScissorsIcon_default = ForwardRef177;

// node_modules/@heroicons/react/outline/esm/SearchCircleIcon.js
var React178 = __toESM(require_react(), 1);
function SearchCircleIcon(props, svgRef) {
  return React178.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React178.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M8 16l2.879-2.879m0 0a3 3 0 104.243-4.242 3 3 0 00-4.243 4.242zM21 12a9 9 0 11-18 0 9 9 0 0118 0z"
  }));
}
var ForwardRef178 = React178.forwardRef(SearchCircleIcon);
var SearchCircleIcon_default = ForwardRef178;

// node_modules/@heroicons/react/outline/esm/SearchIcon.js
var React179 = __toESM(require_react(), 1);
function SearchIcon(props, svgRef) {
  return React179.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React179.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
  }));
}
var ForwardRef179 = React179.forwardRef(SearchIcon);
var SearchIcon_default = ForwardRef179;

// node_modules/@heroicons/react/outline/esm/SelectorIcon.js
var React180 = __toESM(require_react(), 1);
function SelectorIcon(props, svgRef) {
  return React180.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React180.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M8 9l4-4 4 4m0 6l-4 4-4-4"
  }));
}
var ForwardRef180 = React180.forwardRef(SelectorIcon);
var SelectorIcon_default = ForwardRef180;

// node_modules/@heroicons/react/outline/esm/ServerIcon.js
var React181 = __toESM(require_react(), 1);
function ServerIcon(props, svgRef) {
  return React181.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React181.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01"
  }));
}
var ForwardRef181 = React181.forwardRef(ServerIcon);
var ServerIcon_default = ForwardRef181;

// node_modules/@heroicons/react/outline/esm/ShareIcon.js
var React182 = __toESM(require_react(), 1);
function ShareIcon(props, svgRef) {
  return React182.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React182.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"
  }));
}
var ForwardRef182 = React182.forwardRef(ShareIcon);
var ShareIcon_default = ForwardRef182;

// node_modules/@heroicons/react/outline/esm/ShieldCheckIcon.js
var React183 = __toESM(require_react(), 1);
function ShieldCheckIcon(props, svgRef) {
  return React183.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React183.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
  }));
}
var ForwardRef183 = React183.forwardRef(ShieldCheckIcon);
var ShieldCheckIcon_default = ForwardRef183;

// node_modules/@heroicons/react/outline/esm/ShieldExclamationIcon.js
var React184 = __toESM(require_react(), 1);
function ShieldExclamationIcon(props, svgRef) {
  return React184.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React184.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M20.618 5.984A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016zM12 9v2m0 4h.01"
  }));
}
var ForwardRef184 = React184.forwardRef(ShieldExclamationIcon);
var ShieldExclamationIcon_default = ForwardRef184;

// node_modules/@heroicons/react/outline/esm/ShoppingBagIcon.js
var React185 = __toESM(require_react(), 1);
function ShoppingBagIcon(props, svgRef) {
  return React185.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React185.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
  }));
}
var ForwardRef185 = React185.forwardRef(ShoppingBagIcon);
var ShoppingBagIcon_default = ForwardRef185;

// node_modules/@heroicons/react/outline/esm/ShoppingCartIcon.js
var React186 = __toESM(require_react(), 1);
function ShoppingCartIcon(props, svgRef) {
  return React186.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React186.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"
  }));
}
var ForwardRef186 = React186.forwardRef(ShoppingCartIcon);
var ShoppingCartIcon_default = ForwardRef186;

// node_modules/@heroicons/react/outline/esm/SortAscendingIcon.js
var React187 = __toESM(require_react(), 1);
function SortAscendingIcon(props, svgRef) {
  return React187.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React187.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12"
  }));
}
var ForwardRef187 = React187.forwardRef(SortAscendingIcon);
var SortAscendingIcon_default = ForwardRef187;

// node_modules/@heroicons/react/outline/esm/SortDescendingIcon.js
var React188 = __toESM(require_react(), 1);
function SortDescendingIcon(props, svgRef) {
  return React188.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React188.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M3 4h13M3 8h9m-9 4h9m5-4v12m0 0l-4-4m4 4l4-4"
  }));
}
var ForwardRef188 = React188.forwardRef(SortDescendingIcon);
var SortDescendingIcon_default = ForwardRef188;

// node_modules/@heroicons/react/outline/esm/SparklesIcon.js
var React189 = __toESM(require_react(), 1);
function SparklesIcon(props, svgRef) {
  return React189.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React189.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"
  }));
}
var ForwardRef189 = React189.forwardRef(SparklesIcon);
var SparklesIcon_default = ForwardRef189;

// node_modules/@heroicons/react/outline/esm/SpeakerphoneIcon.js
var React190 = __toESM(require_react(), 1);
function SpeakerphoneIcon(props, svgRef) {
  return React190.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React190.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"
  }));
}
var ForwardRef190 = React190.forwardRef(SpeakerphoneIcon);
var SpeakerphoneIcon_default = ForwardRef190;

// node_modules/@heroicons/react/outline/esm/StarIcon.js
var React191 = __toESM(require_react(), 1);
function StarIcon(props, svgRef) {
  return React191.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React191.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
  }));
}
var ForwardRef191 = React191.forwardRef(StarIcon);
var StarIcon_default = ForwardRef191;

// node_modules/@heroicons/react/outline/esm/StatusOfflineIcon.js
var React192 = __toESM(require_react(), 1);
function StatusOfflineIcon(props, svgRef) {
  return React192.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React192.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M18.364 5.636a9 9 0 010 12.728m0 0l-2.829-2.829m2.829 2.829L21 21M15.536 8.464a5 5 0 010 7.072m0 0l-2.829-2.829m-4.243 2.829a4.978 4.978 0 01-1.414-2.83m-1.414 5.658a9 9 0 01-2.167-9.238m7.824 2.167a1 1 0 111.414 1.414m-1.414-1.414L3 3m8.293 8.293l1.414 1.414"
  }));
}
var ForwardRef192 = React192.forwardRef(StatusOfflineIcon);
var StatusOfflineIcon_default = ForwardRef192;

// node_modules/@heroicons/react/outline/esm/StatusOnlineIcon.js
var React193 = __toESM(require_react(), 1);
function StatusOnlineIcon(props, svgRef) {
  return React193.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React193.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M5.636 18.364a9 9 0 010-12.728m12.728 0a9 9 0 010 12.728m-9.9-2.829a5 5 0 010-7.07m7.072 0a5 5 0 010 7.07M13 12a1 1 0 11-2 0 1 1 0 012 0z"
  }));
}
var ForwardRef193 = React193.forwardRef(StatusOnlineIcon);
var StatusOnlineIcon_default = ForwardRef193;

// node_modules/@heroicons/react/outline/esm/StopIcon.js
var React194 = __toESM(require_react(), 1);
function StopIcon(props, svgRef) {
  return React194.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React194.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
  }), React194.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z"
  }));
}
var ForwardRef194 = React194.forwardRef(StopIcon);
var StopIcon_default = ForwardRef194;

// node_modules/@heroicons/react/outline/esm/SunIcon.js
var React195 = __toESM(require_react(), 1);
function SunIcon(props, svgRef) {
  return React195.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React195.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
  }));
}
var ForwardRef195 = React195.forwardRef(SunIcon);
var SunIcon_default = ForwardRef195;

// node_modules/@heroicons/react/outline/esm/SupportIcon.js
var React196 = __toESM(require_react(), 1);
function SupportIcon(props, svgRef) {
  return React196.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React196.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192l-3.536 3.536M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"
  }));
}
var ForwardRef196 = React196.forwardRef(SupportIcon);
var SupportIcon_default = ForwardRef196;

// node_modules/@heroicons/react/outline/esm/SwitchHorizontalIcon.js
var React197 = __toESM(require_react(), 1);
function SwitchHorizontalIcon(props, svgRef) {
  return React197.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React197.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"
  }));
}
var ForwardRef197 = React197.forwardRef(SwitchHorizontalIcon);
var SwitchHorizontalIcon_default = ForwardRef197;

// node_modules/@heroicons/react/outline/esm/SwitchVerticalIcon.js
var React198 = __toESM(require_react(), 1);
function SwitchVerticalIcon(props, svgRef) {
  return React198.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React198.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"
  }));
}
var ForwardRef198 = React198.forwardRef(SwitchVerticalIcon);
var SwitchVerticalIcon_default = ForwardRef198;

// node_modules/@heroicons/react/outline/esm/TableIcon.js
var React199 = __toESM(require_react(), 1);
function TableIcon(props, svgRef) {
  return React199.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React199.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
  }));
}
var ForwardRef199 = React199.forwardRef(TableIcon);
var TableIcon_default = ForwardRef199;

// node_modules/@heroicons/react/outline/esm/TagIcon.js
var React200 = __toESM(require_react(), 1);
function TagIcon(props, svgRef) {
  return React200.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React200.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
  }));
}
var ForwardRef200 = React200.forwardRef(TagIcon);
var TagIcon_default = ForwardRef200;

// node_modules/@heroicons/react/outline/esm/TemplateIcon.js
var React201 = __toESM(require_react(), 1);
function TemplateIcon(props, svgRef) {
  return React201.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React201.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"
  }));
}
var ForwardRef201 = React201.forwardRef(TemplateIcon);
var TemplateIcon_default = ForwardRef201;

// node_modules/@heroicons/react/outline/esm/TerminalIcon.js
var React202 = __toESM(require_react(), 1);
function TerminalIcon(props, svgRef) {
  return React202.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React202.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
  }));
}
var ForwardRef202 = React202.forwardRef(TerminalIcon);
var TerminalIcon_default = ForwardRef202;

// node_modules/@heroicons/react/outline/esm/ThumbDownIcon.js
var React203 = __toESM(require_react(), 1);
function ThumbDownIcon(props, svgRef) {
  return React203.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React203.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M10 14H5.236a2 2 0 01-1.789-2.894l3.5-7A2 2 0 018.736 3h4.018a2 2 0 01.485.06l3.76.94m-7 10v5a2 2 0 002 2h.096c.5 0 .905-.405.905-.904 0-.715.211-1.413.608-2.008L17 13V4m-7 10h2m5-10h2a2 2 0 012 2v6a2 2 0 01-2 2h-2.5"
  }));
}
var ForwardRef203 = React203.forwardRef(ThumbDownIcon);
var ThumbDownIcon_default = ForwardRef203;

// node_modules/@heroicons/react/outline/esm/ThumbUpIcon.js
var React204 = __toESM(require_react(), 1);
function ThumbUpIcon(props, svgRef) {
  return React204.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React204.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"
  }));
}
var ForwardRef204 = React204.forwardRef(ThumbUpIcon);
var ThumbUpIcon_default = ForwardRef204;

// node_modules/@heroicons/react/outline/esm/TicketIcon.js
var React205 = __toESM(require_react(), 1);
function TicketIcon(props, svgRef) {
  return React205.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React205.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"
  }));
}
var ForwardRef205 = React205.forwardRef(TicketIcon);
var TicketIcon_default = ForwardRef205;

// node_modules/@heroicons/react/outline/esm/TranslateIcon.js
var React206 = __toESM(require_react(), 1);
function TranslateIcon(props, svgRef) {
  return React206.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React206.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"
  }));
}
var ForwardRef206 = React206.forwardRef(TranslateIcon);
var TranslateIcon_default = ForwardRef206;

// node_modules/@heroicons/react/outline/esm/TrashIcon.js
var React207 = __toESM(require_react(), 1);
function TrashIcon(props, svgRef) {
  return React207.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React207.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
  }));
}
var ForwardRef207 = React207.forwardRef(TrashIcon);
var TrashIcon_default = ForwardRef207;

// node_modules/@heroicons/react/outline/esm/TrendingDownIcon.js
var React208 = __toESM(require_react(), 1);
function TrendingDownIcon(props, svgRef) {
  return React208.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React208.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"
  }));
}
var ForwardRef208 = React208.forwardRef(TrendingDownIcon);
var TrendingDownIcon_default = ForwardRef208;

// node_modules/@heroicons/react/outline/esm/TrendingUpIcon.js
var React209 = __toESM(require_react(), 1);
function TrendingUpIcon(props, svgRef) {
  return React209.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React209.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
  }));
}
var ForwardRef209 = React209.forwardRef(TrendingUpIcon);
var TrendingUpIcon_default = ForwardRef209;

// node_modules/@heroicons/react/outline/esm/TruckIcon.js
var React210 = __toESM(require_react(), 1);
function TruckIcon(props, svgRef) {
  return React210.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React210.createElement("path", {
    d: "M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z"
  }), React210.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l3.414 3.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0m-4 0a2 2 0 114 0m6 0a2 2 0 104 0m-4 0a2 2 0 114 0"
  }));
}
var ForwardRef210 = React210.forwardRef(TruckIcon);
var TruckIcon_default = ForwardRef210;

// node_modules/@heroicons/react/outline/esm/UploadIcon.js
var React211 = __toESM(require_react(), 1);
function UploadIcon(props, svgRef) {
  return React211.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React211.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"
  }));
}
var ForwardRef211 = React211.forwardRef(UploadIcon);
var UploadIcon_default = ForwardRef211;

// node_modules/@heroicons/react/outline/esm/UserAddIcon.js
var React212 = __toESM(require_react(), 1);
function UserAddIcon(props, svgRef) {
  return React212.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React212.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"
  }));
}
var ForwardRef212 = React212.forwardRef(UserAddIcon);
var UserAddIcon_default = ForwardRef212;

// node_modules/@heroicons/react/outline/esm/UserCircleIcon.js
var React213 = __toESM(require_react(), 1);
function UserCircleIcon(props, svgRef) {
  return React213.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React213.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"
  }));
}
var ForwardRef213 = React213.forwardRef(UserCircleIcon);
var UserCircleIcon_default = ForwardRef213;

// node_modules/@heroicons/react/outline/esm/UserGroupIcon.js
var React214 = __toESM(require_react(), 1);
function UserGroupIcon(props, svgRef) {
  return React214.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React214.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
  }));
}
var ForwardRef214 = React214.forwardRef(UserGroupIcon);
var UserGroupIcon_default = ForwardRef214;

// node_modules/@heroicons/react/outline/esm/UserRemoveIcon.js
var React215 = __toESM(require_react(), 1);
function UserRemoveIcon(props, svgRef) {
  return React215.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React215.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M13 7a4 4 0 11-8 0 4 4 0 018 0zM9 14a6 6 0 00-6 6v1h12v-1a6 6 0 00-6-6zM21 12h-6"
  }));
}
var ForwardRef215 = React215.forwardRef(UserRemoveIcon);
var UserRemoveIcon_default = ForwardRef215;

// node_modules/@heroicons/react/outline/esm/UserIcon.js
var React216 = __toESM(require_react(), 1);
function UserIcon(props, svgRef) {
  return React216.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React216.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
  }));
}
var ForwardRef216 = React216.forwardRef(UserIcon);
var UserIcon_default = ForwardRef216;

// node_modules/@heroicons/react/outline/esm/UsersIcon.js
var React217 = __toESM(require_react(), 1);
function UsersIcon(props, svgRef) {
  return React217.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React217.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
  }));
}
var ForwardRef217 = React217.forwardRef(UsersIcon);
var UsersIcon_default = ForwardRef217;

// node_modules/@heroicons/react/outline/esm/VariableIcon.js
var React218 = __toESM(require_react(), 1);
function VariableIcon(props, svgRef) {
  return React218.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React218.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M4.871 4A17.926 17.926 0 003 12c0 2.874.673 5.59 1.871 8m14.13 0a17.926 17.926 0 001.87-8c0-2.874-.673-5.59-1.87-8M9 9h1.246a1 1 0 01.961.725l1.586 5.55a1 1 0 00.961.725H15m1-7h-.08a2 2 0 00-1.519.698L9.6 15.302A2 2 0 018.08 16H8"
  }));
}
var ForwardRef218 = React218.forwardRef(VariableIcon);
var VariableIcon_default = ForwardRef218;

// node_modules/@heroicons/react/outline/esm/VideoCameraIcon.js
var React219 = __toESM(require_react(), 1);
function VideoCameraIcon(props, svgRef) {
  return React219.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React219.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
  }));
}
var ForwardRef219 = React219.forwardRef(VideoCameraIcon);
var VideoCameraIcon_default = ForwardRef219;

// node_modules/@heroicons/react/outline/esm/ViewBoardsIcon.js
var React220 = __toESM(require_react(), 1);
function ViewBoardsIcon(props, svgRef) {
  return React220.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React220.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2"
  }));
}
var ForwardRef220 = React220.forwardRef(ViewBoardsIcon);
var ViewBoardsIcon_default = ForwardRef220;

// node_modules/@heroicons/react/outline/esm/ViewGridAddIcon.js
var React221 = __toESM(require_react(), 1);
function ViewGridAddIcon(props, svgRef) {
  return React221.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React221.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M17 14v6m-3-3h6M6 10h2a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v2a2 2 0 002 2zm10 0h2a2 2 0 002-2V6a2 2 0 00-2-2h-2a2 2 0 00-2 2v2a2 2 0 002 2zM6 20h2a2 2 0 002-2v-2a2 2 0 00-2-2H6a2 2 0 00-2 2v2a2 2 0 002 2z"
  }));
}
var ForwardRef221 = React221.forwardRef(ViewGridAddIcon);
var ViewGridAddIcon_default = ForwardRef221;

// node_modules/@heroicons/react/outline/esm/ViewGridIcon.js
var React222 = __toESM(require_react(), 1);
function ViewGridIcon(props, svgRef) {
  return React222.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React222.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
  }));
}
var ForwardRef222 = React222.forwardRef(ViewGridIcon);
var ViewGridIcon_default = ForwardRef222;

// node_modules/@heroicons/react/outline/esm/ViewListIcon.js
var React223 = __toESM(require_react(), 1);
function ViewListIcon(props, svgRef) {
  return React223.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React223.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M4 6h16M4 10h16M4 14h16M4 18h16"
  }));
}
var ForwardRef223 = React223.forwardRef(ViewListIcon);
var ViewListIcon_default = ForwardRef223;

// node_modules/@heroicons/react/outline/esm/VolumeOffIcon.js
var React224 = __toESM(require_react(), 1);
function VolumeOffIcon(props, svgRef) {
  return React224.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React224.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z",
    clipRule: "evenodd"
  }), React224.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M17 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2"
  }));
}
var ForwardRef224 = React224.forwardRef(VolumeOffIcon);
var VolumeOffIcon_default = ForwardRef224;

// node_modules/@heroicons/react/outline/esm/VolumeUpIcon.js
var React225 = __toESM(require_react(), 1);
function VolumeUpIcon(props, svgRef) {
  return React225.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React225.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z"
  }));
}
var ForwardRef225 = React225.forwardRef(VolumeUpIcon);
var VolumeUpIcon_default = ForwardRef225;

// node_modules/@heroicons/react/outline/esm/WifiIcon.js
var React226 = __toESM(require_react(), 1);
function WifiIcon(props, svgRef) {
  return React226.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React226.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0"
  }));
}
var ForwardRef226 = React226.forwardRef(WifiIcon);
var WifiIcon_default = ForwardRef226;

// node_modules/@heroicons/react/outline/esm/XCircleIcon.js
var React227 = __toESM(require_react(), 1);
function XCircleIcon(props, svgRef) {
  return React227.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React227.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
  }));
}
var ForwardRef227 = React227.forwardRef(XCircleIcon);
var XCircleIcon_default = ForwardRef227;

// node_modules/@heroicons/react/outline/esm/XIcon.js
var React228 = __toESM(require_react(), 1);
function XIcon(props, svgRef) {
  return React228.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React228.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M6 18L18 6M6 6l12 12"
  }));
}
var ForwardRef228 = React228.forwardRef(XIcon);
var XIcon_default = ForwardRef228;

// node_modules/@heroicons/react/outline/esm/ZoomInIcon.js
var React229 = __toESM(require_react(), 1);
function ZoomInIcon(props, svgRef) {
  return React229.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React229.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"
  }));
}
var ForwardRef229 = React229.forwardRef(ZoomInIcon);
var ZoomInIcon_default = ForwardRef229;

// node_modules/@heroicons/react/outline/esm/ZoomOutIcon.js
var React230 = __toESM(require_react(), 1);
function ZoomOutIcon(props, svgRef) {
  return React230.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: 2,
    stroke: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React230.createElement("path", {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    d: "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM13 10H7"
  }));
}
var ForwardRef230 = React230.forwardRef(ZoomOutIcon);
var ZoomOutIcon_default = ForwardRef230;
export {
  AcademicCapIcon_default as AcademicCapIcon,
  AdjustmentsIcon_default as AdjustmentsIcon,
  AnnotationIcon_default as AnnotationIcon,
  ArchiveIcon_default as ArchiveIcon,
  ArrowCircleDownIcon_default as ArrowCircleDownIcon,
  ArrowCircleLeftIcon_default as ArrowCircleLeftIcon,
  ArrowCircleRightIcon_default as ArrowCircleRightIcon,
  ArrowCircleUpIcon_default as ArrowCircleUpIcon,
  ArrowDownIcon_default as ArrowDownIcon,
  ArrowLeftIcon_default as ArrowLeftIcon,
  ArrowNarrowDownIcon_default as ArrowNarrowDownIcon,
  ArrowNarrowLeftIcon_default as ArrowNarrowLeftIcon,
  ArrowNarrowRightIcon_default as ArrowNarrowRightIcon,
  ArrowNarrowUpIcon_default as ArrowNarrowUpIcon,
  ArrowRightIcon_default as ArrowRightIcon,
  ArrowSmDownIcon_default as ArrowSmDownIcon,
  ArrowSmLeftIcon_default as ArrowSmLeftIcon,
  ArrowSmRightIcon_default as ArrowSmRightIcon,
  ArrowSmUpIcon_default as ArrowSmUpIcon,
  ArrowUpIcon_default as ArrowUpIcon,
  ArrowsExpandIcon_default as ArrowsExpandIcon,
  AtSymbolIcon_default as AtSymbolIcon,
  BackspaceIcon_default as BackspaceIcon,
  BadgeCheckIcon_default as BadgeCheckIcon,
  BanIcon_default as BanIcon,
  BeakerIcon_default as BeakerIcon,
  BellIcon_default as BellIcon,
  BookOpenIcon_default as BookOpenIcon,
  BookmarkAltIcon_default as BookmarkAltIcon,
  BookmarkIcon_default as BookmarkIcon,
  BriefcaseIcon_default as BriefcaseIcon,
  CakeIcon_default as CakeIcon,
  CalculatorIcon_default as CalculatorIcon,
  CalendarIcon_default as CalendarIcon,
  CameraIcon_default as CameraIcon,
  CashIcon_default as CashIcon,
  ChartBarIcon_default as ChartBarIcon,
  ChartPieIcon_default as ChartPieIcon,
  ChartSquareBarIcon_default as ChartSquareBarIcon,
  ChatAlt2Icon_default as ChatAlt2Icon,
  ChatAltIcon_default as ChatAltIcon,
  ChatIcon_default as ChatIcon,
  CheckCircleIcon_default as CheckCircleIcon,
  CheckIcon_default as CheckIcon,
  ChevronDoubleDownIcon_default as ChevronDoubleDownIcon,
  ChevronDoubleLeftIcon_default as ChevronDoubleLeftIcon,
  ChevronDoubleRightIcon_default as ChevronDoubleRightIcon,
  ChevronDoubleUpIcon_default as ChevronDoubleUpIcon,
  ChevronDownIcon_default as ChevronDownIcon,
  ChevronLeftIcon_default as ChevronLeftIcon,
  ChevronRightIcon_default as ChevronRightIcon,
  ChevronUpIcon_default as ChevronUpIcon,
  ChipIcon_default as ChipIcon,
  ClipboardCheckIcon_default as ClipboardCheckIcon,
  ClipboardCopyIcon_default as ClipboardCopyIcon,
  ClipboardIcon_default as ClipboardIcon,
  ClipboardListIcon_default as ClipboardListIcon,
  ClockIcon_default as ClockIcon,
  CloudDownloadIcon_default as CloudDownloadIcon,
  CloudIcon_default as CloudIcon,
  CloudUploadIcon_default as CloudUploadIcon,
  CodeIcon_default as CodeIcon,
  CogIcon_default as CogIcon,
  CollectionIcon_default as CollectionIcon,
  ColorSwatchIcon_default as ColorSwatchIcon,
  CreditCardIcon_default as CreditCardIcon,
  CubeIcon_default as CubeIcon,
  CubeTransparentIcon_default as CubeTransparentIcon,
  CurrencyBangladeshiIcon_default as CurrencyBangladeshiIcon,
  CurrencyDollarIcon_default as CurrencyDollarIcon,
  CurrencyEuroIcon_default as CurrencyEuroIcon,
  CurrencyPoundIcon_default as CurrencyPoundIcon,
  CurrencyRupeeIcon_default as CurrencyRupeeIcon,
  CurrencyYenIcon_default as CurrencyYenIcon,
  CursorClickIcon_default as CursorClickIcon,
  DatabaseIcon_default as DatabaseIcon,
  DesktopComputerIcon_default as DesktopComputerIcon,
  DeviceMobileIcon_default as DeviceMobileIcon,
  DeviceTabletIcon_default as DeviceTabletIcon,
  DocumentAddIcon_default as DocumentAddIcon,
  DocumentDownloadIcon_default as DocumentDownloadIcon,
  DocumentDuplicateIcon_default as DocumentDuplicateIcon,
  DocumentIcon_default as DocumentIcon,
  DocumentRemoveIcon_default as DocumentRemoveIcon,
  DocumentReportIcon_default as DocumentReportIcon,
  DocumentSearchIcon_default as DocumentSearchIcon,
  DocumentTextIcon_default as DocumentTextIcon,
  DotsCircleHorizontalIcon_default as DotsCircleHorizontalIcon,
  DotsHorizontalIcon_default as DotsHorizontalIcon,
  DotsVerticalIcon_default as DotsVerticalIcon,
  DownloadIcon_default as DownloadIcon,
  DuplicateIcon_default as DuplicateIcon,
  EmojiHappyIcon_default as EmojiHappyIcon,
  EmojiSadIcon_default as EmojiSadIcon,
  ExclamationCircleIcon_default as ExclamationCircleIcon,
  ExclamationIcon_default as ExclamationIcon,
  ExternalLinkIcon_default as ExternalLinkIcon,
  EyeIcon_default as EyeIcon,
  EyeOffIcon_default as EyeOffIcon,
  FastForwardIcon_default as FastForwardIcon,
  FilmIcon_default as FilmIcon,
  FilterIcon_default as FilterIcon,
  FingerPrintIcon_default as FingerPrintIcon,
  FireIcon_default as FireIcon,
  FlagIcon_default as FlagIcon,
  FolderAddIcon_default as FolderAddIcon,
  FolderDownloadIcon_default as FolderDownloadIcon,
  FolderIcon_default as FolderIcon,
  FolderOpenIcon_default as FolderOpenIcon,
  FolderRemoveIcon_default as FolderRemoveIcon,
  GiftIcon_default as GiftIcon,
  GlobeAltIcon_default as GlobeAltIcon,
  GlobeIcon_default as GlobeIcon,
  HandIcon_default as HandIcon,
  HashtagIcon_default as HashtagIcon,
  HeartIcon_default as HeartIcon,
  HomeIcon_default as HomeIcon,
  IdentificationIcon_default as IdentificationIcon,
  InboxIcon_default as InboxIcon,
  InboxInIcon_default as InboxInIcon,
  InformationCircleIcon_default as InformationCircleIcon,
  KeyIcon_default as KeyIcon,
  LibraryIcon_default as LibraryIcon,
  LightBulbIcon_default as LightBulbIcon,
  LightningBoltIcon_default as LightningBoltIcon,
  LinkIcon_default as LinkIcon,
  LocationMarkerIcon_default as LocationMarkerIcon,
  LockClosedIcon_default as LockClosedIcon,
  LockOpenIcon_default as LockOpenIcon,
  LoginIcon_default as LoginIcon,
  LogoutIcon_default as LogoutIcon,
  MailIcon_default as MailIcon,
  MailOpenIcon_default as MailOpenIcon,
  MapIcon_default as MapIcon,
  MenuAlt1Icon_default as MenuAlt1Icon,
  MenuAlt2Icon_default as MenuAlt2Icon,
  MenuAlt3Icon_default as MenuAlt3Icon,
  MenuAlt4Icon_default as MenuAlt4Icon,
  MenuIcon_default as MenuIcon,
  MicrophoneIcon_default as MicrophoneIcon,
  MinusCircleIcon_default as MinusCircleIcon,
  MinusIcon_default as MinusIcon,
  MinusSmIcon_default as MinusSmIcon,
  MoonIcon_default as MoonIcon,
  MusicNoteIcon_default as MusicNoteIcon,
  NewspaperIcon_default as NewspaperIcon,
  OfficeBuildingIcon_default as OfficeBuildingIcon,
  PaperAirplaneIcon_default as PaperAirplaneIcon,
  PaperClipIcon_default as PaperClipIcon,
  PauseIcon_default as PauseIcon,
  PencilAltIcon_default as PencilAltIcon,
  PencilIcon_default as PencilIcon,
  PhoneIcon_default as PhoneIcon,
  PhoneIncomingIcon_default as PhoneIncomingIcon,
  PhoneMissedCallIcon_default as PhoneMissedCallIcon,
  PhoneOutgoingIcon_default as PhoneOutgoingIcon,
  PhotographIcon_default as PhotographIcon,
  PlayIcon_default as PlayIcon,
  PlusCircleIcon_default as PlusCircleIcon,
  PlusIcon_default as PlusIcon,
  PlusSmIcon_default as PlusSmIcon,
  PresentationChartBarIcon_default as PresentationChartBarIcon,
  PresentationChartLineIcon_default as PresentationChartLineIcon,
  PrinterIcon_default as PrinterIcon,
  PuzzleIcon_default as PuzzleIcon,
  QrcodeIcon_default as QrcodeIcon,
  QuestionMarkCircleIcon_default as QuestionMarkCircleIcon,
  ReceiptRefundIcon_default as ReceiptRefundIcon,
  ReceiptTaxIcon_default as ReceiptTaxIcon,
  RefreshIcon_default as RefreshIcon,
  ReplyIcon_default as ReplyIcon,
  RewindIcon_default as RewindIcon,
  RssIcon_default as RssIcon,
  SaveAsIcon_default as SaveAsIcon,
  SaveIcon_default as SaveIcon,
  ScaleIcon_default as ScaleIcon,
  ScissorsIcon_default as ScissorsIcon,
  SearchCircleIcon_default as SearchCircleIcon,
  SearchIcon_default as SearchIcon,
  SelectorIcon_default as SelectorIcon,
  ServerIcon_default as ServerIcon,
  ShareIcon_default as ShareIcon,
  ShieldCheckIcon_default as ShieldCheckIcon,
  ShieldExclamationIcon_default as ShieldExclamationIcon,
  ShoppingBagIcon_default as ShoppingBagIcon,
  ShoppingCartIcon_default as ShoppingCartIcon,
  SortAscendingIcon_default as SortAscendingIcon,
  SortDescendingIcon_default as SortDescendingIcon,
  SparklesIcon_default as SparklesIcon,
  SpeakerphoneIcon_default as SpeakerphoneIcon,
  StarIcon_default as StarIcon,
  StatusOfflineIcon_default as StatusOfflineIcon,
  StatusOnlineIcon_default as StatusOnlineIcon,
  StopIcon_default as StopIcon,
  SunIcon_default as SunIcon,
  SupportIcon_default as SupportIcon,
  SwitchHorizontalIcon_default as SwitchHorizontalIcon,
  SwitchVerticalIcon_default as SwitchVerticalIcon,
  TableIcon_default as TableIcon,
  TagIcon_default as TagIcon,
  TemplateIcon_default as TemplateIcon,
  TerminalIcon_default as TerminalIcon,
  ThumbDownIcon_default as ThumbDownIcon,
  ThumbUpIcon_default as ThumbUpIcon,
  TicketIcon_default as TicketIcon,
  TranslateIcon_default as TranslateIcon,
  TrashIcon_default as TrashIcon,
  TrendingDownIcon_default as TrendingDownIcon,
  TrendingUpIcon_default as TrendingUpIcon,
  TruckIcon_default as TruckIcon,
  UploadIcon_default as UploadIcon,
  UserAddIcon_default as UserAddIcon,
  UserCircleIcon_default as UserCircleIcon,
  UserGroupIcon_default as UserGroupIcon,
  UserIcon_default as UserIcon,
  UserRemoveIcon_default as UserRemoveIcon,
  UsersIcon_default as UsersIcon,
  VariableIcon_default as VariableIcon,
  VideoCameraIcon_default as VideoCameraIcon,
  ViewBoardsIcon_default as ViewBoardsIcon,
  ViewGridAddIcon_default as ViewGridAddIcon,
  ViewGridIcon_default as ViewGridIcon,
  ViewListIcon_default as ViewListIcon,
  VolumeOffIcon_default as VolumeOffIcon,
  VolumeUpIcon_default as VolumeUpIcon,
  WifiIcon_default as WifiIcon,
  XCircleIcon_default as XCircleIcon,
  XIcon_default as XIcon,
  ZoomInIcon_default as ZoomInIcon,
  ZoomOutIcon_default as ZoomOutIcon
};
//# sourceMappingURL=@heroicons_react_outline.js.map
