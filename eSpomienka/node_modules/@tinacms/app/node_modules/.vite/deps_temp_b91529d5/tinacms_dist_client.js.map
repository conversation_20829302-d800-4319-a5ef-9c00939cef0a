{"version": 3, "sources": ["../../../../../async-lock/lib/index.js", "../../../../../async-lock/index.js", "../../tinacms/dist/client.mjs"], "sourcesContent": ["'use strict';\n\nvar AsyncLock = function (opts) {\n\topts = opts || {};\n\n\tthis.Promise = opts.Promise || Promise;\n\n\t// format: {key : [fn, fn]}\n\t// queues[key] = null indicates no job running for key\n\tthis.queues = Object.create(null);\n\n\t// lock is reentrant for same domain\n\tthis.domainReentrant = opts.domainReentrant || false;\n\tif (this.domainReentrant) {\n\t\tif (typeof process === 'undefined' || typeof process.domain === 'undefined') {\n\t\t\tthrow new Error(\n\t\t\t\t'Domain-reentrant locks require `process.domain` to exist. Please flip `opts.domainReentrant = false`, ' +\n\t\t\t\t'use a NodeJS version that still implements Domain, or install a browser polyfill.');\n\t\t}\n\t\t// domain of current running func {key : fn}\n\t\tthis.domains = Object.create(null);\n\t}\n\n\tthis.timeout = opts.timeout || AsyncLock.DEFAULT_TIMEOUT;\n\tthis.maxOccupationTime = opts.maxOccupationTime || AsyncLock.DEFAULT_MAX_OCCUPATION_TIME;\n\tthis.maxExecutionTime = opts.maxExecutionTime || AsyncLock.DEFAULT_MAX_EXECUTION_TIME;\n\tif (opts.maxPending === Infinity || (Number.isInteger(opts.maxPending) && opts.maxPending >= 0)) {\n\t\tthis.maxPending = opts.maxPending;\n\t} else {\n\t\tthis.maxPending = AsyncLock.DEFAULT_MAX_PENDING;\n\t}\n};\n\nAsyncLock.DEFAULT_TIMEOUT = 0; //Never\nAsyncLock.DEFAULT_MAX_OCCUPATION_TIME = 0; //Never\nAsyncLock.DEFAULT_MAX_EXECUTION_TIME = 0; //Never\nAsyncLock.DEFAULT_MAX_PENDING = 1000;\n\n/**\n * Acquire Locks\n *\n * @param {String|Array} key \tresource key or keys to lock\n * @param {function} fn \tasync function\n * @param {function} cb \tcallback function, otherwise will return a promise\n * @param {Object} opts \toptions\n */\nAsyncLock.prototype.acquire = function (key, fn, cb, opts) {\n\tif (Array.isArray(key)) {\n\t\treturn this._acquireBatch(key, fn, cb, opts);\n\t}\n\n\tif (typeof (fn) !== 'function') {\n\t\tthrow new Error('You must pass a function to execute');\n\t}\n\n\t// faux-deferred promise using new Promise() (as Promise.defer is deprecated)\n\tvar deferredResolve = null;\n\tvar deferredReject = null;\n\tvar deferred = null;\n\n\tif (typeof (cb) !== 'function') {\n\t\topts = cb;\n\t\tcb = null;\n\n\t\t// will return a promise\n\t\tdeferred = new this.Promise(function(resolve, reject) {\n\t\t\tdeferredResolve = resolve;\n\t\t\tdeferredReject = reject;\n\t\t});\n\t}\n\n\topts = opts || {};\n\n\tvar resolved = false;\n\tvar timer = null;\n\tvar occupationTimer = null;\n\tvar executionTimer = null;\n\tvar self = this;\n\n\tvar done = function (locked, err, ret) {\n\n\t\tif (occupationTimer) {\n\t\t\tclearTimeout(occupationTimer);\n\t\t\toccupationTimer = null;\n\t\t}\n\n\t\tif (executionTimer) {\n\t\t\tclearTimeout(executionTimer);\n\t\t\texecutionTimer = null;\n\t\t}\n\n\t\tif (locked) {\n\t\t\tif (!!self.queues[key] && self.queues[key].length === 0) {\n\t\t\t\tdelete self.queues[key];\n\t\t\t}\n\t\t\tif (self.domainReentrant) {\n\t\t\t\tdelete self.domains[key];\n\t\t\t}\n\t\t}\n\n\t\tif (!resolved) {\n\t\t\tif (!deferred) {\n\t\t\t\tif (typeof (cb) === 'function') {\n\t\t\t\t\tcb(err, ret);\n\t\t\t\t}\n\t\t\t}\n\t\t\telse {\n\t\t\t\t//promise mode\n\t\t\t\tif (err) {\n\t\t\t\t\tdeferredReject(err);\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tdeferredResolve(ret);\n\t\t\t\t}\n\t\t\t}\n\t\t\tresolved = true;\n\t\t}\n\n\t\tif (locked) {\n\t\t\t//run next func\n\t\t\tif (!!self.queues[key] && self.queues[key].length > 0) {\n\t\t\t\tself.queues[key].shift()();\n\t\t\t}\n\t\t}\n\t};\n\n\tvar exec = function (locked) {\n\t\tif (resolved) { // may due to timed out\n\t\t\treturn done(locked);\n\t\t}\n\n\t\tif (timer) {\n\t\t\tclearTimeout(timer);\n\t\t\ttimer = null;\n\t\t}\n\n\t\tif (self.domainReentrant && locked) {\n\t\t\tself.domains[key] = process.domain;\n\t\t}\n\n\t\tvar maxExecutionTime = opts.maxExecutionTime || self.maxExecutionTime;\n\t\tif (maxExecutionTime) {\n\t\t\texecutionTimer = setTimeout(function () {\n\t\t\t\tif (!!self.queues[key]) {\n\t\t\t\t\tdone(locked, new Error('Maximum execution time is exceeded ' + key));\n\t\t\t\t}\n\t\t\t}, maxExecutionTime);\n\t\t}\n\n\t\t// Callback mode\n\t\tif (fn.length === 1) {\n\t\t\tvar called = false;\n\t\t\ttry {\n\t\t\t\tfn(function (err, ret) {\n\t\t\t\t\tif (!called) {\n\t\t\t\t\t\tcalled = true;\n\t\t\t\t\t\tdone(locked, err, ret);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t} catch (err) {\n\t\t\t\t// catching error thrown in user function fn\n\t\t\t\tif (!called) {\n\t\t\t\t\tcalled = true;\n\t\t\t\t\tdone(locked, err);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\telse {\n\t\t\t// Promise mode\n\t\t\tself._promiseTry(function () {\n\t\t\t\treturn fn();\n\t\t\t})\n\t\t\t.then(function(ret){\n\t\t\t\tdone(locked, undefined, ret);\n\t\t\t}, function(error){\n\t\t\t\tdone(locked, error);\n\t\t\t});\n\t\t}\n\t};\n\n\tif (self.domainReentrant && !!process.domain) {\n\t\texec = process.domain.bind(exec);\n\t}\n\n\tvar maxPending = opts.maxPending || self.maxPending;\n\n\tif (!self.queues[key]) {\n\t\tself.queues[key] = [];\n\t\texec(true);\n\t}\n\telse if (self.domainReentrant && !!process.domain && process.domain === self.domains[key]) {\n\t\t// If code is in the same domain of current running task, run it directly\n\t\t// Since lock is re-enterable\n\t\texec(false);\n\t}\n\telse if (self.queues[key].length >= maxPending) {\n\t\tdone(false, new Error('Too many pending tasks in queue ' + key));\n\t}\n\telse {\n\t\tvar taskFn = function () {\n\t\t\texec(true);\n\t\t};\n\t\tif (opts.skipQueue) {\n\t\t\tself.queues[key].unshift(taskFn);\n\t\t} else {\n\t\t\tself.queues[key].push(taskFn);\n\t\t}\n\n\t\tvar timeout = opts.timeout || self.timeout;\n\t\tif (timeout) {\n\t\t\ttimer = setTimeout(function () {\n\t\t\t\ttimer = null;\n\t\t\t\tdone(false, new Error('async-lock timed out in queue ' + key));\n\t\t\t}, timeout);\n\t\t}\n\t}\n\n\tvar maxOccupationTime = opts.maxOccupationTime || self.maxOccupationTime;\n\t\tif (maxOccupationTime) {\n\t\t\toccupationTimer = setTimeout(function () {\n\t\t\t\tif (!!self.queues[key]) {\n\t\t\t\t\tdone(false, new Error('Maximum occupation time is exceeded in queue ' + key));\n\t\t\t\t}\n\t\t\t}, maxOccupationTime);\n\t\t}\n\n\tif (deferred) {\n\t\treturn deferred;\n\t}\n};\n\n/*\n * Below is how this function works:\n *\n * Equivalent code:\n * self.acquire(key1, function(cb){\n *     self.acquire(key2, function(cb){\n *         self.acquire(key3, fn, cb);\n *     }, cb);\n * }, cb);\n *\n * Equivalent code:\n * var fn3 = getFn(key3, fn);\n * var fn2 = getFn(key2, fn3);\n * var fn1 = getFn(key1, fn2);\n * fn1(cb);\n */\nAsyncLock.prototype._acquireBatch = function (keys, fn, cb, opts) {\n\tif (typeof (cb) !== 'function') {\n\t\topts = cb;\n\t\tcb = null;\n\t}\n\n\tvar self = this;\n\tvar getFn = function (key, fn) {\n\t\treturn function (cb) {\n\t\t\tself.acquire(key, fn, cb, opts);\n\t\t};\n\t};\n\n\tvar fnx = keys.reduceRight(function (prev, key) {\n\t\treturn getFn(key, prev);\n\t}, fn);\n\n\tif (typeof (cb) === 'function') {\n\t\tfnx(cb);\n\t}\n\telse {\n\t\treturn new this.Promise(function (resolve, reject) {\n\t\t\t// check for promise mode in case keys is empty array\n\t\t\tif (fnx.length === 1) {\n\t\t\t\tfnx(function (err, ret) {\n\t\t\t\t\tif (err) {\n\t\t\t\t\t\treject(err);\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tresolve(ret);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tresolve(fnx());\n\t\t\t}\n\t\t});\n\t}\n};\n\n/*\n *\tWhether there is any running or pending asyncFunc\n *\n *\t@param {String} key\n */\nAsyncLock.prototype.isBusy = function (key) {\n\tif (!key) {\n\t\treturn Object.keys(this.queues).length > 0;\n\t}\n\telse {\n\t\treturn !!this.queues[key];\n\t}\n};\n\n/**\n * Promise.try() implementation to become independent of Q-specific methods\n */\nAsyncLock.prototype._promiseTry = function(fn) {\n\ttry {\n\t\treturn this.Promise.resolve(fn());\n\t} catch (e) {\n\t\treturn this.Promise.reject(e);\n\t}\n};\n\nmodule.exports = AsyncLock;\n", "'use strict';\nmodule.exports = require('./lib');\n", "import AsyncLock from \"async-lock\";\nconst TINA_HOST = \"content.tinajs.io\";\nfunction replaceGithubPathSplit(url, replacement) {\n  const parts = url.split(\"github/\");\n  if (parts.length > 1 && replacement) {\n    return parts[0] + \"github/\" + replacement;\n  } else {\n    return url;\n  }\n}\nclass TinaClient {\n  constructor({\n    token,\n    url,\n    queries,\n    errorPolicy,\n    cacheDir\n  }) {\n    this.initialized = false;\n    this.apiUrl = url;\n    this.readonlyToken = token == null ? void 0 : token.trim();\n    this.queries = queries(this);\n    this.errorPolicy = errorPolicy || \"throw\";\n    this.cacheDir = cacheDir || \"\";\n  }\n  async init() {\n    if (this.initialized) {\n      return;\n    }\n    try {\n      if (this.cacheDir && typeof window === \"undefined\" && typeof require !== \"undefined\") {\n        const { NodeCache } = await import(\"./node-cache-5e8db9f0.mjs\");\n        this.cache = await NodeCache(this.cacheDir);\n        this.cacheLock = new AsyncLock();\n      }\n    } catch (e) {\n      console.error(e);\n    }\n    this.initialized = true;\n  }\n  async request({ errorPolicy, ...args }, options) {\n    var _a;\n    await this.init();\n    const errorPolicyDefined = errorPolicy || this.errorPolicy;\n    const headers = new Headers();\n    if (this.readonlyToken) {\n      headers.append(\"X-API-KEY\", this.readonlyToken);\n    }\n    headers.append(\"Content-Type\", \"application/json\");\n    if (options == null ? void 0 : options.fetchOptions) {\n      if ((_a = options == null ? void 0 : options.fetchOptions) == null ? void 0 : _a.headers) {\n        Object.entries(options.fetchOptions.headers).forEach(([key2, value]) => {\n          headers.append(key2, value);\n        });\n      }\n    }\n    const { headers: _, ...providedFetchOptions } = (options == null ? void 0 : options.fetchOptions) || {};\n    const bodyString = JSON.stringify({\n      query: args.query,\n      variables: (args == null ? void 0 : args.variables) || {}\n    });\n    const optionsObject = {\n      method: \"POST\",\n      headers,\n      body: bodyString,\n      redirect: \"follow\",\n      ...providedFetchOptions\n    };\n    const draftBranch = headers.get(\"x-branch\");\n    const url = replaceGithubPathSplit((args == null ? void 0 : args.url) || this.apiUrl, draftBranch);\n    let key = \"\";\n    let result;\n    if (this.cache) {\n      key = this.cache.makeKey(bodyString);\n      await this.cacheLock.acquire(key, async () => {\n        result = await this.cache.get(key);\n        if (!result) {\n          result = await requestFromServer(\n            url,\n            args.query,\n            optionsObject,\n            errorPolicyDefined\n          );\n          await this.cache.set(key, result);\n        }\n      });\n    } else {\n      result = await requestFromServer(\n        url,\n        args.query,\n        optionsObject,\n        errorPolicyDefined\n      );\n    }\n    return result;\n  }\n}\nasync function requestFromServer(url, query, optionsObject, errorPolicyDefined) {\n  const res = await fetch(url, optionsObject);\n  if (!res.ok) {\n    let additionalInfo = \"\";\n    if (res.status === 401) {\n      additionalInfo = \"Please check that your client ID, URL and read only token are configured properly.\";\n    }\n    throw new Error(\n      `Server responded with status code ${res.status}, ${res.statusText}. ${additionalInfo ? additionalInfo : \"\"} Please see our FAQ for more information: https://tina.io/docs/errors/faq/`\n    );\n  }\n  const json = await res.json();\n  if (json.errors && errorPolicyDefined === \"throw\") {\n    throw new Error(\n      `Unable to fetch, please see our FAQ for more information: https://tina.io/docs/errors/faq/\n      Errors: \n\t${json.errors.map((error) => error.message).join(\"\\n\")}`\n    );\n  }\n  const result = {\n    data: json == null ? void 0 : json.data,\n    errors: (json == null ? void 0 : json.errors) || null,\n    query\n  };\n  return result;\n}\nfunction createClient(args) {\n  const client = new TinaClient(args);\n  return client;\n}\nexport {\n  TINA_HOST,\n  TinaClient,\n  createClient\n};\n"], "mappings": ";;;;;;;AAAA;AAAA;AAAA;AAEA,QAAIA,aAAY,SAAU,MAAM;AAC/B,aAAO,QAAQ,CAAC;AAEhB,WAAK,UAAU,KAAK,WAAW;AAI/B,WAAK,SAAS,uBAAO,OAAO,IAAI;AAGhC,WAAK,kBAAkB,KAAK,mBAAmB;AAC/C,UAAI,KAAK,iBAAiB;AACzB,YAAI,OAAO,YAAY,eAAe,OAAO,QAAQ,WAAW,aAAa;AAC5E,gBAAM,IAAI;AAAA,YACT;AAAA,UACmF;AAAA,QACrF;AAEA,aAAK,UAAU,uBAAO,OAAO,IAAI;AAAA,MAClC;AAEA,WAAK,UAAU,KAAK,WAAWA,WAAU;AACzC,WAAK,oBAAoB,KAAK,qBAAqBA,WAAU;AAC7D,WAAK,mBAAmB,KAAK,oBAAoBA,WAAU;AAC3D,UAAI,KAAK,eAAe,YAAa,OAAO,UAAU,KAAK,UAAU,KAAK,KAAK,cAAc,GAAI;AAChG,aAAK,aAAa,KAAK;AAAA,MACxB,OAAO;AACN,aAAK,aAAaA,WAAU;AAAA,MAC7B;AAAA,IACD;AAEA,IAAAA,WAAU,kBAAkB;AAC5B,IAAAA,WAAU,8BAA8B;AACxC,IAAAA,WAAU,6BAA6B;AACvC,IAAAA,WAAU,sBAAsB;AAUhC,IAAAA,WAAU,UAAU,UAAU,SAAU,KAAK,IAAI,IAAI,MAAM;AAC1D,UAAI,MAAM,QAAQ,GAAG,GAAG;AACvB,eAAO,KAAK,cAAc,KAAK,IAAI,IAAI,IAAI;AAAA,MAC5C;AAEA,UAAI,OAAQ,OAAQ,YAAY;AAC/B,cAAM,IAAI,MAAM,qCAAqC;AAAA,MACtD;AAGA,UAAI,kBAAkB;AACtB,UAAI,iBAAiB;AACrB,UAAI,WAAW;AAEf,UAAI,OAAQ,OAAQ,YAAY;AAC/B,eAAO;AACP,aAAK;AAGL,mBAAW,IAAI,KAAK,QAAQ,SAAS,SAAS,QAAQ;AACrD,4BAAkB;AAClB,2BAAiB;AAAA,QAClB,CAAC;AAAA,MACF;AAEA,aAAO,QAAQ,CAAC;AAEhB,UAAI,WAAW;AACf,UAAI,QAAQ;AACZ,UAAI,kBAAkB;AACtB,UAAI,iBAAiB;AACrB,UAAI,OAAO;AAEX,UAAI,OAAO,SAAU,QAAQ,KAAK,KAAK;AAEtC,YAAI,iBAAiB;AACpB,uBAAa,eAAe;AAC5B,4BAAkB;AAAA,QACnB;AAEA,YAAI,gBAAgB;AACnB,uBAAa,cAAc;AAC3B,2BAAiB;AAAA,QAClB;AAEA,YAAI,QAAQ;AACX,cAAI,CAAC,CAAC,KAAK,OAAO,GAAG,KAAK,KAAK,OAAO,GAAG,EAAE,WAAW,GAAG;AACxD,mBAAO,KAAK,OAAO,GAAG;AAAA,UACvB;AACA,cAAI,KAAK,iBAAiB;AACzB,mBAAO,KAAK,QAAQ,GAAG;AAAA,UACxB;AAAA,QACD;AAEA,YAAI,CAAC,UAAU;AACd,cAAI,CAAC,UAAU;AACd,gBAAI,OAAQ,OAAQ,YAAY;AAC/B,iBAAG,KAAK,GAAG;AAAA,YACZ;AAAA,UACD,OACK;AAEJ,gBAAI,KAAK;AACR,6BAAe,GAAG;AAAA,YACnB,OACK;AACJ,8BAAgB,GAAG;AAAA,YACpB;AAAA,UACD;AACA,qBAAW;AAAA,QACZ;AAEA,YAAI,QAAQ;AAEX,cAAI,CAAC,CAAC,KAAK,OAAO,GAAG,KAAK,KAAK,OAAO,GAAG,EAAE,SAAS,GAAG;AACtD,iBAAK,OAAO,GAAG,EAAE,MAAM,EAAE;AAAA,UAC1B;AAAA,QACD;AAAA,MACD;AAEA,UAAI,OAAO,SAAU,QAAQ;AAC5B,YAAI,UAAU;AACb,iBAAO,KAAK,MAAM;AAAA,QACnB;AAEA,YAAI,OAAO;AACV,uBAAa,KAAK;AAClB,kBAAQ;AAAA,QACT;AAEA,YAAI,KAAK,mBAAmB,QAAQ;AACnC,eAAK,QAAQ,GAAG,IAAI,QAAQ;AAAA,QAC7B;AAEA,YAAI,mBAAmB,KAAK,oBAAoB,KAAK;AACrD,YAAI,kBAAkB;AACrB,2BAAiB,WAAW,WAAY;AACvC,gBAAI,CAAC,CAAC,KAAK,OAAO,GAAG,GAAG;AACvB,mBAAK,QAAQ,IAAI,MAAM,wCAAwC,GAAG,CAAC;AAAA,YACpE;AAAA,UACD,GAAG,gBAAgB;AAAA,QACpB;AAGA,YAAI,GAAG,WAAW,GAAG;AACpB,cAAI,SAAS;AACb,cAAI;AACH,eAAG,SAAU,KAAK,KAAK;AACtB,kBAAI,CAAC,QAAQ;AACZ,yBAAS;AACT,qBAAK,QAAQ,KAAK,GAAG;AAAA,cACtB;AAAA,YACD,CAAC;AAAA,UACF,SAAS,KAAK;AAEb,gBAAI,CAAC,QAAQ;AACZ,uBAAS;AACT,mBAAK,QAAQ,GAAG;AAAA,YACjB;AAAA,UACD;AAAA,QACD,OACK;AAEJ,eAAK,YAAY,WAAY;AAC5B,mBAAO,GAAG;AAAA,UACX,CAAC,EACA,KAAK,SAAS,KAAI;AAClB,iBAAK,QAAQ,QAAW,GAAG;AAAA,UAC5B,GAAG,SAAS,OAAM;AACjB,iBAAK,QAAQ,KAAK;AAAA,UACnB,CAAC;AAAA,QACF;AAAA,MACD;AAEA,UAAI,KAAK,mBAAmB,CAAC,CAAC,QAAQ,QAAQ;AAC7C,eAAO,QAAQ,OAAO,KAAK,IAAI;AAAA,MAChC;AAEA,UAAI,aAAa,KAAK,cAAc,KAAK;AAEzC,UAAI,CAAC,KAAK,OAAO,GAAG,GAAG;AACtB,aAAK,OAAO,GAAG,IAAI,CAAC;AACpB,aAAK,IAAI;AAAA,MACV,WACS,KAAK,mBAAmB,CAAC,CAAC,QAAQ,UAAU,QAAQ,WAAW,KAAK,QAAQ,GAAG,GAAG;AAG1F,aAAK,KAAK;AAAA,MACX,WACS,KAAK,OAAO,GAAG,EAAE,UAAU,YAAY;AAC/C,aAAK,OAAO,IAAI,MAAM,qCAAqC,GAAG,CAAC;AAAA,MAChE,OACK;AACJ,YAAI,SAAS,WAAY;AACxB,eAAK,IAAI;AAAA,QACV;AACA,YAAI,KAAK,WAAW;AACnB,eAAK,OAAO,GAAG,EAAE,QAAQ,MAAM;AAAA,QAChC,OAAO;AACN,eAAK,OAAO,GAAG,EAAE,KAAK,MAAM;AAAA,QAC7B;AAEA,YAAI,UAAU,KAAK,WAAW,KAAK;AACnC,YAAI,SAAS;AACZ,kBAAQ,WAAW,WAAY;AAC9B,oBAAQ;AACR,iBAAK,OAAO,IAAI,MAAM,mCAAmC,GAAG,CAAC;AAAA,UAC9D,GAAG,OAAO;AAAA,QACX;AAAA,MACD;AAEA,UAAI,oBAAoB,KAAK,qBAAqB,KAAK;AACtD,UAAI,mBAAmB;AACtB,0BAAkB,WAAW,WAAY;AACxC,cAAI,CAAC,CAAC,KAAK,OAAO,GAAG,GAAG;AACvB,iBAAK,OAAO,IAAI,MAAM,kDAAkD,GAAG,CAAC;AAAA,UAC7E;AAAA,QACD,GAAG,iBAAiB;AAAA,MACrB;AAED,UAAI,UAAU;AACb,eAAO;AAAA,MACR;AAAA,IACD;AAkBA,IAAAA,WAAU,UAAU,gBAAgB,SAAU,MAAM,IAAI,IAAI,MAAM;AACjE,UAAI,OAAQ,OAAQ,YAAY;AAC/B,eAAO;AACP,aAAK;AAAA,MACN;AAEA,UAAI,OAAO;AACX,UAAI,QAAQ,SAAU,KAAKC,KAAI;AAC9B,eAAO,SAAUC,KAAI;AACpB,eAAK,QAAQ,KAAKD,KAAIC,KAAI,IAAI;AAAA,QAC/B;AAAA,MACD;AAEA,UAAI,MAAM,KAAK,YAAY,SAAU,MAAM,KAAK;AAC/C,eAAO,MAAM,KAAK,IAAI;AAAA,MACvB,GAAG,EAAE;AAEL,UAAI,OAAQ,OAAQ,YAAY;AAC/B,YAAI,EAAE;AAAA,MACP,OACK;AACJ,eAAO,IAAI,KAAK,QAAQ,SAAU,SAAS,QAAQ;AAElD,cAAI,IAAI,WAAW,GAAG;AACrB,gBAAI,SAAU,KAAK,KAAK;AACvB,kBAAI,KAAK;AACR,uBAAO,GAAG;AAAA,cACX,OACK;AACJ,wBAAQ,GAAG;AAAA,cACZ;AAAA,YACD,CAAC;AAAA,UACF,OAAO;AACN,oBAAQ,IAAI,CAAC;AAAA,UACd;AAAA,QACD,CAAC;AAAA,MACF;AAAA,IACD;AAOA,IAAAF,WAAU,UAAU,SAAS,SAAU,KAAK;AAC3C,UAAI,CAAC,KAAK;AACT,eAAO,OAAO,KAAK,KAAK,MAAM,EAAE,SAAS;AAAA,MAC1C,OACK;AACJ,eAAO,CAAC,CAAC,KAAK,OAAO,GAAG;AAAA,MACzB;AAAA,IACD;AAKA,IAAAA,WAAU,UAAU,cAAc,SAAS,IAAI;AAC9C,UAAI;AACH,eAAO,KAAK,QAAQ,QAAQ,GAAG,CAAC;AAAA,MACjC,SAAS,GAAG;AACX,eAAO,KAAK,QAAQ,OAAO,CAAC;AAAA,MAC7B;AAAA,IACD;AAEA,WAAO,UAAUA;AAAA;AAAA;;;ACvTjB;AAAA;AAAA;AACA,WAAO,UAAU;AAAA;AAAA;;;ACDjB,wBAAsB;AACtB,IAAM,YAAY;AAClB,SAAS,uBAAuB,KAAK,aAAa;AAChD,QAAM,QAAQ,IAAI,MAAM,SAAS;AACjC,MAAI,MAAM,SAAS,KAAK,aAAa;AACnC,WAAO,MAAM,CAAC,IAAI,YAAY;AAAA,EAChC,OAAO;AACL,WAAO;AAAA,EACT;AACF;AACA,IAAM,aAAN,MAAiB;AAAA,EACf,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG;AACD,SAAK,cAAc;AACnB,SAAK,SAAS;AACd,SAAK,gBAAgB,SAAS,OAAO,SAAS,MAAM,KAAK;AACzD,SAAK,UAAU,QAAQ,IAAI;AAC3B,SAAK,cAAc,eAAe;AAClC,SAAK,WAAW,YAAY;AAAA,EAC9B;AAAA,EACA,MAAM,OAAO;AACX,QAAI,KAAK,aAAa;AACpB;AAAA,IACF;AACA,QAAI;AACF,UAAI,KAAK,YAAY,OAAO,WAAW,eAAe,OAAO,cAAY,aAAa;AACpF,cAAM,EAAE,UAAU,IAAI,MAAM,OAAO,mCAA2B;AAC9D,aAAK,QAAQ,MAAM,UAAU,KAAK,QAAQ;AAC1C,aAAK,YAAY,IAAI,kBAAAG,QAAU;AAAA,MACjC;AAAA,IACF,SAAS,GAAG;AACV,cAAQ,MAAM,CAAC;AAAA,IACjB;AACA,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,MAAM,QAAQ,EAAE,aAAa,GAAG,KAAK,GAAG,SAAS;AAC/C,QAAI;AACJ,UAAM,KAAK,KAAK;AAChB,UAAM,qBAAqB,eAAe,KAAK;AAC/C,UAAM,UAAU,IAAI,QAAQ;AAC5B,QAAI,KAAK,eAAe;AACtB,cAAQ,OAAO,aAAa,KAAK,aAAa;AAAA,IAChD;AACA,YAAQ,OAAO,gBAAgB,kBAAkB;AACjD,QAAI,WAAW,OAAO,SAAS,QAAQ,cAAc;AACnD,WAAK,KAAK,WAAW,OAAO,SAAS,QAAQ,iBAAiB,OAAO,SAAS,GAAG,SAAS;AACxF,eAAO,QAAQ,QAAQ,aAAa,OAAO,EAAE,QAAQ,CAAC,CAAC,MAAM,KAAK,MAAM;AACtE,kBAAQ,OAAO,MAAM,KAAK;AAAA,QAC5B,CAAC;AAAA,MACH;AAAA,IACF;AACA,UAAM,EAAE,SAAS,GAAG,GAAG,qBAAqB,KAAK,WAAW,OAAO,SAAS,QAAQ,iBAAiB,CAAC;AACtG,UAAM,aAAa,KAAK,UAAU;AAAA,MAChC,OAAO,KAAK;AAAA,MACZ,YAAY,QAAQ,OAAO,SAAS,KAAK,cAAc,CAAC;AAAA,IAC1D,CAAC;AACD,UAAM,gBAAgB;AAAA,MACpB,QAAQ;AAAA,MACR;AAAA,MACA,MAAM;AAAA,MACN,UAAU;AAAA,MACV,GAAG;AAAA,IACL;AACA,UAAM,cAAc,QAAQ,IAAI,UAAU;AAC1C,UAAM,MAAM,wBAAwB,QAAQ,OAAO,SAAS,KAAK,QAAQ,KAAK,QAAQ,WAAW;AACjG,QAAI,MAAM;AACV,QAAI;AACJ,QAAI,KAAK,OAAO;AACd,YAAM,KAAK,MAAM,QAAQ,UAAU;AACnC,YAAM,KAAK,UAAU,QAAQ,KAAK,YAAY;AAC5C,iBAAS,MAAM,KAAK,MAAM,IAAI,GAAG;AACjC,YAAI,CAAC,QAAQ;AACX,mBAAS,MAAM;AAAA,YACb;AAAA,YACA,KAAK;AAAA,YACL;AAAA,YACA;AAAA,UACF;AACA,gBAAM,KAAK,MAAM,IAAI,KAAK,MAAM;AAAA,QAClC;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,eAAS,MAAM;AAAA,QACb;AAAA,QACA,KAAK;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AACA,eAAe,kBAAkB,KAAK,OAAO,eAAe,oBAAoB;AAC9E,QAAM,MAAM,MAAM,MAAM,KAAK,aAAa;AAC1C,MAAI,CAAC,IAAI,IAAI;AACX,QAAI,iBAAiB;AACrB,QAAI,IAAI,WAAW,KAAK;AACtB,uBAAiB;AAAA,IACnB;AACA,UAAM,IAAI;AAAA,MACR,qCAAqC,IAAI,MAAM,KAAK,IAAI,UAAU,KAAK,iBAAiB,iBAAiB,EAAE;AAAA,IAC7G;AAAA,EACF;AACA,QAAM,OAAO,MAAM,IAAI,KAAK;AAC5B,MAAI,KAAK,UAAU,uBAAuB,SAAS;AACjD,UAAM,IAAI;AAAA,MACR;AAAA;AAAA,GAEH,KAAK,OAAO,IAAI,CAAC,UAAU,MAAM,OAAO,EAAE,KAAK,IAAI,CAAC;AAAA,IACnD;AAAA,EACF;AACA,QAAM,SAAS;AAAA,IACb,MAAM,QAAQ,OAAO,SAAS,KAAK;AAAA,IACnC,SAAS,QAAQ,OAAO,SAAS,KAAK,WAAW;AAAA,IACjD;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,aAAa,MAAM;AAC1B,QAAM,SAAS,IAAI,WAAW,IAAI;AAClC,SAAO;AACT;", "names": ["AsyncLock", "fn", "cb", "AsyncLock"]}