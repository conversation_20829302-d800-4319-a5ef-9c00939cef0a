{"version": 3, "sources": ["optional-peer-dep:__vite-optional-peer-dep:graphql-ws:@graphiql/toolkit", "../../../../../@graphiql/toolkit/src/async-helpers/index.ts", "../../../../../meros/node/index.mjs", "../../../../../@n1ru4l/push-pull-async-iterable-iterator/index.mjs", "../../../../../@graphiql/toolkit/src/create-fetcher/lib.ts", "../../../../../@graphiql/toolkit/src/create-fetcher/createFetcher.ts", "../../../../../@graphiql/toolkit/src/format/index.ts", "../../../../../@graphiql/toolkit/src/graphql-helpers/auto-complete.ts", "../../../../../@graphiql/toolkit/src/graphql-helpers/merge-ast.ts", "../../../../../@graphiql/toolkit/src/graphql-helpers/operation-name.ts", "../../../../../@graphiql/toolkit/src/storage/base.ts", "../../../../../@graphiql/toolkit/src/storage/query.ts", "../../../../../@graphiql/toolkit/src/storage/history.ts"], "sourcesContent": ["throw new Error(`Could not resolve \"graphql-ws\" imported by \"@graphiql/toolkit\". Is it installed?`)", "import {\n  Fetcher<PERSON><PERSON>ult,\n  FetcherReturnType,\n  Observable,\n} from '../create-fetcher';\n\n// Duck-type promise detection.\nexport function isPromise<T>(value: Promise<T> | any): value is Promise<T> {\n  return (\n    typeof value === 'object' &&\n    value !== null &&\n    typeof value.then === 'function'\n  );\n}\n\n// Duck-type Observable.take(1).toPromise()\nfunction observableToPromise<T>(observable: Observable<T>): Promise<T> {\n  return new Promise((resolve, reject) => {\n    const subscription = observable.subscribe({\n      next: v => {\n        resolve(v);\n        subscription.unsubscribe();\n      },\n      error: reject,\n      complete: () => {\n        reject(new Error('no value resolved'));\n      },\n    });\n  });\n}\n\n// Duck-type observable detection.\nexport function isObservable<T>(value: any): value is Observable<T> {\n  return (\n    typeof value === 'object' &&\n    value !== null &&\n    'subscribe' in value &&\n    typeof value.subscribe === 'function'\n  );\n}\n\nexport function isAsyncIterable(\n  input: unknown,\n): input is AsyncIterable<unknown> {\n  return (\n    typeof input === 'object' &&\n    input !== null &&\n    // Some browsers still don't have Symbol.asyncIterator implemented (iOS Safari)\n    // That means every custom AsyncIterable must be built using a AsyncGeneratorFunction (async function * () {})\n    ((input as any)[Symbol.toStringTag] === 'AsyncGenerator' ||\n      Symbol.asyncIterator in input)\n  );\n}\n\nasync function asyncIterableToPromise<T>(\n  input: AsyncIterable<T> | AsyncIterableIterator<T>,\n): Promise<T> {\n  // Also support AsyncGenerator on Safari iOS.\n  // As mentioned in the isAsyncIterable function, there is no Symbol.asyncIterator available,\n  // so every AsyncIterable must be implemented using AsyncGenerator.\n  const iteratorReturn = (\n    'return' in input ? input : input[Symbol.asyncIterator]()\n  ).return?.bind(input);\n  const iteratorNext = (\n    'next' in input ? input : input[Symbol.asyncIterator]()\n  ).next.bind(input);\n\n  const result = await iteratorNext();\n  // ensure cleanup\n  void iteratorReturn?.();\n  return result.value;\n}\n\nexport async function fetcherReturnToPromise(\n  fetcherResult: FetcherReturnType,\n): Promise<FetcherResult> {\n  const result = await fetcherResult;\n  if (isAsyncIterable(result)) {\n    return asyncIterableToPromise(result);\n  }\n  if (isObservable(result)) {\n    return observableToPromise(result);\n  }\n  return result;\n}\n", "async function e(e,t){let n=e.headers[\"content-type\"];if(!n||!~n.indexOf(\"multipart/\"))return e;let r=n.indexOf(\"boundary=\"),i=\"-\";if(~r){let e=r+9,t=n.indexOf(\";\",e);i=n.slice(e,t>-1?t:void 0).trim().replace(/\"/g,\"\")}return async function*(e,t,n){let r,i,f,a=!n||!n.multiple,l=Buffer.byteLength(t),o=Buffer.alloc(0),s=[];e:for await(let n of e){r=o.byteLength,o=Buffer.concat([o,n]);let e=n.indexOf(t);for(~e?r+=e:r=o.indexOf(t),s=[];~r;){let e=o.subarray(0,r),n=o.subarray(r+l);if(i){let t=e.indexOf(\"\\r\\n\\r\\n\")+4,r=e.lastIndexOf(\"\\r\\n\",t),i=!1,l=e.subarray(t,r>-1?void 0:r),o=String(e.subarray(0,t)).trim().split(\"\\r\\n\"),d={},y=o.length;for(;f=o[--y];f=f.split(\": \"),d[f.shift().toLowerCase()]=f.join(\": \"));if(f=d[\"content-type\"],f&&~f.indexOf(\"application/json\"))try{l=JSON.parse(String(l)),i=!0}catch(e){}if(f={headers:d,body:l,json:i},a?yield f:s.push(f),45===n[0]&&45===n[1])break e}else t=\"\\r\\n\"+t,i=l+=2;o=n,r=o.indexOf(t)}s.length&&(yield s)}s.length&&(yield s)}(e,`--${i}`,t)}export{e as meros};", "/**\r\n * Attaches a cleanup handler to a AsyncIterable.\r\n *\r\n * @param source The source that should have a return handler attached\r\n * @param onReturn The return handler that should be attached\r\n * @returns\r\n */\r\nfunction withHandlers(source, onReturn, onThrow) {\r\n    const stream = (async function* withReturnSource() {\r\n        yield* source;\r\n    })();\r\n    const originalReturn = stream.return.bind(stream);\r\n    if (onReturn) {\r\n        stream.return = (...args) => {\r\n            onReturn();\r\n            return originalReturn(...args);\r\n        };\r\n    }\r\n    if (onThrow) {\r\n        const originalThrow = stream.throw.bind(stream);\r\n        stream.throw = (err) => {\r\n            onThrow(err);\r\n            return originalThrow(err);\r\n        };\r\n    }\r\n    return stream;\r\n}\n\nfunction createDeferred() {\r\n    const d = {};\r\n    d.promise = new Promise((resolve, reject) => {\r\n        d.resolve = resolve;\r\n        d.reject = reject;\r\n    });\r\n    return d;\r\n}\r\n/**\r\n * makePushPullAsyncIterableIterator\r\n *\r\n * The iterable will publish values until return or throw is called.\r\n * Afterwards it is in the completed state and cannot be used for publishing any further values.\r\n * It will handle back-pressure and keep pushed values until they are consumed by a source.\r\n */\r\nfunction makePushPullAsyncIterableIterator() {\r\n    let state = {\r\n        type: \"running\" /* running */\r\n    };\r\n    let next = createDeferred();\r\n    const values = [];\r\n    function pushValue(value) {\r\n        if (state.type !== \"running\" /* running */) {\r\n            return;\r\n        }\r\n        values.push(value);\r\n        next.resolve();\r\n        next = createDeferred();\r\n    }\r\n    const source = (async function* PushPullAsyncIterableIterator() {\r\n        while (true) {\r\n            if (values.length > 0) {\r\n                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\r\n                yield values.shift();\r\n            }\r\n            else {\r\n                if (state.type === \"error\" /* error */) {\r\n                    throw state.error;\r\n                }\r\n                if (state.type === \"finished\" /* finished */) {\r\n                    return;\r\n                }\r\n                await next.promise;\r\n            }\r\n        }\r\n    })();\r\n    const stream = withHandlers(source, () => {\r\n        if (state.type !== \"running\" /* running */) {\r\n            return;\r\n        }\r\n        state = {\r\n            type: \"finished\" /* finished */\r\n        };\r\n        next.resolve();\r\n    }, (error) => {\r\n        if (state.type !== \"running\" /* running */) {\r\n            return;\r\n        }\r\n        state = {\r\n            type: \"error\" /* error */,\r\n            error\r\n        };\r\n        next.resolve();\r\n    });\r\n    return {\r\n        pushValue,\r\n        asyncIterableIterator: stream\r\n    };\r\n}\n\nconst makeAsyncIterableIteratorFromSink = (make) => {\r\n    const { pushValue, asyncIterableIterator } = makePushPullAsyncIterableIterator();\r\n    const dispose = make({\r\n        next: (value) => {\r\n            pushValue(value);\r\n        },\r\n        complete: () => {\r\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\r\n            asyncIterableIterator.return();\r\n        },\r\n        error: (err) => {\r\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\r\n            asyncIterableIterator.throw(err);\r\n        }\r\n    });\r\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\r\n    const originalReturn = asyncIterableIterator.return;\r\n    let returnValue = undefined;\r\n    asyncIterableIterator.return = () => {\r\n        if (returnValue === undefined) {\r\n            dispose();\r\n            returnValue = originalReturn();\r\n        }\r\n        return returnValue;\r\n    };\r\n    return asyncIterableIterator;\r\n};\n\nfunction applyAsyncIterableIteratorToSink(asyncIterableIterator, sink) {\r\n    const run = async () => {\r\n        try {\r\n            for await (const value of asyncIterableIterator) {\r\n                sink.next(value);\r\n            }\r\n            sink.complete();\r\n        }\r\n        catch (err) {\r\n            sink.error(err);\r\n        }\r\n    };\r\n    run();\r\n    return () => {\r\n        var _a;\r\n        (_a = asyncIterableIterator.return) === null || _a === void 0 ? void 0 : _a.call(asyncIterableIterator);\r\n    };\r\n}\n\nfunction isAsyncIterable(input) {\r\n    return (typeof input === \"object\" &&\r\n        input !== null &&\r\n        // The AsyncGenerator check is for Safari on iOS which currently does not have\r\n        // Symbol.asyncIterator implemented\r\n        // That means every custom AsyncIterable must be built using a AsyncGeneratorFunction (async function * () {})\r\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n        (input[Symbol.toStringTag] === \"AsyncGenerator\" ||\r\n            (Symbol.asyncIterator && Symbol.asyncIterator in input)));\r\n}\n\n/**\r\n * Attaches a cleanup handler from and AsyncIterable to an AsyncIterable.\r\n *\r\n * @param source\r\n * @param target\r\n */\r\nfunction withHandlersFrom(\r\n/** The source that should be returned with attached handlers. */\r\nsource, \r\n/**The target on which the return and throw methods should be called. */\r\ntarget) {\r\n    return withHandlers(source, () => { var _a; return (_a = target.return) === null || _a === void 0 ? void 0 : _a.call(target); }, err => { var _a; return (_a = target.throw) === null || _a === void 0 ? void 0 : _a.call(target, err); });\r\n}\n\nfunction filter(filter) {\r\n    return async function* filterGenerator(asyncIterable) {\r\n        for await (const value of asyncIterable) {\r\n            if (filter(value)) {\r\n                yield value;\r\n            }\r\n        }\r\n    };\r\n}\n\n/**\r\n * Map the events published by an AsyncIterable.\r\n */\r\nconst map = (map) => async function* mapGenerator(asyncIterable) {\r\n    for await (const value of asyncIterable) {\r\n        yield map(value);\r\n    }\r\n};\n\nexport { applyAsyncIterableIteratorToSink, filter, isAsyncIterable, makeAsyncIterableIteratorFromSink, makePushPullAsyncIterableIterator, map, withHandlers, withHandlersFrom };\n", "import { DocumentNode, visit } from 'graphql';\nimport { meros } from 'meros';\nimport {\n  Client,\n  ClientOptions,\n  ExecutionResult,\n  createClient as createClientType,\n} from 'graphql-ws';\nimport {\n  isAsyncIterable,\n  makeAsyncIterableIteratorFromSink,\n} from '@n1ru4l/push-pull-async-iterable-iterator';\n\nimport type {\n  <PERSON><PERSON>er,\n  FetcherParams,\n  FetcherOpts,\n  ExecutionResultPayload,\n  CreateFetcherOptions,\n} from './types';\n\nconst errorHasCode = (err: unknown): err is { code: string } => {\n  return typeof err === 'object' && err !== null && 'code' in err;\n};\n\n/**\n * Returns true if the name matches a subscription in the AST\n *\n * @param document {DocumentNode}\n * @param name the operation name to lookup\n * @returns {boolean}\n */\nexport const isSubscriptionWithName = (\n  document: DocumentNode,\n  name: string | undefined,\n): boolean => {\n  let isSubscription = false;\n  visit(document, {\n    OperationDefinition(node) {\n      if (name === node.name?.value && node.operation === 'subscription') {\n        isSubscription = true;\n      }\n    },\n  });\n  return isSubscription;\n};\n\n/**\n * create a simple HTTP/S fetcher using a fetch implementation where\n * multipart is not needed\n *\n * @param options {CreateFetcherOptions}\n * @param httpFetch {typeof fetch}\n * @returns {Fetcher}\n */\nexport const createSimpleFetcher =\n  (options: CreateFetcherOptions, httpFetch: typeof fetch): Fetcher =>\n  async (graphQLParams: FetcherParams, fetcherOpts?: FetcherOpts) => {\n    const data = await httpFetch(options.url, {\n      method: 'POST',\n      body: JSON.stringify(graphQLParams),\n      headers: {\n        'content-type': 'application/json',\n        ...options.headers,\n        ...fetcherOpts?.headers,\n      },\n    });\n    return data.json();\n  };\n\nexport const createWebsocketsFetcherFromUrl = (\n  url: string,\n  connectionParams?: ClientOptions['connectionParams'],\n) => {\n  let wsClient;\n  try {\n    const { createClient } = require('graphql-ws') as {\n      createClient: typeof createClientType;\n    };\n\n    // TODO: defaults?\n    wsClient = createClient({\n      url,\n      connectionParams,\n    });\n    return createWebsocketsFetcherFromClient(wsClient);\n  } catch (err) {\n    if (errorHasCode(err) && err.code === 'MODULE_NOT_FOUND') {\n      throw new Error(\n        \"You need to install the 'graphql-ws' package to use websockets when passing a 'subscriptionUrl'\",\n      );\n    }\n    // eslint-disable-next-line no-console\n    console.error(`Error creating websocket client for ${url}`, err);\n  }\n};\n\n/**\n * Create ws/s fetcher using provided wsClient implementation\n *\n * @param wsClient {Client}\n * @returns {Fetcher}\n */\nexport const createWebsocketsFetcherFromClient =\n  (wsClient: Client) => (graphQLParams: FetcherParams) =>\n    makeAsyncIterableIteratorFromSink<ExecutionResult>(sink =>\n      wsClient.subscribe(graphQLParams, {\n        ...sink,\n        error: err => {\n          if (err instanceof CloseEvent) {\n            sink.error(\n              new Error(\n                `Socket closed with event ${err.code} ${\n                  err.reason || ''\n                }`.trim(),\n              ),\n            );\n          } else {\n            sink.error(err);\n          }\n        },\n      }),\n    );\n\n/**\n * Allow legacy websockets protocol client, but no definitions for it,\n * as the library is deprecated and has security issues\n *\n * @param legacyWsClient\n * @returns\n */\nexport const createLegacyWebsocketsFetcher =\n  (legacyWsClient: { request: (params: FetcherParams) => unknown }) =>\n  (graphQLParams: FetcherParams) => {\n    const observable = legacyWsClient.request(graphQLParams);\n    return makeAsyncIterableIteratorFromSink<ExecutionResult>(\n      // @ts-ignore\n      sink => observable.subscribe(sink).unsubscribe,\n    );\n  };\n/**\n * create a fetcher with the `IncrementalDelivery` HTTP/S spec for\n * `@stream` and `@defer` support using `fetch-multipart-graphql`\n *\n * @param options {CreateFetcherOptions}\n * @returns {Fetcher}\n */\nexport const createMultipartFetcher = (\n  options: CreateFetcherOptions,\n  httpFetch: typeof fetch,\n): Fetcher =>\n  async function* (graphQLParams: FetcherParams, fetcherOpts?: FetcherOpts) {\n    const response = await httpFetch(options.url, {\n      method: 'POST',\n      body: JSON.stringify(graphQLParams),\n      headers: {\n        'content-type': 'application/json',\n        accept: 'application/json, multipart/mixed',\n        ...options.headers,\n        // allow user-defined headers to override\n        // the static provided headers\n        ...fetcherOpts?.headers,\n      },\n    }).then(r =>\n      meros<Extract<ExecutionResultPayload, { hasNext: boolean }>>(r, {\n        multiple: true,\n      }),\n    );\n\n    // Follows the same as createSimpleFetcher above, in that we simply return it as json.\n    if (!isAsyncIterable(response)) {\n      return yield response.json();\n    }\n\n    for await (const chunk of response) {\n      if (chunk.some(part => !part.json)) {\n        const message = chunk.map(\n          part => `Headers::\\n${part.headers}\\n\\nBody::\\n${part.body}`,\n        );\n        throw new Error(\n          `Expected multipart chunks to be of json type. got:\\n${message}`,\n        );\n      }\n      yield chunk.map(part => part.body);\n    }\n  };\n\n/**\n * If `wsClient` or `legacyClient` are provided, then `subscriptionUrl` is overridden.\n * @param options {CreateFetcherOptions}\n * @returns\n */\nexport const getWsFetcher = (\n  options: CreateFetcherOptions,\n  fetcherOpts: FetcherOpts | undefined,\n) => {\n  if (options.wsClient) {\n    return createWebsocketsFetcherFromClient(options.wsClient);\n  }\n  if (options.subscriptionUrl) {\n    return createWebsocketsFetcherFromUrl(options.subscriptionUrl, {\n      ...options.wsConnectionParams,\n      ...fetcherOpts?.headers,\n    });\n  }\n  const legacyWebsocketsClient = options.legacyClient || options.legacyWsClient;\n  if (legacyWebsocketsClient) {\n    return createLegacyWebsocketsFetcher(legacyWebsocketsClient);\n  }\n};\n", "import type { Fetcher, CreateFetcherOptions } from './types';\n\nimport {\n  createMultipartFetcher,\n  createSimpleFetcher,\n  isSubscriptionWithName,\n  getWsFetcher,\n} from './lib';\n\n/**\n * build a GraphiQL fetcher that is:\n * - backwards compatible\n * - optionally supports graphql-ws or `\n *\n * @param options {CreateFetcherOptions}\n * @returns {Fetcher}\n */\nexport function createGraphiQLFetcher(options: CreateFetcherOptions): Fetcher {\n  let httpFetch;\n  if (typeof window !== 'undefined' && window.fetch) {\n    httpFetch = window.fetch;\n  }\n  if (\n    options?.enableIncrementalDelivery === null ||\n    options.enableIncrementalDelivery !== false\n  ) {\n    options.enableIncrementalDelivery = true;\n  }\n  if (options.fetch) {\n    httpFetch = options.fetch;\n  }\n  if (!httpFetch) {\n    throw new Error('No valid fetcher implementation available');\n  }\n  // simpler fetcher for schema requests\n  const simpleFetcher = createSimpleFetcher(options, httpFetch);\n\n  const httpFetcher = options.enableIncrementalDelivery\n    ? createMultipartFetcher(options, httpFetch)\n    : simpleFetcher;\n\n  return (graphQLParams, fetcherOpts) => {\n    if (graphQLParams.operationName === 'IntrospectionQuery') {\n      return (options.schemaFetcher || simpleFetcher)(\n        graphQLParams,\n        fetcherOpts,\n      );\n    }\n    const isSubscription = fetcherOpts?.documentAST\n      ? isSubscriptionWithName(\n          fetcherOpts.documentAST,\n          graphQLParams.operationName || undefined,\n        )\n      : false;\n    if (isSubscription) {\n      const wsFetcher = getWsFetcher(options, fetcherOpts);\n\n      if (!wsFetcher) {\n        throw new Error(\n          `Your GraphiQL createFetcher is not properly configured for websocket subscriptions yet. ${\n            options.subscriptionUrl\n              ? `Provided URL ${options.subscriptionUrl} failed`\n              : `Please provide subscriptionUrl, wsClient or legacyClient option first.`\n          }`,\n        );\n      }\n      return wsFetcher(graphQLParams);\n    }\n    return httpFetcher(graphQLParams, fetcherOpts);\n  };\n}\n", "function stringify(obj: unknown): string {\n  return JSON.stringify(obj, null, 2);\n}\n\nfunction formatSingleError(error: Error): Error {\n  return {\n    ...error,\n    // Raise these details even if they're non-enumerable\n    message: error.message,\n    stack: error.stack,\n  };\n}\n\nfunction handleSingleError(error: unknown) {\n  if (error instanceof Error) {\n    return formatSingleError(error);\n  }\n  return error;\n}\n\nexport function formatError(error: unknown): string {\n  if (Array.isArray(error)) {\n    return stringify({\n      errors: error.map(e => handleSingleError(e)),\n    });\n  }\n  return stringify({ errors: [handleSingleError(error)] });\n}\n\nexport function formatResult(result: any): string {\n  return stringify(result);\n}\n", "import {\n  DocumentNode,\n  getNamedType,\n  GraphQLOutputType,\n  GraphQLSchema,\n  GraphQLType,\n  isLeafType,\n  Kind,\n  parse,\n  print,\n  SelectionSetNode,\n  TypeInfo,\n  visit,\n} from 'graphql';\n\ntype Insertion = {\n  index: number;\n  string: string;\n};\n\nexport type GetDefaultFieldNamesFn = (type: GraphQLType) => string[];\n\n/**\n * Given a document string which may not be valid due to terminal fields not\n * representing leaf values (Spec Section: \"Leaf Field Selections\"), and a\n * function which provides reasonable default field names for a given type,\n * this function will attempt to produce a schema which is valid after filling\n * in selection sets for the invalid fields.\n *\n * Note that there is no guarantee that the result will be a valid query, this\n * utility represents a \"best effort\" which may be useful within IDE tools.\n */\nexport function fillLeafs(\n  schema?: GraphQLSchema | null,\n  docString?: string,\n  getDefaultFieldNames?: GetDefaultFieldNamesFn,\n) {\n  const insertions: Insertion[] = [];\n\n  if (!schema || !docString) {\n    return { insertions, result: docString };\n  }\n\n  let ast: DocumentNode;\n  try {\n    ast = parse(docString);\n  } catch {\n    return { insertions, result: docString };\n  }\n\n  const fieldNameFn = getDefaultFieldNames || defaultGetDefaultFieldNames;\n  const typeInfo = new TypeInfo(schema);\n  visit(ast, {\n    leave(node) {\n      typeInfo.leave(node);\n    },\n    enter(node) {\n      typeInfo.enter(node);\n      if (node.kind === 'Field' && !node.selectionSet) {\n        const fieldType = typeInfo.getType();\n        const selectionSet = buildSelectionSet(\n          isFieldType(fieldType) as GraphQLOutputType,\n          fieldNameFn,\n        );\n        if (selectionSet && node.loc) {\n          const indent = getIndentation(docString, node.loc.start);\n          insertions.push({\n            index: node.loc.end,\n            string: ' ' + print(selectionSet).replaceAll('\\n', '\\n' + indent),\n          });\n        }\n      }\n    },\n  });\n\n  // Apply the insertions, but also return the insertions metadata.\n  return {\n    insertions,\n    result: withInsertions(docString, insertions),\n  };\n}\n\n// The default function to use for producing the default fields from a type.\n// This function first looks for some common patterns, and falls back to\n// including all leaf-type fields.\nfunction defaultGetDefaultFieldNames(type: GraphQLType) {\n  // If this type cannot access fields, then return an empty set.\n  // if (!type.getFields) {\n  if (!('getFields' in type)) {\n    return [];\n  }\n\n  const fields = type.getFields();\n\n  // Is there an `id` field?\n  if (fields.id) {\n    return ['id'];\n  }\n\n  // Is there an `edges` field?\n  if (fields.edges) {\n    return ['edges'];\n  }\n\n  // Is there an `node` field?\n  if (fields.node) {\n    return ['node'];\n  }\n\n  // Include all leaf-type fields.\n  const leafFieldNames: Array<string> = [];\n  for (const fieldName of Object.keys(fields)) {\n    if (isLeafType(fields[fieldName].type)) {\n      leafFieldNames.push(fieldName);\n    }\n  }\n  return leafFieldNames;\n}\n\n// Given a GraphQL type, and a function which produces field names, recursively\n// generate a SelectionSet which includes default fields.\nfunction buildSelectionSet(\n  type: GraphQLOutputType,\n  getDefaultFieldNames: GetDefaultFieldNamesFn,\n): SelectionSetNode | undefined {\n  // Unwrap any non-null or list types.\n  const namedType = getNamedType(type);\n\n  // Unknown types and leaf types do not have selection sets.\n  if (!type || isLeafType(type)) {\n    return;\n  }\n\n  // Get an array of field names to use.\n  const fieldNames = getDefaultFieldNames(namedType);\n\n  // If there are no field names to use, return no selection set.\n  if (\n    !Array.isArray(fieldNames) ||\n    fieldNames.length === 0 ||\n    !('getFields' in namedType)\n  ) {\n    return;\n  }\n\n  // Build a selection set of each field, calling buildSelectionSet recursively.\n  return {\n    kind: Kind.SELECTION_SET,\n    selections: fieldNames.map(fieldName => {\n      const fieldDef = namedType.getFields()[fieldName];\n      const fieldType = fieldDef ? fieldDef.type : null;\n      return {\n        kind: Kind.FIELD,\n        name: {\n          kind: Kind.NAME,\n          value: fieldName,\n        },\n        // we can use as here, because we already know that fieldType\n        // comes from an origin parameter\n        selectionSet: buildSelectionSet(\n          fieldType as GraphQLOutputType,\n          getDefaultFieldNames,\n        ),\n      };\n    }),\n  };\n}\n\n// Given an initial string, and a list of \"insertion\" { index, string } objects,\n// return a new string with these insertions applied.\nfunction withInsertions(initial: string, insertions: Insertion[]) {\n  if (insertions.length === 0) {\n    return initial;\n  }\n  let edited = '';\n  let prevIndex = 0;\n  for (const { index, string } of insertions) {\n    edited += initial.slice(prevIndex, index) + string;\n    prevIndex = index;\n  }\n  edited += initial.slice(prevIndex);\n  return edited;\n}\n\n// Given a string and an index, look backwards to find the string of whitespace\n// following the next previous line break.\nfunction getIndentation(str: string, index: number) {\n  let indentStart = index;\n  let indentEnd = index;\n  while (indentStart) {\n    const c = str.charCodeAt(indentStart - 1);\n    // line break\n    if (c === 10 || c === 13 || c === 0x2028 || c === 0x2029) {\n      break;\n    }\n    indentStart--;\n    // not white space\n    if (c !== 9 && c !== 11 && c !== 12 && c !== 32 && c !== 160) {\n      indentEnd = indentStart;\n    }\n  }\n  return str.slice(indentStart, indentEnd);\n}\n\nfunction isFieldType(\n  fieldType: GraphQLOutputType | null | undefined,\n): GraphQLOutputType | void {\n  if (fieldType) {\n    return fieldType;\n  }\n}\n", "import {\n  DocumentNode,\n  FieldNode,\n  FragmentDefinitionNode,\n  GraphQLOutputType,\n  GraphQLSchema,\n  SelectionNode,\n  TypeInfo,\n  getNamedType,\n  visit,\n  visitWithTypeInfo,\n  ASTVisitor,\n  Kind,\n} from 'graphql';\n\nfunction uniqueBy<T>(\n  array: readonly SelectionNode[],\n  iteratee: (item: FieldNode) => T,\n) {\n  const FilteredMap = new Map<T, FieldNode>();\n  const result: SelectionNode[] = [];\n  for (const item of array) {\n    if (item.kind === 'Field') {\n      const uniqueValue = iteratee(item);\n      const existing = FilteredMap.get(uniqueValue);\n      if (item.directives?.length) {\n        // Cannot inline fields with directives (yet)\n        const itemClone = { ...item };\n        result.push(itemClone);\n      } else if (existing?.selectionSet && item.selectionSet) {\n        // Merge the selection sets\n        existing.selectionSet.selections = [\n          ...existing.selectionSet.selections,\n          ...item.selectionSet.selections,\n        ];\n      } else if (!existing) {\n        const itemClone = { ...item };\n        FilteredMap.set(uniqueValue, itemClone);\n        result.push(itemClone);\n      }\n    } else {\n      result.push(item);\n    }\n  }\n  return result;\n}\n\nfunction inlineRelevantFragmentSpreads(\n  fragmentDefinitions: {\n    [key: string]: FragmentDefinitionNode | undefined;\n  },\n  selections: readonly SelectionNode[],\n  selectionSetType?: GraphQLOutputType | null,\n): readonly SelectionNode[] {\n  const selectionSetTypeName = selectionSetType\n    ? getNamedType(selectionSetType).name\n    : null;\n  const outputSelections = [];\n  const seenSpreads: string[] = [];\n  for (let selection of selections) {\n    if (selection.kind === 'FragmentSpread') {\n      const fragmentName = selection.name.value;\n      if (!selection.directives || selection.directives.length === 0) {\n        if (seenSpreads.includes(fragmentName)) {\n          /* It's a duplicate - skip it! */\n          continue;\n        } else {\n          seenSpreads.push(fragmentName);\n        }\n      }\n      const fragmentDefinition = fragmentDefinitions[selection.name.value];\n      if (fragmentDefinition) {\n        const { typeCondition, directives, selectionSet } = fragmentDefinition;\n        selection = {\n          kind: Kind.INLINE_FRAGMENT,\n          typeCondition,\n          directives,\n          selectionSet,\n        };\n      }\n    }\n    if (\n      selection.kind === Kind.INLINE_FRAGMENT &&\n      // Cannot inline if there are directives\n      (!selection.directives || selection.directives?.length === 0)\n    ) {\n      const fragmentTypeName = selection.typeCondition\n        ? selection.typeCondition.name.value\n        : null;\n      if (!fragmentTypeName || fragmentTypeName === selectionSetTypeName) {\n        outputSelections.push(\n          ...inlineRelevantFragmentSpreads(\n            fragmentDefinitions,\n            selection.selectionSet.selections,\n            selectionSetType,\n          ),\n        );\n        continue;\n      }\n    }\n    outputSelections.push(selection);\n  }\n  return outputSelections;\n}\n\n/**\n * Given a document AST, inline all named fragment definitions.\n */\nexport function mergeAst(\n  documentAST: DocumentNode,\n  schema?: GraphQLSchema | null,\n): DocumentNode {\n  // If we're given the schema, we can simplify even further by resolving object\n  // types vs unions/interfaces\n  const typeInfo = schema ? new TypeInfo(schema) : null;\n\n  const fragmentDefinitions: {\n    [key: string]: FragmentDefinitionNode | undefined;\n  } = Object.create(null);\n\n  for (const definition of documentAST.definitions) {\n    if (definition.kind === Kind.FRAGMENT_DEFINITION) {\n      fragmentDefinitions[definition.name.value] = definition;\n    }\n  }\n\n  const visitors: ASTVisitor = {\n    SelectionSet(node: any) {\n      const selectionSetType = typeInfo ? typeInfo.getParentType() : null;\n      let { selections } = node;\n\n      selections = inlineRelevantFragmentSpreads(\n        fragmentDefinitions,\n        selections,\n        selectionSetType,\n      );\n\n      selections = uniqueBy(selections, selection =>\n        selection.alias ? selection.alias.value : selection.name.value,\n      );\n\n      return {\n        ...node,\n        selections,\n      };\n    },\n    FragmentDefinition() {\n      return null;\n    },\n  };\n\n  return visit(\n    documentAST,\n    typeInfo ? visitWithTypeInfo(typeInfo, visitors) : visitors,\n  );\n}\n", "import { OperationDefinitionNode } from 'graphql';\n\n/**\n * Provided optional previous operations and selected name, and a next list of\n * operations, determine what the next selected operation should be.\n */\nexport function getSelectedOperationName(\n  prevOperations?: OperationDefinitionNode[] | undefined,\n  prevSelectedOperationName?: string,\n  operations?: OperationDefinitionNode[],\n) {\n  // If there are not enough operations to bother with, return nothing.\n  if (!operations || operations.length < 1) {\n    return;\n  }\n\n  // If a previous selection still exists, continue to use it.\n  const names = operations.map(op => op.name?.value);\n  if (prevSelectedOperationName && names.includes(prevSelectedOperationName)) {\n    return prevSelectedOperationName;\n  }\n\n  // If a previous selection was the Nth operation, use the same Nth.\n  if (prevSelectedOperationName && prevOperations) {\n    const prevNames = prevOperations.map(op => op.name?.value);\n    const prevIndex = prevNames.indexOf(prevSelectedOperationName);\n    if (prevIndex !== -1 && prevIndex < names.length) {\n      return names[prevIndex];\n    }\n  }\n\n  // Use the first operation.\n  return names[0];\n}\n", "/**\n * This describes the attributes and methods that a store has to support in\n * order to be used with GraphiQL. It closely resembles the `localStorage`\n * API as it is the default storage used in GraphiQL.\n */\nexport type Storage = {\n  /**\n   * Retrieve an item from the store by its key.\n   * @param key The key of the item to retrieve.\n   * @returns {?string} The stored value for the given key if it exists, `null`\n   * otherwise.\n   */\n  getItem(key: string): string | null;\n  /**\n   * Add a value to the store for a given key. If there already exists a value\n   * for the given key, this method will override the value.\n   * @param key The key to store the value for.\n   * @param value The value to store.\n   */\n  setItem(key: string, value: string): void;\n  /**\n   * Remove the value for a given key from the store. If there is no value for\n   * the given key this method does nothing.\n   * @param key The key to remove the value from the store.\n   */\n  removeItem(key: string): void;\n  /**\n   * Remove all items from the store.\n   */\n  clear(): void;\n  /**\n   * The number of items that are currently stored.\n   */\n  length: number;\n};\n\nfunction isQuotaError(storage: Storage, e: unknown) {\n  return (\n    e instanceof DOMException &&\n    // everything except Firefox\n    (e.code === 22 ||\n      // Firefox\n      e.code === 1014 ||\n      // test name field too, because code might not be present\n      // everything except Firefox\n      e.name === 'QuotaExceededError' ||\n      // Firefox\n      e.name === 'NS_ERROR_DOM_QUOTA_REACHED') &&\n    // acknowledge QuotaExceededError only if there's something already stored\n    storage.length !== 0\n  );\n}\n\nexport class StorageAPI {\n  storage: Storage | null;\n\n  constructor(storage?: Storage | null) {\n    if (storage) {\n      this.storage = storage;\n    } else if (storage === null) {\n      // Passing `null` creates a noop storage\n      this.storage = null;\n    } else if (typeof window === 'undefined') {\n      this.storage = null;\n    } else {\n      this.storage = {\n        getItem: window.localStorage.getItem.bind(window.localStorage),\n        setItem: window.localStorage.setItem.bind(window.localStorage),\n        removeItem: window.localStorage.removeItem.bind(window.localStorage),\n\n        get length() {\n          let keys = 0;\n          for (const key in window.localStorage) {\n            if (key.indexOf(`${STORAGE_NAMESPACE}:`) === 0) {\n              keys += 1;\n            }\n          }\n          return keys;\n        },\n\n        clear: () => {\n          // We only want to clear the namespaced items\n          for (const key in window.localStorage) {\n            if (key.indexOf(`${STORAGE_NAMESPACE}:`) === 0) {\n              window.localStorage.removeItem(key);\n            }\n          }\n        },\n      };\n    }\n  }\n\n  get(name: string): string | null {\n    if (!this.storage) {\n      return null;\n    }\n\n    const key = `${STORAGE_NAMESPACE}:${name}`;\n    const value = this.storage.getItem(key);\n    // Clean up any inadvertently saved null/undefined values.\n    if (value === 'null' || value === 'undefined') {\n      this.storage.removeItem(key);\n      return null;\n    }\n\n    return value || null;\n  }\n\n  set(\n    name: string,\n    value: string,\n  ): { isQuotaError: boolean; error: Error | null } {\n    let quotaError = false;\n    let error: Error | null = null;\n\n    if (this.storage) {\n      const key = `${STORAGE_NAMESPACE}:${name}`;\n      if (value) {\n        try {\n          this.storage.setItem(key, value);\n        } catch (e) {\n          error = e instanceof Error ? e : new Error(`${e}`);\n          quotaError = isQuotaError(this.storage, e);\n        }\n      } else {\n        // Clean up by removing the item if there's no value to set\n        this.storage.removeItem(key);\n      }\n    }\n\n    return { isQuotaError: quotaError, error };\n  }\n\n  clear() {\n    if (this.storage) {\n      this.storage.clear();\n    }\n  }\n}\n\nconst STORAGE_NAMESPACE = 'graphiql';\n", "import { StorageAPI } from './base';\n\nexport type QueryStoreItem = {\n  query?: string;\n  variables?: string;\n  headers?: string;\n  operationName?: string;\n  label?: string;\n  favorite?: boolean;\n};\n\nexport class QueryStore {\n  items: Array<QueryStoreItem>;\n\n  constructor(\n    private key: string,\n    private storage: StorageAPI,\n    private maxSize: number | null = null,\n  ) {\n    this.items = this.fetchAll();\n  }\n\n  get length() {\n    return this.items.length;\n  }\n\n  contains(item: QueryStoreItem) {\n    return this.items.some(\n      x =>\n        x.query === item.query &&\n        x.variables === item.variables &&\n        x.headers === item.headers &&\n        x.operationName === item.operationName,\n    );\n  }\n\n  edit(item: QueryStoreItem) {\n    const itemIndex = this.items.findIndex(\n      x =>\n        x.query === item.query &&\n        x.variables === item.variables &&\n        x.headers === item.headers &&\n        x.operationName === item.operationName,\n    );\n    if (itemIndex !== -1) {\n      this.items.splice(itemIndex, 1, item);\n      this.save();\n    }\n  }\n\n  delete(item: QueryStoreItem) {\n    const itemIndex = this.items.findIndex(\n      x =>\n        x.query === item.query &&\n        x.variables === item.variables &&\n        x.headers === item.headers &&\n        x.operationName === item.operationName,\n    );\n    if (itemIndex !== -1) {\n      this.items.splice(itemIndex, 1);\n      this.save();\n    }\n  }\n\n  fetchRecent() {\n    return this.items.at(-1);\n  }\n\n  fetchAll() {\n    const raw = this.storage.get(this.key);\n    if (raw) {\n      return JSON.parse(raw)[this.key] as Array<QueryStoreItem>;\n    }\n    return [];\n  }\n\n  push(item: QueryStoreItem) {\n    const items = [...this.items, item];\n\n    if (this.maxSize && items.length > this.maxSize) {\n      items.shift();\n    }\n\n    for (let attempts = 0; attempts < 5; attempts++) {\n      const response = this.storage.set(\n        this.key,\n        JSON.stringify({ [this.key]: items }),\n      );\n      if (!response?.error) {\n        this.items = items;\n      } else if (response.isQuotaError && this.maxSize) {\n        // Only try to delete last items on LRU stores\n        items.shift();\n      } else {\n        return; // We don't know what happened in this case, so just bailing out\n      }\n    }\n  }\n\n  save() {\n    this.storage.set(this.key, JSON.stringify({ [this.key]: this.items }));\n  }\n}\n", "import { parse } from 'graphql';\n\nimport { StorageAPI } from './base';\nimport { QueryStore, QueryStoreItem } from './query';\n\nconst MAX_QUERY_SIZE = 100000;\n\nexport class HistoryStore {\n  queries: Array<QueryStoreItem>;\n  history: QueryStore;\n  favorite: QueryStore;\n\n  constructor(private storage: StorageAPI, private maxHistoryLength: number) {\n    this.history = new QueryStore(\n      'queries',\n      this.storage,\n      this.maxHistoryLength,\n    );\n    // favorites are not automatically deleted, so there's no need for a max length\n    this.favorite = new QueryStore('favorites', this.storage, null);\n\n    this.queries = [...this.history.fetchAll(), ...this.favorite.fetchAll()];\n  }\n\n  private shouldSaveQuery(\n    query?: string,\n    variables?: string,\n    headers?: string,\n    lastQuerySaved?: QueryStoreItem,\n  ) {\n    if (!query) {\n      return false;\n    }\n\n    try {\n      parse(query);\n    } catch {\n      return false;\n    }\n\n    // Don't try to save giant queries\n    if (query.length > MAX_QUERY_SIZE) {\n      return false;\n    }\n    if (!lastQuerySaved) {\n      return true;\n    }\n    if (JSON.stringify(query) === JSON.stringify(lastQuerySaved.query)) {\n      if (\n        JSON.stringify(variables) === JSON.stringify(lastQuerySaved.variables)\n      ) {\n        if (\n          JSON.stringify(headers) === JSON.stringify(lastQuerySaved.headers)\n        ) {\n          return false;\n        }\n        if (headers && !lastQuerySaved.headers) {\n          return false;\n        }\n      }\n      if (variables && !lastQuerySaved.variables) {\n        return false;\n      }\n    }\n    return true;\n  }\n\n  updateHistory = (\n    query?: string,\n    variables?: string,\n    headers?: string,\n    operationName?: string,\n  ) => {\n    if (\n      this.shouldSaveQuery(\n        query,\n        variables,\n        headers,\n        this.history.fetchRecent(),\n      )\n    ) {\n      this.history.push({\n        query,\n        variables,\n        headers,\n        operationName,\n      });\n      const historyQueries = this.history.items;\n      const favoriteQueries = this.favorite.items;\n      this.queries = historyQueries.concat(favoriteQueries);\n    }\n  };\n\n  toggleFavorite(\n    query?: string,\n    variables?: string,\n    headers?: string,\n    operationName?: string,\n    label?: string,\n    favorite?: boolean,\n  ) {\n    const item: QueryStoreItem = {\n      query,\n      variables,\n      headers,\n      operationName,\n      label,\n    };\n    if (!this.favorite.contains(item)) {\n      item.favorite = true;\n      this.favorite.push(item);\n    } else if (favorite) {\n      item.favorite = false;\n      this.favorite.delete(item);\n    }\n    this.queries = [...this.history.items, ...this.favorite.items];\n  }\n\n  editLabel(\n    query?: string,\n    variables?: string,\n    headers?: string,\n    operationName?: string,\n    label?: string,\n    favorite?: boolean,\n  ) {\n    const item = {\n      query,\n      variables,\n      headers,\n      operationName,\n      label,\n    };\n    if (favorite) {\n      this.favorite.edit({ ...item, favorite });\n    } else {\n      this.history.edit(item);\n    }\n    this.queries = [...this.history.items, ...this.favorite.items];\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAAA;AAAA;AAAA,UAAM,IAAI,MAAM,kFAAkF;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACO5F,SAAU,UAAa,OAAuB;AAClD,SACE,OAAO,UAAU,YACjB,UAAU,QACV,OAAO,MAAM,SAAS;AAE1B;AAGA,SAAS,oBAAuB,YAAyB;AACvD,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,UAAM,eAAe,WAAW,UAAU;MACxC,MAAM,OAAI;AACR,gBAAQ,CAAC;AACT,qBAAa,YAAW;MAC1B;MACA,OAAO;MACP,UAAU,MAAK;AACb,eAAO,IAAI,MAAM,mBAAmB,CAAC;MACvC;KACD;EACH,CAAC;AACH;AAGM,SAAU,aAAgB,OAAU;AACxC,SACE,OAAO,UAAU,YACjB,UAAU,QACV,eAAe,SACf,OAAO,MAAM,cAAc;AAE/B;AAEM,SAAU,gBACd,OAAc;AAEd,SACE,OAAO,UAAU,YACjB,UAAU,SAGR,MAAc,OAAO,WAAW,MAAM,oBACtC,OAAO,iBAAiB;AAE9B;AAEA,SAAe,uBACb,OAAkD;;;AAKlD,UAAM,kBAAiB,MACrB,YAAY,QAAQ,QAAQ,MAAM,OAAO,aAAa,EAAC,GACvD,YAAM,QAAA,OAAA,SAAA,SAAA,GAAE,KAAK,KAAK;AACpB,UAAM,gBACJ,UAAU,QAAQ,QAAQ,MAAM,OAAO,aAAa,EAAC,GACrD,KAAK,KAAK,KAAK;AAEjB,UAAM,SAAS,MAAM,aAAY;AAEjC,UAAK,mBAAc,QAAd,mBAAc,SAAA,SAAd,eAAc;AACnB,WAAO,OAAO;;;AAGV,SAAgB,uBACpB,eAAgC;;AAEhC,UAAM,SAAS,MAAM;AACrB,QAAI,gBAAgB,MAAM,GAAG;AAC3B,aAAO,uBAAuB,MAAM;;AAEtC,QAAI,aAAa,MAAM,GAAG;AACxB,aAAO,oBAAoB,MAAM;;AAEnC,WAAO;EACT,CAAC;;;;ACpFD,eAAe,EAAEA,IAAE,GAAE;AAAC,MAAI,IAAEA,GAAE,QAAQ,cAAc;AAAE,MAAG,CAAC,KAAG,CAAC,CAAC,EAAE,QAAQ,YAAY;AAAE,WAAOA;AAAE,MAAI,IAAE,EAAE,QAAQ,WAAW,GAAE,IAAE;AAAI,MAAG,CAAC,GAAE;AAAC,QAAIA,KAAE,IAAE,GAAEC,KAAE,EAAE,QAAQ,KAAID,EAAC;AAAE,QAAE,EAAE,MAAMA,IAAEC,KAAE,KAAGA,KAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,MAAK,EAAE;AAAA,EAAC;AAAC,SAAO,iBAAgBD,IAAEC,IAAEC,IAAE;AAAC,QAAIC,IAAEC,IAAE,GAAE,IAAE,CAACF,MAAG,CAACA,GAAE,UAAS,IAAE,OAAO,WAAWD,EAAC,GAAE,IAAE,OAAO,MAAM,CAAC,GAAE,IAAE,CAAC;AAAE;AAAE,qBAAcC,MAAKF,IAAE;AAAC,QAAAG,KAAE,EAAE,YAAW,IAAE,OAAO,OAAO,CAAC,GAAED,EAAC,CAAC;AAAE,YAAIF,KAAEE,GAAE,QAAQD,EAAC;AAAE,aAAI,CAACD,KAAEG,MAAGH,KAAEG,KAAE,EAAE,QAAQF,EAAC,GAAE,IAAE,CAAC,GAAE,CAACE,MAAG;AAAC,cAAIH,KAAE,EAAE,SAAS,GAAEG,EAAC,GAAED,KAAE,EAAE,SAASC,KAAE,CAAC;AAAE,cAAGC,IAAE;AAAC,gBAAIH,KAAED,GAAE,QAAQ,UAAU,IAAE,GAAEG,KAAEH,GAAE,YAAY,QAAOC,EAAC,GAAEG,KAAE,OAAGC,KAAEL,GAAE,SAASC,IAAEE,KAAE,KAAG,SAAOA,EAAC,GAAEG,KAAE,OAAON,GAAE,SAAS,GAAEC,EAAC,CAAC,EAAE,KAAK,EAAE,MAAM,MAAM,GAAE,IAAE,CAAC,GAAE,IAAEK,GAAE;AAAO,mBAAK,IAAEA,GAAE,EAAE,CAAC,GAAE,IAAE,EAAE,MAAM,IAAI,GAAE,EAAE,EAAE,MAAM,EAAE,YAAY,CAAC,IAAE,EAAE,KAAK,IAAI;AAAE;AAAC,gBAAG,IAAE,EAAE,cAAc,GAAE,KAAG,CAAC,EAAE,QAAQ,kBAAkB;AAAE,kBAAG;AAAC,gBAAAD,KAAE,KAAK,MAAM,OAAOA,EAAC,CAAC,GAAED,KAAE;AAAA,cAAE,SAAOJ,IAAE;AAAA,cAAC;AAAC,gBAAG,IAAE,EAAC,SAAQ,GAAE,MAAKK,IAAE,MAAKD,GAAC,GAAE,IAAE,MAAM,IAAE,EAAE,KAAK,CAAC,GAAE,OAAKF,GAAE,CAAC,KAAG,OAAKA,GAAE,CAAC;AAAE,oBAAM;AAAA,UAAC;AAAM,YAAAD,KAAE,SAAOA,IAAEG,KAAE,KAAG;AAAE,cAAEF,IAAEC,KAAE,EAAE,QAAQF,EAAC;AAAA,QAAC;AAAC,UAAE,WAAS,MAAM;AAAA,MAAE;AAAC,MAAE,WAAS,MAAM;AAAA,EAAE,EAAED,IAAE,KAAK,CAAC,IAAG,CAAC;AAAC;;;ACO39B,SAAS,aAAa,QAAQ,UAAU,SAAS;AAC7C,QAAM,SAAU,gBAAgB,mBAAmB;AAC/C,WAAO;AAAA,EACX,EAAG;AACH,QAAM,iBAAiB,OAAO,OAAO,KAAK,MAAM;AAChD,MAAI,UAAU;AACV,WAAO,SAAS,IAAI,SAAS;AACzB,eAAS;AACT,aAAO,eAAe,GAAG,IAAI;AAAA,IACjC;AAAA,EACJ;AACA,MAAI,SAAS;AACT,UAAM,gBAAgB,OAAO,MAAM,KAAK,MAAM;AAC9C,WAAO,QAAQ,CAAC,QAAQ;AACpB,cAAQ,GAAG;AACX,aAAO,cAAc,GAAG;AAAA,IAC5B;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,iBAAiB;AACtB,QAAM,IAAI,CAAC;AACX,IAAE,UAAU,IAAI,QAAQ,CAAC,SAAS,WAAW;AACzC,MAAE,UAAU;AACZ,MAAE,SAAS;AAAA,EACf,CAAC;AACD,SAAO;AACX;AAQA,SAAS,oCAAoC;AACzC,MAAI,QAAQ;AAAA,IACR,MAAM;AAAA;AAAA,EACV;AACA,MAAI,OAAO,eAAe;AAC1B,QAAM,SAAS,CAAC;AAChB,WAAS,UAAU,OAAO;AACtB,QAAI,MAAM,SAAS,WAAyB;AACxC;AAAA,IACJ;AACA,WAAO,KAAK,KAAK;AACjB,SAAK,QAAQ;AACb,WAAO,eAAe;AAAA,EAC1B;AACA,QAAM,SAAU,gBAAgB,gCAAgC;AAC5D,WAAO,MAAM;AACT,UAAI,OAAO,SAAS,GAAG;AAEnB,cAAM,OAAO,MAAM;AAAA,MACvB,OACK;AACD,YAAI,MAAM,SAAS,SAAqB;AACpC,gBAAM,MAAM;AAAA,QAChB;AACA,YAAI,MAAM,SAAS,YAA2B;AAC1C;AAAA,QACJ;AACA,cAAM,KAAK;AAAA,MACf;AAAA,IACJ;AAAA,EACJ,EAAG;AACH,QAAM,SAAS,aAAa,QAAQ,MAAM;AACtC,QAAI,MAAM,SAAS,WAAyB;AACxC;AAAA,IACJ;AACA,YAAQ;AAAA,MACJ,MAAM;AAAA;AAAA,IACV;AACA,SAAK,QAAQ;AAAA,EACjB,GAAG,CAAC,UAAU;AACV,QAAI,MAAM,SAAS,WAAyB;AACxC;AAAA,IACJ;AACA,YAAQ;AAAA,MACJ,MAAM;AAAA,MACN;AAAA,IACJ;AACA,SAAK,QAAQ;AAAA,EACjB,CAAC;AACD,SAAO;AAAA,IACH;AAAA,IACA,uBAAuB;AAAA,EAC3B;AACJ;AAEA,IAAM,oCAAoC,CAAC,SAAS;AAChD,QAAM,EAAE,WAAW,sBAAsB,IAAI,kCAAkC;AAC/E,QAAM,UAAU,KAAK;AAAA,IACjB,MAAM,CAAC,UAAU;AACb,gBAAU,KAAK;AAAA,IACnB;AAAA,IACA,UAAU,MAAM;AAEZ,4BAAsB,OAAO;AAAA,IACjC;AAAA,IACA,OAAO,CAAC,QAAQ;AAEZ,4BAAsB,MAAM,GAAG;AAAA,IACnC;AAAA,EACJ,CAAC;AAED,QAAM,iBAAiB,sBAAsB;AAC7C,MAAI,cAAc;AAClB,wBAAsB,SAAS,MAAM;AACjC,QAAI,gBAAgB,QAAW;AAC3B,cAAQ;AACR,oBAAc,eAAe;AAAA,IACjC;AACA,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAqBA,SAASO,iBAAgB,OAAO;AAC5B,SAAQ,OAAO,UAAU,YACrB,UAAU;AAAA;AAAA;AAAA;AAAA,GAKT,MAAM,OAAO,WAAW,MAAM,oBAC1B,OAAO,iBAAiB,OAAO,iBAAiB;AAC7D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrIA,IAAM,eAAe,CAAC,QAAyC;AAC7D,SAAO,OAAO,QAAQ,YAAY,QAAQ,QAAQ,UAAU;AAC9D;AASO,IAAM,yBAAyB,CACpC,UACA,SACW;AACX,MAAI,iBAAiB;AACrB,QAAM,UAAU;IACd,oBAAoB,MAAI;;AACtB,UAAI,WAAS,KAAA,KAAK,UAAI,QAAA,OAAA,SAAA,SAAA,GAAE,UAAS,KAAK,cAAc,gBAAgB;AAClE,yBAAiB;;IAErB;GACD;AACD,SAAO;AACT;AAUO,IAAM,sBACX,CAAC,SAA+B,cAChC,CAAO,eAA8B,gBAA6BC,WAAA,QAAA,QAAA,QAAA,aAAA;AAChE,QAAM,OAAO,MAAM,UAAU,QAAQ,KAAK;IACxC,QAAQ;IACR,MAAM,KAAK,UAAU,aAAa;IAClC,SAAO,OAAA,OAAA,OAAA,OAAA,EACL,gBAAgB,mBAAkB,GAC/B,QAAQ,OAAO,GACf,gBAAW,QAAX,gBAAW,SAAA,SAAX,YAAa,OAAO;GAE1B;AACD,SAAO,KAAK,KAAI;AAClB,CAAC;AAEI,IAAM,iCAAiC,CAC5C,KACA,qBACE;AACF,MAAI;AACJ,MAAI;AACF,UAAM,EAAE,aAAY,IAAK;AAKzB,eAAW,aAAa;MACtB;MACA;KACD;AACD,WAAO,kCAAkC,QAAQ;WAC1C,KAAK;AACZ,QAAI,aAAa,GAAG,KAAK,IAAI,SAAS,oBAAoB;AACxD,YAAM,IAAI,MACR,iGAAiG;;AAIrG,YAAQ,MAAM,uCAAuC,GAAG,IAAI,GAAG;;AAEnE;AAQO,IAAM,oCACX,CAAC,aAAqB,CAAC,kBACrB,kCAAmD,UACjD,SAAS,UAAU,eAAa,OAAA,OAAA,OAAA,OAAA,CAAA,GAC3B,IAAI,GAAA,EACP,OAAO,SAAM;AACX,MAAI,eAAe,YAAY;AAC7B,SAAK,MACH,IAAI,MACF,4BAA4B,IAAI,IAAI,IAClC,IAAI,UAAU,EAChB,GAAG,KAAI,CAAE,CACV;SAEE;AACL,SAAK,MAAM,GAAG;;AAElB,EAAC,CAAA,CAAA,CACD;AAUD,IAAM,gCACX,CAAC,mBACD,CAAC,kBAAgC;AAC/B,QAAM,aAAa,eAAe,QAAQ,aAAa;AACvD,SAAO,kCAEL,UAAQ,WAAW,UAAU,IAAI,EAAE,WAAW;AAElD;AAQK,IAAM,yBAAyB,CACpC,SACA,cAEA,SAAiB,eAA8B,aAAyB;;;AACtE,UAAM,WAAW,MAAA,QAAM,UAAU,QAAQ,KAAK;MAC5C,QAAQ;MACR,MAAM,KAAK,UAAU,aAAa;MAClC,SAAO,OAAA,OAAA,OAAA,OAAA,EACL,gBAAgB,oBAChB,QAAQ,oCAAmC,GACxC,QAAQ,OAAO,GAGf,gBAAW,QAAX,gBAAW,SAAA,SAAX,YAAa,OAAO;KAE1B,EAAE,KAAK,OACN,EAA6D,GAAG;MAC9D,UAAU;KACX,CAAC,CACH;AAGD,QAAI,CAACC,iBAAgB,QAAQ,GAAG;AAC9B,aAAA,MAAA,QAAO,MAAA,MAAA,QAAM,SAAS,KAAI,CAAE,CAAA;;;AAG9B,eAA0B,aAAA,cAAA,QAAQ,GAAA,cAAA,eAAA,MAAA,QAAA,WAAA,KAAA,CAAA,GAAA,CAAA,aAAA,QAAA;AAAvB,cAAM,QAAK,aAAA;AACpB,YAAI,MAAM,KAAK,UAAQ,CAAC,KAAK,IAAI,GAAG;AAClC,gBAAM,UAAU,MAAM,IACpB,UAAQ;EAAc,KAAK,OAAO;;;EAAe,KAAK,IAAI,EAAE;AAE9D,gBAAM,IAAI,MACR;EAAuD,OAAO,EAAE;;AAGpE,cAAA,MAAA,QAAM,MAAM,IAAI,UAAQ,KAAK,IAAI,CAAC;;;;;;;;;;;;;EAEtC,CAAC;;AAOI,IAAM,eAAe,CAC1B,SACA,gBACE;AACF,MAAI,QAAQ,UAAU;AACpB,WAAO,kCAAkC,QAAQ,QAAQ;;AAE3D,MAAI,QAAQ,iBAAiB;AAC3B,WAAO,+BAA+B,QAAQ,iBAAe,OAAA,OAAA,OAAA,OAAA,CAAA,GACxD,QAAQ,kBAAkB,GAC1B,gBAAW,QAAX,gBAAW,SAAA,SAAX,YAAa,OAAO,CAAA;;AAG3B,QAAM,yBAAyB,QAAQ,gBAAgB,QAAQ;AAC/D,MAAI,wBAAwB;AAC1B,WAAO,8BAA8B,sBAAsB;;AAE/D;;;AChMM,SAAU,sBAAsB,SAA6B;AACjE,MAAI;AACJ,MAAI,OAAO,WAAW,eAAe,OAAO,OAAO;AACjD,gBAAY,OAAO;;AAErB,OACE,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,+BAA8B,QACvC,QAAQ,8BAA8B,OACtC;AACA,YAAQ,4BAA4B;;AAEtC,MAAI,QAAQ,OAAO;AACjB,gBAAY,QAAQ;;AAEtB,MAAI,CAAC,WAAW;AACd,UAAM,IAAI,MAAM,2CAA2C;;AAG7D,QAAM,gBAAgB,oBAAoB,SAAS,SAAS;AAE5D,QAAM,cAAc,QAAQ,4BACxB,uBAAuB,SAAS,SAAS,IACzC;AAEJ,SAAO,CAAC,eAAe,gBAAe;AACpC,QAAI,cAAc,kBAAkB,sBAAsB;AACxD,cAAQ,QAAQ,iBAAiB,eAC/B,eACA,WAAW;;AAGf,UAAM,kBAAiB,gBAAW,QAAX,gBAAW,SAAA,SAAX,YAAa,eAChC,uBACE,YAAY,aACZ,cAAc,iBAAiB,MAAS,IAE1C;AACJ,QAAI,gBAAgB;AAClB,YAAM,YAAY,aAAa,SAAS,WAAW;AAEnD,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MACR,2FACE,QAAQ,kBACJ,gBAAgB,QAAQ,eAAe,YACvC,wEACN,EAAE;;AAGN,aAAO,UAAU,aAAa;;AAEhC,WAAO,YAAY,eAAe,WAAW;EAC/C;AACF;;;ACtEA,SAAS,UAAU,KAAY;AAC7B,SAAO,KAAK,UAAU,KAAK,MAAM,CAAC;AACpC;AAEA,SAAS,kBAAkB,OAAY;AACrC,SAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GACK,KAAK,GAAA,EAER,SAAS,MAAM,SACf,OAAO,MAAM,MAAK,CAAA;AAEtB;AAEA,SAAS,kBAAkB,OAAc;AACvC,MAAI,iBAAiB,OAAO;AAC1B,WAAO,kBAAkB,KAAK;;AAEhC,SAAO;AACT;AAEM,SAAU,YAAY,OAAc;AACxC,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,WAAO,UAAU;MACf,QAAQ,MAAM,IAAI,CAAAC,OAAK,kBAAkBA,EAAC,CAAC;KAC5C;;AAEH,SAAO,UAAU,EAAE,QAAQ,CAAC,kBAAkB,KAAK,CAAC,EAAC,CAAE;AACzD;AAEM,SAAU,aAAa,QAAW;AACtC,SAAO,UAAU,MAAM;AACzB;;;ACCM,SAAU,UACd,QACA,WACA,sBAA6C;AAE7C,QAAM,aAA0B,CAAA;AAEhC,MAAI,CAAC,UAAU,CAAC,WAAW;AACzB,WAAO,EAAE,YAAY,QAAQ,UAAS;;AAGxC,MAAI;AACJ,MAAI;AACF,UAAM,MAAM,SAAS;WACrB,IAAM;AACN,WAAO,EAAE,YAAY,QAAQ,UAAS;;AAGxC,QAAM,cAAc,wBAAwB;AAC5C,QAAM,WAAW,IAAI,SAAS,MAAM;AACpC,QAAM,KAAK;IACT,MAAM,MAAI;AACR,eAAS,MAAM,IAAI;IACrB;IACA,MAAM,MAAI;AACR,eAAS,MAAM,IAAI;AACnB,UAAI,KAAK,SAAS,WAAW,CAAC,KAAK,cAAc;AAC/C,cAAM,YAAY,SAAS,QAAO;AAClC,cAAM,eAAe,kBACnB,YAAY,SAAS,GACrB,WAAW;AAEb,YAAI,gBAAgB,KAAK,KAAK;AAC5B,gBAAM,SAAS,eAAe,WAAW,KAAK,IAAI,KAAK;AACvD,qBAAW,KAAK;YACd,OAAO,KAAK,IAAI;YAChB,QAAQ,MAAM,MAAM,YAAY,EAAE,WAAW,MAAM,OAAO,MAAM;WACjE;;;IAGP;GACD;AAGD,SAAO;IACL;IACA,QAAQ,eAAe,WAAW,UAAU;;AAEhD;AAKA,SAAS,4BAA4B,MAAiB;AAGpD,MAAI,EAAE,eAAe,OAAO;AAC1B,WAAO,CAAA;;AAGT,QAAM,SAAS,KAAK,UAAS;AAG7B,MAAI,OAAO,IAAI;AACb,WAAO,CAAC,IAAI;;AAId,MAAI,OAAO,OAAO;AAChB,WAAO,CAAC,OAAO;;AAIjB,MAAI,OAAO,MAAM;AACf,WAAO,CAAC,MAAM;;AAIhB,QAAM,iBAAgC,CAAA;AACtC,aAAW,aAAa,OAAO,KAAK,MAAM,GAAG;AAC3C,QAAI,WAAW,OAAO,SAAS,EAAE,IAAI,GAAG;AACtC,qBAAe,KAAK,SAAS;;;AAGjC,SAAO;AACT;AAIA,SAAS,kBACP,MACA,sBAA4C;AAG5C,QAAM,YAAY,aAAa,IAAI;AAGnC,MAAI,CAAC,QAAQ,WAAW,IAAI,GAAG;AAC7B;;AAIF,QAAM,aAAa,qBAAqB,SAAS;AAGjD,MACE,CAAC,MAAM,QAAQ,UAAU,KACzB,WAAW,WAAW,KACtB,EAAE,eAAe,YACjB;AACA;;AAIF,SAAO;IACL,MAAM,KAAK;IACX,YAAY,WAAW,IAAI,eAAY;AACrC,YAAM,WAAW,UAAU,UAAS,EAAG,SAAS;AAChD,YAAM,YAAY,WAAW,SAAS,OAAO;AAC7C,aAAO;QACL,MAAM,KAAK;QACX,MAAM;UACJ,MAAM,KAAK;UACX,OAAO;;QAIT,cAAc,kBACZ,WACA,oBAAoB;;IAG1B,CAAC;;AAEL;AAIA,SAAS,eAAe,SAAiB,YAAuB;AAC9D,MAAI,WAAW,WAAW,GAAG;AAC3B,WAAO;;AAET,MAAI,SAAS;AACb,MAAI,YAAY;AAChB,aAAW,EAAE,OAAO,OAAM,KAAM,YAAY;AAC1C,cAAU,QAAQ,MAAM,WAAW,KAAK,IAAI;AAC5C,gBAAY;;AAEd,YAAU,QAAQ,MAAM,SAAS;AACjC,SAAO;AACT;AAIA,SAAS,eAAe,KAAa,OAAa;AAChD,MAAI,cAAc;AAClB,MAAI,YAAY;AAChB,SAAO,aAAa;AAClB,UAAM,IAAI,IAAI,WAAW,cAAc,CAAC;AAExC,QAAI,MAAM,MAAM,MAAM,MAAM,MAAM,QAAU,MAAM,MAAQ;AACxD;;AAEF;AAEA,QAAI,MAAM,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,KAAK;AAC5D,kBAAY;;;AAGhB,SAAO,IAAI,MAAM,aAAa,SAAS;AACzC;AAEA,SAAS,YACP,WAA+C;AAE/C,MAAI,WAAW;AACb,WAAO;;AAEX;;;ACnMA,SAAS,SACP,OACA,UAAgC;;AAEhC,QAAM,cAAc,oBAAI,IAAG;AAC3B,QAAM,SAA0B,CAAA;AAChC,aAAW,QAAQ,OAAO;AACxB,QAAI,KAAK,SAAS,SAAS;AACzB,YAAM,cAAc,SAAS,IAAI;AACjC,YAAM,WAAW,YAAY,IAAI,WAAW;AAC5C,WAAI,KAAA,KAAK,gBAAU,QAAA,OAAA,SAAA,SAAA,GAAE,QAAQ;AAE3B,cAAM,YAAS,OAAA,OAAA,CAAA,GAAQ,IAAI;AAC3B,eAAO,KAAK,SAAS;kBACZ,aAAQ,QAAR,aAAQ,SAAA,SAAR,SAAU,iBAAgB,KAAK,cAAc;AAEtD,iBAAS,aAAa,aAAa;UACjC,GAAG,SAAS,aAAa;UACzB,GAAG,KAAK,aAAa;;iBAEd,CAAC,UAAU;AACpB,cAAM,YAAS,OAAA,OAAA,CAAA,GAAQ,IAAI;AAC3B,oBAAY,IAAI,aAAa,SAAS;AACtC,eAAO,KAAK,SAAS;;WAElB;AACL,aAAO,KAAK,IAAI;;;AAGpB,SAAO;AACT;AAEA,SAAS,8BACP,qBAGA,YACA,kBAA2C;;AAE3C,QAAM,uBAAuB,mBACzB,aAAa,gBAAgB,EAAE,OAC/B;AACJ,QAAM,mBAAmB,CAAA;AACzB,QAAM,cAAwB,CAAA;AAC9B,WAAS,aAAa,YAAY;AAChC,QAAI,UAAU,SAAS,kBAAkB;AACvC,YAAM,eAAe,UAAU,KAAK;AACpC,UAAI,CAAC,UAAU,cAAc,UAAU,WAAW,WAAW,GAAG;AAC9D,YAAI,YAAY,SAAS,YAAY,GAAG;AAEtC;eACK;AACL,sBAAY,KAAK,YAAY;;;AAGjC,YAAM,qBAAqB,oBAAoB,UAAU,KAAK,KAAK;AACnE,UAAI,oBAAoB;AACtB,cAAM,EAAE,eAAe,YAAY,aAAY,IAAK;AACpD,oBAAY;UACV,MAAM,KAAK;UACX;UACA;UACA;;;;AAIN,QACE,UAAU,SAAS,KAAK,oBAEvB,CAAC,UAAU,gBAAc,KAAA,UAAU,gBAAU,QAAA,OAAA,SAAA,SAAA,GAAE,YAAW,IAC3D;AACA,YAAM,mBAAmB,UAAU,gBAC/B,UAAU,cAAc,KAAK,QAC7B;AACJ,UAAI,CAAC,oBAAoB,qBAAqB,sBAAsB;AAClE,yBAAiB,KACf,GAAG,8BACD,qBACA,UAAU,aAAa,YACvB,gBAAgB,CACjB;AAEH;;;AAGJ,qBAAiB,KAAK,SAAS;;AAEjC,SAAO;AACT;AAKM,SAAU,SACd,aACA,QAA6B;AAI7B,QAAM,WAAW,SAAS,IAAI,SAAS,MAAM,IAAI;AAEjD,QAAM,sBAEF,uBAAO,OAAO,IAAI;AAEtB,aAAW,cAAc,YAAY,aAAa;AAChD,QAAI,WAAW,SAAS,KAAK,qBAAqB;AAChD,0BAAoB,WAAW,KAAK,KAAK,IAAI;;;AAIjD,QAAM,WAAuB;IAC3B,aAAa,MAAS;AACpB,YAAM,mBAAmB,WAAW,SAAS,cAAa,IAAK;AAC/D,UAAI,EAAE,WAAU,IAAK;AAErB,mBAAa,8BACX,qBACA,YACA,gBAAgB;AAGlB,mBAAa,SAAS,YAAY,eAChC,UAAU,QAAQ,UAAU,MAAM,QAAQ,UAAU,KAAK,KAAK;AAGhE,aAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GACK,IAAI,GAAA,EACP,WAAU,CAAA;IAEd;IACA,qBAAkB;AAChB,aAAO;IACT;;AAGF,SAAO,MACL,aACA,WAAW,kBAAkB,UAAU,QAAQ,IAAI,QAAQ;AAE/D;;;ACrJM,SAAU,yBACd,gBACA,2BACA,YAAsC;AAGtC,MAAI,CAAC,cAAc,WAAW,SAAS,GAAG;AACxC;;AAIF,QAAM,QAAQ,WAAW,IAAI,QAAK;AAAA,QAAA;AAAC,YAAA,KAAA,GAAG,UAAI,QAAA,OAAA,SAAA,SAAA,GAAE;EAAK,CAAA;AACjD,MAAI,6BAA6B,MAAM,SAAS,yBAAyB,GAAG;AAC1E,WAAO;;AAIT,MAAI,6BAA6B,gBAAgB;AAC/C,UAAM,YAAY,eAAe,IAAI,QAAK;AAAA,UAAA;AAAC,cAAA,KAAA,GAAG,UAAI,QAAA,OAAA,SAAA,SAAA,GAAE;IAAK,CAAA;AACzD,UAAM,YAAY,UAAU,QAAQ,yBAAyB;AAC7D,QAAI,cAAc,MAAM,YAAY,MAAM,QAAQ;AAChD,aAAO,MAAM,SAAS;;;AAK1B,SAAO,MAAM,CAAC;AAChB;;;ACGA,SAAS,aAAa,SAAkBC,IAAU;AAChD,SACEA,cAAa,iBAEZA,GAAE,SAAS,MAEVA,GAAE,SAAS,QAGXA,GAAE,SAAS,wBAEXA,GAAE,SAAS,iCAEb,QAAQ,WAAW;AAEvB;AAEM,IAAO,aAAP,MAAiB;EAGrB,YAAY,SAAwB;AAClC,QAAI,SAAS;AACX,WAAK,UAAU;eACN,YAAY,MAAM;AAE3B,WAAK,UAAU;eACN,OAAO,WAAW,aAAa;AACxC,WAAK,UAAU;WACV;AACL,WAAK,UAAU;QACb,SAAS,OAAO,aAAa,QAAQ,KAAK,OAAO,YAAY;QAC7D,SAAS,OAAO,aAAa,QAAQ,KAAK,OAAO,YAAY;QAC7D,YAAY,OAAO,aAAa,WAAW,KAAK,OAAO,YAAY;QAEnE,IAAI,SAAM;AACR,cAAI,OAAO;AACX,qBAAW,OAAO,OAAO,cAAc;AACrC,gBAAI,IAAI,QAAQ,GAAG,iBAAiB,GAAG,MAAM,GAAG;AAC9C,sBAAQ;;;AAGZ,iBAAO;QACT;QAEA,OAAO,MAAK;AAEV,qBAAW,OAAO,OAAO,cAAc;AACrC,gBAAI,IAAI,QAAQ,GAAG,iBAAiB,GAAG,MAAM,GAAG;AAC9C,qBAAO,aAAa,WAAW,GAAG;;;QAGxC;;;EAGN;EAEA,IAAI,MAAY;AACd,QAAI,CAAC,KAAK,SAAS;AACjB,aAAO;;AAGT,UAAM,MAAM,GAAG,iBAAiB,IAAI,IAAI;AACxC,UAAM,QAAQ,KAAK,QAAQ,QAAQ,GAAG;AAEtC,QAAI,UAAU,UAAU,UAAU,aAAa;AAC7C,WAAK,QAAQ,WAAW,GAAG;AAC3B,aAAO;;AAGT,WAAO,SAAS;EAClB;EAEA,IACE,MACA,OAAa;AAEb,QAAI,aAAa;AACjB,QAAI,QAAsB;AAE1B,QAAI,KAAK,SAAS;AAChB,YAAM,MAAM,GAAG,iBAAiB,IAAI,IAAI;AACxC,UAAI,OAAO;AACT,YAAI;AACF,eAAK,QAAQ,QAAQ,KAAK,KAAK;iBACxBA,IAAG;AACV,kBAAQA,cAAa,QAAQA,KAAI,IAAI,MAAM,GAAGA,EAAC,EAAE;AACjD,uBAAa,aAAa,KAAK,SAASA,EAAC;;aAEtC;AAEL,aAAK,QAAQ,WAAW,GAAG;;;AAI/B,WAAO,EAAE,cAAc,YAAY,MAAK;EAC1C;EAEA,QAAK;AACH,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ,MAAK;;EAEtB;;AAGF,IAAM,oBAAoB;;;ACjIpB,IAAO,aAAP,MAAiB;EAGrB,YACU,KACA,SACA,UAAyB,MAAI;AAF7B,SAAA,MAAA;AACA,SAAA,UAAA;AACA,SAAA,UAAA;AAER,SAAK,QAAQ,KAAK,SAAQ;EAC5B;EAEA,IAAI,SAAM;AACR,WAAO,KAAK,MAAM;EACpB;EAEA,SAAS,MAAoB;AAC3B,WAAO,KAAK,MAAM,KAChB,OACE,EAAE,UAAU,KAAK,SACjB,EAAE,cAAc,KAAK,aACrB,EAAE,YAAY,KAAK,WACnB,EAAE,kBAAkB,KAAK,aAAa;EAE5C;EAEA,KAAK,MAAoB;AACvB,UAAM,YAAY,KAAK,MAAM,UAC3B,OACE,EAAE,UAAU,KAAK,SACjB,EAAE,cAAc,KAAK,aACrB,EAAE,YAAY,KAAK,WACnB,EAAE,kBAAkB,KAAK,aAAa;AAE1C,QAAI,cAAc,IAAI;AACpB,WAAK,MAAM,OAAO,WAAW,GAAG,IAAI;AACpC,WAAK,KAAI;;EAEb;EAEA,OAAO,MAAoB;AACzB,UAAM,YAAY,KAAK,MAAM,UAC3B,OACE,EAAE,UAAU,KAAK,SACjB,EAAE,cAAc,KAAK,aACrB,EAAE,YAAY,KAAK,WACnB,EAAE,kBAAkB,KAAK,aAAa;AAE1C,QAAI,cAAc,IAAI;AACpB,WAAK,MAAM,OAAO,WAAW,CAAC;AAC9B,WAAK,KAAI;;EAEb;EAEA,cAAW;AACT,WAAO,KAAK,MAAM,GAAG,EAAE;EACzB;EAEA,WAAQ;AACN,UAAM,MAAM,KAAK,QAAQ,IAAI,KAAK,GAAG;AACrC,QAAI,KAAK;AACP,aAAO,KAAK,MAAM,GAAG,EAAE,KAAK,GAAG;;AAEjC,WAAO,CAAA;EACT;EAEA,KAAK,MAAoB;AACvB,UAAM,QAAQ,CAAC,GAAG,KAAK,OAAO,IAAI;AAElC,QAAI,KAAK,WAAW,MAAM,SAAS,KAAK,SAAS;AAC/C,YAAM,MAAK;;AAGb,aAAS,WAAW,GAAG,WAAW,GAAG,YAAY;AAC/C,YAAM,WAAW,KAAK,QAAQ,IAC5B,KAAK,KACL,KAAK,UAAU,EAAE,CAAC,KAAK,GAAG,GAAG,MAAK,CAAE,CAAC;AAEvC,UAAI,EAAC,aAAQ,QAAR,aAAQ,SAAA,SAAR,SAAU,QAAO;AACpB,aAAK,QAAQ;iBACJ,SAAS,gBAAgB,KAAK,SAAS;AAEhD,cAAM,MAAK;aACN;AACL;;;EAGN;EAEA,OAAI;AACF,SAAK,QAAQ,IAAI,KAAK,KAAK,KAAK,UAAU,EAAE,CAAC,KAAK,GAAG,GAAG,KAAK,MAAK,CAAE,CAAC;EACvE;;;;AChGF,IAAM,iBAAiB;AAEjB,IAAO,eAAP,MAAmB;EAKvB,YAAoB,SAA6B,kBAAwB;AAArD,SAAA,UAAA;AAA6B,SAAA,mBAAA;AAuDjD,SAAA,gBAAgB,CACd,OACA,WACA,SACA,kBACE;AACF,UACE,KAAK,gBACH,OACA,WACA,SACA,KAAK,QAAQ,YAAW,CAAE,GAE5B;AACA,aAAK,QAAQ,KAAK;UAChB;UACA;UACA;UACA;SACD;AACD,cAAM,iBAAiB,KAAK,QAAQ;AACpC,cAAM,kBAAkB,KAAK,SAAS;AACtC,aAAK,UAAU,eAAe,OAAO,eAAe;;IAExD;AA9EE,SAAK,UAAU,IAAI,WACjB,WACA,KAAK,SACL,KAAK,gBAAgB;AAGvB,SAAK,WAAW,IAAI,WAAW,aAAa,KAAK,SAAS,IAAI;AAE9D,SAAK,UAAU,CAAC,GAAG,KAAK,QAAQ,SAAQ,GAAI,GAAG,KAAK,SAAS,SAAQ,CAAE;EACzE;EAEQ,gBACN,OACA,WACA,SACA,gBAA+B;AAE/B,QAAI,CAAC,OAAO;AACV,aAAO;;AAGT,QAAI;AACF,YAAM,KAAK;aACX,IAAM;AACN,aAAO;;AAIT,QAAI,MAAM,SAAS,gBAAgB;AACjC,aAAO;;AAET,QAAI,CAAC,gBAAgB;AACnB,aAAO;;AAET,QAAI,KAAK,UAAU,KAAK,MAAM,KAAK,UAAU,eAAe,KAAK,GAAG;AAClE,UACE,KAAK,UAAU,SAAS,MAAM,KAAK,UAAU,eAAe,SAAS,GACrE;AACA,YACE,KAAK,UAAU,OAAO,MAAM,KAAK,UAAU,eAAe,OAAO,GACjE;AACA,iBAAO;;AAET,YAAI,WAAW,CAAC,eAAe,SAAS;AACtC,iBAAO;;;AAGX,UAAI,aAAa,CAAC,eAAe,WAAW;AAC1C,eAAO;;;AAGX,WAAO;EACT;EA4BA,eACE,OACA,WACA,SACA,eACA,OACA,UAAkB;AAElB,UAAM,OAAuB;MAC3B;MACA;MACA;MACA;MACA;;AAEF,QAAI,CAAC,KAAK,SAAS,SAAS,IAAI,GAAG;AACjC,WAAK,WAAW;AAChB,WAAK,SAAS,KAAK,IAAI;eACd,UAAU;AACnB,WAAK,WAAW;AAChB,WAAK,SAAS,OAAO,IAAI;;AAE3B,SAAK,UAAU,CAAC,GAAG,KAAK,QAAQ,OAAO,GAAG,KAAK,SAAS,KAAK;EAC/D;EAEA,UACE,OACA,WACA,SACA,eACA,OACA,UAAkB;AAElB,UAAM,OAAO;MACX;MACA;MACA;MACA;MACA;;AAEF,QAAI,UAAU;AACZ,WAAK,SAAS,KAAI,OAAA,OAAA,OAAA,OAAA,CAAA,GAAM,IAAI,GAAA,EAAE,SAAQ,CAAA,CAAA;WACjC;AACL,WAAK,QAAQ,KAAK,IAAI;;AAExB,SAAK,UAAU,CAAC,GAAG,KAAK,QAAQ,OAAO,GAAG,KAAK,SAAS,KAAK;EAC/D;;", "names": ["e", "t", "n", "r", "i", "l", "o", "isAsyncIterable", "__awaiter", "isAsyncIterable", "e", "e"]}