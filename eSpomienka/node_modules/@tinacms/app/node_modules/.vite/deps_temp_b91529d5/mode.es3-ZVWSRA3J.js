import {
  r
} from "./chunk-A4B2V6D5.js";
import {
  d
} from "./chunk-WIXYZC3K.js";
import "./chunk-MEKKV4OY.js";
import {
  list,
  onlineParser,
  p,
  t
} from "./chunk-4LQY6QSN.js";
import "./chunk-HD22INE4.js";
import "./chunk-AUZ3RYOM.js";

// node_modules/@graphiql/react/dist/mode.es3.js
d.defineMode("graphql-results", (r2) => {
  const u = onlineParser({
    eatWhitespace: (l) => l.eatSpace(),
    lexRules: o,
    parseRules: c,
    editorConfig: { tabSize: r2.tabSize }
  });
  return {
    config: r2,
    startState: u.startState,
    token: u.token,
    indent: r,
    electricInput: /^\s*[}\]]/,
    fold: "brace",
    closeBrackets: {
      pairs: '[]{}""',
      explode: "[]{}"
    }
  };
});
var o = {
  Punctuation: /^\[|]|\{|\}|:|,/,
  Number: /^-?(?:0|(?:[1-9][0-9]*))(?:\.[0-9]*)?(?:[eE][+-]?[0-9]+)?/,
  String: /^"(?:[^"\\]|\\(?:"|\/|\\|b|f|n|r|t|u[0-9a-fA-F]{4}))*"?/,
  Keyword: /^true|false|null/
};
var c = {
  Document: [p("{"), list("Entry", p(",")), p("}")],
  Entry: [t("String", "def"), p(":"), "Value"],
  Value(r2) {
    switch (r2.kind) {
      case "Number":
        return "NumberValue";
      case "String":
        return "StringValue";
      case "Punctuation":
        switch (r2.value) {
          case "[":
            return "ListValue";
          case "{":
            return "ObjectValue";
        }
        return null;
      case "Keyword":
        switch (r2.value) {
          case "true":
          case "false":
            return "BooleanValue";
          case "null":
            return "NullValue";
        }
        return null;
    }
  },
  NumberValue: [t("Number", "number")],
  StringValue: [t("String", "string")],
  BooleanValue: [t("Keyword", "builtin")],
  NullValue: [t("Keyword", "keyword")],
  ListValue: [p("["), list("Value", p(",")), p("]")],
  ObjectValue: [p("{"), list("ObjectField", p(",")), p("}")],
  ObjectField: [t("String", "property"), p(":"), "Value"]
};
//# sourceMappingURL=mode.es3-ZVWSRA3J.js.map
