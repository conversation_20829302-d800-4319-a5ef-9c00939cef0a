import {
  cu,
  hu
} from "./chunk-MEKKV4OY.js";
import "./chunk-AUZ3RYOM.js";

// node_modules/@graphiql/react/dist/lint.es.js
var U = Object.defineProperty;
var s = (h, v) => U(h, "name", { value: v, configurable: true });
function R(h, v) {
  for (var l = 0; l < v.length; l++) {
    const u = v[l];
    if (typeof u != "string" && !Array.isArray(u)) {
      for (const g in u)
        if (g !== "default" && !(g in h)) {
          const c = Object.getOwnPropertyDescriptor(u, g);
          c && Object.defineProperty(h, g, c.get ? c : {
            enumerable: true,
            get: () => u[g]
          });
        }
    }
  }
  return Object.freeze(Object.defineProperty(h, Symbol.toStringTag, { value: "Module" }));
}
s(R, "_mergeNamespaces");
var V = { exports: {} };
(function(h, v) {
  (function(l) {
    l(cu());
  })(function(l) {
    var u = "CodeMirror-lint-markers", g = "CodeMirror-lint-line-";
    function c(t, e, r) {
      var n = document.createElement("div");
      n.className = "CodeMirror-lint-tooltip cm-s-" + t.options.theme, n.appendChild(r.cloneNode(true)), t.state.lint.options.selfContain ? t.getWrapperElement().appendChild(n) : document.body.appendChild(n);
      function o(i) {
        if (!n.parentNode)
          return l.off(document, "mousemove", o);
        n.style.top = Math.max(0, i.clientY - n.offsetHeight - 5) + "px", n.style.left = i.clientX + 5 + "px";
      }
      return s(o, "position"), l.on(document, "mousemove", o), o(e), n.style.opacity != null && (n.style.opacity = 1), n;
    }
    s(c, "showTooltip");
    function L(t) {
      t.parentNode && t.parentNode.removeChild(t);
    }
    s(L, "rm");
    function _(t) {
      t.parentNode && (t.style.opacity == null && L(t), t.style.opacity = 0, setTimeout(function() {
        L(t);
      }, 600));
    }
    s(_, "hideTooltip");
    function M(t, e, r, n) {
      var o = c(t, e, r);
      function i() {
        l.off(n, "mouseout", i), o && (_(o), o = null);
      }
      s(i, "hide");
      var a = setInterval(function() {
        if (o)
          for (var f = n; ; f = f.parentNode) {
            if (f && f.nodeType == 11 && (f = f.host), f == document.body)
              return;
            if (!f) {
              i();
              break;
            }
          }
        if (!o)
          return clearInterval(a);
      }, 400);
      l.on(n, "mouseout", i);
    }
    s(M, "showTooltipFor");
    function A(t, e, r) {
      this.marked = [], e instanceof Function && (e = { getAnnotations: e }), (!e || e === true) && (e = {}), this.options = {}, this.linterOptions = e.options || {};
      for (var n in C)
        this.options[n] = C[n];
      for (var n in e)
        C.hasOwnProperty(n) ? e[n] != null && (this.options[n] = e[n]) : e.options || (this.linterOptions[n] = e[n]);
      this.timeout = null, this.hasGutter = r, this.onMouseOver = function(o) {
        P(t, o);
      }, this.waitingFor = 0;
    }
    s(A, "LintState");
    var C = {
      highlightLines: false,
      tooltips: true,
      delay: 500,
      lintOnChange: true,
      getAnnotations: null,
      async: false,
      selfContain: null,
      formatAnnotation: null,
      onUpdateLinting: null
    };
    function E(t) {
      var e = t.state.lint;
      e.hasGutter && t.clearGutter(u), e.options.highlightLines && F(t);
      for (var r = 0; r < e.marked.length; ++r)
        e.marked[r].clear();
      e.marked.length = 0;
    }
    s(E, "clearMarks");
    function F(t) {
      t.eachLine(function(e) {
        var r = e.wrapClass && /\bCodeMirror-lint-line-\w+\b/.exec(e.wrapClass);
        r && t.removeLineClass(e, "wrap", r[0]);
      });
    }
    s(F, "clearErrorLines");
    function G(t, e, r, n, o) {
      var i = document.createElement("div"), a = i;
      return i.className = "CodeMirror-lint-marker CodeMirror-lint-marker-" + r, n && (a = i.appendChild(document.createElement("div")), a.className = "CodeMirror-lint-marker CodeMirror-lint-marker-multiple"), o != false && l.on(a, "mouseover", function(f) {
        M(t, f, e, a);
      }), i;
    }
    s(G, "makeMarker");
    function I(t, e) {
      return t == "error" ? t : e;
    }
    s(I, "getMaxSeverity");
    function D(t) {
      for (var e = [], r = 0; r < t.length; ++r) {
        var n = t[r], o = n.from.line;
        (e[o] || (e[o] = [])).push(n);
      }
      return e;
    }
    s(D, "groupByLine");
    function N(t) {
      var e = t.severity;
      e || (e = "error");
      var r = document.createElement("div");
      return r.className = "CodeMirror-lint-message CodeMirror-lint-message-" + e, typeof t.messageHTML < "u" ? r.innerHTML = t.messageHTML : r.appendChild(document.createTextNode(t.message)), r;
    }
    s(N, "annotationTooltip");
    function j(t, e) {
      var r = t.state.lint, n = ++r.waitingFor;
      function o() {
        n = -1, t.off("change", o);
      }
      s(o, "abort"), t.on("change", o), e(t.getValue(), function(i, a) {
        t.off("change", o), r.waitingFor == n && (a && i instanceof l && (i = a), t.operation(function() {
          O(t, i);
        }));
      }, r.linterOptions, t);
    }
    s(j, "lintAsync");
    function k(t) {
      var e = t.state.lint;
      if (e) {
        var r = e.options, n = r.getAnnotations || t.getHelper(l.Pos(0, 0), "lint");
        if (n)
          if (r.async || n.async)
            j(t, n);
          else {
            var o = n(t.getValue(), e.linterOptions, t);
            if (!o)
              return;
            o.then ? o.then(function(i) {
              t.operation(function() {
                O(t, i);
              });
            }) : t.operation(function() {
              O(t, o);
            });
          }
      }
    }
    s(k, "startLinting");
    function O(t, e) {
      var r = t.state.lint;
      if (r) {
        var n = r.options;
        E(t);
        for (var o = D(e), i = 0; i < o.length; ++i) {
          var a = o[i];
          if (a) {
            var f = [];
            a = a.filter(function(x) {
              return f.indexOf(x.message) > -1 ? false : f.push(x.message);
            });
            for (var p = null, m = r.hasGutter && document.createDocumentFragment(), T = 0; T < a.length; ++T) {
              var d = a[T], y = d.severity;
              y || (y = "error"), p = I(p, y), n.formatAnnotation && (d = n.formatAnnotation(d)), r.hasGutter && m.appendChild(N(d)), d.to && r.marked.push(t.markText(d.from, d.to, {
                className: "CodeMirror-lint-mark CodeMirror-lint-mark-" + y,
                __annotation: d
              }));
            }
            r.hasGutter && t.setGutterMarker(i, u, G(
              t,
              m,
              p,
              o[i].length > 1,
              n.tooltips
            )), n.highlightLines && t.addLineClass(i, "wrap", g + p);
          }
        }
        n.onUpdateLinting && n.onUpdateLinting(e, o, t);
      }
    }
    s(O, "updateLinting");
    function b(t) {
      var e = t.state.lint;
      e && (clearTimeout(e.timeout), e.timeout = setTimeout(function() {
        k(t);
      }, e.options.delay));
    }
    s(b, "onChange");
    function H(t, e, r) {
      for (var n = r.target || r.srcElement, o = document.createDocumentFragment(), i = 0; i < e.length; i++) {
        var a = e[i];
        o.appendChild(N(a));
      }
      M(t, r, o, n);
    }
    s(H, "popupTooltips");
    function P(t, e) {
      var r = e.target || e.srcElement;
      if (/\bCodeMirror-lint-mark-/.test(r.className)) {
        for (var n = r.getBoundingClientRect(), o = (n.left + n.right) / 2, i = (n.top + n.bottom) / 2, a = t.findMarksAt(t.coordsChar({ left: o, top: i }, "client")), f = [], p = 0; p < a.length; ++p) {
          var m = a[p].__annotation;
          m && f.push(m);
        }
        f.length && H(t, f, e);
      }
    }
    s(P, "onMouseOver"), l.defineOption("lint", false, function(t, e, r) {
      if (r && r != l.Init && (E(t), t.state.lint.options.lintOnChange !== false && t.off("change", b), l.off(t.getWrapperElement(), "mouseover", t.state.lint.onMouseOver), clearTimeout(t.state.lint.timeout), delete t.state.lint), e) {
        for (var n = t.getOption("gutters"), o = false, i = 0; i < n.length; ++i)
          n[i] == u && (o = true);
        var a = t.state.lint = new A(t, e, o);
        a.options.lintOnChange && t.on("change", b), a.options.tooltips != false && a.options.tooltips != "gutter" && l.on(t.getWrapperElement(), "mouseover", a.onMouseOver), k(t);
      }
    }), l.defineExtension("performLint", function() {
      k(this);
    });
  });
})();
var w = V.exports;
var $ = hu(w);
var X = R({
  __proto__: null,
  default: $
}, [w]);
export {
  X as l
};
//# sourceMappingURL=lint.es-VGEU4HVL.js.map
