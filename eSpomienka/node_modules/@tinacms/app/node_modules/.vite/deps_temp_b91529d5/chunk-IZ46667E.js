import {
  d
} from "./chunk-WIXYZC3K.js";

// node_modules/@graphiql/react/dist/info-addon.es.js
var g = Object.defineProperty;
var r = (e, t) => g(e, "name", { value: t, configurable: true });
d.defineOption("info", false, (e, t, n) => {
  if (n && n !== d.Init) {
    const o = e.state.info.onMouseOver;
    d.off(e.getWrapperElement(), "mouseover", o), clearTimeout(e.state.info.hoverTimeout), delete e.state.info;
  }
  if (t) {
    const o = e.state.info = T(t);
    o.onMouseOver = M.bind(null, e), d.on(e.getWrapperElement(), "mouseover", o.onMouseOver);
  }
});
function T(e) {
  return {
    options: e instanceof Function ? { render: e } : e === true ? {} : e
  };
}
r(T, "createState");
function h(e) {
  const { options: t } = e.state.info;
  return (t == null ? void 0 : t.hoverTime) || 500;
}
r(h, "getHoverTime");
function M(e, t) {
  const n = e.state.info, o = t.target || t.srcElement;
  if (!(o instanceof HTMLElement) || o.nodeName !== "SPAN" || n.hoverTimeout !== void 0)
    return;
  const s = o.getBoundingClientRect(), u = r(function() {
    clearTimeout(n.hoverTimeout), n.hoverTimeout = setTimeout(p, a);
  }, "onMouseMove"), f = r(function() {
    d.off(document, "mousemove", u), d.off(e.getWrapperElement(), "mouseout", f), clearTimeout(n.hoverTimeout), n.hoverTimeout = void 0;
  }, "onMouseOut"), p = r(function() {
    d.off(document, "mousemove", u), d.off(e.getWrapperElement(), "mouseout", f), n.hoverTimeout = void 0, w(e, s);
  }, "onHover"), a = h(e);
  n.hoverTimeout = setTimeout(p, a), d.on(document, "mousemove", u), d.on(e.getWrapperElement(), "mouseout", f);
}
r(M, "onMouseOver");
function w(e, t) {
  const n = e.coordsChar({
    left: (t.left + t.right) / 2,
    top: (t.top + t.bottom) / 2
  }, "window"), o = e.state.info, { options: s } = o, u = s.render || e.getHelper(n, "info");
  if (u) {
    const f = e.getTokenAt(n, true);
    if (f) {
      const p = u(f, s, e, n);
      p && y(e, t, p);
    }
  }
}
r(w, "onMouseHover");
function y(e, t, n) {
  const o = document.createElement("div");
  o.className = "CodeMirror-info", o.append(n), document.body.append(o);
  const s = o.getBoundingClientRect(), u = window.getComputedStyle(o), f = s.right - s.left + parseFloat(u.marginLeft) + parseFloat(u.marginRight), p = s.bottom - s.top + parseFloat(u.marginTop) + parseFloat(u.marginBottom);
  let a = t.bottom;
  p > window.innerHeight - t.bottom - 15 && t.top > window.innerHeight - t.bottom && (a = t.top - p), a < 0 && (a = t.bottom);
  let l = Math.max(0, window.innerWidth - f - 15);
  l > t.left && (l = t.left), o.style.opacity = "1", o.style.top = a + "px", o.style.left = l + "px";
  let c;
  const d2 = r(function() {
    clearTimeout(c);
  }, "onMouseOverPopup"), m = r(function() {
    clearTimeout(c), c = setTimeout(v, 200);
  }, "onMouseOut"), v = r(function() {
    d.off(o, "mouseover", d2), d.off(o, "mouseout", m), d.off(e.getWrapperElement(), "mouseout", m), o.style.opacity ? (o.style.opacity = "0", setTimeout(() => {
      o.parentNode && o.remove();
    }, 600)) : o.parentNode && o.remove();
  }, "hidePopup");
  d.on(o, "mouseover", d2), d.on(o, "mouseout", m), d.on(e.getWrapperElement(), "mouseout", m);
}
r(y, "showPopup");
//# sourceMappingURL=chunk-IZ46667E.js.map
