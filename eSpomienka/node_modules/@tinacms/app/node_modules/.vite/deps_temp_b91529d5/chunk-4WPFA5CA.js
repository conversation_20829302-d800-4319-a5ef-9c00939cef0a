import {
  cu
} from "./chunk-MEKKV4OY.js";

// node_modules/@graphiql/react/dist/searchcursor.es2.js
var W = Object.defineProperty;
var o = (d, E) => W(d, "name", { value: E, configurable: true });
var N = { exports: {} };
var b;
function K() {
  return b || (b = 1, function(d, E) {
    (function(m) {
      m(cu());
    })(function(m) {
      var s = m.Pos;
      function B(e) {
        var t = e.flags;
        return t ?? (e.ignoreCase ? "i" : "") + (e.global ? "g" : "") + (e.multiline ? "m" : "");
      }
      o(B, "regexpFlags");
      function F(e, t) {
        for (var n = B(e), r = n, l = 0; l < t.length; l++)
          r.indexOf(t.charAt(l)) == -1 && (r += t.charAt(l));
        return n == r ? e : new RegExp(e.source, r);
      }
      o(F, "ensureFlags");
      function R(e) {
        return /\\s|\\n|\n|\\W|\\D|\[\^/.test(e.source);
      }
      o(R, "maybeMultiline");
      function I(e, t, n) {
        t = F(t, "g");
        for (var r = n.line, l = n.ch, i = e.lastLine(); r <= i; r++, l = 0) {
          t.lastIndex = l;
          var h = e.getLine(r), f = t.exec(h);
          if (f)
            return {
              from: s(r, f.index),
              to: s(r, f.index + f[0].length),
              match: f
            };
        }
      }
      o(I, "searchRegexpForward");
      function j(e, t, n) {
        if (!R(t))
          return I(e, t, n);
        t = F(t, "gm");
        for (var r, l = 1, i = n.line, h = e.lastLine(); i <= h; ) {
          for (var f = 0; f < l && !(i > h); f++) {
            var p = e.getLine(i++);
            r = r == null ? p : r + `
` + p;
          }
          l = l * 2, t.lastIndex = n.ch;
          var u = t.exec(r);
          if (u) {
            var a = r.slice(0, u.index).split(`
`), c = u[0].split(`
`), g = n.line + a.length - 1, v = a[a.length - 1].length;
            return {
              from: s(g, v),
              to: s(
                g + c.length - 1,
                c.length == 1 ? v + c[0].length : c[c.length - 1].length
              ),
              match: u
            };
          }
        }
      }
      o(j, "searchRegexpForwardMultiline");
      function z(e, t, n) {
        for (var r, l = 0; l <= e.length; ) {
          t.lastIndex = l;
          var i = t.exec(e);
          if (!i)
            break;
          var h = i.index + i[0].length;
          if (h > e.length - n)
            break;
          (!r || h > r.index + r[0].length) && (r = i), l = i.index + 1;
        }
        return r;
      }
      o(z, "lastMatchIn");
      function D(e, t, n) {
        t = F(t, "g");
        for (var r = n.line, l = n.ch, i = e.firstLine(); r >= i; r--, l = -1) {
          var h = e.getLine(r), f = z(h, t, l < 0 ? 0 : h.length - l);
          if (f)
            return {
              from: s(r, f.index),
              to: s(r, f.index + f[0].length),
              match: f
            };
        }
      }
      o(D, "searchRegexpBackward");
      function A(e, t, n) {
        if (!R(t))
          return D(e, t, n);
        t = F(t, "gm");
        for (var r, l = 1, i = e.getLine(n.line).length - n.ch, h = n.line, f = e.firstLine(); h >= f; ) {
          for (var p = 0; p < l && h >= f; p++) {
            var u = e.getLine(h--);
            r = r == null ? u : u + `
` + r;
          }
          l *= 2;
          var a = z(r, t, i);
          if (a) {
            var c = r.slice(0, a.index).split(`
`), g = a[0].split(`
`), v = h + c.length, x = c[c.length - 1].length;
            return {
              from: s(v, x),
              to: s(
                v + g.length - 1,
                g.length == 1 ? x + g[0].length : g[g.length - 1].length
              ),
              match: a
            };
          }
        }
      }
      o(A, "searchRegexpBackwardMultiline");
      var P, k;
      String.prototype.normalize ? (P = o(function(e) {
        return e.normalize("NFD").toLowerCase();
      }, "doFold"), k = o(function(e) {
        return e.normalize("NFD");
      }, "noFold")) : (P = o(function(e) {
        return e.toLowerCase();
      }, "doFold"), k = o(function(e) {
        return e;
      }, "noFold"));
      function L(e, t, n, r) {
        if (e.length == t.length)
          return n;
        for (var l = 0, i = n + Math.max(0, e.length - t.length); ; ) {
          if (l == i)
            return l;
          var h = l + i >> 1, f = r(e.slice(0, h)).length;
          if (f == n)
            return h;
          f > n ? i = h : l = h + 1;
        }
      }
      o(L, "adjustPos");
      function y(e, t, n, r) {
        if (!t.length)
          return null;
        var l = r ? P : k, i = l(t).split(/\r|\n\r?/);
        t:
          for (var h = n.line, f = n.ch, p = e.lastLine() + 1 - i.length; h <= p; h++, f = 0) {
            var u = e.getLine(h).slice(f), a = l(u);
            if (i.length == 1) {
              var c = a.indexOf(i[0]);
              if (c == -1)
                continue t;
              var n = L(u, a, c, l) + f;
              return {
                from: s(h, L(u, a, c, l) + f),
                to: s(h, L(u, a, c + i[0].length, l) + f)
              };
            } else {
              var g = a.length - i[0].length;
              if (a.slice(g) != i[0])
                continue t;
              for (var v = 1; v < i.length - 1; v++)
                if (l(e.getLine(h + v)) != i[v])
                  continue t;
              var x = e.getLine(h + i.length - 1), O = l(x), S = i[i.length - 1];
              if (O.slice(0, S.length) != S)
                continue t;
              return {
                from: s(h, L(u, a, g, l) + f),
                to: s(h + i.length - 1, L(x, O, S.length, l))
              };
            }
          }
      }
      o(y, "searchStringForward");
      function C(e, t, n, r) {
        if (!t.length)
          return null;
        var l = r ? P : k, i = l(t).split(/\r|\n\r?/);
        t:
          for (var h = n.line, f = n.ch, p = e.firstLine() - 1 + i.length; h >= p; h--, f = -1) {
            var u = e.getLine(h);
            f > -1 && (u = u.slice(0, f));
            var a = l(u);
            if (i.length == 1) {
              var c = a.lastIndexOf(i[0]);
              if (c == -1)
                continue t;
              return {
                from: s(h, L(u, a, c, l)),
                to: s(h, L(u, a, c + i[0].length, l))
              };
            } else {
              var g = i[i.length - 1];
              if (a.slice(0, g.length) != g)
                continue t;
              for (var v = 1, n = h - i.length + 1; v < i.length - 1; v++)
                if (l(e.getLine(n + v)) != i[v])
                  continue t;
              var x = e.getLine(h + 1 - i.length), O = l(x);
              if (O.slice(O.length - i[0].length) != i[0])
                continue t;
              return {
                from: s(h + 1 - i.length, L(x, O, x.length - i[0].length, l)),
                to: s(h, L(u, a, g.length, l))
              };
            }
          }
      }
      o(C, "searchStringBackward");
      function w(e, t, n, r) {
        this.atOccurrence = false, this.afterEmptyMatch = false, this.doc = e, n = n ? e.clipPos(n) : s(0, 0), this.pos = { from: n, to: n };
        var l;
        typeof r == "object" ? l = r.caseFold : (l = r, r = null), typeof t == "string" ? (l == null && (l = false), this.matches = function(i, h) {
          return (i ? C : y)(e, t, h, l);
        }) : (t = F(t, "gm"), !r || r.multiline !== false ? this.matches = function(i, h) {
          return (i ? A : j)(e, t, h);
        } : this.matches = function(i, h) {
          return (i ? D : I)(e, t, h);
        });
      }
      o(w, "SearchCursor"), w.prototype = {
        findNext: function() {
          return this.find(false);
        },
        findPrevious: function() {
          return this.find(true);
        },
        find: function(e) {
          var t = this.doc.clipPos(e ? this.pos.from : this.pos.to);
          if (this.afterEmptyMatch && this.atOccurrence && (t = s(t.line, t.ch), e ? (t.ch--, t.ch < 0 && (t.line--, t.ch = (this.doc.getLine(t.line) || "").length)) : (t.ch++, t.ch > (this.doc.getLine(t.line) || "").length && (t.ch = 0, t.line++)), m.cmpPos(t, this.doc.clipPos(t)) != 0))
            return this.atOccurrence = false;
          var n = this.matches(e, t);
          if (this.afterEmptyMatch = n && m.cmpPos(n.from, n.to) == 0, n)
            return this.pos = n, this.atOccurrence = true, this.pos.match || true;
          var r = s(e ? this.doc.firstLine() : this.doc.lastLine() + 1, 0);
          return this.pos = { from: r, to: r }, this.atOccurrence = false;
        },
        from: function() {
          if (this.atOccurrence)
            return this.pos.from;
        },
        to: function() {
          if (this.atOccurrence)
            return this.pos.to;
        },
        replace: function(e, t) {
          if (this.atOccurrence) {
            var n = m.splitLines(e);
            this.doc.replaceRange(n, this.pos.from, this.pos.to, t), this.pos.to = s(
              this.pos.from.line + n.length - 1,
              n[n.length - 1].length + (n.length == 1 ? this.pos.from.ch : 0)
            );
          }
        }
      }, m.defineExtension("getSearchCursor", function(e, t, n) {
        return new w(this.doc, e, t, n);
      }), m.defineDocExtension("getSearchCursor", function(e, t, n) {
        return new w(this, e, t, n);
      }), m.defineExtension("selectMatches", function(e, t) {
        for (var n = [], r = this.getSearchCursor(e, this.getCursor("from"), t); r.findNext() && !(m.cmpPos(r.to(), this.getCursor("to")) > 0); )
          n.push({ anchor: r.from(), head: r.to() });
        n.length && this.setSelections(n, 0);
      });
    });
  }()), N.exports;
}
o(K, "requireSearchcursor");

export {
  K
};
//# sourceMappingURL=chunk-4WPFA5CA.js.map
