{"version": 3, "sources": ["../../../../../@graphiql/codemirror-graphql/esm/utils/mode-indent.js"], "sourcesContent": ["export default function indent(state, textAfter) {\n    var _a, _b;\n    const { levels, indentLevel } = state;\n    const level = !levels || levels.length === 0\n        ? indentLevel\n        : levels.at(-1) - (((_a = this.electricInput) === null || _a === void 0 ? void 0 : _a.test(textAfter)) ? 1 : 0);\n    return (level || 0) * (((_b = this.config) === null || _b === void 0 ? void 0 : _b.indentUnit) || 0);\n}\n//# sourceMappingURL=mode-indent.js.map"], "mappings": ";;;AAAe,SAASA,EAAOC,GAAOC,GAAW;AAC7C,MAAIC,GAAIC;AACR,QAAM,EAAE,QAAAC,GAAQ,aAAAC,EAAa,IAAGL;AAIhC,WAHc,CAACI,KAAUA,EAAO,WAAW,IACrCC,IACAD,EAAO,GAAG,EAAE,KAAO,GAAAF,IAAK,KAAK,mBAAmB,QAAQA,MAAO,WAAkBA,EAAG,KAAKD,CAAS,IAAK,IAAI,OAChG,QAAQE,IAAK,KAAK,YAAY,QAAQA,MAAO,SAAS,SAASA,EAAG,eAAe;AACtG;AAPwBG,EAAAP,GAAA,QAAA;", "names": ["indent", "state", "textAfter", "_a", "_b", "levels", "indentLevel", "__name"]}