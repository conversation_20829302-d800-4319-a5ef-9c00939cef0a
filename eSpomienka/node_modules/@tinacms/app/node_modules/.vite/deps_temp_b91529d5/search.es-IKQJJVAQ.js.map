{"version": 3, "sources": ["../../../../../node_modules/codemirror/addon/search/search.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/LICENSE\n\n// Define search commands. Depends on dialog.js or another\n// implementation of the openDialog method.\n\n// Replace works a little oddly -- it will do the replace on the next\n// Ctrl-G (or whatever is bound to find<PERSON>ext) press. You prevent a\n// replace by making sure the match is no longer selected when hitting\n// Ctrl-G.\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"), require(\"./searchcursor\"), require(\"../dialog/dialog\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\", \"./searchcursor\", \"../dialog/dialog\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n\n  // default search panel location\n  CodeMirror.defineOption(\"search\", {bottom: false});\n\n  function searchOverlay(query, caseInsensitive) {\n    if (typeof query == \"string\")\n      query = new RegExp(query.replace(/[\\-\\[\\]\\/\\{\\}\\(\\)\\*\\+\\?\\.\\\\\\^\\$\\|]/g, \"\\\\$&\"), caseInsensitive ? \"gi\" : \"g\");\n    else if (!query.global)\n      query = new RegExp(query.source, query.ignoreCase ? \"gi\" : \"g\");\n\n    return {token: function(stream) {\n      query.lastIndex = stream.pos;\n      var match = query.exec(stream.string);\n      if (match && match.index == stream.pos) {\n        stream.pos += match[0].length || 1;\n        return \"searching\";\n      } else if (match) {\n        stream.pos = match.index;\n      } else {\n        stream.skipToEnd();\n      }\n    }};\n  }\n\n  function SearchState() {\n    this.posFrom = this.posTo = this.lastQuery = this.query = null;\n    this.overlay = null;\n  }\n\n  function getSearchState(cm) {\n    return cm.state.search || (cm.state.search = new SearchState());\n  }\n\n  function queryCaseInsensitive(query) {\n    return typeof query == \"string\" && query == query.toLowerCase();\n  }\n\n  function getSearchCursor(cm, query, pos) {\n    // Heuristic: if the query string is all lowercase, do a case insensitive search.\n    return cm.getSearchCursor(query, pos, {caseFold: queryCaseInsensitive(query), multiline: true});\n  }\n\n  function persistentDialog(cm, text, deflt, onEnter, onKeyDown) {\n    cm.openDialog(text, onEnter, {\n      value: deflt,\n      selectValueOnOpen: true,\n      closeOnEnter: false,\n      onClose: function() { clearSearch(cm); },\n      onKeyDown: onKeyDown,\n      bottom: cm.options.search.bottom\n    });\n  }\n\n  function dialog(cm, text, shortText, deflt, f) {\n    if (cm.openDialog) cm.openDialog(text, f, {value: deflt, selectValueOnOpen: true, bottom: cm.options.search.bottom});\n    else f(prompt(shortText, deflt));\n  }\n\n  function confirmDialog(cm, text, shortText, fs) {\n    if (cm.openConfirm) cm.openConfirm(text, fs);\n    else if (confirm(shortText)) fs[0]();\n  }\n\n  function parseString(string) {\n    return string.replace(/\\\\([nrt\\\\])/g, function(match, ch) {\n      if (ch == \"n\") return \"\\n\"\n      if (ch == \"r\") return \"\\r\"\n      if (ch == \"t\") return \"\\t\"\n      if (ch == \"\\\\\") return \"\\\\\"\n      return match\n    })\n  }\n\n  function parseQuery(query) {\n    var isRE = query.match(/^\\/(.*)\\/([a-z]*)$/);\n    if (isRE) {\n      try { query = new RegExp(isRE[1], isRE[2].indexOf(\"i\") == -1 ? \"\" : \"i\"); }\n      catch(e) {} // Not a regular expression after all, do a string search\n    } else {\n      query = parseString(query)\n    }\n    if (typeof query == \"string\" ? query == \"\" : query.test(\"\"))\n      query = /x^/;\n    return query;\n  }\n\n  function startSearch(cm, state, query) {\n    state.queryText = query;\n    state.query = parseQuery(query);\n    cm.removeOverlay(state.overlay, queryCaseInsensitive(state.query));\n    state.overlay = searchOverlay(state.query, queryCaseInsensitive(state.query));\n    cm.addOverlay(state.overlay);\n    if (cm.showMatchesOnScrollbar) {\n      if (state.annotate) { state.annotate.clear(); state.annotate = null; }\n      state.annotate = cm.showMatchesOnScrollbar(state.query, queryCaseInsensitive(state.query));\n    }\n  }\n\n  function doSearch(cm, rev, persistent, immediate) {\n    var state = getSearchState(cm);\n    if (state.query) return findNext(cm, rev);\n    var q = cm.getSelection() || state.lastQuery;\n    if (q instanceof RegExp && q.source == \"x^\") q = null\n    if (persistent && cm.openDialog) {\n      var hiding = null\n      var searchNext = function(query, event) {\n        CodeMirror.e_stop(event);\n        if (!query) return;\n        if (query != state.queryText) {\n          startSearch(cm, state, query);\n          state.posFrom = state.posTo = cm.getCursor();\n        }\n        if (hiding) hiding.style.opacity = 1\n        findNext(cm, event.shiftKey, function(_, to) {\n          var dialog\n          if (to.line < 3 && document.querySelector &&\n              (dialog = cm.display.wrapper.querySelector(\".CodeMirror-dialog\")) &&\n              dialog.getBoundingClientRect().bottom - 4 > cm.cursorCoords(to, \"window\").top)\n            (hiding = dialog).style.opacity = .4\n        })\n      };\n      persistentDialog(cm, getQueryDialog(cm), q, searchNext, function(event, query) {\n        var keyName = CodeMirror.keyName(event)\n        var extra = cm.getOption('extraKeys'), cmd = (extra && extra[keyName]) || CodeMirror.keyMap[cm.getOption(\"keyMap\")][keyName]\n        if (cmd == \"findNext\" || cmd == \"findPrev\" ||\n          cmd == \"findPersistentNext\" || cmd == \"findPersistentPrev\") {\n          CodeMirror.e_stop(event);\n          startSearch(cm, getSearchState(cm), query);\n          cm.execCommand(cmd);\n        } else if (cmd == \"find\" || cmd == \"findPersistent\") {\n          CodeMirror.e_stop(event);\n          searchNext(query, event);\n        }\n      });\n      if (immediate && q) {\n        startSearch(cm, state, q);\n        findNext(cm, rev);\n      }\n    } else {\n      dialog(cm, getQueryDialog(cm), \"Search for:\", q, function(query) {\n        if (query && !state.query) cm.operation(function() {\n          startSearch(cm, state, query);\n          state.posFrom = state.posTo = cm.getCursor();\n          findNext(cm, rev);\n        });\n      });\n    }\n  }\n\n  function findNext(cm, rev, callback) {cm.operation(function() {\n    var state = getSearchState(cm);\n    var cursor = getSearchCursor(cm, state.query, rev ? state.posFrom : state.posTo);\n    if (!cursor.find(rev)) {\n      cursor = getSearchCursor(cm, state.query, rev ? CodeMirror.Pos(cm.lastLine()) : CodeMirror.Pos(cm.firstLine(), 0));\n      if (!cursor.find(rev)) return;\n    }\n    cm.setSelection(cursor.from(), cursor.to());\n    cm.scrollIntoView({from: cursor.from(), to: cursor.to()}, 20);\n    state.posFrom = cursor.from(); state.posTo = cursor.to();\n    if (callback) callback(cursor.from(), cursor.to())\n  });}\n\n  function clearSearch(cm) {cm.operation(function() {\n    var state = getSearchState(cm);\n    state.lastQuery = state.query;\n    if (!state.query) return;\n    state.query = state.queryText = null;\n    cm.removeOverlay(state.overlay);\n    if (state.annotate) { state.annotate.clear(); state.annotate = null; }\n  });}\n\n  function el(tag, attrs) {\n    var element = tag ? document.createElement(tag) : document.createDocumentFragment();\n    for (var key in attrs) {\n      element[key] = attrs[key];\n    }\n    for (var i = 2; i < arguments.length; i++) {\n      var child = arguments[i]\n      element.appendChild(typeof child == \"string\" ? document.createTextNode(child) : child);\n    }\n    return element;\n  }\n\n  function getQueryDialog(cm)  {\n    return el(\"\", null,\n              el(\"span\", {className: \"CodeMirror-search-label\"}, cm.phrase(\"Search:\")), \" \",\n              el(\"input\", {type: \"text\", \"style\": \"width: 10em\", className: \"CodeMirror-search-field\"}), \" \",\n              el(\"span\", {style: \"color: #888\", className: \"CodeMirror-search-hint\"},\n                 cm.phrase(\"(Use /re/ syntax for regexp search)\")));\n  }\n  function getReplaceQueryDialog(cm) {\n    return el(\"\", null, \" \",\n              el(\"input\", {type: \"text\", \"style\": \"width: 10em\", className: \"CodeMirror-search-field\"}), \" \",\n              el(\"span\", {style: \"color: #888\", className: \"CodeMirror-search-hint\"},\n                 cm.phrase(\"(Use /re/ syntax for regexp search)\")));\n  }\n  function getReplacementQueryDialog(cm) {\n    return el(\"\", null,\n              el(\"span\", {className: \"CodeMirror-search-label\"}, cm.phrase(\"With:\")), \" \",\n              el(\"input\", {type: \"text\", \"style\": \"width: 10em\", className: \"CodeMirror-search-field\"}));\n  }\n  function getDoReplaceConfirm(cm) {\n    return el(\"\", null,\n              el(\"span\", {className: \"CodeMirror-search-label\"}, cm.phrase(\"Replace?\")), \" \",\n              el(\"button\", {}, cm.phrase(\"Yes\")), \" \",\n              el(\"button\", {}, cm.phrase(\"No\")), \" \",\n              el(\"button\", {}, cm.phrase(\"All\")), \" \",\n              el(\"button\", {}, cm.phrase(\"Stop\")));\n  }\n\n  function replaceAll(cm, query, text) {\n    cm.operation(function() {\n      for (var cursor = getSearchCursor(cm, query); cursor.findNext();) {\n        if (typeof query != \"string\") {\n          var match = cm.getRange(cursor.from(), cursor.to()).match(query);\n          cursor.replace(text.replace(/\\$(\\d)/g, function(_, i) {return match[i];}));\n        } else cursor.replace(text);\n      }\n    });\n  }\n\n  function replace(cm, all) {\n    if (cm.getOption(\"readOnly\")) return;\n    var query = cm.getSelection() || getSearchState(cm).lastQuery;\n    var dialogText = all ? cm.phrase(\"Replace all:\") : cm.phrase(\"Replace:\")\n    var fragment = el(\"\", null,\n                      el(\"span\", {className: \"CodeMirror-search-label\"}, dialogText),\n                      getReplaceQueryDialog(cm))\n    dialog(cm, fragment, dialogText, query, function(query) {\n      if (!query) return;\n      query = parseQuery(query);\n      dialog(cm, getReplacementQueryDialog(cm), cm.phrase(\"Replace with:\"), \"\", function(text) {\n        text = parseString(text)\n        if (all) {\n          replaceAll(cm, query, text)\n        } else {\n          clearSearch(cm);\n          var cursor = getSearchCursor(cm, query, cm.getCursor(\"from\"));\n          var advance = function() {\n            var start = cursor.from(), match;\n            if (!(match = cursor.findNext())) {\n              cursor = getSearchCursor(cm, query);\n              if (!(match = cursor.findNext()) ||\n                  (start && cursor.from().line == start.line && cursor.from().ch == start.ch)) return;\n            }\n            cm.setSelection(cursor.from(), cursor.to());\n            cm.scrollIntoView({from: cursor.from(), to: cursor.to()});\n            confirmDialog(cm, getDoReplaceConfirm(cm), cm.phrase(\"Replace?\"),\n                          [function() {doReplace(match);}, advance,\n                           function() {replaceAll(cm, query, text)}]);\n          };\n          var doReplace = function(match) {\n            cursor.replace(typeof query == \"string\" ? text :\n                           text.replace(/\\$(\\d)/g, function(_, i) {return match[i];}));\n            advance();\n          };\n          advance();\n        }\n      });\n    });\n  }\n\n  CodeMirror.commands.find = function(cm) {clearSearch(cm); doSearch(cm);};\n  CodeMirror.commands.findPersistent = function(cm) {clearSearch(cm); doSearch(cm, false, true);};\n  CodeMirror.commands.findPersistentNext = function(cm) {doSearch(cm, false, true, true);};\n  CodeMirror.commands.findPersistentPrev = function(cm) {doSearch(cm, true, true, true);};\n  CodeMirror.commands.findNext = doSearch;\n  CodeMirror.commands.findPrev = function(cm) {doSearch(cm, true);};\n  CodeMirror.commands.clearSearch = clearSearch;\n  CodeMirror.commands.replace = replace;\n  CodeMirror.commands.replaceAll = function(cm) {replace(cm, true);};\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWA,GAAC,SAASA,GAAK;AAEXA,MAAIC,GAA+B,GAAEC,EAAAA,GAA2BC,EAA2B,CAAA;EAK9F,GAAE,SAASC,GAAY;AAItBA,MAAW,aAAa,UAAU,EAAC,QAAQ,MAAK,CAAC;AAEjD,aAASC,EAAcC,GAAOC,GAAiB;AAC7C,aAAI,OAAOD,KAAS,WAClBA,IAAQ,IAAI,OAAOA,EAAM,QAAQ,uCAAuC,MAAM,GAAGC,IAAkB,OAAO,GAAG,IACrGD,EAAM,WACdA,IAAQ,IAAI,OAAOA,EAAM,QAAQA,EAAM,aAAa,OAAO,GAAG,IAEzD,EAAC,OAAO,SAASE,GAAQ;AAC9BF,UAAM,YAAYE,EAAO;AACzB,YAAIC,IAAQH,EAAM,KAAKE,EAAO,MAAM;AACpC,YAAIC,KAASA,EAAM,SAASD,EAAO;AACjC,iBAAAA,EAAO,OAAOC,EAAM,CAAC,EAAE,UAAU,GAC1B;AACEA,YACTD,EAAO,MAAMC,EAAM,QAEnBD,EAAO,UAAS;MAExB,EAAK;IACF;AAlBQE,MAAAL,GAAA,eAAA;AAoBT,aAASM,IAAc;AACrB,WAAK,UAAU,KAAK,QAAQ,KAAK,YAAY,KAAK,QAAQ,MAC1D,KAAK,UAAU;IAChB;AAHQD,MAAAC,GAAA,aAAA;AAKT,aAASC,EAAeC,GAAI;AAC1B,aAAOA,EAAG,MAAM,WAAWA,EAAG,MAAM,SAAS,IAAIF,EAAW;IAC7D;AAFQD,MAAAE,GAAA,gBAAA;AAIT,aAASE,EAAqBR,GAAO;AACnC,aAAO,OAAOA,KAAS,YAAYA,KAASA,EAAM,YAAW;IAC9D;AAFQI,MAAAI,GAAA,sBAAA;AAIT,aAASC,EAAgBF,GAAIP,GAAOU,GAAK;AAEvC,aAAOH,EAAG,gBAAgBP,GAAOU,GAAK,EAAC,UAAUF,EAAqBR,CAAK,GAAG,WAAW,KAAI,CAAC;IAC/F;AAHQI,MAAAK,GAAA,iBAAA;AAKT,aAASE,EAAiBJ,GAAIK,GAAMC,GAAOC,GAASC,GAAW;AAC7DR,QAAG,WAAWK,GAAME,GAAS;QAC3B,OAAOD;QACP,mBAAmB;QACnB,cAAc;QACd,SAAS,WAAW;AAAEG,YAAYT,CAAE;QAAI;QACxC,WAAWQ;QACX,QAAQR,EAAG,QAAQ,OAAO;MAChC,CAAK;IACF;AATQH,MAAAO,GAAA,kBAAA;AAWT,aAASM,EAAOV,GAAIK,GAAMM,GAAWL,GAAOM,GAAG;AACzCZ,QAAG,aAAYA,EAAG,WAAWK,GAAMO,GAAG,EAAC,OAAON,GAAO,mBAAmB,MAAM,QAAQN,EAAG,QAAQ,OAAO,OAAM,CAAC,IAC9GY,EAAE,OAAOD,GAAWL,CAAK,CAAC;IAChC;AAHQT,MAAAa,GAAA,QAAA;AAKT,aAASG,EAAcb,GAAIK,GAAMM,GAAWG,GAAI;AAC1Cd,QAAG,cAAaA,EAAG,YAAYK,GAAMS,CAAE,IAClC,QAAQH,CAAS,KAAGG,EAAG,CAAC,EAAC;IACnC;AAHQjB,MAAAgB,GAAA,eAAA;AAKT,aAASE,EAAYC,GAAQ;AAC3B,aAAOA,EAAO,QAAQ,gBAAgB,SAASpB,GAAOqB,GAAI;AACxD,eAAIA,KAAM,MAAY;IAClBA,KAAM,MAAY,OAClBA,KAAM,MAAY,MAClBA,KAAM,OAAa,OAChBrB;MACb,CAAK;IACF;AARQC,MAAAkB,GAAA,aAAA;AAUT,aAASG,EAAWzB,GAAO;AACzB,UAAI0B,IAAO1B,EAAM,MAAM,oBAAoB;AAC3C,UAAI0B;AACF,YAAI;AAAE1B,cAAQ,IAAI,OAAO0B,EAAK,CAAC,GAAGA,EAAK,CAAC,EAAE,QAAQ,GAAG,KAAK,KAAK,KAAK,GAAG;QAAI,QAC3E;QAAW;;AAEX1B,YAAQsB,EAAYtB,CAAK;AAE3B,cAAI,OAAOA,KAAS,WAAWA,KAAS,KAAKA,EAAM,KAAK,EAAE,OACxDA,IAAQ,OACHA;IACR;AAXQI,MAAAqB,GAAA,YAAA;AAaT,aAASE,EAAYpB,GAAIqB,GAAO5B,GAAO;AACrC4B,QAAM,YAAY5B,GAClB4B,EAAM,QAAQH,EAAWzB,CAAK,GAC9BO,EAAG,cAAcqB,EAAM,SAASpB,EAAqBoB,EAAM,KAAK,CAAC,GACjEA,EAAM,UAAU7B,EAAc6B,EAAM,OAAOpB,EAAqBoB,EAAM,KAAK,CAAC,GAC5ErB,EAAG,WAAWqB,EAAM,OAAO,GACvBrB,EAAG,2BACDqB,EAAM,aAAYA,EAAM,SAAS,MAAO,GAAEA,EAAM,WAAW,OAC/DA,EAAM,WAAWrB,EAAG,uBAAuBqB,EAAM,OAAOpB,EAAqBoB,EAAM,KAAK,CAAC;IAE5F;AAVQxB,MAAAuB,GAAA,aAAA;AAYT,aAASE,EAAStB,GAAIuB,GAAKC,GAAYC,GAAW;AAChD,UAAIJ,IAAQtB,EAAeC,CAAE;AAC7B,UAAIqB,EAAM;AAAO,eAAOK,EAAS1B,GAAIuB,CAAG;AACxC,UAAII,IAAI3B,EAAG,aAAY,KAAMqB,EAAM;AAEnC,UADIM,aAAa,UAAUA,EAAE,UAAU,SAAMA,IAAI,OAC7CH,KAAcxB,EAAG,YAAY;AAC/B,YAAI4B,IAAS,MACTC,IAAahC,EAAA,SAASJ,GAAOqC,GAAO;AACtCvC,YAAW,OAAOuC,CAAK,GAClBrC,MACDA,KAAS4B,EAAM,cACjBD,EAAYpB,GAAIqB,GAAO5B,CAAK,GAC5B4B,EAAM,UAAUA,EAAM,QAAQrB,EAAG,UAAS,IAExC4B,MAAQA,EAAO,MAAM,UAAU,IACnCF,EAAS1B,GAAI8B,EAAM,UAAU,SAASC,GAAGC,GAAI;AAC3C,gBAAItB;AACAsB,cAAG,OAAO,KAAK,SAAS,kBACvBtB,IAASV,EAAG,QAAQ,QAAQ,cAAc,oBAAoB,MAC/DU,EAAO,sBAAA,EAAwB,SAAS,IAAIV,EAAG,aAAagC,GAAI,QAAQ,EAAE,SAC3EJ,IAASlB,GAAQ,MAAM,UAAU;UAC9C,CAAS;QACT,GAfuB,YAAA;AAgBjBN,UAAiBJ,GAAIiC,EAAejC,CAAE,GAAG2B,GAAGE,GAAY,SAASC,GAAOrC,GAAO;AAC7E,cAAIyC,IAAU3C,EAAW,QAAQuC,CAAK,GAClCK,IAAQnC,EAAG,UAAU,WAAW,GAAGoC,IAAOD,KAASA,EAAMD,CAAO,KAAM3C,EAAW,OAAOS,EAAG,UAAU,QAAQ,CAAC,EAAEkC,CAAO;AACvHE,eAAO,cAAcA,KAAO,cAC9BA,KAAO,wBAAwBA,KAAO,wBACtC7C,EAAW,OAAOuC,CAAK,GACvBV,EAAYpB,GAAID,EAAeC,CAAE,GAAGP,CAAK,GACzCO,EAAG,YAAYoC,CAAG,MACTA,KAAO,UAAUA,KAAO,sBACjC7C,EAAW,OAAOuC,CAAK,GACvBD,EAAWpC,GAAOqC,CAAK;QAEjC,CAAO,GACGL,KAAaE,MACfP,EAAYpB,GAAIqB,GAAOM,CAAC,GACxBD,EAAS1B,GAAIuB,CAAG;MAAA;AAGlBb,UAAOV,GAAIiC,EAAejC,CAAE,GAAG,eAAe2B,GAAG,SAASlC,GAAO;AAC3DA,eAAS,CAAC4B,EAAM,SAAOrB,EAAG,UAAU,WAAW;AACjDoB,cAAYpB,GAAIqB,GAAO5B,CAAK,GAC5B4B,EAAM,UAAUA,EAAM,QAAQrB,EAAG,UAAS,GAC1C0B,EAAS1B,GAAIuB,CAAG;UAC1B,CAAS;QACT,CAAO;IAEJ;AAjDQ1B,MAAAyB,GAAA,UAAA;AAmDT,aAASI,EAAS1B,GAAIuB,GAAKc,GAAU;AAACrC,QAAG,UAAU,WAAW;AAC5D,YAAIqB,IAAQtB,EAAeC,CAAE,GACzBsC,IAASpC,EAAgBF,GAAIqB,EAAM,OAAOE,IAAMF,EAAM,UAAUA,EAAM,KAAK;AAC3E,SAACiB,EAAO,KAAKf,CAAG,MAClBe,IAASpC,EAAgBF,GAAIqB,EAAM,OAAOE,IAAMhC,EAAW,IAAIS,EAAG,SAAU,CAAA,IAAIT,EAAW,IAAIS,EAAG,UAAW,GAAE,CAAC,CAAC,GAC7G,CAACsC,EAAO,KAAKf,CAAG,OAEtBvB,EAAG,aAAasC,EAAO,KAAM,GAAEA,EAAO,GAAE,CAAE,GAC1CtC,EAAG,eAAe,EAAC,MAAMsC,EAAO,KAAI,GAAI,IAAIA,EAAO,GAAA,EAAI,GAAG,EAAE,GAC5DjB,EAAM,UAAUiB,EAAO,KAAM,GAAEjB,EAAM,QAAQiB,EAAO,GAAA,GAChDD,KAAUA,EAASC,EAAO,KAAI,GAAIA,EAAO,GAAA,CAAI;MAClD,CAAA;IAAE;AAXMzC,MAAA6B,GAAA,UAAA;AAaT,aAASjB,EAAYT,GAAI;AAACA,QAAG,UAAU,WAAW;AAChD,YAAIqB,IAAQtB,EAAeC,CAAE;AAC7BqB,UAAM,YAAYA,EAAM,OACnBA,EAAM,UACXA,EAAM,QAAQA,EAAM,YAAY,MAChCrB,EAAG,cAAcqB,EAAM,OAAO,GAC1BA,EAAM,aAAYA,EAAM,SAAS,MAAO,GAAEA,EAAM,WAAW;MAChE,CAAA;IAAE;AAPMxB,MAAAY,GAAA,aAAA;AAST,aAAS8B,EAAGC,GAAKC,GAAO;AACtB,UAAIC,IAAUF,IAAM,SAAS,cAAcA,CAAG,IAAI,SAAS,uBAAA;AAC3D,eAASG,KAAOF;AACdC,UAAQC,CAAG,IAAIF,EAAME,CAAG;AAE1B,eAASC,IAAI,GAAGA,IAAI,UAAU,QAAQA,KAAK;AACzC,YAAIC,IAAQ,UAAUD,CAAC;AACvBF,UAAQ,YAAY,OAAOG,KAAS,WAAW,SAAS,eAAeA,CAAK,IAAIA,CAAK;MAAA;AAEvF,aAAOH;IACR;AAVQ7C,MAAA0C,GAAA,IAAA;AAYT,aAASN,EAAejC,GAAK;AAC3B,aAAOuC;QAAG;QAAI;QACJA,EAAG,QAAQ,EAAC,WAAW,0BAAyB,GAAGvC,EAAG,OAAO,SAAS,CAAC;QAAG;QAC1EuC,EAAG,SAAS,EAAC,MAAM,QAAQ,OAAS,eAAe,WAAW,0BAAyB,CAAC;QAAG;QAC3FA;UAAG;UAAQ,EAAC,OAAO,eAAe,WAAW,yBAAwB;UAClEvC,EAAG,OAAO,qCAAqC;QAAC;MAAC;IAC/D;AANQH,MAAAoC,GAAA,gBAAA;AAOT,aAASa,GAAsB9C,GAAI;AACjC,aAAOuC;QAAG;QAAI;QAAM;QACVA,EAAG,SAAS,EAAC,MAAM,QAAQ,OAAS,eAAe,WAAW,0BAAyB,CAAC;QAAG;QAC3FA;UAAG;UAAQ,EAAC,OAAO,eAAe,WAAW,yBAAwB;UAClEvC,EAAG,OAAO,qCAAqC;QAAC;MAAC;IAC/D;AALQH,MAAAiD,IAAA,uBAAA;AAMT,aAASC,EAA0B/C,GAAI;AACrC,aAAOuC;QAAG;QAAI;QACJA,EAAG,QAAQ,EAAC,WAAW,0BAAyB,GAAGvC,EAAG,OAAO,OAAO,CAAC;QAAG;QACxEuC,EAAG,SAAS,EAAC,MAAM,QAAQ,OAAS,eAAe,WAAW,0BAAyB,CAAC;MAAC;IACpG;AAJQ1C,MAAAkD,GAAA,2BAAA;AAKT,aAASC,EAAoBhD,GAAI;AAC/B,aAAOuC;QAAG;QAAI;QACJA,EAAG,QAAQ,EAAC,WAAW,0BAAyB,GAAGvC,EAAG,OAAO,UAAU,CAAC;QAAG;QAC3EuC,EAAG,UAAU,CAAA,GAAIvC,EAAG,OAAO,KAAK,CAAC;QAAG;QACpCuC,EAAG,UAAU,CAAA,GAAIvC,EAAG,OAAO,IAAI,CAAC;QAAG;QACnCuC,EAAG,UAAU,CAAA,GAAIvC,EAAG,OAAO,KAAK,CAAC;QAAG;QACpCuC,EAAG,UAAU,CAAA,GAAIvC,EAAG,OAAO,MAAM,CAAC;MAAC;IAC9C;AAPQH,MAAAmD,GAAA,qBAAA;AAST,aAASC,EAAWjD,GAAIP,GAAOY,GAAM;AACnCL,QAAG,UAAU,WAAW;AACtB,iBAASsC,IAASpC,EAAgBF,GAAIP,CAAK,GAAG6C,EAAO,SAAA;AACnD,cAAI,OAAO7C,KAAS,UAAU;AAC5B,gBAAIG,IAAQI,EAAG,SAASsC,EAAO,KAAI,GAAIA,EAAO,GAAI,CAAA,EAAE,MAAM7C,CAAK;AAC/D6C,cAAO,QAAQjC,EAAK,QAAQ,WAAW,SAAS0B,GAAGa,GAAG;AAAC,qBAAOhD,EAAMgD,CAAC;YAAE,CAAC,CAAC;UAAA;AACpEN,cAAO,QAAQjC,CAAI;MAElC,CAAK;IACF;AATQR,MAAAoD,GAAA,YAAA;AAWT,aAASC,EAAQlD,GAAImD,GAAK;AACxB,UAAI,CAAAnD,EAAG,UAAU,UAAU,GAC3B;AAAA,YAAIP,IAAQO,EAAG,aAAY,KAAMD,EAAeC,CAAE,EAAE,WAChDoD,IAAaD,IAAMnD,EAAG,OAAO,cAAc,IAAIA,EAAG,OAAO,UAAU,GACnEqD,IAAWd;UAAG;UAAI;UACJA,EAAG,QAAQ,EAAC,WAAW,0BAAyB,GAAGa,CAAU;UAC7DN,GAAsB9C,CAAE;QAAC;AAC3CU,UAAOV,GAAIqD,GAAUD,GAAY3D,GAAO,SAASA,GAAO;AACjDA,gBACLA,IAAQyB,EAAWzB,CAAK,GACxBiB,EAAOV,GAAI+C,EAA0B/C,CAAE,GAAGA,EAAG,OAAO,eAAe,GAAG,IAAI,SAASK,GAAM;AAEvF,gBADAA,IAAOU,EAAYV,CAAI,GACnB8C;AACFF,gBAAWjD,GAAIP,GAAOY,CAAI;iBACrB;AACLI,gBAAYT,CAAE;AACd,kBAAIsC,IAASpC,EAAgBF,GAAIP,GAAOO,EAAG,UAAU,MAAM,CAAC,GACxDsD,IAAUzD,EAAA,WAAW;AACvB,oBAAI0D,IAAQjB,EAAO,KAAI,GAAI1C;AACvB,kBAAEA,IAAQ0C,EAAO,SAAU,OAC7BA,IAASpC,EAAgBF,GAAIP,CAAK,GAC9B,EAAEG,IAAQ0C,EAAO,SAAA,MAChBiB,KAASjB,EAAO,KAAA,EAAO,QAAQiB,EAAM,QAAQjB,EAAO,KAAI,EAAG,MAAMiB,EAAM,QAE9EvD,EAAG,aAAasC,EAAO,KAAM,GAAEA,EAAO,GAAE,CAAE,GAC1CtC,EAAG,eAAe,EAAC,MAAMsC,EAAO,KAAA,GAAQ,IAAIA,EAAO,GAAI,EAAA,CAAC,GACxDzB;kBAAcb;kBAAIgD,EAAoBhD,CAAE;kBAAGA,EAAG,OAAO,UAAU;kBACjD;oBAAC,WAAW;AAACwD,wBAAU5D,CAAK;oBAAE;oBAAG0D;oBAChC,WAAW;AAACL,wBAAWjD,GAAIP,GAAOY,CAAI;oBAAC;kBAAC;gBAAC;cACpE,GAZwB,SAAA,GAaVmD,IAAY3D,EAAA,SAASD,GAAO;AAC9B0C,kBAAO,QAAQ,OAAO7C,KAAS,WAAWY,IAC3BA,EAAK,QAAQ,WAAW,SAAS0B,GAAGa,GAAG;AAAC,yBAAOhD,EAAMgD,CAAC;gBAAE,CAAC,CAAC,GACzEU,EAAAA;cACZ,GAJ0B,WAAA;AAKhBA,gBAAAA;;UAEV,CAAO;QACP,CAAK;MAAA;IACF;AAvCQzD,MAAAqD,GAAA,SAAA,GAyCT3D,EAAW,SAAS,OAAO,SAASS,GAAI;AAACS,QAAYT,CAAE,GAAGsB,EAAStB,CAAE;IAAE,GACvET,EAAW,SAAS,iBAAiB,SAASS,GAAI;AAACS,QAAYT,CAAE,GAAGsB,EAAStB,GAAI,OAAO,IAAI;IAAE,GAC9FT,EAAW,SAAS,qBAAqB,SAASS,GAAI;AAACsB,QAAStB,GAAI,OAAO,MAAM,IAAI;IAAE,GACvFT,EAAW,SAAS,qBAAqB,SAASS,GAAI;AAACsB,QAAStB,GAAI,MAAM,MAAM,IAAI;IAAE,GACtFT,EAAW,SAAS,WAAW+B,GAC/B/B,EAAW,SAAS,WAAW,SAASS,GAAI;AAACsB,QAAStB,GAAI,IAAI;IAAE,GAChET,EAAW,SAAS,cAAckB,GAClClB,EAAW,SAAS,UAAU2D,GAC9B3D,EAAW,SAAS,aAAa,SAASS,GAAI;AAACkD,QAAQlD,GAAI,IAAI;IAAE;EACnE,CAAC;;;;;;;;", "names": ["mod", "require$$0", "require$$1", "require$$2", "CodeMirror", "searchOverlay", "query", "caseInsensitive", "stream", "match", "__name", "SearchState", "getSearchState", "cm", "queryCaseInsensitive", "getSearchCursor", "pos", "persistentDialog", "text", "deflt", "onEnter", "onKeyDown", "clearSearch", "dialog", "shortText", "f", "confirmDialog", "fs", "parseString", "string", "ch", "parse<PERSON><PERSON>y", "isRE", "startSearch", "state", "doSearch", "rev", "persistent", "immediate", "findNext", "q", "hiding", "searchNext", "event", "_", "to", "getQueryDialog", "keyName", "extra", "cmd", "callback", "cursor", "el", "tag", "attrs", "element", "key", "i", "child", "getReplaceQueryDialog", "getReplacementQueryDialog", "getDoReplaceConfirm", "replaceAll", "replace", "all", "dialogText", "fragment", "advance", "start", "doReplace"]}