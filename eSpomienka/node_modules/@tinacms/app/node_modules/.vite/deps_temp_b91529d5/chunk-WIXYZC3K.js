import {
  cu,
  hu
} from "./chunk-MEKKV4OY.js";

// node_modules/@graphiql/react/dist/codemirror.es.js
var c = Object.defineProperty;
var n = (r, o) => c(r, "name", { value: o, configurable: true });
function u(r, o) {
  for (var a = 0; a < o.length; a++) {
    const e = o[a];
    if (typeof e != "string" && !Array.isArray(e)) {
      for (const t in e)
        if (t !== "default" && !(t in r)) {
          const i = Object.getOwnPropertyDescriptor(e, t);
          i && Object.defineProperty(r, t, i.get ? i : {
            enumerable: true,
            get: () => e[t]
          });
        }
    }
  }
  return Object.freeze(Object.defineProperty(r, Symbol.toStringTag, { value: "Module" }));
}
n(u, "_mergeNamespaces");
var s = cu();
var d = hu(s);
var m = u({
  __proto__: null,
  default: d
}, [s]);

export {
  d,
  m
};
//# sourceMappingURL=chunk-WIXYZC3K.js.map
