import {
  Kind,
  TypeInfo,
  getNamedType,
  isLeafType,
  parse,
  print,
  visit,
  visitWithTypeInfo
} from "./chunk-HD22INE4.js";
import {
  __commonJS
} from "./chunk-AUZ3RYOM.js";

// optional-peer-dep:__vite-optional-peer-dep:graphql-ws:@graphiql/toolkit
var require_toolkit = __commonJS({
  "optional-peer-dep:__vite-optional-peer-dep:graphql-ws:@graphiql/toolkit"() {
    throw new Error(`Could not resolve "graphql-ws" imported by "@graphiql/toolkit". Is it installed?`);
  }
});

// node_modules/@graphiql/toolkit/esm/async-helpers/index.js
var __awaiter = function(thisArg, _arguments, P, generator) {
  function adopt(value) {
    return value instanceof P ? value : new P(function(resolve) {
      resolve(value);
    });
  }
  return new (P || (P = Promise))(function(resolve, reject) {
    function fulfilled(value) {
      try {
        step(generator.next(value));
      } catch (e2) {
        reject(e2);
      }
    }
    function rejected(value) {
      try {
        step(generator["throw"](value));
      } catch (e2) {
        reject(e2);
      }
    }
    function step(result) {
      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
    }
    step((generator = generator.apply(thisArg, _arguments || [])).next());
  });
};
function isPromise(value) {
  return typeof value === "object" && value !== null && typeof value.then === "function";
}
function observableToPromise(observable) {
  return new Promise((resolve, reject) => {
    const subscription = observable.subscribe({
      next: (v) => {
        resolve(v);
        subscription.unsubscribe();
      },
      error: reject,
      complete: () => {
        reject(new Error("no value resolved"));
      }
    });
  });
}
function isObservable(value) {
  return typeof value === "object" && value !== null && "subscribe" in value && typeof value.subscribe === "function";
}
function isAsyncIterable(input) {
  return typeof input === "object" && input !== null && (input[Symbol.toStringTag] === "AsyncGenerator" || Symbol.asyncIterator in input);
}
function asyncIterableToPromise(input) {
  var _a;
  return __awaiter(this, void 0, void 0, function* () {
    const iteratorReturn = (_a = ("return" in input ? input : input[Symbol.asyncIterator]()).return) === null || _a === void 0 ? void 0 : _a.bind(input);
    const iteratorNext = ("next" in input ? input : input[Symbol.asyncIterator]()).next.bind(input);
    const result = yield iteratorNext();
    void (iteratorReturn === null || iteratorReturn === void 0 ? void 0 : iteratorReturn());
    return result.value;
  });
}
function fetcherReturnToPromise(fetcherResult) {
  return __awaiter(this, void 0, void 0, function* () {
    const result = yield fetcherResult;
    if (isAsyncIterable(result)) {
      return asyncIterableToPromise(result);
    }
    if (isObservable(result)) {
      return observableToPromise(result);
    }
    return result;
  });
}

// node_modules/meros/node/index.mjs
async function e(e2, t) {
  let n = e2.headers["content-type"];
  if (!n || !~n.indexOf("multipart/"))
    return e2;
  let r = n.indexOf("boundary="), i = "-";
  if (~r) {
    let e3 = r + 9, t2 = n.indexOf(";", e3);
    i = n.slice(e3, t2 > -1 ? t2 : void 0).trim().replace(/"/g, "");
  }
  return async function* (e3, t2, n2) {
    let r2, i2, f, a = !n2 || !n2.multiple, l = Buffer.byteLength(t2), o = Buffer.alloc(0), s = [];
    e:
      for await (let n3 of e3) {
        r2 = o.byteLength, o = Buffer.concat([o, n3]);
        let e4 = n3.indexOf(t2);
        for (~e4 ? r2 += e4 : r2 = o.indexOf(t2), s = []; ~r2; ) {
          let e5 = o.subarray(0, r2), n4 = o.subarray(r2 + l);
          if (i2) {
            let t3 = e5.indexOf("\r\n\r\n") + 4, r3 = e5.lastIndexOf("\r\n", t3), i3 = false, l2 = e5.subarray(t3, r3 > -1 ? void 0 : r3), o2 = String(e5.subarray(0, t3)).trim().split("\r\n"), d = {}, y = o2.length;
            for (; f = o2[--y]; f = f.split(": "), d[f.shift().toLowerCase()] = f.join(": "))
              ;
            if (f = d["content-type"], f && ~f.indexOf("application/json"))
              try {
                l2 = JSON.parse(String(l2)), i3 = true;
              } catch (e6) {
              }
            if (f = { headers: d, body: l2, json: i3 }, a ? yield f : s.push(f), 45 === n4[0] && 45 === n4[1])
              break e;
          } else
            t2 = "\r\n" + t2, i2 = l += 2;
          o = n4, r2 = o.indexOf(t2);
        }
        s.length && (yield s);
      }
    s.length && (yield s);
  }(e2, `--${i}`, t);
}

// node_modules/@n1ru4l/push-pull-async-iterable-iterator/index.mjs
function withHandlers(source, onReturn, onThrow) {
  const stream = async function* withReturnSource() {
    yield* source;
  }();
  const originalReturn = stream.return.bind(stream);
  if (onReturn) {
    stream.return = (...args) => {
      onReturn();
      return originalReturn(...args);
    };
  }
  if (onThrow) {
    const originalThrow = stream.throw.bind(stream);
    stream.throw = (err) => {
      onThrow(err);
      return originalThrow(err);
    };
  }
  return stream;
}
function createDeferred() {
  const d = {};
  d.promise = new Promise((resolve, reject) => {
    d.resolve = resolve;
    d.reject = reject;
  });
  return d;
}
function makePushPullAsyncIterableIterator() {
  let state = {
    type: "running"
    /* running */
  };
  let next = createDeferred();
  const values = [];
  function pushValue(value) {
    if (state.type !== "running") {
      return;
    }
    values.push(value);
    next.resolve();
    next = createDeferred();
  }
  const source = async function* PushPullAsyncIterableIterator() {
    while (true) {
      if (values.length > 0) {
        yield values.shift();
      } else {
        if (state.type === "error") {
          throw state.error;
        }
        if (state.type === "finished") {
          return;
        }
        await next.promise;
      }
    }
  }();
  const stream = withHandlers(source, () => {
    if (state.type !== "running") {
      return;
    }
    state = {
      type: "finished"
      /* finished */
    };
    next.resolve();
  }, (error) => {
    if (state.type !== "running") {
      return;
    }
    state = {
      type: "error",
      error
    };
    next.resolve();
  });
  return {
    pushValue,
    asyncIterableIterator: stream
  };
}
var makeAsyncIterableIteratorFromSink = (make) => {
  const { pushValue, asyncIterableIterator } = makePushPullAsyncIterableIterator();
  const dispose = make({
    next: (value) => {
      pushValue(value);
    },
    complete: () => {
      asyncIterableIterator.return();
    },
    error: (err) => {
      asyncIterableIterator.throw(err);
    }
  });
  const originalReturn = asyncIterableIterator.return;
  let returnValue = void 0;
  asyncIterableIterator.return = () => {
    if (returnValue === void 0) {
      dispose();
      returnValue = originalReturn();
    }
    return returnValue;
  };
  return asyncIterableIterator;
};
function isAsyncIterable2(input) {
  return typeof input === "object" && input !== null && // The AsyncGenerator check is for Safari on iOS which currently does not have
  // Symbol.asyncIterator implemented
  // That means every custom AsyncIterable must be built using a AsyncGeneratorFunction (async function * () {})
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  (input[Symbol.toStringTag] === "AsyncGenerator" || Symbol.asyncIterator && Symbol.asyncIterator in input);
}

// node_modules/@graphiql/toolkit/esm/create-fetcher/lib.js
var __awaiter2 = function(thisArg, _arguments, P, generator) {
  function adopt(value) {
    return value instanceof P ? value : new P(function(resolve) {
      resolve(value);
    });
  }
  return new (P || (P = Promise))(function(resolve, reject) {
    function fulfilled(value) {
      try {
        step(generator.next(value));
      } catch (e2) {
        reject(e2);
      }
    }
    function rejected(value) {
      try {
        step(generator["throw"](value));
      } catch (e2) {
        reject(e2);
      }
    }
    function step(result) {
      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
    }
    step((generator = generator.apply(thisArg, _arguments || [])).next());
  });
};
var __await = function(v) {
  return this instanceof __await ? (this.v = v, this) : new __await(v);
};
var __asyncValues = function(o) {
  if (!Symbol.asyncIterator)
    throw new TypeError("Symbol.asyncIterator is not defined.");
  var m = o[Symbol.asyncIterator], i;
  return m ? m.call(o) : (o = typeof __values === "function" ? __values(o) : o[Symbol.iterator](), i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function() {
    return this;
  }, i);
  function verb(n) {
    i[n] = o[n] && function(v) {
      return new Promise(function(resolve, reject) {
        v = o[n](v), settle(resolve, reject, v.done, v.value);
      });
    };
  }
  function settle(resolve, reject, d, v) {
    Promise.resolve(v).then(function(v2) {
      resolve({ value: v2, done: d });
    }, reject);
  }
};
var __asyncGenerator = function(thisArg, _arguments, generator) {
  if (!Symbol.asyncIterator)
    throw new TypeError("Symbol.asyncIterator is not defined.");
  var g = generator.apply(thisArg, _arguments || []), i, q = [];
  return i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function() {
    return this;
  }, i;
  function verb(n) {
    if (g[n])
      i[n] = function(v) {
        return new Promise(function(a, b) {
          q.push([n, v, a, b]) > 1 || resume(n, v);
        });
      };
  }
  function resume(n, v) {
    try {
      step(g[n](v));
    } catch (e2) {
      settle(q[0][3], e2);
    }
  }
  function step(r) {
    r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);
  }
  function fulfill(value) {
    resume("next", value);
  }
  function reject(value) {
    resume("throw", value);
  }
  function settle(f, v) {
    if (f(v), q.shift(), q.length)
      resume(q[0][0], q[0][1]);
  }
};
var errorHasCode = (err) => {
  return typeof err === "object" && err !== null && "code" in err;
};
var isSubscriptionWithName = (document, name) => {
  let isSubscription = false;
  visit(document, {
    OperationDefinition(node) {
      var _a;
      if (name === ((_a = node.name) === null || _a === void 0 ? void 0 : _a.value) && node.operation === "subscription") {
        isSubscription = true;
      }
    }
  });
  return isSubscription;
};
var createSimpleFetcher = (options, httpFetch) => (graphQLParams, fetcherOpts) => __awaiter2(void 0, void 0, void 0, function* () {
  const data = yield httpFetch(options.url, {
    method: "POST",
    body: JSON.stringify(graphQLParams),
    headers: Object.assign(Object.assign({ "content-type": "application/json" }, options.headers), fetcherOpts === null || fetcherOpts === void 0 ? void 0 : fetcherOpts.headers)
  });
  return data.json();
});
var createWebsocketsFetcherFromUrl = (url, connectionParams) => {
  let wsClient;
  try {
    const { createClient } = require_toolkit();
    wsClient = createClient({
      url,
      connectionParams
    });
    return createWebsocketsFetcherFromClient(wsClient);
  } catch (err) {
    if (errorHasCode(err) && err.code === "MODULE_NOT_FOUND") {
      throw new Error("You need to install the 'graphql-ws' package to use websockets when passing a 'subscriptionUrl'");
    }
    console.error(`Error creating websocket client for ${url}`, err);
  }
};
var createWebsocketsFetcherFromClient = (wsClient) => (graphQLParams) => makeAsyncIterableIteratorFromSink((sink) => wsClient.subscribe(graphQLParams, Object.assign(Object.assign({}, sink), { error: (err) => {
  if (err instanceof CloseEvent) {
    sink.error(new Error(`Socket closed with event ${err.code} ${err.reason || ""}`.trim()));
  } else {
    sink.error(err);
  }
} })));
var createLegacyWebsocketsFetcher = (legacyWsClient) => (graphQLParams) => {
  const observable = legacyWsClient.request(graphQLParams);
  return makeAsyncIterableIteratorFromSink((sink) => observable.subscribe(sink).unsubscribe);
};
var createMultipartFetcher = (options, httpFetch) => function(graphQLParams, fetcherOpts) {
  return __asyncGenerator(this, arguments, function* () {
    var e_1, _a;
    const response = yield __await(httpFetch(options.url, {
      method: "POST",
      body: JSON.stringify(graphQLParams),
      headers: Object.assign(Object.assign({ "content-type": "application/json", accept: "application/json, multipart/mixed" }, options.headers), fetcherOpts === null || fetcherOpts === void 0 ? void 0 : fetcherOpts.headers)
    }).then((r) => e(r, {
      multiple: true
    })));
    if (!isAsyncIterable2(response)) {
      return yield __await(yield yield __await(response.json()));
    }
    try {
      for (var response_1 = __asyncValues(response), response_1_1; response_1_1 = yield __await(response_1.next()), !response_1_1.done; ) {
        const chunk = response_1_1.value;
        if (chunk.some((part) => !part.json)) {
          const message = chunk.map((part) => `Headers::
${part.headers}

Body::
${part.body}`);
          throw new Error(`Expected multipart chunks to be of json type. got:
${message}`);
        }
        yield yield __await(chunk.map((part) => part.body));
      }
    } catch (e_1_1) {
      e_1 = { error: e_1_1 };
    } finally {
      try {
        if (response_1_1 && !response_1_1.done && (_a = response_1.return))
          yield __await(_a.call(response_1));
      } finally {
        if (e_1)
          throw e_1.error;
      }
    }
  });
};
var getWsFetcher = (options, fetcherOpts) => {
  if (options.wsClient) {
    return createWebsocketsFetcherFromClient(options.wsClient);
  }
  if (options.subscriptionUrl) {
    return createWebsocketsFetcherFromUrl(options.subscriptionUrl, Object.assign(Object.assign({}, options.wsConnectionParams), fetcherOpts === null || fetcherOpts === void 0 ? void 0 : fetcherOpts.headers));
  }
  const legacyWebsocketsClient = options.legacyClient || options.legacyWsClient;
  if (legacyWebsocketsClient) {
    return createLegacyWebsocketsFetcher(legacyWebsocketsClient);
  }
};

// node_modules/@graphiql/toolkit/esm/create-fetcher/createFetcher.js
function createGraphiQLFetcher(options) {
  let httpFetch;
  if (typeof window !== "undefined" && window.fetch) {
    httpFetch = window.fetch;
  }
  if ((options === null || options === void 0 ? void 0 : options.enableIncrementalDelivery) === null || options.enableIncrementalDelivery !== false) {
    options.enableIncrementalDelivery = true;
  }
  if (options.fetch) {
    httpFetch = options.fetch;
  }
  if (!httpFetch) {
    throw new Error("No valid fetcher implementation available");
  }
  const simpleFetcher = createSimpleFetcher(options, httpFetch);
  const httpFetcher = options.enableIncrementalDelivery ? createMultipartFetcher(options, httpFetch) : simpleFetcher;
  return (graphQLParams, fetcherOpts) => {
    if (graphQLParams.operationName === "IntrospectionQuery") {
      return (options.schemaFetcher || simpleFetcher)(graphQLParams, fetcherOpts);
    }
    const isSubscription = (fetcherOpts === null || fetcherOpts === void 0 ? void 0 : fetcherOpts.documentAST) ? isSubscriptionWithName(fetcherOpts.documentAST, graphQLParams.operationName || void 0) : false;
    if (isSubscription) {
      const wsFetcher = getWsFetcher(options, fetcherOpts);
      if (!wsFetcher) {
        throw new Error(`Your GraphiQL createFetcher is not properly configured for websocket subscriptions yet. ${options.subscriptionUrl ? `Provided URL ${options.subscriptionUrl} failed` : `Please provide subscriptionUrl, wsClient or legacyClient option first.`}`);
      }
      return wsFetcher(graphQLParams);
    }
    return httpFetcher(graphQLParams, fetcherOpts);
  };
}

// node_modules/@graphiql/toolkit/esm/format/index.js
function stringify(obj) {
  return JSON.stringify(obj, null, 2);
}
function formatSingleError(error) {
  return Object.assign(Object.assign({}, error), { message: error.message, stack: error.stack });
}
function handleSingleError(error) {
  if (error instanceof Error) {
    return formatSingleError(error);
  }
  return error;
}
function formatError(error) {
  if (Array.isArray(error)) {
    return stringify({
      errors: error.map((e2) => handleSingleError(e2))
    });
  }
  return stringify({ errors: [handleSingleError(error)] });
}
function formatResult(result) {
  return stringify(result);
}

// node_modules/@graphiql/toolkit/esm/graphql-helpers/auto-complete.js
function fillLeafs(schema, docString, getDefaultFieldNames) {
  const insertions = [];
  if (!schema || !docString) {
    return { insertions, result: docString };
  }
  let ast;
  try {
    ast = parse(docString);
  } catch (_a) {
    return { insertions, result: docString };
  }
  const fieldNameFn = getDefaultFieldNames || defaultGetDefaultFieldNames;
  const typeInfo = new TypeInfo(schema);
  visit(ast, {
    leave(node) {
      typeInfo.leave(node);
    },
    enter(node) {
      typeInfo.enter(node);
      if (node.kind === "Field" && !node.selectionSet) {
        const fieldType = typeInfo.getType();
        const selectionSet = buildSelectionSet(isFieldType(fieldType), fieldNameFn);
        if (selectionSet && node.loc) {
          const indent = getIndentation(docString, node.loc.start);
          insertions.push({
            index: node.loc.end,
            string: " " + print(selectionSet).replaceAll("\n", "\n" + indent)
          });
        }
      }
    }
  });
  return {
    insertions,
    result: withInsertions(docString, insertions)
  };
}
function defaultGetDefaultFieldNames(type) {
  if (!("getFields" in type)) {
    return [];
  }
  const fields = type.getFields();
  if (fields.id) {
    return ["id"];
  }
  if (fields.edges) {
    return ["edges"];
  }
  if (fields.node) {
    return ["node"];
  }
  const leafFieldNames = [];
  for (const fieldName of Object.keys(fields)) {
    if (isLeafType(fields[fieldName].type)) {
      leafFieldNames.push(fieldName);
    }
  }
  return leafFieldNames;
}
function buildSelectionSet(type, getDefaultFieldNames) {
  const namedType = getNamedType(type);
  if (!type || isLeafType(type)) {
    return;
  }
  const fieldNames = getDefaultFieldNames(namedType);
  if (!Array.isArray(fieldNames) || fieldNames.length === 0 || !("getFields" in namedType)) {
    return;
  }
  return {
    kind: Kind.SELECTION_SET,
    selections: fieldNames.map((fieldName) => {
      const fieldDef = namedType.getFields()[fieldName];
      const fieldType = fieldDef ? fieldDef.type : null;
      return {
        kind: Kind.FIELD,
        name: {
          kind: Kind.NAME,
          value: fieldName
        },
        selectionSet: buildSelectionSet(fieldType, getDefaultFieldNames)
      };
    })
  };
}
function withInsertions(initial, insertions) {
  if (insertions.length === 0) {
    return initial;
  }
  let edited = "";
  let prevIndex = 0;
  for (const { index, string } of insertions) {
    edited += initial.slice(prevIndex, index) + string;
    prevIndex = index;
  }
  edited += initial.slice(prevIndex);
  return edited;
}
function getIndentation(str, index) {
  let indentStart = index;
  let indentEnd = index;
  while (indentStart) {
    const c = str.charCodeAt(indentStart - 1);
    if (c === 10 || c === 13 || c === 8232 || c === 8233) {
      break;
    }
    indentStart--;
    if (c !== 9 && c !== 11 && c !== 12 && c !== 32 && c !== 160) {
      indentEnd = indentStart;
    }
  }
  return str.slice(indentStart, indentEnd);
}
function isFieldType(fieldType) {
  if (fieldType) {
    return fieldType;
  }
}

// node_modules/@graphiql/toolkit/esm/graphql-helpers/merge-ast.js
function uniqueBy(array, iteratee) {
  var _a;
  const FilteredMap = /* @__PURE__ */ new Map();
  const result = [];
  for (const item of array) {
    if (item.kind === "Field") {
      const uniqueValue = iteratee(item);
      const existing = FilteredMap.get(uniqueValue);
      if ((_a = item.directives) === null || _a === void 0 ? void 0 : _a.length) {
        const itemClone = Object.assign({}, item);
        result.push(itemClone);
      } else if ((existing === null || existing === void 0 ? void 0 : existing.selectionSet) && item.selectionSet) {
        existing.selectionSet.selections = [
          ...existing.selectionSet.selections,
          ...item.selectionSet.selections
        ];
      } else if (!existing) {
        const itemClone = Object.assign({}, item);
        FilteredMap.set(uniqueValue, itemClone);
        result.push(itemClone);
      }
    } else {
      result.push(item);
    }
  }
  return result;
}
function inlineRelevantFragmentSpreads(fragmentDefinitions, selections, selectionSetType) {
  var _a;
  const selectionSetTypeName = selectionSetType ? getNamedType(selectionSetType).name : null;
  const outputSelections = [];
  const seenSpreads = [];
  for (let selection of selections) {
    if (selection.kind === "FragmentSpread") {
      const fragmentName = selection.name.value;
      if (!selection.directives || selection.directives.length === 0) {
        if (seenSpreads.includes(fragmentName)) {
          continue;
        } else {
          seenSpreads.push(fragmentName);
        }
      }
      const fragmentDefinition = fragmentDefinitions[selection.name.value];
      if (fragmentDefinition) {
        const { typeCondition, directives, selectionSet } = fragmentDefinition;
        selection = {
          kind: Kind.INLINE_FRAGMENT,
          typeCondition,
          directives,
          selectionSet
        };
      }
    }
    if (selection.kind === Kind.INLINE_FRAGMENT && (!selection.directives || ((_a = selection.directives) === null || _a === void 0 ? void 0 : _a.length) === 0)) {
      const fragmentTypeName = selection.typeCondition ? selection.typeCondition.name.value : null;
      if (!fragmentTypeName || fragmentTypeName === selectionSetTypeName) {
        outputSelections.push(...inlineRelevantFragmentSpreads(fragmentDefinitions, selection.selectionSet.selections, selectionSetType));
        continue;
      }
    }
    outputSelections.push(selection);
  }
  return outputSelections;
}
function mergeAst(documentAST, schema) {
  const typeInfo = schema ? new TypeInfo(schema) : null;
  const fragmentDefinitions = /* @__PURE__ */ Object.create(null);
  for (const definition of documentAST.definitions) {
    if (definition.kind === Kind.FRAGMENT_DEFINITION) {
      fragmentDefinitions[definition.name.value] = definition;
    }
  }
  const visitors = {
    SelectionSet(node) {
      const selectionSetType = typeInfo ? typeInfo.getParentType() : null;
      let { selections } = node;
      selections = inlineRelevantFragmentSpreads(fragmentDefinitions, selections, selectionSetType);
      selections = uniqueBy(selections, (selection) => selection.alias ? selection.alias.value : selection.name.value);
      return Object.assign(Object.assign({}, node), { selections });
    },
    FragmentDefinition() {
      return null;
    }
  };
  return visit(documentAST, typeInfo ? visitWithTypeInfo(typeInfo, visitors) : visitors);
}

// node_modules/@graphiql/toolkit/esm/graphql-helpers/operation-name.js
function getSelectedOperationName(prevOperations, prevSelectedOperationName, operations) {
  if (!operations || operations.length < 1) {
    return;
  }
  const names = operations.map((op) => {
    var _a;
    return (_a = op.name) === null || _a === void 0 ? void 0 : _a.value;
  });
  if (prevSelectedOperationName && names.includes(prevSelectedOperationName)) {
    return prevSelectedOperationName;
  }
  if (prevSelectedOperationName && prevOperations) {
    const prevNames = prevOperations.map((op) => {
      var _a;
      return (_a = op.name) === null || _a === void 0 ? void 0 : _a.value;
    });
    const prevIndex = prevNames.indexOf(prevSelectedOperationName);
    if (prevIndex !== -1 && prevIndex < names.length) {
      return names[prevIndex];
    }
  }
  return names[0];
}

// node_modules/@graphiql/toolkit/esm/storage/base.js
function isQuotaError(storage, e2) {
  return e2 instanceof DOMException && (e2.code === 22 || e2.code === 1014 || e2.name === "QuotaExceededError" || e2.name === "NS_ERROR_DOM_QUOTA_REACHED") && storage.length !== 0;
}
var StorageAPI = class {
  constructor(storage) {
    if (storage) {
      this.storage = storage;
    } else if (storage === null) {
      this.storage = null;
    } else if (typeof window === "undefined") {
      this.storage = null;
    } else {
      this.storage = {
        getItem: window.localStorage.getItem.bind(window.localStorage),
        setItem: window.localStorage.setItem.bind(window.localStorage),
        removeItem: window.localStorage.removeItem.bind(window.localStorage),
        get length() {
          let keys = 0;
          for (const key in window.localStorage) {
            if (key.indexOf(`${STORAGE_NAMESPACE}:`) === 0) {
              keys += 1;
            }
          }
          return keys;
        },
        clear: () => {
          for (const key in window.localStorage) {
            if (key.indexOf(`${STORAGE_NAMESPACE}:`) === 0) {
              window.localStorage.removeItem(key);
            }
          }
        }
      };
    }
  }
  get(name) {
    if (!this.storage) {
      return null;
    }
    const key = `${STORAGE_NAMESPACE}:${name}`;
    const value = this.storage.getItem(key);
    if (value === "null" || value === "undefined") {
      this.storage.removeItem(key);
      return null;
    }
    return value || null;
  }
  set(name, value) {
    let quotaError = false;
    let error = null;
    if (this.storage) {
      const key = `${STORAGE_NAMESPACE}:${name}`;
      if (value) {
        try {
          this.storage.setItem(key, value);
        } catch (e2) {
          error = e2 instanceof Error ? e2 : new Error(`${e2}`);
          quotaError = isQuotaError(this.storage, e2);
        }
      } else {
        this.storage.removeItem(key);
      }
    }
    return { isQuotaError: quotaError, error };
  }
  clear() {
    if (this.storage) {
      this.storage.clear();
    }
  }
};
var STORAGE_NAMESPACE = "graphiql";

// node_modules/@graphiql/toolkit/esm/storage/query.js
var QueryStore = class {
  constructor(key, storage, maxSize = null) {
    this.key = key;
    this.storage = storage;
    this.maxSize = maxSize;
    this.items = this.fetchAll();
  }
  get length() {
    return this.items.length;
  }
  contains(item) {
    return this.items.some((x) => x.query === item.query && x.variables === item.variables && x.headers === item.headers && x.operationName === item.operationName);
  }
  edit(item) {
    const itemIndex = this.items.findIndex((x) => x.query === item.query && x.variables === item.variables && x.headers === item.headers && x.operationName === item.operationName);
    if (itemIndex !== -1) {
      this.items.splice(itemIndex, 1, item);
      this.save();
    }
  }
  delete(item) {
    const itemIndex = this.items.findIndex((x) => x.query === item.query && x.variables === item.variables && x.headers === item.headers && x.operationName === item.operationName);
    if (itemIndex !== -1) {
      this.items.splice(itemIndex, 1);
      this.save();
    }
  }
  fetchRecent() {
    return this.items.at(-1);
  }
  fetchAll() {
    const raw = this.storage.get(this.key);
    if (raw) {
      return JSON.parse(raw)[this.key];
    }
    return [];
  }
  push(item) {
    const items = [...this.items, item];
    if (this.maxSize && items.length > this.maxSize) {
      items.shift();
    }
    for (let attempts = 0; attempts < 5; attempts++) {
      const response = this.storage.set(this.key, JSON.stringify({ [this.key]: items }));
      if (!(response === null || response === void 0 ? void 0 : response.error)) {
        this.items = items;
      } else if (response.isQuotaError && this.maxSize) {
        items.shift();
      } else {
        return;
      }
    }
  }
  save() {
    this.storage.set(this.key, JSON.stringify({ [this.key]: this.items }));
  }
};

// node_modules/@graphiql/toolkit/esm/storage/history.js
var MAX_QUERY_SIZE = 1e5;
var HistoryStore = class {
  constructor(storage, maxHistoryLength) {
    this.storage = storage;
    this.maxHistoryLength = maxHistoryLength;
    this.updateHistory = (query, variables, headers, operationName) => {
      if (this.shouldSaveQuery(query, variables, headers, this.history.fetchRecent())) {
        this.history.push({
          query,
          variables,
          headers,
          operationName
        });
        const historyQueries = this.history.items;
        const favoriteQueries = this.favorite.items;
        this.queries = historyQueries.concat(favoriteQueries);
      }
    };
    this.history = new QueryStore("queries", this.storage, this.maxHistoryLength);
    this.favorite = new QueryStore("favorites", this.storage, null);
    this.queries = [...this.history.fetchAll(), ...this.favorite.fetchAll()];
  }
  shouldSaveQuery(query, variables, headers, lastQuerySaved) {
    if (!query) {
      return false;
    }
    try {
      parse(query);
    } catch (_a) {
      return false;
    }
    if (query.length > MAX_QUERY_SIZE) {
      return false;
    }
    if (!lastQuerySaved) {
      return true;
    }
    if (JSON.stringify(query) === JSON.stringify(lastQuerySaved.query)) {
      if (JSON.stringify(variables) === JSON.stringify(lastQuerySaved.variables)) {
        if (JSON.stringify(headers) === JSON.stringify(lastQuerySaved.headers)) {
          return false;
        }
        if (headers && !lastQuerySaved.headers) {
          return false;
        }
      }
      if (variables && !lastQuerySaved.variables) {
        return false;
      }
    }
    return true;
  }
  toggleFavorite(query, variables, headers, operationName, label, favorite) {
    const item = {
      query,
      variables,
      headers,
      operationName,
      label
    };
    if (!this.favorite.contains(item)) {
      item.favorite = true;
      this.favorite.push(item);
    } else if (favorite) {
      item.favorite = false;
      this.favorite.delete(item);
    }
    this.queries = [...this.history.items, ...this.favorite.items];
  }
  editLabel(query, variables, headers, operationName, label, favorite) {
    const item = {
      query,
      variables,
      headers,
      operationName,
      label
    };
    if (favorite) {
      this.favorite.edit(Object.assign(Object.assign({}, item), { favorite }));
    } else {
      this.history.edit(item);
    }
    this.queries = [...this.history.items, ...this.favorite.items];
  }
};

export {
  isPromise,
  isObservable,
  isAsyncIterable,
  fetcherReturnToPromise,
  createGraphiQLFetcher,
  formatError,
  formatResult,
  fillLeafs,
  mergeAst,
  getSelectedOperationName,
  StorageAPI,
  QueryStore,
  HistoryStore
};
//# sourceMappingURL=chunk-BRI56Q3P.js.map
