import {
  require_react
} from "./chunk-AWTNXPUB.js";
import {
  __toESM
} from "./chunk-AUZ3RYOM.js";

// node_modules/@heroicons/react/solid/esm/AcademicCapIcon.js
var React = __toESM(require_react(), 1);
function AcademicCapIcon(props, svgRef) {
  return React.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React.createElement("path", {
    d: "M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"
  }));
}
var ForwardRef = React.forwardRef(AcademicCapIcon);
var AcademicCapIcon_default = ForwardRef;

// node_modules/@heroicons/react/solid/esm/AdjustmentsIcon.js
var React2 = __toESM(require_react(), 1);
function AdjustmentsIcon(props, svgRef) {
  return React2.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React2.createElement("path", {
    d: "M5 4a1 1 0 00-2 0v7.268a2 2 0 000 3.464V16a1 1 0 102 0v-1.268a2 2 0 000-3.464V4zM11 4a1 1 0 10-2 0v1.268a2 2 0 000 3.464V16a1 1 0 102 0V8.732a2 2 0 000-3.464V4zM16 3a1 1 0 011 1v7.268a2 2 0 010 3.464V16a1 1 0 11-2 0v-1.268a2 2 0 010-3.464V4a1 1 0 011-1z"
  }));
}
var ForwardRef2 = React2.forwardRef(AdjustmentsIcon);
var AdjustmentsIcon_default = ForwardRef2;

// node_modules/@heroicons/react/solid/esm/AnnotationIcon.js
var React3 = __toESM(require_react(), 1);
function AnnotationIcon(props, svgRef) {
  return React3.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React3.createElement("path", {
    fillRule: "evenodd",
    d: "M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z",
    clipRule: "evenodd"
  }));
}
var ForwardRef3 = React3.forwardRef(AnnotationIcon);
var AnnotationIcon_default = ForwardRef3;

// node_modules/@heroicons/react/solid/esm/ArchiveIcon.js
var React4 = __toESM(require_react(), 1);
function ArchiveIcon(props, svgRef) {
  return React4.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React4.createElement("path", {
    d: "M4 3a2 2 0 100 4h12a2 2 0 100-4H4z"
  }), React4.createElement("path", {
    fillRule: "evenodd",
    d: "M3 8h14v7a2 2 0 01-2 2H5a2 2 0 01-2-2V8zm5 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z",
    clipRule: "evenodd"
  }));
}
var ForwardRef4 = React4.forwardRef(ArchiveIcon);
var ArchiveIcon_default = ForwardRef4;

// node_modules/@heroicons/react/solid/esm/ArrowCircleDownIcon.js
var React5 = __toESM(require_react(), 1);
function ArrowCircleDownIcon(props, svgRef) {
  return React5.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React5.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v3.586L7.707 9.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 10.586V7z",
    clipRule: "evenodd"
  }));
}
var ForwardRef5 = React5.forwardRef(ArrowCircleDownIcon);
var ArrowCircleDownIcon_default = ForwardRef5;

// node_modules/@heroicons/react/solid/esm/ArrowCircleLeftIcon.js
var React6 = __toESM(require_react(), 1);
function ArrowCircleLeftIcon(props, svgRef) {
  return React6.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React6.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 100-16 8 8 0 000 16zm.707-10.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L9.414 11H13a1 1 0 100-2H9.414l1.293-1.293z",
    clipRule: "evenodd"
  }));
}
var ForwardRef6 = React6.forwardRef(ArrowCircleLeftIcon);
var ArrowCircleLeftIcon_default = ForwardRef6;

// node_modules/@heroicons/react/solid/esm/ArrowCircleRightIcon.js
var React7 = __toESM(require_react(), 1);
function ArrowCircleRightIcon(props, svgRef) {
  return React7.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React7.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z",
    clipRule: "evenodd"
  }));
}
var ForwardRef7 = React7.forwardRef(ArrowCircleRightIcon);
var ArrowCircleRightIcon_default = ForwardRef7;

// node_modules/@heroicons/react/solid/esm/ArrowCircleUpIcon.js
var React8 = __toESM(require_react(), 1);
function ArrowCircleUpIcon(props, svgRef) {
  return React8.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React8.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z",
    clipRule: "evenodd"
  }));
}
var ForwardRef8 = React8.forwardRef(ArrowCircleUpIcon);
var ArrowCircleUpIcon_default = ForwardRef8;

// node_modules/@heroicons/react/solid/esm/ArrowDownIcon.js
var React9 = __toESM(require_react(), 1);
function ArrowDownIcon(props, svgRef) {
  return React9.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React9.createElement("path", {
    fillRule: "evenodd",
    d: "M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l4.293-4.293a1 1 0 011.414 0z",
    clipRule: "evenodd"
  }));
}
var ForwardRef9 = React9.forwardRef(ArrowDownIcon);
var ArrowDownIcon_default = ForwardRef9;

// node_modules/@heroicons/react/solid/esm/ArrowLeftIcon.js
var React10 = __toESM(require_react(), 1);
function ArrowLeftIcon(props, svgRef) {
  return React10.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React10.createElement("path", {
    fillRule: "evenodd",
    d: "M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z",
    clipRule: "evenodd"
  }));
}
var ForwardRef10 = React10.forwardRef(ArrowLeftIcon);
var ArrowLeftIcon_default = ForwardRef10;

// node_modules/@heroicons/react/solid/esm/ArrowNarrowDownIcon.js
var React11 = __toESM(require_react(), 1);
function ArrowNarrowDownIcon(props, svgRef) {
  return React11.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React11.createElement("path", {
    fillRule: "evenodd",
    d: "M14.707 12.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l2.293-2.293a1 1 0 011.414 0z",
    clipRule: "evenodd"
  }));
}
var ForwardRef11 = React11.forwardRef(ArrowNarrowDownIcon);
var ArrowNarrowDownIcon_default = ForwardRef11;

// node_modules/@heroicons/react/solid/esm/ArrowNarrowLeftIcon.js
var React12 = __toESM(require_react(), 1);
function ArrowNarrowLeftIcon(props, svgRef) {
  return React12.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React12.createElement("path", {
    fillRule: "evenodd",
    d: "M7.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l2.293 2.293a1 1 0 010 1.414z",
    clipRule: "evenodd"
  }));
}
var ForwardRef12 = React12.forwardRef(ArrowNarrowLeftIcon);
var ArrowNarrowLeftIcon_default = ForwardRef12;

// node_modules/@heroicons/react/solid/esm/ArrowNarrowRightIcon.js
var React13 = __toESM(require_react(), 1);
function ArrowNarrowRightIcon(props, svgRef) {
  return React13.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React13.createElement("path", {
    fillRule: "evenodd",
    d: "M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z",
    clipRule: "evenodd"
  }));
}
var ForwardRef13 = React13.forwardRef(ArrowNarrowRightIcon);
var ArrowNarrowRightIcon_default = ForwardRef13;

// node_modules/@heroicons/react/solid/esm/ArrowNarrowUpIcon.js
var React14 = __toESM(require_react(), 1);
function ArrowNarrowUpIcon(props, svgRef) {
  return React14.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React14.createElement("path", {
    fillRule: "evenodd",
    d: "M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z",
    clipRule: "evenodd"
  }));
}
var ForwardRef14 = React14.forwardRef(ArrowNarrowUpIcon);
var ArrowNarrowUpIcon_default = ForwardRef14;

// node_modules/@heroicons/react/solid/esm/ArrowRightIcon.js
var React15 = __toESM(require_react(), 1);
function ArrowRightIcon(props, svgRef) {
  return React15.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React15.createElement("path", {
    fillRule: "evenodd",
    d: "M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z",
    clipRule: "evenodd"
  }));
}
var ForwardRef15 = React15.forwardRef(ArrowRightIcon);
var ArrowRightIcon_default = ForwardRef15;

// node_modules/@heroicons/react/solid/esm/ArrowSmDownIcon.js
var React16 = __toESM(require_react(), 1);
function ArrowSmDownIcon(props, svgRef) {
  return React16.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React16.createElement("path", {
    fillRule: "evenodd",
    d: "M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z",
    clipRule: "evenodd"
  }));
}
var ForwardRef16 = React16.forwardRef(ArrowSmDownIcon);
var ArrowSmDownIcon_default = ForwardRef16;

// node_modules/@heroicons/react/solid/esm/ArrowSmLeftIcon.js
var React17 = __toESM(require_react(), 1);
function ArrowSmLeftIcon(props, svgRef) {
  return React17.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React17.createElement("path", {
    fillRule: "evenodd",
    d: "M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z",
    clipRule: "evenodd"
  }));
}
var ForwardRef17 = React17.forwardRef(ArrowSmLeftIcon);
var ArrowSmLeftIcon_default = ForwardRef17;

// node_modules/@heroicons/react/solid/esm/ArrowSmRightIcon.js
var React18 = __toESM(require_react(), 1);
function ArrowSmRightIcon(props, svgRef) {
  return React18.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React18.createElement("path", {
    fillRule: "evenodd",
    d: "M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z",
    clipRule: "evenodd"
  }));
}
var ForwardRef18 = React18.forwardRef(ArrowSmRightIcon);
var ArrowSmRightIcon_default = ForwardRef18;

// node_modules/@heroicons/react/solid/esm/ArrowSmUpIcon.js
var React19 = __toESM(require_react(), 1);
function ArrowSmUpIcon(props, svgRef) {
  return React19.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React19.createElement("path", {
    fillRule: "evenodd",
    d: "M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z",
    clipRule: "evenodd"
  }));
}
var ForwardRef19 = React19.forwardRef(ArrowSmUpIcon);
var ArrowSmUpIcon_default = ForwardRef19;

// node_modules/@heroicons/react/solid/esm/ArrowUpIcon.js
var React20 = __toESM(require_react(), 1);
function ArrowUpIcon(props, svgRef) {
  return React20.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React20.createElement("path", {
    fillRule: "evenodd",
    d: "M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L4.707 9.707a1 1 0 01-1.414 0z",
    clipRule: "evenodd"
  }));
}
var ForwardRef20 = React20.forwardRef(ArrowUpIcon);
var ArrowUpIcon_default = ForwardRef20;

// node_modules/@heroicons/react/solid/esm/ArrowsExpandIcon.js
var React21 = __toESM(require_react(), 1);
function ArrowsExpandIcon(props, svgRef) {
  return React21.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React21.createElement("path", {
    fillRule: "evenodd",
    d: "M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 01-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 110-2h4a1 1 0 011 1v4a1 1 0 11-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 7a1 1 0 112 0v1.586l2.293-2.293a1 1 0 011.414 1.414L6.414 15H8a1 1 0 110 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 110-2h1.586l-2.293-2.293a1 1 0 011.414-1.414L15 13.586V12a1 1 0 011-1z",
    clipRule: "evenodd"
  }));
}
var ForwardRef21 = React21.forwardRef(ArrowsExpandIcon);
var ArrowsExpandIcon_default = ForwardRef21;

// node_modules/@heroicons/react/solid/esm/AtSymbolIcon.js
var React22 = __toESM(require_react(), 1);
function AtSymbolIcon(props, svgRef) {
  return React22.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React22.createElement("path", {
    fillRule: "evenodd",
    d: "M14.243 5.757a6 6 0 10-.986 9.284 1 1 0 111.087 1.678A8 8 0 1118 10a3 3 0 01-4.8 2.401A4 4 0 1114 10a1 1 0 102 0c0-1.537-.586-3.07-1.757-4.243zM12 10a2 2 0 10-4 0 2 2 0 004 0z",
    clipRule: "evenodd"
  }));
}
var ForwardRef22 = React22.forwardRef(AtSymbolIcon);
var AtSymbolIcon_default = ForwardRef22;

// node_modules/@heroicons/react/solid/esm/BackspaceIcon.js
var React23 = __toESM(require_react(), 1);
function BackspaceIcon(props, svgRef) {
  return React23.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React23.createElement("path", {
    fillRule: "evenodd",
    d: "M6.707 4.879A3 3 0 018.828 4H15a3 3 0 013 3v6a3 3 0 01-3 3H8.828a3 3 0 01-2.12-.879l-4.415-4.414a1 1 0 010-1.414l4.414-4.414zm4 2.414a1 1 0 00-1.414 1.414L10.586 10l-1.293 1.293a1 1 0 101.414 1.414L12 11.414l1.293 1.293a1 1 0 001.414-1.414L13.414 10l1.293-1.293a1 1 0 00-1.414-1.414L12 8.586l-1.293-1.293z",
    clipRule: "evenodd"
  }));
}
var ForwardRef23 = React23.forwardRef(BackspaceIcon);
var BackspaceIcon_default = ForwardRef23;

// node_modules/@heroicons/react/solid/esm/BadgeCheckIcon.js
var React24 = __toESM(require_react(), 1);
function BadgeCheckIcon(props, svgRef) {
  return React24.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React24.createElement("path", {
    fillRule: "evenodd",
    d: "M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",
    clipRule: "evenodd"
  }));
}
var ForwardRef24 = React24.forwardRef(BadgeCheckIcon);
var BadgeCheckIcon_default = ForwardRef24;

// node_modules/@heroicons/react/solid/esm/BanIcon.js
var React25 = __toESM(require_react(), 1);
function BanIcon(props, svgRef) {
  return React25.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React25.createElement("path", {
    fillRule: "evenodd",
    d: "M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z",
    clipRule: "evenodd"
  }));
}
var ForwardRef25 = React25.forwardRef(BanIcon);
var BanIcon_default = ForwardRef25;

// node_modules/@heroicons/react/solid/esm/BeakerIcon.js
var React26 = __toESM(require_react(), 1);
function BeakerIcon(props, svgRef) {
  return React26.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React26.createElement("path", {
    fillRule: "evenodd",
    d: "M7 2a1 1 0 00-.707 1.707L7 4.414v3.758a1 1 0 01-.293.707l-4 4C.817 14.769 2.156 18 4.828 18h10.343c2.673 0 4.012-3.231 2.122-5.121l-4-4A1 1 0 0113 8.172V4.414l.707-.707A1 1 0 0013 2H7zm2 6.172V4h2v4.172a3 3 0 00.879 2.12l1.027 1.028a4 4 0 00-2.171.102l-.47.156a4 4 0 01-2.53 0l-.563-.187a1.993 1.993 0 00-.114-.035l1.063-1.063A3 3 0 009 8.172z",
    clipRule: "evenodd"
  }));
}
var ForwardRef26 = React26.forwardRef(BeakerIcon);
var BeakerIcon_default = ForwardRef26;

// node_modules/@heroicons/react/solid/esm/BellIcon.js
var React27 = __toESM(require_react(), 1);
function BellIcon(props, svgRef) {
  return React27.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React27.createElement("path", {
    d: "M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"
  }));
}
var ForwardRef27 = React27.forwardRef(BellIcon);
var BellIcon_default = ForwardRef27;

// node_modules/@heroicons/react/solid/esm/BookOpenIcon.js
var React28 = __toESM(require_react(), 1);
function BookOpenIcon(props, svgRef) {
  return React28.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React28.createElement("path", {
    d: "M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z"
  }));
}
var ForwardRef28 = React28.forwardRef(BookOpenIcon);
var BookOpenIcon_default = ForwardRef28;

// node_modules/@heroicons/react/solid/esm/BookmarkAltIcon.js
var React29 = __toESM(require_react(), 1);
function BookmarkAltIcon(props, svgRef) {
  return React29.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React29.createElement("path", {
    fillRule: "evenodd",
    d: "M3 5a2 2 0 012-2h10a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2V5zm11 1H6v8l4-2 4 2V6z",
    clipRule: "evenodd"
  }));
}
var ForwardRef29 = React29.forwardRef(BookmarkAltIcon);
var BookmarkAltIcon_default = ForwardRef29;

// node_modules/@heroicons/react/solid/esm/BookmarkIcon.js
var React30 = __toESM(require_react(), 1);
function BookmarkIcon(props, svgRef) {
  return React30.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React30.createElement("path", {
    d: "M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z"
  }));
}
var ForwardRef30 = React30.forwardRef(BookmarkIcon);
var BookmarkIcon_default = ForwardRef30;

// node_modules/@heroicons/react/solid/esm/BriefcaseIcon.js
var React31 = __toESM(require_react(), 1);
function BriefcaseIcon(props, svgRef) {
  return React31.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React31.createElement("path", {
    fillRule: "evenodd",
    d: "M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z",
    clipRule: "evenodd"
  }), React31.createElement("path", {
    d: "M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z"
  }));
}
var ForwardRef31 = React31.forwardRef(BriefcaseIcon);
var BriefcaseIcon_default = ForwardRef31;

// node_modules/@heroicons/react/solid/esm/CakeIcon.js
var React32 = __toESM(require_react(), 1);
function CakeIcon(props, svgRef) {
  return React32.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React32.createElement("path", {
    fillRule: "evenodd",
    d: "M6 3a1 1 0 011-1h.01a1 1 0 010 2H7a1 1 0 01-1-1zm2 3a1 1 0 00-2 0v1a2 2 0 00-2 2v1a2 2 0 00-2 2v.683a3.7 3.7 0 011.055.485 1.704 1.704 0 001.89 0 3.704 3.704 0 014.11 0 1.704 1.704 0 001.89 0 3.704 3.704 0 014.11 0 1.704 1.704 0 001.89 0A3.7 3.7 0 0118 12.683V12a2 2 0 00-2-2V9a2 2 0 00-2-2V6a1 1 0 10-2 0v1h-1V6a1 1 0 10-2 0v1H8V6zm10 8.868a3.704 3.704 0 01-4.055-.036 1.704 1.704 0 00-1.89 0 3.704 3.704 0 01-4.11 0 1.704 1.704 0 00-1.89 0A3.704 3.704 0 012 14.868V17a1 1 0 001 1h14a1 1 0 001-1v-2.132zM9 3a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1zm3 0a1 1 0 011-1h.01a1 1 0 110 2H13a1 1 0 01-1-1z",
    clipRule: "evenodd"
  }));
}
var ForwardRef32 = React32.forwardRef(CakeIcon);
var CakeIcon_default = ForwardRef32;

// node_modules/@heroicons/react/solid/esm/CalculatorIcon.js
var React33 = __toESM(require_react(), 1);
function CalculatorIcon(props, svgRef) {
  return React33.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React33.createElement("path", {
    fillRule: "evenodd",
    d: "M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H6zm1 2a1 1 0 000 2h6a1 1 0 100-2H7zm6 7a1 1 0 011 1v3a1 1 0 11-2 0v-3a1 1 0 011-1zm-3 3a1 1 0 100 2h.01a1 1 0 100-2H10zm-4 1a1 1 0 011-1h.01a1 1 0 110 2H7a1 1 0 01-1-1zm1-4a1 1 0 100 2h.01a1 1 0 100-2H7zm2 1a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1zm4-4a1 1 0 100 2h.01a1 1 0 100-2H13zM9 9a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1zM7 8a1 1 0 000 2h.01a1 1 0 000-2H7z",
    clipRule: "evenodd"
  }));
}
var ForwardRef33 = React33.forwardRef(CalculatorIcon);
var CalculatorIcon_default = ForwardRef33;

// node_modules/@heroicons/react/solid/esm/CalendarIcon.js
var React34 = __toESM(require_react(), 1);
function CalendarIcon(props, svgRef) {
  return React34.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React34.createElement("path", {
    fillRule: "evenodd",
    d: "M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z",
    clipRule: "evenodd"
  }));
}
var ForwardRef34 = React34.forwardRef(CalendarIcon);
var CalendarIcon_default = ForwardRef34;

// node_modules/@heroicons/react/solid/esm/CameraIcon.js
var React35 = __toESM(require_react(), 1);
function CameraIcon(props, svgRef) {
  return React35.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React35.createElement("path", {
    fillRule: "evenodd",
    d: "M4 5a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V7a2 2 0 00-2-2h-1.586a1 1 0 01-.707-.293l-1.121-1.121A2 2 0 0011.172 3H8.828a2 2 0 00-1.414.586L6.293 4.707A1 1 0 015.586 5H4zm6 9a3 3 0 100-6 3 3 0 000 6z",
    clipRule: "evenodd"
  }));
}
var ForwardRef35 = React35.forwardRef(CameraIcon);
var CameraIcon_default = ForwardRef35;

// node_modules/@heroicons/react/solid/esm/CashIcon.js
var React36 = __toESM(require_react(), 1);
function CashIcon(props, svgRef) {
  return React36.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React36.createElement("path", {
    fillRule: "evenodd",
    d: "M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z",
    clipRule: "evenodd"
  }));
}
var ForwardRef36 = React36.forwardRef(CashIcon);
var CashIcon_default = ForwardRef36;

// node_modules/@heroicons/react/solid/esm/ChartBarIcon.js
var React37 = __toESM(require_react(), 1);
function ChartBarIcon(props, svgRef) {
  return React37.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React37.createElement("path", {
    d: "M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"
  }));
}
var ForwardRef37 = React37.forwardRef(ChartBarIcon);
var ChartBarIcon_default = ForwardRef37;

// node_modules/@heroicons/react/solid/esm/ChartPieIcon.js
var React38 = __toESM(require_react(), 1);
function ChartPieIcon(props, svgRef) {
  return React38.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React38.createElement("path", {
    d: "M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"
  }), React38.createElement("path", {
    d: "M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"
  }));
}
var ForwardRef38 = React38.forwardRef(ChartPieIcon);
var ChartPieIcon_default = ForwardRef38;

// node_modules/@heroicons/react/solid/esm/ChartSquareBarIcon.js
var React39 = __toESM(require_react(), 1);
function ChartSquareBarIcon(props, svgRef) {
  return React39.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React39.createElement("path", {
    fillRule: "evenodd",
    d: "M5 3a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2V5a2 2 0 00-2-2H5zm9 4a1 1 0 10-2 0v6a1 1 0 102 0V7zm-3 2a1 1 0 10-2 0v4a1 1 0 102 0V9zm-3 3a1 1 0 10-2 0v1a1 1 0 102 0v-1z",
    clipRule: "evenodd"
  }));
}
var ForwardRef39 = React39.forwardRef(ChartSquareBarIcon);
var ChartSquareBarIcon_default = ForwardRef39;

// node_modules/@heroicons/react/solid/esm/ChatAlt2Icon.js
var React40 = __toESM(require_react(), 1);
function ChatAlt2Icon(props, svgRef) {
  return React40.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React40.createElement("path", {
    d: "M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z"
  }), React40.createElement("path", {
    d: "M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z"
  }));
}
var ForwardRef40 = React40.forwardRef(ChatAlt2Icon);
var ChatAlt2Icon_default = ForwardRef40;

// node_modules/@heroicons/react/solid/esm/ChatAltIcon.js
var React41 = __toESM(require_react(), 1);
function ChatAltIcon(props, svgRef) {
  return React41.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React41.createElement("path", {
    fillRule: "evenodd",
    d: "M18 5v8a2 2 0 01-2 2h-5l-5 4v-4H4a2 2 0 01-2-2V5a2 2 0 012-2h12a2 2 0 012 2zM7 8H5v2h2V8zm2 0h2v2H9V8zm6 0h-2v2h2V8z",
    clipRule: "evenodd"
  }));
}
var ForwardRef41 = React41.forwardRef(ChatAltIcon);
var ChatAltIcon_default = ForwardRef41;

// node_modules/@heroicons/react/solid/esm/ChatIcon.js
var React42 = __toESM(require_react(), 1);
function ChatIcon(props, svgRef) {
  return React42.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React42.createElement("path", {
    fillRule: "evenodd",
    d: "M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z",
    clipRule: "evenodd"
  }));
}
var ForwardRef42 = React42.forwardRef(ChatIcon);
var ChatIcon_default = ForwardRef42;

// node_modules/@heroicons/react/solid/esm/CheckCircleIcon.js
var React43 = __toESM(require_react(), 1);
function CheckCircleIcon(props, svgRef) {
  return React43.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React43.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",
    clipRule: "evenodd"
  }));
}
var ForwardRef43 = React43.forwardRef(CheckCircleIcon);
var CheckCircleIcon_default = ForwardRef43;

// node_modules/@heroicons/react/solid/esm/CheckIcon.js
var React44 = __toESM(require_react(), 1);
function CheckIcon(props, svgRef) {
  return React44.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React44.createElement("path", {
    fillRule: "evenodd",
    d: "M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",
    clipRule: "evenodd"
  }));
}
var ForwardRef44 = React44.forwardRef(CheckIcon);
var CheckIcon_default = ForwardRef44;

// node_modules/@heroicons/react/solid/esm/ChevronDoubleDownIcon.js
var React45 = __toESM(require_react(), 1);
function ChevronDoubleDownIcon(props, svgRef) {
  return React45.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React45.createElement("path", {
    fillRule: "evenodd",
    d: "M15.707 4.293a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-5-5a1 1 0 011.414-1.414L10 8.586l4.293-4.293a1 1 0 011.414 0zm0 6a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-5-5a1 1 0 111.414-1.414L10 14.586l4.293-4.293a1 1 0 011.414 0z",
    clipRule: "evenodd"
  }));
}
var ForwardRef45 = React45.forwardRef(ChevronDoubleDownIcon);
var ChevronDoubleDownIcon_default = ForwardRef45;

// node_modules/@heroicons/react/solid/esm/ChevronDoubleLeftIcon.js
var React46 = __toESM(require_react(), 1);
function ChevronDoubleLeftIcon(props, svgRef) {
  return React46.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React46.createElement("path", {
    fillRule: "evenodd",
    d: "M15.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 010 1.414zm-6 0a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 011.414 1.414L5.414 10l4.293 4.293a1 1 0 010 1.414z",
    clipRule: "evenodd"
  }));
}
var ForwardRef46 = React46.forwardRef(ChevronDoubleLeftIcon);
var ChevronDoubleLeftIcon_default = ForwardRef46;

// node_modules/@heroicons/react/solid/esm/ChevronDoubleRightIcon.js
var React47 = __toESM(require_react(), 1);
function ChevronDoubleRightIcon(props, svgRef) {
  return React47.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React47.createElement("path", {
    fillRule: "evenodd",
    d: "M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z",
    clipRule: "evenodd"
  }), React47.createElement("path", {
    fillRule: "evenodd",
    d: "M4.293 15.707a1 1 0 010-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z",
    clipRule: "evenodd"
  }));
}
var ForwardRef47 = React47.forwardRef(ChevronDoubleRightIcon);
var ChevronDoubleRightIcon_default = ForwardRef47;

// node_modules/@heroicons/react/solid/esm/ChevronDoubleUpIcon.js
var React48 = __toESM(require_react(), 1);
function ChevronDoubleUpIcon(props, svgRef) {
  return React48.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React48.createElement("path", {
    fillRule: "evenodd",
    d: "M4.293 15.707a1 1 0 010-1.414l5-5a1 1 0 011.414 0l5 5a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414 0zm0-6a1 1 0 010-1.414l5-5a1 1 0 011.414 0l5 5a1 1 0 01-1.414 1.414L10 5.414 5.707 9.707a1 1 0 01-1.414 0z",
    clipRule: "evenodd"
  }));
}
var ForwardRef48 = React48.forwardRef(ChevronDoubleUpIcon);
var ChevronDoubleUpIcon_default = ForwardRef48;

// node_modules/@heroicons/react/solid/esm/ChevronDownIcon.js
var React49 = __toESM(require_react(), 1);
function ChevronDownIcon(props, svgRef) {
  return React49.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React49.createElement("path", {
    fillRule: "evenodd",
    d: "M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",
    clipRule: "evenodd"
  }));
}
var ForwardRef49 = React49.forwardRef(ChevronDownIcon);
var ChevronDownIcon_default = ForwardRef49;

// node_modules/@heroicons/react/solid/esm/ChevronLeftIcon.js
var React50 = __toESM(require_react(), 1);
function ChevronLeftIcon(props, svgRef) {
  return React50.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React50.createElement("path", {
    fillRule: "evenodd",
    d: "M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z",
    clipRule: "evenodd"
  }));
}
var ForwardRef50 = React50.forwardRef(ChevronLeftIcon);
var ChevronLeftIcon_default = ForwardRef50;

// node_modules/@heroicons/react/solid/esm/ChevronRightIcon.js
var React51 = __toESM(require_react(), 1);
function ChevronRightIcon(props, svgRef) {
  return React51.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React51.createElement("path", {
    fillRule: "evenodd",
    d: "M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",
    clipRule: "evenodd"
  }));
}
var ForwardRef51 = React51.forwardRef(ChevronRightIcon);
var ChevronRightIcon_default = ForwardRef51;

// node_modules/@heroicons/react/solid/esm/ChevronUpIcon.js
var React52 = __toESM(require_react(), 1);
function ChevronUpIcon(props, svgRef) {
  return React52.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React52.createElement("path", {
    fillRule: "evenodd",
    d: "M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z",
    clipRule: "evenodd"
  }));
}
var ForwardRef52 = React52.forwardRef(ChevronUpIcon);
var ChevronUpIcon_default = ForwardRef52;

// node_modules/@heroicons/react/solid/esm/ChipIcon.js
var React53 = __toESM(require_react(), 1);
function ChipIcon(props, svgRef) {
  return React53.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React53.createElement("path", {
    d: "M13 7H7v6h6V7z"
  }), React53.createElement("path", {
    fillRule: "evenodd",
    d: "M7 2a1 1 0 012 0v1h2V2a1 1 0 112 0v1h2a2 2 0 012 2v2h1a1 1 0 110 2h-1v2h1a1 1 0 110 2h-1v2a2 2 0 01-2 2h-2v1a1 1 0 11-2 0v-1H9v1a1 1 0 11-2 0v-1H5a2 2 0 01-2-2v-2H2a1 1 0 110-2h1V9H2a1 1 0 010-2h1V5a2 2 0 012-2h2V2zM5 5h10v10H5V5z",
    clipRule: "evenodd"
  }));
}
var ForwardRef53 = React53.forwardRef(ChipIcon);
var ChipIcon_default = ForwardRef53;

// node_modules/@heroicons/react/solid/esm/ClipboardCheckIcon.js
var React54 = __toESM(require_react(), 1);
function ClipboardCheckIcon(props, svgRef) {
  return React54.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React54.createElement("path", {
    d: "M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"
  }), React54.createElement("path", {
    fillRule: "evenodd",
    d: "M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm9.707 5.707a1 1 0 00-1.414-1.414L9 12.586l-1.293-1.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",
    clipRule: "evenodd"
  }));
}
var ForwardRef54 = React54.forwardRef(ClipboardCheckIcon);
var ClipboardCheckIcon_default = ForwardRef54;

// node_modules/@heroicons/react/solid/esm/ClipboardCopyIcon.js
var React55 = __toESM(require_react(), 1);
function ClipboardCopyIcon(props, svgRef) {
  return React55.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React55.createElement("path", {
    d: "M8 2a1 1 0 000 2h2a1 1 0 100-2H8z"
  }), React55.createElement("path", {
    d: "M3 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v6h-4.586l1.293-1.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L10.414 13H15v3a2 2 0 01-2 2H5a2 2 0 01-2-2V5zM15 11h2a1 1 0 110 2h-2v-2z"
  }));
}
var ForwardRef55 = React55.forwardRef(ClipboardCopyIcon);
var ClipboardCopyIcon_default = ForwardRef55;

// node_modules/@heroicons/react/solid/esm/ClipboardListIcon.js
var React56 = __toESM(require_react(), 1);
function ClipboardListIcon(props, svgRef) {
  return React56.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React56.createElement("path", {
    d: "M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"
  }), React56.createElement("path", {
    fillRule: "evenodd",
    d: "M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z",
    clipRule: "evenodd"
  }));
}
var ForwardRef56 = React56.forwardRef(ClipboardListIcon);
var ClipboardListIcon_default = ForwardRef56;

// node_modules/@heroicons/react/solid/esm/ClipboardIcon.js
var React57 = __toESM(require_react(), 1);
function ClipboardIcon(props, svgRef) {
  return React57.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React57.createElement("path", {
    d: "M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"
  }), React57.createElement("path", {
    d: "M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z"
  }));
}
var ForwardRef57 = React57.forwardRef(ClipboardIcon);
var ClipboardIcon_default = ForwardRef57;

// node_modules/@heroicons/react/solid/esm/ClockIcon.js
var React58 = __toESM(require_react(), 1);
function ClockIcon(props, svgRef) {
  return React58.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React58.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z",
    clipRule: "evenodd"
  }));
}
var ForwardRef58 = React58.forwardRef(ClockIcon);
var ClockIcon_default = ForwardRef58;

// node_modules/@heroicons/react/solid/esm/CloudDownloadIcon.js
var React59 = __toESM(require_react(), 1);
function CloudDownloadIcon(props, svgRef) {
  return React59.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React59.createElement("path", {
    fillRule: "evenodd",
    d: "M2 9.5A3.5 3.5 0 005.5 13H9v2.586l-1.293-1.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 15.586V13h2.5a4.5 4.5 0 10-.616-8.958 4.002 4.002 0 10-7.753 1.977A3.5 3.5 0 002 9.5zm9 3.5H9V8a1 1 0 012 0v5z",
    clipRule: "evenodd"
  }));
}
var ForwardRef59 = React59.forwardRef(CloudDownloadIcon);
var CloudDownloadIcon_default = ForwardRef59;

// node_modules/@heroicons/react/solid/esm/CloudUploadIcon.js
var React60 = __toESM(require_react(), 1);
function CloudUploadIcon(props, svgRef) {
  return React60.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React60.createElement("path", {
    d: "M5.5 13a3.5 3.5 0 01-.369-6.98 4 4 0 117.753-1.977A4.5 4.5 0 1113.5 13H11V9.413l1.293 1.293a1 1 0 001.414-1.414l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13H5.5z"
  }), React60.createElement("path", {
    d: "M9 13h2v5a1 1 0 11-2 0v-5z"
  }));
}
var ForwardRef60 = React60.forwardRef(CloudUploadIcon);
var CloudUploadIcon_default = ForwardRef60;

// node_modules/@heroicons/react/solid/esm/CloudIcon.js
var React61 = __toESM(require_react(), 1);
function CloudIcon(props, svgRef) {
  return React61.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React61.createElement("path", {
    d: "M5.5 16a3.5 3.5 0 01-.369-6.98 4 4 0 117.753-1.977A4.5 4.5 0 1113.5 16h-8z"
  }));
}
var ForwardRef61 = React61.forwardRef(CloudIcon);
var CloudIcon_default = ForwardRef61;

// node_modules/@heroicons/react/solid/esm/CodeIcon.js
var React62 = __toESM(require_react(), 1);
function CodeIcon(props, svgRef) {
  return React62.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React62.createElement("path", {
    fillRule: "evenodd",
    d: "M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z",
    clipRule: "evenodd"
  }));
}
var ForwardRef62 = React62.forwardRef(CodeIcon);
var CodeIcon_default = ForwardRef62;

// node_modules/@heroicons/react/solid/esm/CogIcon.js
var React63 = __toESM(require_react(), 1);
function CogIcon(props, svgRef) {
  return React63.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React63.createElement("path", {
    fillRule: "evenodd",
    d: "M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z",
    clipRule: "evenodd"
  }));
}
var ForwardRef63 = React63.forwardRef(CogIcon);
var CogIcon_default = ForwardRef63;

// node_modules/@heroicons/react/solid/esm/CollectionIcon.js
var React64 = __toESM(require_react(), 1);
function CollectionIcon(props, svgRef) {
  return React64.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React64.createElement("path", {
    d: "M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z"
  }));
}
var ForwardRef64 = React64.forwardRef(CollectionIcon);
var CollectionIcon_default = ForwardRef64;

// node_modules/@heroicons/react/solid/esm/ColorSwatchIcon.js
var React65 = __toESM(require_react(), 1);
function ColorSwatchIcon(props, svgRef) {
  return React65.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React65.createElement("path", {
    fillRule: "evenodd",
    d: "M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z",
    clipRule: "evenodd"
  }));
}
var ForwardRef65 = React65.forwardRef(ColorSwatchIcon);
var ColorSwatchIcon_default = ForwardRef65;

// node_modules/@heroicons/react/solid/esm/CreditCardIcon.js
var React66 = __toESM(require_react(), 1);
function CreditCardIcon(props, svgRef) {
  return React66.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React66.createElement("path", {
    d: "M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z"
  }), React66.createElement("path", {
    fillRule: "evenodd",
    d: "M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z",
    clipRule: "evenodd"
  }));
}
var ForwardRef66 = React66.forwardRef(CreditCardIcon);
var CreditCardIcon_default = ForwardRef66;

// node_modules/@heroicons/react/solid/esm/CubeTransparentIcon.js
var React67 = __toESM(require_react(), 1);
function CubeTransparentIcon(props, svgRef) {
  return React67.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React67.createElement("path", {
    fillRule: "evenodd",
    d: "M9.504 1.132a1 1 0 01.992 0l1.75 1a1 1 0 11-.992 1.736L10 3.152l-1.254.716a1 1 0 11-.992-1.736l1.75-1zM5.618 4.504a1 1 0 01-.372 1.364L5.016 6l.23.132a1 1 0 11-.992 1.736L4 7.723V8a1 1 0 01-2 0V6a.996.996 0 01.52-.878l1.734-.99a1 1 0 011.364.372zm8.764 0a1 1 0 011.364-.372l1.733.99A1.002 1.002 0 0118 6v2a1 1 0 11-2 0v-.277l-.254.145a1 1 0 11-.992-1.736l.23-.132-.23-.132a1 1 0 01-.372-1.364zm-7 4a1 1 0 011.364-.372L10 8.848l1.254-.716a1 1 0 11.992 1.736L11 10.58V12a1 1 0 11-2 0v-1.42l-1.246-.712a1 1 0 01-.372-1.364zM3 11a1 1 0 011 1v1.42l1.246.712a1 1 0 11-.992 1.736l-1.75-1A1 1 0 012 14v-2a1 1 0 011-1zm14 0a1 1 0 011 1v2a1 1 0 01-.504.868l-1.75 1a1 1 0 11-.992-1.736L16 13.42V12a1 1 0 011-1zm-9.618 5.504a1 1 0 011.364-.372l.254.145V16a1 1 0 112 0v.277l.254-.145a1 1 0 11.992 1.736l-1.735.992a.995.995 0 01-1.022 0l-1.735-.992a1 1 0 01-.372-1.364z",
    clipRule: "evenodd"
  }));
}
var ForwardRef67 = React67.forwardRef(CubeTransparentIcon);
var CubeTransparentIcon_default = ForwardRef67;

// node_modules/@heroicons/react/solid/esm/CubeIcon.js
var React68 = __toESM(require_react(), 1);
function CubeIcon(props, svgRef) {
  return React68.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React68.createElement("path", {
    d: "M11 17a1 1 0 001.447.894l4-2A1 1 0 0017 15V9.236a1 1 0 00-1.447-.894l-4 2a1 1 0 00-.553.894V17zM15.211 6.276a1 1 0 000-1.788l-4.764-2.382a1 1 0 00-.894 0L4.789 4.488a1 1 0 000 1.788l4.764 2.382a1 1 0 00.894 0l4.764-2.382zM4.447 8.342A1 1 0 003 9.236V15a1 1 0 00.553.894l4 2A1 1 0 009 17v-5.764a1 1 0 00-.553-.894l-4-2z"
  }));
}
var ForwardRef68 = React68.forwardRef(CubeIcon);
var CubeIcon_default = ForwardRef68;

// node_modules/@heroicons/react/solid/esm/CurrencyBangladeshiIcon.js
var React69 = __toESM(require_react(), 1);
function CurrencyBangladeshiIcon(props, svgRef) {
  return React69.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React69.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 100-16 8 8 0 000 16zM7 4a1 1 0 000 2 1 1 0 011 1v1H7a1 1 0 000 2h1v3a3 3 0 106 0v-1a1 1 0 10-2 0v1a1 1 0 11-2 0v-3h3a1 1 0 100-2h-3V7a3 3 0 00-3-3z",
    clipRule: "evenodd"
  }));
}
var ForwardRef69 = React69.forwardRef(CurrencyBangladeshiIcon);
var CurrencyBangladeshiIcon_default = ForwardRef69;

// node_modules/@heroicons/react/solid/esm/CurrencyDollarIcon.js
var React70 = __toESM(require_react(), 1);
function CurrencyDollarIcon(props, svgRef) {
  return React70.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React70.createElement("path", {
    d: "M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"
  }), React70.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z",
    clipRule: "evenodd"
  }));
}
var ForwardRef70 = React70.forwardRef(CurrencyDollarIcon);
var CurrencyDollarIcon_default = ForwardRef70;

// node_modules/@heroicons/react/solid/esm/CurrencyEuroIcon.js
var React71 = __toESM(require_react(), 1);
function CurrencyEuroIcon(props, svgRef) {
  return React71.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React71.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 100-16 8 8 0 000 16zM8.736 6.979C9.208 6.193 9.696 6 10 6c.304 0 .792.193 1.264.979a1 1 0 001.715-1.029C12.279 4.784 11.232 4 10 4s-2.279.784-2.979 1.95c-.285.475-.507 1-.67 1.55H6a1 1 0 000 2h.013a9.358 9.358 0 000 1H6a1 1 0 100 2h.351c.163.55.385 1.075.67 1.55C7.721 15.216 8.768 16 10 16s2.279-.784 2.979-1.95a1 1 0 10-1.715-1.029c-.472.786-.96.979-1.264.979-.304 0-.792-.193-1.264-.979a4.265 4.265 0 01-.264-.521H10a1 1 0 100-2H8.017a7.36 7.36 0 010-1H10a1 1 0 100-2H8.472c.08-.185.167-.36.264-.521z",
    clipRule: "evenodd"
  }));
}
var ForwardRef71 = React71.forwardRef(CurrencyEuroIcon);
var CurrencyEuroIcon_default = ForwardRef71;

// node_modules/@heroicons/react/solid/esm/CurrencyPoundIcon.js
var React72 = __toESM(require_react(), 1);
function CurrencyPoundIcon(props, svgRef) {
  return React72.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React72.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 100-16 8 8 0 000 16zm1-14a3 3 0 00-3 3v2H7a1 1 0 000 2h1v1a1 1 0 01-1 1 1 1 0 100 2h6a1 1 0 100-2H9.83c.11-.313.17-.65.17-1v-1h1a1 1 0 100-2h-1V7a1 1 0 112 0 1 1 0 102 0 3 3 0 00-3-3z",
    clipRule: "evenodd"
  }));
}
var ForwardRef72 = React72.forwardRef(CurrencyPoundIcon);
var CurrencyPoundIcon_default = ForwardRef72;

// node_modules/@heroicons/react/solid/esm/CurrencyRupeeIcon.js
var React73 = __toESM(require_react(), 1);
function CurrencyRupeeIcon(props, svgRef) {
  return React73.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React73.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 100-16 8 8 0 000 16zM7 5a1 1 0 100 2h1a2 2 0 011.732 1H7a1 1 0 100 2h2.732A2 2 0 018 11H7a1 1 0 00-.707 1.707l3 3a1 1 0 001.414-1.414l-1.483-1.484A4.008 4.008 0 0011.874 10H13a1 1 0 100-2h-1.126a3.976 3.976 0 00-.41-1H13a1 1 0 100-2H7z",
    clipRule: "evenodd"
  }));
}
var ForwardRef73 = React73.forwardRef(CurrencyRupeeIcon);
var CurrencyRupeeIcon_default = ForwardRef73;

// node_modules/@heroicons/react/solid/esm/CurrencyYenIcon.js
var React74 = __toESM(require_react(), 1);
function CurrencyYenIcon(props, svgRef) {
  return React74.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React74.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 100-16 8 8 0 000 16zM7.858 5.485a1 1 0 00-1.715 1.03L7.633 9H7a1 1 0 100 2h1.834l.166.277V12H7a1 1 0 100 2h2v1a1 1 0 102 0v-1h2a1 1 0 100-2h-2v-.723l.166-.277H13a1 1 0 100-2h-.634l1.492-2.486a1 1 0 10-1.716-1.029L10.034 9h-.068L7.858 5.485z",
    clipRule: "evenodd"
  }));
}
var ForwardRef74 = React74.forwardRef(CurrencyYenIcon);
var CurrencyYenIcon_default = ForwardRef74;

// node_modules/@heroicons/react/solid/esm/CursorClickIcon.js
var React75 = __toESM(require_react(), 1);
function CursorClickIcon(props, svgRef) {
  return React75.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React75.createElement("path", {
    fillRule: "evenodd",
    d: "M6.672 1.911a1 1 0 10-1.932.518l.259.966a1 1 0 001.932-.518l-.26-.966zM2.429 4.74a1 1 0 10-.517 1.932l.966.259a1 1 0 00.517-1.932l-.966-.26zm8.814-.569a1 1 0 00-1.415-1.414l-.707.707a1 1 0 101.415 1.415l.707-.708zm-7.071 7.072l.707-.707A1 1 0 003.465 9.12l-.708.707a1 1 0 001.415 1.415zm3.2-5.171a1 1 0 00-1.3 1.3l4 10a1 1 0 001.823.075l1.38-2.759 3.018 3.02a1 1 0 001.414-1.415l-3.019-3.02 2.76-1.379a1 1 0 00-.076-1.822l-10-4z",
    clipRule: "evenodd"
  }));
}
var ForwardRef75 = React75.forwardRef(CursorClickIcon);
var CursorClickIcon_default = ForwardRef75;

// node_modules/@heroicons/react/solid/esm/DatabaseIcon.js
var React76 = __toESM(require_react(), 1);
function DatabaseIcon(props, svgRef) {
  return React76.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React76.createElement("path", {
    d: "M3 12v3c0 1.657 3.134 3 7 3s7-1.343 7-3v-3c0 1.657-3.134 3-7 3s-7-1.343-7-3z"
  }), React76.createElement("path", {
    d: "M3 7v3c0 1.657 3.134 3 7 3s7-1.343 7-3V7c0 1.657-3.134 3-7 3S3 8.657 3 7z"
  }), React76.createElement("path", {
    d: "M17 5c0 1.657-3.134 3-7 3S3 6.657 3 5s3.134-3 7-3 7 1.343 7 3z"
  }));
}
var ForwardRef76 = React76.forwardRef(DatabaseIcon);
var DatabaseIcon_default = ForwardRef76;

// node_modules/@heroicons/react/solid/esm/DesktopComputerIcon.js
var React77 = __toESM(require_react(), 1);
function DesktopComputerIcon(props, svgRef) {
  return React77.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React77.createElement("path", {
    fillRule: "evenodd",
    d: "M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z",
    clipRule: "evenodd"
  }));
}
var ForwardRef77 = React77.forwardRef(DesktopComputerIcon);
var DesktopComputerIcon_default = ForwardRef77;

// node_modules/@heroicons/react/solid/esm/DeviceMobileIcon.js
var React78 = __toESM(require_react(), 1);
function DeviceMobileIcon(props, svgRef) {
  return React78.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React78.createElement("path", {
    fillRule: "evenodd",
    d: "M7 2a2 2 0 00-2 2v12a2 2 0 002 2h6a2 2 0 002-2V4a2 2 0 00-2-2H7zm3 14a1 1 0 100-2 1 1 0 000 2z",
    clipRule: "evenodd"
  }));
}
var ForwardRef78 = React78.forwardRef(DeviceMobileIcon);
var DeviceMobileIcon_default = ForwardRef78;

// node_modules/@heroicons/react/solid/esm/DeviceTabletIcon.js
var React79 = __toESM(require_react(), 1);
function DeviceTabletIcon(props, svgRef) {
  return React79.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React79.createElement("path", {
    fillRule: "evenodd",
    d: "M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H6zm4 14a1 1 0 100-2 1 1 0 000 2z",
    clipRule: "evenodd"
  }));
}
var ForwardRef79 = React79.forwardRef(DeviceTabletIcon);
var DeviceTabletIcon_default = ForwardRef79;

// node_modules/@heroicons/react/solid/esm/DocumentAddIcon.js
var React80 = __toESM(require_react(), 1);
function DocumentAddIcon(props, svgRef) {
  return React80.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React80.createElement("path", {
    fillRule: "evenodd",
    d: "M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V8z",
    clipRule: "evenodd"
  }));
}
var ForwardRef80 = React80.forwardRef(DocumentAddIcon);
var DocumentAddIcon_default = ForwardRef80;

// node_modules/@heroicons/react/solid/esm/DocumentDownloadIcon.js
var React81 = __toESM(require_react(), 1);
function DocumentDownloadIcon(props, svgRef) {
  return React81.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React81.createElement("path", {
    fillRule: "evenodd",
    d: "M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V8z",
    clipRule: "evenodd"
  }));
}
var ForwardRef81 = React81.forwardRef(DocumentDownloadIcon);
var DocumentDownloadIcon_default = ForwardRef81;

// node_modules/@heroicons/react/solid/esm/DocumentDuplicateIcon.js
var React82 = __toESM(require_react(), 1);
function DocumentDuplicateIcon(props, svgRef) {
  return React82.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React82.createElement("path", {
    d: "M9 2a2 2 0 00-2 2v8a2 2 0 002 2h6a2 2 0 002-2V6.414A2 2 0 0016.414 5L14 2.586A2 2 0 0012.586 2H9z"
  }), React82.createElement("path", {
    d: "M3 8a2 2 0 012-2v10h8a2 2 0 01-2 2H5a2 2 0 01-2-2V8z"
  }));
}
var ForwardRef82 = React82.forwardRef(DocumentDuplicateIcon);
var DocumentDuplicateIcon_default = ForwardRef82;

// node_modules/@heroicons/react/solid/esm/DocumentRemoveIcon.js
var React83 = __toESM(require_react(), 1);
function DocumentRemoveIcon(props, svgRef) {
  return React83.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React83.createElement("path", {
    fillRule: "evenodd",
    d: "M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm1 8a1 1 0 100 2h6a1 1 0 100-2H7z",
    clipRule: "evenodd"
  }));
}
var ForwardRef83 = React83.forwardRef(DocumentRemoveIcon);
var DocumentRemoveIcon_default = ForwardRef83;

// node_modules/@heroicons/react/solid/esm/DocumentReportIcon.js
var React84 = __toESM(require_react(), 1);
function DocumentReportIcon(props, svgRef) {
  return React84.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React84.createElement("path", {
    fillRule: "evenodd",
    d: "M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm2 10a1 1 0 10-2 0v3a1 1 0 102 0v-3zm2-3a1 1 0 011 1v5a1 1 0 11-2 0v-5a1 1 0 011-1zm4-1a1 1 0 10-2 0v7a1 1 0 102 0V8z",
    clipRule: "evenodd"
  }));
}
var ForwardRef84 = React84.forwardRef(DocumentReportIcon);
var DocumentReportIcon_default = ForwardRef84;

// node_modules/@heroicons/react/solid/esm/DocumentSearchIcon.js
var React85 = __toESM(require_react(), 1);
function DocumentSearchIcon(props, svgRef) {
  return React85.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React85.createElement("path", {
    d: "M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2h-1.528A6 6 0 004 9.528V4z"
  }), React85.createElement("path", {
    fillRule: "evenodd",
    d: "M8 10a4 4 0 00-3.446 6.032l-1.261 1.26a1 1 0 101.414 1.415l1.261-1.261A4 4 0 108 10zm-2 4a2 2 0 114 0 2 2 0 01-4 0z",
    clipRule: "evenodd"
  }));
}
var ForwardRef85 = React85.forwardRef(DocumentSearchIcon);
var DocumentSearchIcon_default = ForwardRef85;

// node_modules/@heroicons/react/solid/esm/DocumentTextIcon.js
var React86 = __toESM(require_react(), 1);
function DocumentTextIcon(props, svgRef) {
  return React86.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React86.createElement("path", {
    fillRule: "evenodd",
    d: "M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z",
    clipRule: "evenodd"
  }));
}
var ForwardRef86 = React86.forwardRef(DocumentTextIcon);
var DocumentTextIcon_default = ForwardRef86;

// node_modules/@heroicons/react/solid/esm/DocumentIcon.js
var React87 = __toESM(require_react(), 1);
function DocumentIcon(props, svgRef) {
  return React87.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React87.createElement("path", {
    fillRule: "evenodd",
    d: "M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z",
    clipRule: "evenodd"
  }));
}
var ForwardRef87 = React87.forwardRef(DocumentIcon);
var DocumentIcon_default = ForwardRef87;

// node_modules/@heroicons/react/solid/esm/DotsCircleHorizontalIcon.js
var React88 = __toESM(require_react(), 1);
function DotsCircleHorizontalIcon(props, svgRef) {
  return React88.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React88.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 100-16 8 8 0 000 16zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z",
    clipRule: "evenodd"
  }));
}
var ForwardRef88 = React88.forwardRef(DotsCircleHorizontalIcon);
var DotsCircleHorizontalIcon_default = ForwardRef88;

// node_modules/@heroicons/react/solid/esm/DotsHorizontalIcon.js
var React89 = __toESM(require_react(), 1);
function DotsHorizontalIcon(props, svgRef) {
  return React89.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React89.createElement("path", {
    d: "M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z"
  }));
}
var ForwardRef89 = React89.forwardRef(DotsHorizontalIcon);
var DotsHorizontalIcon_default = ForwardRef89;

// node_modules/@heroicons/react/solid/esm/DotsVerticalIcon.js
var React90 = __toESM(require_react(), 1);
function DotsVerticalIcon(props, svgRef) {
  return React90.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React90.createElement("path", {
    d: "M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"
  }));
}
var ForwardRef90 = React90.forwardRef(DotsVerticalIcon);
var DotsVerticalIcon_default = ForwardRef90;

// node_modules/@heroicons/react/solid/esm/DownloadIcon.js
var React91 = __toESM(require_react(), 1);
function DownloadIcon(props, svgRef) {
  return React91.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React91.createElement("path", {
    fillRule: "evenodd",
    d: "M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z",
    clipRule: "evenodd"
  }));
}
var ForwardRef91 = React91.forwardRef(DownloadIcon);
var DownloadIcon_default = ForwardRef91;

// node_modules/@heroicons/react/solid/esm/DuplicateIcon.js
var React92 = __toESM(require_react(), 1);
function DuplicateIcon(props, svgRef) {
  return React92.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React92.createElement("path", {
    d: "M7 9a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2H9a2 2 0 01-2-2V9z"
  }), React92.createElement("path", {
    d: "M5 3a2 2 0 00-2 2v6a2 2 0 002 2V5h8a2 2 0 00-2-2H5z"
  }));
}
var ForwardRef92 = React92.forwardRef(DuplicateIcon);
var DuplicateIcon_default = ForwardRef92;

// node_modules/@heroicons/react/solid/esm/EmojiHappyIcon.js
var React93 = __toESM(require_react(), 1);
function EmojiHappyIcon(props, svgRef) {
  return React93.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React93.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 100-16 8 8 0 000 16zM7 9a1 1 0 100-2 1 1 0 000 2zm7-1a1 1 0 11-2 0 1 1 0 012 0zm-.464 5.535a1 1 0 10-1.415-1.414 3 3 0 01-4.242 0 1 1 0 00-1.415 1.414 5 5 0 007.072 0z",
    clipRule: "evenodd"
  }));
}
var ForwardRef93 = React93.forwardRef(EmojiHappyIcon);
var EmojiHappyIcon_default = ForwardRef93;

// node_modules/@heroicons/react/solid/esm/EmojiSadIcon.js
var React94 = __toESM(require_react(), 1);
function EmojiSadIcon(props, svgRef) {
  return React94.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React94.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 100-16 8 8 0 000 16zM7 9a1 1 0 100-2 1 1 0 000 2zm7-1a1 1 0 11-2 0 1 1 0 012 0zm-7.536 5.879a1 1 0 001.415 0 3 3 0 014.242 0 1 1 0 001.415-1.415 5 5 0 00-7.072 0 1 1 0 000 1.415z",
    clipRule: "evenodd"
  }));
}
var ForwardRef94 = React94.forwardRef(EmojiSadIcon);
var EmojiSadIcon_default = ForwardRef94;

// node_modules/@heroicons/react/solid/esm/ExclamationCircleIcon.js
var React95 = __toESM(require_react(), 1);
function ExclamationCircleIcon(props, svgRef) {
  return React95.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React95.createElement("path", {
    fillRule: "evenodd",
    d: "M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",
    clipRule: "evenodd"
  }));
}
var ForwardRef95 = React95.forwardRef(ExclamationCircleIcon);
var ExclamationCircleIcon_default = ForwardRef95;

// node_modules/@heroicons/react/solid/esm/ExclamationIcon.js
var React96 = __toESM(require_react(), 1);
function ExclamationIcon(props, svgRef) {
  return React96.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React96.createElement("path", {
    fillRule: "evenodd",
    d: "M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",
    clipRule: "evenodd"
  }));
}
var ForwardRef96 = React96.forwardRef(ExclamationIcon);
var ExclamationIcon_default = ForwardRef96;

// node_modules/@heroicons/react/solid/esm/ExternalLinkIcon.js
var React97 = __toESM(require_react(), 1);
function ExternalLinkIcon(props, svgRef) {
  return React97.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React97.createElement("path", {
    d: "M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z"
  }), React97.createElement("path", {
    d: "M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z"
  }));
}
var ForwardRef97 = React97.forwardRef(ExternalLinkIcon);
var ExternalLinkIcon_default = ForwardRef97;

// node_modules/@heroicons/react/solid/esm/EyeOffIcon.js
var React98 = __toESM(require_react(), 1);
function EyeOffIcon(props, svgRef) {
  return React98.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React98.createElement("path", {
    fillRule: "evenodd",
    d: "M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z",
    clipRule: "evenodd"
  }), React98.createElement("path", {
    d: "M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z"
  }));
}
var ForwardRef98 = React98.forwardRef(EyeOffIcon);
var EyeOffIcon_default = ForwardRef98;

// node_modules/@heroicons/react/solid/esm/EyeIcon.js
var React99 = __toESM(require_react(), 1);
function EyeIcon(props, svgRef) {
  return React99.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React99.createElement("path", {
    d: "M10 12a2 2 0 100-4 2 2 0 000 4z"
  }), React99.createElement("path", {
    fillRule: "evenodd",
    d: "M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z",
    clipRule: "evenodd"
  }));
}
var ForwardRef99 = React99.forwardRef(EyeIcon);
var EyeIcon_default = ForwardRef99;

// node_modules/@heroicons/react/solid/esm/FastForwardIcon.js
var React100 = __toESM(require_react(), 1);
function FastForwardIcon(props, svgRef) {
  return React100.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React100.createElement("path", {
    d: "M4.555 5.168A1 1 0 003 6v8a1 1 0 001.555.832L10 11.202V14a1 1 0 001.555.832l6-4a1 1 0 000-1.664l-6-4A1 1 0 0010 6v2.798l-5.445-3.63z"
  }));
}
var ForwardRef100 = React100.forwardRef(FastForwardIcon);
var FastForwardIcon_default = ForwardRef100;

// node_modules/@heroicons/react/solid/esm/FilmIcon.js
var React101 = __toESM(require_react(), 1);
function FilmIcon(props, svgRef) {
  return React101.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React101.createElement("path", {
    fillRule: "evenodd",
    d: "M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm3 2h6v4H7V5zm8 8v2h1v-2h-1zm-2-2H7v4h6v-4zm2 0h1V9h-1v2zm1-4V5h-1v2h1zM5 5v2H4V5h1zm0 4H4v2h1V9zm-1 4h1v2H4v-2z",
    clipRule: "evenodd"
  }));
}
var ForwardRef101 = React101.forwardRef(FilmIcon);
var FilmIcon_default = ForwardRef101;

// node_modules/@heroicons/react/solid/esm/FilterIcon.js
var React102 = __toESM(require_react(), 1);
function FilterIcon(props, svgRef) {
  return React102.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React102.createElement("path", {
    fillRule: "evenodd",
    d: "M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z",
    clipRule: "evenodd"
  }));
}
var ForwardRef102 = React102.forwardRef(FilterIcon);
var FilterIcon_default = ForwardRef102;

// node_modules/@heroicons/react/solid/esm/FingerPrintIcon.js
var React103 = __toESM(require_react(), 1);
function FingerPrintIcon(props, svgRef) {
  return React103.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React103.createElement("path", {
    fillRule: "evenodd",
    d: "M6.625 2.655A9 9 0 0119 11a1 1 0 11-2 0 7 7 0 00-9.625-6.492 1 1 0 11-.75-1.853zM4.662 4.959A1 1 0 014.75 6.37 6.97 6.97 0 003 11a1 1 0 11-2 0 8.97 8.97 0 012.25-5.953 1 1 0 011.412-.088z",
    clipRule: "evenodd"
  }), React103.createElement("path", {
    fillRule: "evenodd",
    d: "M5 11a5 5 0 1110 0 1 1 0 11-2 0 3 3 0 10-6 0c0 1.677-.345 3.276-.968 4.729a1 1 0 11-1.838-.789A9.964 9.964 0 005 11zm8.921 2.012a1 1 0 01.831 1.145 19.86 19.86 0 01-.545 2.436 1 1 0 11-1.92-.558c.207-.713.371-1.445.49-2.192a1 1 0 011.144-.83z",
    clipRule: "evenodd"
  }), React103.createElement("path", {
    fillRule: "evenodd",
    d: "M10 10a1 1 0 011 1c0 2.236-.46 4.368-1.29 6.304a1 1 0 01-1.838-.789A13.952 13.952 0 009 11a1 1 0 011-1z",
    clipRule: "evenodd"
  }));
}
var ForwardRef103 = React103.forwardRef(FingerPrintIcon);
var FingerPrintIcon_default = ForwardRef103;

// node_modules/@heroicons/react/solid/esm/FireIcon.js
var React104 = __toESM(require_react(), 1);
function FireIcon(props, svgRef) {
  return React104.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React104.createElement("path", {
    fillRule: "evenodd",
    d: "M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z",
    clipRule: "evenodd"
  }));
}
var ForwardRef104 = React104.forwardRef(FireIcon);
var FireIcon_default = ForwardRef104;

// node_modules/@heroicons/react/solid/esm/FlagIcon.js
var React105 = __toESM(require_react(), 1);
function FlagIcon(props, svgRef) {
  return React105.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React105.createElement("path", {
    fillRule: "evenodd",
    d: "M3 6a3 3 0 013-3h10a1 1 0 01.8 1.6L14.25 8l2.55 3.4A1 1 0 0116 13H6a1 1 0 00-1 1v3a1 1 0 11-2 0V6z",
    clipRule: "evenodd"
  }));
}
var ForwardRef105 = React105.forwardRef(FlagIcon);
var FlagIcon_default = ForwardRef105;

// node_modules/@heroicons/react/solid/esm/FolderAddIcon.js
var React106 = __toESM(require_react(), 1);
function FolderAddIcon(props, svgRef) {
  return React106.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React106.createElement("path", {
    fillRule: "evenodd",
    d: "M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V8a2 2 0 00-2-2h-5L9 4H4zm7 5a1 1 0 10-2 0v1H8a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V9z"
  }));
}
var ForwardRef106 = React106.forwardRef(FolderAddIcon);
var FolderAddIcon_default = ForwardRef106;

// node_modules/@heroicons/react/solid/esm/FolderDownloadIcon.js
var React107 = __toESM(require_react(), 1);
function FolderDownloadIcon(props, svgRef) {
  return React107.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React107.createElement("path", {
    fillRule: "evenodd",
    d: "M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V8a2 2 0 00-2-2h-5L9 4H4zm7 5a1 1 0 10-2 0v1.586l-.293-.293a1 1 0 10-1.414 1.414l2 2 .002.002a.997.997 0 001.41 0l.002-.002 2-2a1 1 0 00-1.414-1.414l-.293.293V9z"
  }));
}
var ForwardRef107 = React107.forwardRef(FolderDownloadIcon);
var FolderDownloadIcon_default = ForwardRef107;

// node_modules/@heroicons/react/solid/esm/FolderOpenIcon.js
var React108 = __toESM(require_react(), 1);
function FolderOpenIcon(props, svgRef) {
  return React108.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React108.createElement("path", {
    fillRule: "evenodd",
    d: "M2 6a2 2 0 012-2h4l2 2h4a2 2 0 012 2v1H8a3 3 0 00-3 3v1.5a1.5 1.5 0 01-3 0V6z",
    clipRule: "evenodd"
  }), React108.createElement("path", {
    d: "M6 12a2 2 0 012-2h8a2 2 0 012 2v2a2 2 0 01-2 2H2h2a2 2 0 002-2v-2z"
  }));
}
var ForwardRef108 = React108.forwardRef(FolderOpenIcon);
var FolderOpenIcon_default = ForwardRef108;

// node_modules/@heroicons/react/solid/esm/FolderRemoveIcon.js
var React109 = __toESM(require_react(), 1);
function FolderRemoveIcon(props, svgRef) {
  return React109.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React109.createElement("path", {
    fillRule: "evenodd",
    d: "M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V8a2 2 0 00-2-2h-5L9 4H4zm4 6a1 1 0 100 2h4a1 1 0 100-2H8z"
  }));
}
var ForwardRef109 = React109.forwardRef(FolderRemoveIcon);
var FolderRemoveIcon_default = ForwardRef109;

// node_modules/@heroicons/react/solid/esm/FolderIcon.js
var React110 = __toESM(require_react(), 1);
function FolderIcon(props, svgRef) {
  return React110.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React110.createElement("path", {
    d: "M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"
  }));
}
var ForwardRef110 = React110.forwardRef(FolderIcon);
var FolderIcon_default = ForwardRef110;

// node_modules/@heroicons/react/solid/esm/GiftIcon.js
var React111 = __toESM(require_react(), 1);
function GiftIcon(props, svgRef) {
  return React111.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React111.createElement("path", {
    fillRule: "evenodd",
    d: "M5 5a3 3 0 015-2.236A3 3 0 0114.83 6H16a2 2 0 110 4h-5V9a1 1 0 10-2 0v1H4a2 2 0 110-4h1.17C5.06 5.687 5 5.35 5 5zm4 1V5a1 1 0 10-1 1h1zm3 0a1 1 0 10-1-1v1h1z",
    clipRule: "evenodd"
  }), React111.createElement("path", {
    d: "M9 11H3v5a2 2 0 002 2h4v-7zM11 18h4a2 2 0 002-2v-5h-6v7z"
  }));
}
var ForwardRef111 = React111.forwardRef(GiftIcon);
var GiftIcon_default = ForwardRef111;

// node_modules/@heroicons/react/solid/esm/GlobeAltIcon.js
var React112 = __toESM(require_react(), 1);
function GlobeAltIcon(props, svgRef) {
  return React112.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React112.createElement("path", {
    fillRule: "evenodd",
    d: "M4.083 9h1.946c.089-1.546.383-2.97.837-4.118A6.004 6.004 0 004.083 9zM10 2a8 8 0 100 16 8 8 0 000-16zm0 2c-.076 0-.232.032-.465.262-.238.234-.497.623-.737 1.182-.389.907-.673 2.142-.766 3.556h3.936c-.093-1.414-.377-2.649-.766-3.556-.24-.56-.5-.948-.737-1.182C10.232 4.032 10.076 4 10 4zm3.971 5c-.089-1.546-.383-2.97-.837-4.118A6.004 6.004 0 0115.917 9h-1.946zm-2.003 2H8.032c.093 1.414.377 2.649.766 3.556.24.56.5.948.737 ************.389.262.465.262.076 0 .232-.032.465-.262.238-.234.498-.623.737-1.182.389-.907.673-2.142.766-3.556zm1.166 4.118c.454-1.147.748-2.572.837-4.118h1.946a6.004 6.004 0 01-2.783 4.118zm-6.268 0C6.412 13.97 6.118 12.546 6.03 11H4.083a6.004 6.004 0 002.783 4.118z",
    clipRule: "evenodd"
  }));
}
var ForwardRef112 = React112.forwardRef(GlobeAltIcon);
var GlobeAltIcon_default = ForwardRef112;

// node_modules/@heroicons/react/solid/esm/GlobeIcon.js
var React113 = __toESM(require_react(), 1);
function GlobeIcon(props, svgRef) {
  return React113.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React113.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 100-16 8 8 0 000 16zM4.332 8.027a6.012 6.012 0 011.912-2.706C6.512 5.73 6.974 6 7.5 6A1.5 1.5 0 019 7.5V8a2 2 0 004 0 2 2 0 011.523-1.943A5.977 5.977 0 0116 10c0 .34-.028.675-.083 1H15a2 2 0 00-2 2v2.197A5.973 5.973 0 0110 16v-2a2 2 0 00-2-2 2 2 0 01-2-2 2 2 0 00-1.668-1.973z",
    clipRule: "evenodd"
  }));
}
var ForwardRef113 = React113.forwardRef(GlobeIcon);
var GlobeIcon_default = ForwardRef113;

// node_modules/@heroicons/react/solid/esm/HandIcon.js
var React114 = __toESM(require_react(), 1);
function HandIcon(props, svgRef) {
  return React114.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React114.createElement("path", {
    fillRule: "evenodd",
    d: "M9 3a1 1 0 012 0v5.5a.5.5 0 001 0V4a1 1 0 112 0v4.5a.5.5 0 001 0V6a1 1 0 112 0v5a7 7 0 11-14 0V9a1 1 0 012 0v2.5a.5.5 0 001 0V4a1 1 0 012 0v4.5a.5.5 0 001 0V3z",
    clipRule: "evenodd"
  }));
}
var ForwardRef114 = React114.forwardRef(HandIcon);
var HandIcon_default = ForwardRef114;

// node_modules/@heroicons/react/solid/esm/HashtagIcon.js
var React115 = __toESM(require_react(), 1);
function HashtagIcon(props, svgRef) {
  return React115.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React115.createElement("path", {
    fillRule: "evenodd",
    d: "M9.243 3.03a1 1 0 01.727 1.213L9.53 6h2.94l.56-2.243a1 1 0 111.94.486L14.53 6H17a1 1 0 110 2h-2.97l-1 4H15a1 1 0 110 2h-2.47l-.56 2.242a1 1 0 11-1.94-.485L10.47 14H7.53l-.56 2.242a1 1 0 11-1.94-.485L5.47 14H3a1 1 0 110-2h2.97l1-4H5a1 1 0 110-2h2.47l.56-2.243a1 1 0 011.213-.727zM9.03 8l-1 4h2.938l1-4H9.031z",
    clipRule: "evenodd"
  }));
}
var ForwardRef115 = React115.forwardRef(HashtagIcon);
var HashtagIcon_default = ForwardRef115;

// node_modules/@heroicons/react/solid/esm/HeartIcon.js
var React116 = __toESM(require_react(), 1);
function HeartIcon(props, svgRef) {
  return React116.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React116.createElement("path", {
    fillRule: "evenodd",
    d: "M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z",
    clipRule: "evenodd"
  }));
}
var ForwardRef116 = React116.forwardRef(HeartIcon);
var HeartIcon_default = ForwardRef116;

// node_modules/@heroicons/react/solid/esm/HomeIcon.js
var React117 = __toESM(require_react(), 1);
function HomeIcon(props, svgRef) {
  return React117.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React117.createElement("path", {
    d: "M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"
  }));
}
var ForwardRef117 = React117.forwardRef(HomeIcon);
var HomeIcon_default = ForwardRef117;

// node_modules/@heroicons/react/solid/esm/IdentificationIcon.js
var React118 = __toESM(require_react(), 1);
function IdentificationIcon(props, svgRef) {
  return React118.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React118.createElement("path", {
    fillRule: "evenodd",
    d: "M10 2a1 1 0 00-1 1v1a1 1 0 002 0V3a1 1 0 00-1-1zM4 4h3a3 3 0 006 0h3a2 2 0 012 2v9a2 2 0 01-2 2H4a2 2 0 01-2-2V6a2 2 0 012-2zm2.5 7a1.5 1.5 0 100-3 1.5 1.5 0 000 3zm2.45 4a2.5 2.5 0 10-4.9 0h4.9zM12 9a1 1 0 100 2h3a1 1 0 100-2h-3zm-1 4a1 1 0 011-1h2a1 1 0 110 2h-2a1 1 0 01-1-1z",
    clipRule: "evenodd"
  }));
}
var ForwardRef118 = React118.forwardRef(IdentificationIcon);
var IdentificationIcon_default = ForwardRef118;

// node_modules/@heroicons/react/solid/esm/InboxInIcon.js
var React119 = __toESM(require_react(), 1);
function InboxInIcon(props, svgRef) {
  return React119.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React119.createElement("path", {
    d: "M8.707 7.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l2-2a1 1 0 00-1.414-1.414L11 7.586V3a1 1 0 10-2 0v4.586l-.293-.293z"
  }), React119.createElement("path", {
    d: "M3 5a2 2 0 012-2h1a1 1 0 010 2H5v7h2l1 2h4l1-2h2V5h-1a1 1 0 110-2h1a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2V5z"
  }));
}
var ForwardRef119 = React119.forwardRef(InboxInIcon);
var InboxInIcon_default = ForwardRef119;

// node_modules/@heroicons/react/solid/esm/InboxIcon.js
var React120 = __toESM(require_react(), 1);
function InboxIcon(props, svgRef) {
  return React120.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React120.createElement("path", {
    fillRule: "evenodd",
    d: "M5 3a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2V5a2 2 0 00-2-2H5zm0 2h10v7h-2l-1 2H8l-1-2H5V5z",
    clipRule: "evenodd"
  }));
}
var ForwardRef120 = React120.forwardRef(InboxIcon);
var InboxIcon_default = ForwardRef120;

// node_modules/@heroicons/react/solid/esm/InformationCircleIcon.js
var React121 = __toESM(require_react(), 1);
function InformationCircleIcon(props, svgRef) {
  return React121.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React121.createElement("path", {
    fillRule: "evenodd",
    d: "M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",
    clipRule: "evenodd"
  }));
}
var ForwardRef121 = React121.forwardRef(InformationCircleIcon);
var InformationCircleIcon_default = ForwardRef121;

// node_modules/@heroicons/react/solid/esm/KeyIcon.js
var React122 = __toESM(require_react(), 1);
function KeyIcon(props, svgRef) {
  return React122.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React122.createElement("path", {
    fillRule: "evenodd",
    d: "M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z",
    clipRule: "evenodd"
  }));
}
var ForwardRef122 = React122.forwardRef(KeyIcon);
var KeyIcon_default = ForwardRef122;

// node_modules/@heroicons/react/solid/esm/LibraryIcon.js
var React123 = __toESM(require_react(), 1);
function LibraryIcon(props, svgRef) {
  return React123.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React123.createElement("path", {
    fillRule: "evenodd",
    d: "M10.496 2.132a1 1 0 00-.992 0l-7 4A1 1 0 003 8v7a1 1 0 100 2h14a1 1 0 100-2V8a1 1 0 00.496-1.868l-7-4zM6 9a1 1 0 00-1 1v3a1 1 0 102 0v-3a1 1 0 00-1-1zm3 1a1 1 0 012 0v3a1 1 0 11-2 0v-3zm5-1a1 1 0 00-1 1v3a1 1 0 102 0v-3a1 1 0 00-1-1z",
    clipRule: "evenodd"
  }));
}
var ForwardRef123 = React123.forwardRef(LibraryIcon);
var LibraryIcon_default = ForwardRef123;

// node_modules/@heroicons/react/solid/esm/LightBulbIcon.js
var React124 = __toESM(require_react(), 1);
function LightBulbIcon(props, svgRef) {
  return React124.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React124.createElement("path", {
    d: "M11 3a1 1 0 10-2 0v1a1 1 0 102 0V3zM15.657 5.757a1 1 0 00-1.414-1.414l-.707.707a1 1 0 001.414 1.414l.707-.707zM18 10a1 1 0 01-1 1h-1a1 1 0 110-2h1a1 1 0 011 1zM5.05 6.464A1 1 0 106.464 5.05l-.707-.707a1 1 0 00-1.414 1.414l.707.707zM5 10a1 1 0 01-1 1H3a1 1 0 110-2h1a1 1 0 011 1zM8 16v-1h4v1a2 2 0 11-4 0zM12 14c.015-.34.208-.646.477-.859a4 4 0 10-4.954 0c.27.213.462.519.476.859h4.002z"
  }));
}
var ForwardRef124 = React124.forwardRef(LightBulbIcon);
var LightBulbIcon_default = ForwardRef124;

// node_modules/@heroicons/react/solid/esm/LightningBoltIcon.js
var React125 = __toESM(require_react(), 1);
function LightningBoltIcon(props, svgRef) {
  return React125.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React125.createElement("path", {
    fillRule: "evenodd",
    d: "M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z",
    clipRule: "evenodd"
  }));
}
var ForwardRef125 = React125.forwardRef(LightningBoltIcon);
var LightningBoltIcon_default = ForwardRef125;

// node_modules/@heroicons/react/solid/esm/LinkIcon.js
var React126 = __toESM(require_react(), 1);
function LinkIcon(props, svgRef) {
  return React126.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React126.createElement("path", {
    fillRule: "evenodd",
    d: "M12.586 4.586a2 2 0 112.828 2.828l-3 3a2 2 0 01-2.828 0 1 1 0 00-1.414 1.414 4 4 0 005.656 0l3-3a4 4 0 00-5.656-5.656l-1.5 1.5a1 1 0 101.414 1.414l1.5-1.5zm-5 5a2 2 0 012.828 0 1 1 0 101.414-1.414 4 4 0 00-5.656 0l-3 3a4 4 0 105.656 5.656l1.5-1.5a1 1 0 10-1.414-1.414l-1.5 1.5a2 2 0 11-2.828-2.828l3-3z",
    clipRule: "evenodd"
  }));
}
var ForwardRef126 = React126.forwardRef(LinkIcon);
var LinkIcon_default = ForwardRef126;

// node_modules/@heroicons/react/solid/esm/LocationMarkerIcon.js
var React127 = __toESM(require_react(), 1);
function LocationMarkerIcon(props, svgRef) {
  return React127.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React127.createElement("path", {
    fillRule: "evenodd",
    d: "M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z",
    clipRule: "evenodd"
  }));
}
var ForwardRef127 = React127.forwardRef(LocationMarkerIcon);
var LocationMarkerIcon_default = ForwardRef127;

// node_modules/@heroicons/react/solid/esm/LockClosedIcon.js
var React128 = __toESM(require_react(), 1);
function LockClosedIcon(props, svgRef) {
  return React128.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React128.createElement("path", {
    fillRule: "evenodd",
    d: "M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z",
    clipRule: "evenodd"
  }));
}
var ForwardRef128 = React128.forwardRef(LockClosedIcon);
var LockClosedIcon_default = ForwardRef128;

// node_modules/@heroicons/react/solid/esm/LockOpenIcon.js
var React129 = __toESM(require_react(), 1);
function LockOpenIcon(props, svgRef) {
  return React129.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React129.createElement("path", {
    d: "M10 2a5 5 0 00-5 5v2a2 2 0 00-2 2v5a2 2 0 002 2h10a2 2 0 002-2v-5a2 2 0 00-2-2H7V7a3 3 0 015.905-.75 1 1 0 001.937-.5A5.002 5.002 0 0010 2z"
  }));
}
var ForwardRef129 = React129.forwardRef(LockOpenIcon);
var LockOpenIcon_default = ForwardRef129;

// node_modules/@heroicons/react/solid/esm/LoginIcon.js
var React130 = __toESM(require_react(), 1);
function LoginIcon(props, svgRef) {
  return React130.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React130.createElement("path", {
    fillRule: "evenodd",
    d: "M3 3a1 1 0 011 1v12a1 1 0 11-2 0V4a1 1 0 011-1zm7.707 3.293a1 1 0 010 1.414L9.414 9H17a1 1 0 110 2H9.414l1.293 1.293a1 1 0 01-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0z",
    clipRule: "evenodd"
  }));
}
var ForwardRef130 = React130.forwardRef(LoginIcon);
var LoginIcon_default = ForwardRef130;

// node_modules/@heroicons/react/solid/esm/LogoutIcon.js
var React131 = __toESM(require_react(), 1);
function LogoutIcon(props, svgRef) {
  return React131.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React131.createElement("path", {
    fillRule: "evenodd",
    d: "M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z",
    clipRule: "evenodd"
  }));
}
var ForwardRef131 = React131.forwardRef(LogoutIcon);
var LogoutIcon_default = ForwardRef131;

// node_modules/@heroicons/react/solid/esm/MailOpenIcon.js
var React132 = __toESM(require_react(), 1);
function MailOpenIcon(props, svgRef) {
  return React132.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React132.createElement("path", {
    fillRule: "evenodd",
    d: "M2.94 6.412A2 2 0 002 8.108V16a2 2 0 002 2h12a2 2 0 002-2V8.108a2 2 0 00-.94-1.696l-6-3.75a2 2 0 00-2.12 0l-6 3.75zm2.615 2.423a1 1 0 10-1.11 1.664l5 3.333a1 1 0 001.11 0l5-3.333a1 1 0 00-1.11-1.664L10 11.798 5.555 8.835z",
    clipRule: "evenodd"
  }));
}
var ForwardRef132 = React132.forwardRef(MailOpenIcon);
var MailOpenIcon_default = ForwardRef132;

// node_modules/@heroicons/react/solid/esm/MailIcon.js
var React133 = __toESM(require_react(), 1);
function MailIcon(props, svgRef) {
  return React133.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React133.createElement("path", {
    d: "M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"
  }), React133.createElement("path", {
    d: "M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"
  }));
}
var ForwardRef133 = React133.forwardRef(MailIcon);
var MailIcon_default = ForwardRef133;

// node_modules/@heroicons/react/solid/esm/MapIcon.js
var React134 = __toESM(require_react(), 1);
function MapIcon(props, svgRef) {
  return React134.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React134.createElement("path", {
    fillRule: "evenodd",
    d: "M12 1.586l-4 4v12.828l4-4V1.586zM3.707 3.293A1 1 0 002 4v10a1 1 0 00.293.707L6 18.414V5.586L3.707 3.293zM17.707 5.293L14 1.586v12.828l2.293 2.293A1 1 0 0018 16V6a1 1 0 00-.293-.707z",
    clipRule: "evenodd"
  }));
}
var ForwardRef134 = React134.forwardRef(MapIcon);
var MapIcon_default = ForwardRef134;

// node_modules/@heroicons/react/solid/esm/MenuAlt1Icon.js
var React135 = __toESM(require_react(), 1);
function MenuAlt1Icon(props, svgRef) {
  return React135.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React135.createElement("path", {
    fillRule: "evenodd",
    d: "M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h6a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z",
    clipRule: "evenodd"
  }));
}
var ForwardRef135 = React135.forwardRef(MenuAlt1Icon);
var MenuAlt1Icon_default = ForwardRef135;

// node_modules/@heroicons/react/solid/esm/MenuAlt2Icon.js
var React136 = __toESM(require_react(), 1);
function MenuAlt2Icon(props, svgRef) {
  return React136.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React136.createElement("path", {
    fillRule: "evenodd",
    d: "M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h6a1 1 0 110 2H4a1 1 0 01-1-1z",
    clipRule: "evenodd"
  }));
}
var ForwardRef136 = React136.forwardRef(MenuAlt2Icon);
var MenuAlt2Icon_default = ForwardRef136;

// node_modules/@heroicons/react/solid/esm/MenuAlt3Icon.js
var React137 = __toESM(require_react(), 1);
function MenuAlt3Icon(props, svgRef) {
  return React137.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React137.createElement("path", {
    fillRule: "evenodd",
    d: "M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM9 15a1 1 0 011-1h6a1 1 0 110 2h-6a1 1 0 01-1-1z",
    clipRule: "evenodd"
  }));
}
var ForwardRef137 = React137.forwardRef(MenuAlt3Icon);
var MenuAlt3Icon_default = ForwardRef137;

// node_modules/@heroicons/react/solid/esm/MenuAlt4Icon.js
var React138 = __toESM(require_react(), 1);
function MenuAlt4Icon(props, svgRef) {
  return React138.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React138.createElement("path", {
    fillRule: "evenodd",
    d: "M3 7a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 13a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z",
    clipRule: "evenodd"
  }));
}
var ForwardRef138 = React138.forwardRef(MenuAlt4Icon);
var MenuAlt4Icon_default = ForwardRef138;

// node_modules/@heroicons/react/solid/esm/MenuIcon.js
var React139 = __toESM(require_react(), 1);
function MenuIcon(props, svgRef) {
  return React139.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React139.createElement("path", {
    fillRule: "evenodd",
    d: "M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z",
    clipRule: "evenodd"
  }));
}
var ForwardRef139 = React139.forwardRef(MenuIcon);
var MenuIcon_default = ForwardRef139;

// node_modules/@heroicons/react/solid/esm/MicrophoneIcon.js
var React140 = __toESM(require_react(), 1);
function MicrophoneIcon(props, svgRef) {
  return React140.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React140.createElement("path", {
    fillRule: "evenodd",
    d: "M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z",
    clipRule: "evenodd"
  }));
}
var ForwardRef140 = React140.forwardRef(MicrophoneIcon);
var MicrophoneIcon_default = ForwardRef140;

// node_modules/@heroicons/react/solid/esm/MinusCircleIcon.js
var React141 = __toESM(require_react(), 1);
function MinusCircleIcon(props, svgRef) {
  return React141.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React141.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 100-16 8 8 0 000 16zM7 9a1 1 0 000 2h6a1 1 0 100-2H7z",
    clipRule: "evenodd"
  }));
}
var ForwardRef141 = React141.forwardRef(MinusCircleIcon);
var MinusCircleIcon_default = ForwardRef141;

// node_modules/@heroicons/react/solid/esm/MinusSmIcon.js
var React142 = __toESM(require_react(), 1);
function MinusSmIcon(props, svgRef) {
  return React142.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React142.createElement("path", {
    fillRule: "evenodd",
    d: "M5 10a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z",
    clipRule: "evenodd"
  }));
}
var ForwardRef142 = React142.forwardRef(MinusSmIcon);
var MinusSmIcon_default = ForwardRef142;

// node_modules/@heroicons/react/solid/esm/MinusIcon.js
var React143 = __toESM(require_react(), 1);
function MinusIcon(props, svgRef) {
  return React143.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React143.createElement("path", {
    fillRule: "evenodd",
    d: "M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z",
    clipRule: "evenodd"
  }));
}
var ForwardRef143 = React143.forwardRef(MinusIcon);
var MinusIcon_default = ForwardRef143;

// node_modules/@heroicons/react/solid/esm/MoonIcon.js
var React144 = __toESM(require_react(), 1);
function MoonIcon(props, svgRef) {
  return React144.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React144.createElement("path", {
    d: "M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"
  }));
}
var ForwardRef144 = React144.forwardRef(MoonIcon);
var MoonIcon_default = ForwardRef144;

// node_modules/@heroicons/react/solid/esm/MusicNoteIcon.js
var React145 = __toESM(require_react(), 1);
function MusicNoteIcon(props, svgRef) {
  return React145.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React145.createElement("path", {
    d: "M18 3a1 1 0 00-1.196-.98l-10 2A1 1 0 006 5v9.114A4.369 4.369 0 005 14c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V7.82l8-1.6v5.894A4.37 4.37 0 0015 12c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V3z"
  }));
}
var ForwardRef145 = React145.forwardRef(MusicNoteIcon);
var MusicNoteIcon_default = ForwardRef145;

// node_modules/@heroicons/react/solid/esm/NewspaperIcon.js
var React146 = __toESM(require_react(), 1);
function NewspaperIcon(props, svgRef) {
  return React146.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React146.createElement("path", {
    fillRule: "evenodd",
    d: "M2 5a2 2 0 012-2h8a2 2 0 012 2v10a2 2 0 002 2H4a2 2 0 01-2-2V5zm3 1h6v4H5V6zm6 6H5v2h6v-2z",
    clipRule: "evenodd"
  }), React146.createElement("path", {
    d: "M15 7h1a2 2 0 012 2v5.5a1.5 1.5 0 01-3 0V7z"
  }));
}
var ForwardRef146 = React146.forwardRef(NewspaperIcon);
var NewspaperIcon_default = ForwardRef146;

// node_modules/@heroicons/react/solid/esm/OfficeBuildingIcon.js
var React147 = __toESM(require_react(), 1);
function OfficeBuildingIcon(props, svgRef) {
  return React147.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React147.createElement("path", {
    fillRule: "evenodd",
    d: "M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 110 2h-3a1 1 0 01-1-1v-2a1 1 0 00-1-1H9a1 1 0 00-1 1v2a1 1 0 01-1 1H4a1 1 0 110-2V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z",
    clipRule: "evenodd"
  }));
}
var ForwardRef147 = React147.forwardRef(OfficeBuildingIcon);
var OfficeBuildingIcon_default = ForwardRef147;

// node_modules/@heroicons/react/solid/esm/PaperAirplaneIcon.js
var React148 = __toESM(require_react(), 1);
function PaperAirplaneIcon(props, svgRef) {
  return React148.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React148.createElement("path", {
    d: "M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"
  }));
}
var ForwardRef148 = React148.forwardRef(PaperAirplaneIcon);
var PaperAirplaneIcon_default = ForwardRef148;

// node_modules/@heroicons/react/solid/esm/PaperClipIcon.js
var React149 = __toESM(require_react(), 1);
function PaperClipIcon(props, svgRef) {
  return React149.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React149.createElement("path", {
    fillRule: "evenodd",
    d: "M8 4a3 3 0 00-3 3v4a5 5 0 0010 0V7a1 1 0 112 0v4a7 7 0 11-14 0V7a5 5 0 0110 0v4a3 3 0 11-6 0V7a1 1 0 012 0v4a1 1 0 102 0V7a3 3 0 00-3-3z",
    clipRule: "evenodd"
  }));
}
var ForwardRef149 = React149.forwardRef(PaperClipIcon);
var PaperClipIcon_default = ForwardRef149;

// node_modules/@heroicons/react/solid/esm/PauseIcon.js
var React150 = __toESM(require_react(), 1);
function PauseIcon(props, svgRef) {
  return React150.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React150.createElement("path", {
    fillRule: "evenodd",
    d: "M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z",
    clipRule: "evenodd"
  }));
}
var ForwardRef150 = React150.forwardRef(PauseIcon);
var PauseIcon_default = ForwardRef150;

// node_modules/@heroicons/react/solid/esm/PencilAltIcon.js
var React151 = __toESM(require_react(), 1);
function PencilAltIcon(props, svgRef) {
  return React151.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React151.createElement("path", {
    d: "M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z"
  }), React151.createElement("path", {
    fillRule: "evenodd",
    d: "M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z",
    clipRule: "evenodd"
  }));
}
var ForwardRef151 = React151.forwardRef(PencilAltIcon);
var PencilAltIcon_default = ForwardRef151;

// node_modules/@heroicons/react/solid/esm/PencilIcon.js
var React152 = __toESM(require_react(), 1);
function PencilIcon(props, svgRef) {
  return React152.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React152.createElement("path", {
    d: "M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"
  }));
}
var ForwardRef152 = React152.forwardRef(PencilIcon);
var PencilIcon_default = ForwardRef152;

// node_modules/@heroicons/react/solid/esm/PhoneIncomingIcon.js
var React153 = __toESM(require_react(), 1);
function PhoneIncomingIcon(props, svgRef) {
  return React153.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React153.createElement("path", {
    d: "M14.414 7l3.293-3.293a1 1 0 00-1.414-1.414L13 5.586V4a1 1 0 10-2 0v4.003a.996.996 0 00.617.921A.997.997 0 0012 9h4a1 1 0 100-2h-1.586z"
  }), React153.createElement("path", {
    d: "M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"
  }));
}
var ForwardRef153 = React153.forwardRef(PhoneIncomingIcon);
var PhoneIncomingIcon_default = ForwardRef153;

// node_modules/@heroicons/react/solid/esm/PhoneMissedCallIcon.js
var React154 = __toESM(require_react(), 1);
function PhoneMissedCallIcon(props, svgRef) {
  return React154.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React154.createElement("path", {
    d: "M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"
  }), React154.createElement("path", {
    d: "M16.707 3.293a1 1 0 010 1.414L15.414 6l1.293 1.293a1 1 0 01-1.414 1.414L14 7.414l-1.293 1.293a1 1 0 11-1.414-1.414L12.586 6l-1.293-1.293a1 1 0 011.414-1.414L14 4.586l1.293-1.293a1 1 0 011.414 0z"
  }));
}
var ForwardRef154 = React154.forwardRef(PhoneMissedCallIcon);
var PhoneMissedCallIcon_default = ForwardRef154;

// node_modules/@heroicons/react/solid/esm/PhoneOutgoingIcon.js
var React155 = __toESM(require_react(), 1);
function PhoneOutgoingIcon(props, svgRef) {
  return React155.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React155.createElement("path", {
    d: "M17.924 2.617a.997.997 0 00-.215-.322l-.004-.004A.997.997 0 0017 2h-4a1 1 0 100 2h1.586l-3.293 3.293a1 1 0 001.414 1.414L16 5.414V7a1 1 0 102 0V3a.997.997 0 00-.076-.383z"
  }), React155.createElement("path", {
    d: "M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"
  }));
}
var ForwardRef155 = React155.forwardRef(PhoneOutgoingIcon);
var PhoneOutgoingIcon_default = ForwardRef155;

// node_modules/@heroicons/react/solid/esm/PhoneIcon.js
var React156 = __toESM(require_react(), 1);
function PhoneIcon(props, svgRef) {
  return React156.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React156.createElement("path", {
    d: "M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"
  }));
}
var ForwardRef156 = React156.forwardRef(PhoneIcon);
var PhoneIcon_default = ForwardRef156;

// node_modules/@heroicons/react/solid/esm/PhotographIcon.js
var React157 = __toESM(require_react(), 1);
function PhotographIcon(props, svgRef) {
  return React157.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React157.createElement("path", {
    fillRule: "evenodd",
    d: "M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z",
    clipRule: "evenodd"
  }));
}
var ForwardRef157 = React157.forwardRef(PhotographIcon);
var PhotographIcon_default = ForwardRef157;

// node_modules/@heroicons/react/solid/esm/PlayIcon.js
var React158 = __toESM(require_react(), 1);
function PlayIcon(props, svgRef) {
  return React158.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React158.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z",
    clipRule: "evenodd"
  }));
}
var ForwardRef158 = React158.forwardRef(PlayIcon);
var PlayIcon_default = ForwardRef158;

// node_modules/@heroicons/react/solid/esm/PlusCircleIcon.js
var React159 = __toESM(require_react(), 1);
function PlusCircleIcon(props, svgRef) {
  return React159.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React159.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z",
    clipRule: "evenodd"
  }));
}
var ForwardRef159 = React159.forwardRef(PlusCircleIcon);
var PlusCircleIcon_default = ForwardRef159;

// node_modules/@heroicons/react/solid/esm/PlusSmIcon.js
var React160 = __toESM(require_react(), 1);
function PlusSmIcon(props, svgRef) {
  return React160.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React160.createElement("path", {
    fillRule: "evenodd",
    d: "M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z",
    clipRule: "evenodd"
  }));
}
var ForwardRef160 = React160.forwardRef(PlusSmIcon);
var PlusSmIcon_default = ForwardRef160;

// node_modules/@heroicons/react/solid/esm/PlusIcon.js
var React161 = __toESM(require_react(), 1);
function PlusIcon(props, svgRef) {
  return React161.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React161.createElement("path", {
    fillRule: "evenodd",
    d: "M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z",
    clipRule: "evenodd"
  }));
}
var ForwardRef161 = React161.forwardRef(PlusIcon);
var PlusIcon_default = ForwardRef161;

// node_modules/@heroicons/react/solid/esm/PresentationChartBarIcon.js
var React162 = __toESM(require_react(), 1);
function PresentationChartBarIcon(props, svgRef) {
  return React162.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React162.createElement("path", {
    fillRule: "evenodd",
    d: "M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11 4a1 1 0 10-2 0v4a1 1 0 102 0V7zm-3 1a1 1 0 10-2 0v3a1 1 0 102 0V8zM8 9a1 1 0 00-2 0v2a1 1 0 102 0V9z",
    clipRule: "evenodd"
  }));
}
var ForwardRef162 = React162.forwardRef(PresentationChartBarIcon);
var PresentationChartBarIcon_default = ForwardRef162;

// node_modules/@heroicons/react/solid/esm/PresentationChartLineIcon.js
var React163 = __toESM(require_react(), 1);
function PresentationChartLineIcon(props, svgRef) {
  return React163.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React163.createElement("path", {
    fillRule: "evenodd",
    d: "M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z",
    clipRule: "evenodd"
  }));
}
var ForwardRef163 = React163.forwardRef(PresentationChartLineIcon);
var PresentationChartLineIcon_default = ForwardRef163;

// node_modules/@heroicons/react/solid/esm/PrinterIcon.js
var React164 = __toESM(require_react(), 1);
function PrinterIcon(props, svgRef) {
  return React164.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React164.createElement("path", {
    fillRule: "evenodd",
    d: "M5 4v3H4a2 2 0 00-2 2v3a2 2 0 002 2h1v2a2 2 0 002 2h6a2 2 0 002-2v-2h1a2 2 0 002-2V9a2 2 0 00-2-2h-1V4a2 2 0 00-2-2H7a2 2 0 00-2 2zm8 0H7v3h6V4zm0 8H7v4h6v-4z",
    clipRule: "evenodd"
  }));
}
var ForwardRef164 = React164.forwardRef(PrinterIcon);
var PrinterIcon_default = ForwardRef164;

// node_modules/@heroicons/react/solid/esm/PuzzleIcon.js
var React165 = __toESM(require_react(), 1);
function PuzzleIcon(props, svgRef) {
  return React165.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React165.createElement("path", {
    d: "M10 3.5a1.5 1.5 0 013 0V4a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-.5a1.5 1.5 0 000 3h.5a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-.5a1.5 1.5 0 00-3 0v.5a1 1 0 01-1 1H6a1 1 0 01-1-1v-3a1 1 0 00-1-1h-.5a1.5 1.5 0 010-3H4a1 1 0 001-1V6a1 1 0 011-1h3a1 1 0 001-1v-.5z"
  }));
}
var ForwardRef165 = React165.forwardRef(PuzzleIcon);
var PuzzleIcon_default = ForwardRef165;

// node_modules/@heroicons/react/solid/esm/QrcodeIcon.js
var React166 = __toESM(require_react(), 1);
function QrcodeIcon(props, svgRef) {
  return React166.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React166.createElement("path", {
    fillRule: "evenodd",
    d: "M3 4a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm2 2V5h1v1H5zM3 13a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1H4a1 1 0 01-1-1v-3zm2 2v-1h1v1H5zM13 3a1 1 0 00-1 1v3a1 1 0 001 1h3a1 1 0 001-1V4a1 1 0 00-1-1h-3zm1 2v1h1V5h-1z",
    clipRule: "evenodd"
  }), React166.createElement("path", {
    d: "M11 4a1 1 0 10-2 0v1a1 1 0 002 0V4zM10 7a1 1 0 011 1v1h2a1 1 0 110 2h-3a1 1 0 01-1-1V8a1 1 0 011-1zM16 9a1 1 0 100 2 1 1 0 000-2zM9 13a1 1 0 011-1h1a1 1 0 110 2v2a1 1 0 11-2 0v-3zM7 11a1 1 0 100-2H4a1 1 0 100 2h3zM17 13a1 1 0 01-1 1h-2a1 1 0 110-2h2a1 1 0 011 1zM16 17a1 1 0 100-2h-3a1 1 0 100 2h3z"
  }));
}
var ForwardRef166 = React166.forwardRef(QrcodeIcon);
var QrcodeIcon_default = ForwardRef166;

// node_modules/@heroicons/react/solid/esm/QuestionMarkCircleIcon.js
var React167 = __toESM(require_react(), 1);
function QuestionMarkCircleIcon(props, svgRef) {
  return React167.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React167.createElement("path", {
    fillRule: "evenodd",
    d: "M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z",
    clipRule: "evenodd"
  }));
}
var ForwardRef167 = React167.forwardRef(QuestionMarkCircleIcon);
var QuestionMarkCircleIcon_default = ForwardRef167;

// node_modules/@heroicons/react/solid/esm/ReceiptRefundIcon.js
var React168 = __toESM(require_react(), 1);
function ReceiptRefundIcon(props, svgRef) {
  return React168.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React168.createElement("path", {
    fillRule: "evenodd",
    d: "M5 2a2 2 0 00-2 2v14l3.5-2 3.5 2 3.5-2 3.5 2V4a2 2 0 00-2-2H5zm4.707 3.707a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L8.414 9H10a3 3 0 013 3v1a1 1 0 102 0v-1a5 5 0 00-5-5H8.414l1.293-1.293z",
    clipRule: "evenodd"
  }));
}
var ForwardRef168 = React168.forwardRef(ReceiptRefundIcon);
var ReceiptRefundIcon_default = ForwardRef168;

// node_modules/@heroicons/react/solid/esm/ReceiptTaxIcon.js
var React169 = __toESM(require_react(), 1);
function ReceiptTaxIcon(props, svgRef) {
  return React169.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React169.createElement("path", {
    fillRule: "evenodd",
    d: "M5 2a2 2 0 00-2 2v14l3.5-2 3.5 2 3.5-2 3.5 2V4a2 2 0 00-2-2H5zm2.5 3a1.5 1.5 0 100 3 1.5 1.5 0 000-3zm6.207.293a1 1 0 00-1.414 0l-6 6a1 1 0 101.414 1.414l6-6a1 1 0 000-1.414zM12.5 10a1.5 1.5 0 100 3 1.5 1.5 0 000-3z",
    clipRule: "evenodd"
  }));
}
var ForwardRef169 = React169.forwardRef(ReceiptTaxIcon);
var ReceiptTaxIcon_default = ForwardRef169;

// node_modules/@heroicons/react/solid/esm/RefreshIcon.js
var React170 = __toESM(require_react(), 1);
function RefreshIcon(props, svgRef) {
  return React170.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React170.createElement("path", {
    fillRule: "evenodd",
    d: "M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z",
    clipRule: "evenodd"
  }));
}
var ForwardRef170 = React170.forwardRef(RefreshIcon);
var RefreshIcon_default = ForwardRef170;

// node_modules/@heroicons/react/solid/esm/ReplyIcon.js
var React171 = __toESM(require_react(), 1);
function ReplyIcon(props, svgRef) {
  return React171.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React171.createElement("path", {
    fillRule: "evenodd",
    d: "M7.707 3.293a1 1 0 010 1.414L5.414 7H11a7 7 0 017 7v2a1 1 0 11-2 0v-2a5 5 0 00-5-5H5.414l2.293 2.293a1 1 0 11-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z",
    clipRule: "evenodd"
  }));
}
var ForwardRef171 = React171.forwardRef(ReplyIcon);
var ReplyIcon_default = ForwardRef171;

// node_modules/@heroicons/react/solid/esm/RewindIcon.js
var React172 = __toESM(require_react(), 1);
function RewindIcon(props, svgRef) {
  return React172.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React172.createElement("path", {
    d: "M8.445 14.832A1 1 0 0010 14v-2.798l5.445 3.63A1 1 0 0017 14V6a1 1 0 00-1.555-.832L10 8.798V6a1 1 0 00-1.555-.832l-6 4a1 1 0 000 1.664l6 4z"
  }));
}
var ForwardRef172 = React172.forwardRef(RewindIcon);
var RewindIcon_default = ForwardRef172;

// node_modules/@heroicons/react/solid/esm/RssIcon.js
var React173 = __toESM(require_react(), 1);
function RssIcon(props, svgRef) {
  return React173.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React173.createElement("path", {
    d: "M5 3a1 1 0 000 2c5.523 0 10 4.477 10 10a1 1 0 102 0C17 8.373 11.627 3 5 3z"
  }), React173.createElement("path", {
    d: "M4 9a1 1 0 011-1 7 7 0 017 7 1 1 0 11-2 0 5 5 0 00-5-5 1 1 0 01-1-1zM3 15a2 2 0 114 0 2 2 0 01-4 0z"
  }));
}
var ForwardRef173 = React173.forwardRef(RssIcon);
var RssIcon_default = ForwardRef173;

// node_modules/@heroicons/react/solid/esm/SaveAsIcon.js
var React174 = __toESM(require_react(), 1);
function SaveAsIcon(props, svgRef) {
  return React174.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React174.createElement("path", {
    d: "M9.707 7.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L13 8.586V5h3a2 2 0 012 2v5a2 2 0 01-2 2H8a2 2 0 01-2-2V7a2 2 0 012-2h3v3.586L9.707 7.293zM11 3a1 1 0 112 0v2h-2V3z"
  }), React174.createElement("path", {
    d: "M4 9a2 2 0 00-2 2v5a2 2 0 002 2h8a2 2 0 002-2H4V9z"
  }));
}
var ForwardRef174 = React174.forwardRef(SaveAsIcon);
var SaveAsIcon_default = ForwardRef174;

// node_modules/@heroicons/react/solid/esm/SaveIcon.js
var React175 = __toESM(require_react(), 1);
function SaveIcon(props, svgRef) {
  return React175.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React175.createElement("path", {
    d: "M7.707 10.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V6h5a2 2 0 012 2v7a2 2 0 01-2 2H4a2 2 0 01-2-2V8a2 2 0 012-2h5v5.586l-1.293-1.293zM9 4a1 1 0 012 0v2H9V4z"
  }));
}
var ForwardRef175 = React175.forwardRef(SaveIcon);
var SaveIcon_default = ForwardRef175;

// node_modules/@heroicons/react/solid/esm/ScaleIcon.js
var React176 = __toESM(require_react(), 1);
function ScaleIcon(props, svgRef) {
  return React176.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React176.createElement("path", {
    fillRule: "evenodd",
    d: "M10 2a1 1 0 011 1v1.323l3.954 1.582 1.599-.8a1 1 0 01.894 1.79l-1.233.616 1.738 5.42a1 1 0 01-.285 1.05A3.989 3.989 0 0115 15a3.989 3.989 0 01-2.667-1.019 1 1 0 01-.285-1.05l1.715-5.349L11 6.477V16h2a1 1 0 110 2H7a1 1 0 110-2h2V6.477L6.237 7.582l1.715 5.349a1 1 0 01-.285 1.05A3.989 3.989 0 015 15a3.989 3.989 0 01-2.667-1.019 1 1 0 01-.285-1.05l1.738-5.42-1.233-.617a1 1 0 01.894-1.788l1.599.799L9 4.323V3a1 1 0 011-1zm-5 8.274l-.818 2.552c.25.112.526.174.818.174.292 0 .569-.062.818-.174L5 10.274zm10 0l-.818 2.552c.25.112.526.174.818.174.292 0 .569-.062.818-.174L15 10.274z",
    clipRule: "evenodd"
  }));
}
var ForwardRef176 = React176.forwardRef(ScaleIcon);
var ScaleIcon_default = ForwardRef176;

// node_modules/@heroicons/react/solid/esm/ScissorsIcon.js
var React177 = __toESM(require_react(), 1);
function ScissorsIcon(props, svgRef) {
  return React177.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React177.createElement("path", {
    fillRule: "evenodd",
    d: "M5.5 2a3.5 3.5 0 101.665 6.58L8.585 10l-1.42 1.42a3.5 3.5 0 101.414 1.414l8.128-8.127a1 1 0 00-1.414-1.414L10 8.586l-1.42-1.42A3.5 3.5 0 005.5 2zM4 5.5a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zm0 9a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0z",
    clipRule: "evenodd"
  }), React177.createElement("path", {
    d: "M12.828 11.414a1 1 0 00-1.414 1.414l3.879 3.88a1 1 0 001.414-1.415l-3.879-3.879z"
  }));
}
var ForwardRef177 = React177.forwardRef(ScissorsIcon);
var ScissorsIcon_default = ForwardRef177;

// node_modules/@heroicons/react/solid/esm/SearchCircleIcon.js
var React178 = __toESM(require_react(), 1);
function SearchCircleIcon(props, svgRef) {
  return React178.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React178.createElement("path", {
    d: "M9 9a2 2 0 114 0 2 2 0 01-4 0z"
  }), React178.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a4 4 0 00-3.446 6.032l-2.261 2.26a1 1 0 101.414 1.415l2.261-2.261A4 4 0 1011 5z",
    clipRule: "evenodd"
  }));
}
var ForwardRef178 = React178.forwardRef(SearchCircleIcon);
var SearchCircleIcon_default = ForwardRef178;

// node_modules/@heroicons/react/solid/esm/SearchIcon.js
var React179 = __toESM(require_react(), 1);
function SearchIcon(props, svgRef) {
  return React179.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React179.createElement("path", {
    fillRule: "evenodd",
    d: "M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z",
    clipRule: "evenodd"
  }));
}
var ForwardRef179 = React179.forwardRef(SearchIcon);
var SearchIcon_default = ForwardRef179;

// node_modules/@heroicons/react/solid/esm/SelectorIcon.js
var React180 = __toESM(require_react(), 1);
function SelectorIcon(props, svgRef) {
  return React180.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React180.createElement("path", {
    fillRule: "evenodd",
    d: "M10 3a1 1 0 01.707.293l3 3a1 1 0 01-1.414 1.414L10 5.414 7.707 7.707a1 1 0 01-1.414-1.414l3-3A1 1 0 0110 3zm-3.707 9.293a1 1 0 011.414 0L10 14.586l2.293-2.293a1 1 0 011.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z",
    clipRule: "evenodd"
  }));
}
var ForwardRef180 = React180.forwardRef(SelectorIcon);
var SelectorIcon_default = ForwardRef180;

// node_modules/@heroicons/react/solid/esm/ServerIcon.js
var React181 = __toESM(require_react(), 1);
function ServerIcon(props, svgRef) {
  return React181.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React181.createElement("path", {
    fillRule: "evenodd",
    d: "M2 5a2 2 0 012-2h12a2 2 0 012 2v2a2 2 0 01-2 2H4a2 2 0 01-2-2V5zm14 1a1 1 0 11-2 0 1 1 0 012 0zM2 13a2 2 0 012-2h12a2 2 0 012 2v2a2 2 0 01-2 2H4a2 2 0 01-2-2v-2zm14 1a1 1 0 11-2 0 1 1 0 012 0z",
    clipRule: "evenodd"
  }));
}
var ForwardRef181 = React181.forwardRef(ServerIcon);
var ServerIcon_default = ForwardRef181;

// node_modules/@heroicons/react/solid/esm/ShareIcon.js
var React182 = __toESM(require_react(), 1);
function ShareIcon(props, svgRef) {
  return React182.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React182.createElement("path", {
    d: "M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z"
  }));
}
var ForwardRef182 = React182.forwardRef(ShareIcon);
var ShareIcon_default = ForwardRef182;

// node_modules/@heroicons/react/solid/esm/ShieldCheckIcon.js
var React183 = __toESM(require_react(), 1);
function ShieldCheckIcon(props, svgRef) {
  return React183.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React183.createElement("path", {
    fillRule: "evenodd",
    d: "M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",
    clipRule: "evenodd"
  }));
}
var ForwardRef183 = React183.forwardRef(ShieldCheckIcon);
var ShieldCheckIcon_default = ForwardRef183;

// node_modules/@heroicons/react/solid/esm/ShieldExclamationIcon.js
var React184 = __toESM(require_react(), 1);
function ShieldExclamationIcon(props, svgRef) {
  return React184.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React184.createElement("path", {
    fillRule: "evenodd",
    d: "M10 1.944A11.954 11.954 0 012.166 5C2.056 5.649 2 6.319 2 7c0 5.225 3.34 9.67 8 11.317C14.66 16.67 18 12.225 18 7c0-.682-.057-1.35-.166-2.001A11.954 11.954 0 0110 1.944zM11 14a1 1 0 11-2 0 1 1 0 012 0zm0-7a1 1 0 10-2 0v3a1 1 0 102 0V7z",
    clipRule: "evenodd"
  }));
}
var ForwardRef184 = React184.forwardRef(ShieldExclamationIcon);
var ShieldExclamationIcon_default = ForwardRef184;

// node_modules/@heroicons/react/solid/esm/ShoppingBagIcon.js
var React185 = __toESM(require_react(), 1);
function ShoppingBagIcon(props, svgRef) {
  return React185.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React185.createElement("path", {
    fillRule: "evenodd",
    d: "M10 2a4 4 0 00-4 4v1H5a1 1 0 00-.994.89l-1 9A1 1 0 004 18h12a1 1 0 00.994-1.11l-1-9A1 1 0 0015 7h-1V6a4 4 0 00-4-4zm2 5V6a2 2 0 10-4 0v1h4zm-6 3a1 1 0 112 0 1 1 0 01-2 0zm7-1a1 1 0 100 2 1 1 0 000-2z",
    clipRule: "evenodd"
  }));
}
var ForwardRef185 = React185.forwardRef(ShoppingBagIcon);
var ShoppingBagIcon_default = ForwardRef185;

// node_modules/@heroicons/react/solid/esm/ShoppingCartIcon.js
var React186 = __toESM(require_react(), 1);
function ShoppingCartIcon(props, svgRef) {
  return React186.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React186.createElement("path", {
    d: "M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"
  }));
}
var ForwardRef186 = React186.forwardRef(ShoppingCartIcon);
var ShoppingCartIcon_default = ForwardRef186;

// node_modules/@heroicons/react/solid/esm/SortAscendingIcon.js
var React187 = __toESM(require_react(), 1);
function SortAscendingIcon(props, svgRef) {
  return React187.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React187.createElement("path", {
    d: "M3 3a1 1 0 000 2h11a1 1 0 100-2H3zM3 7a1 1 0 000 2h5a1 1 0 000-2H3zM3 11a1 1 0 100 2h4a1 1 0 100-2H3zM13 16a1 1 0 102 0v-5.586l1.293 1.293a1 1 0 001.414-1.414l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 101.414 1.414L13 10.414V16z"
  }));
}
var ForwardRef187 = React187.forwardRef(SortAscendingIcon);
var SortAscendingIcon_default = ForwardRef187;

// node_modules/@heroicons/react/solid/esm/SortDescendingIcon.js
var React188 = __toESM(require_react(), 1);
function SortDescendingIcon(props, svgRef) {
  return React188.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React188.createElement("path", {
    d: "M3 3a1 1 0 000 2h11a1 1 0 100-2H3zM3 7a1 1 0 000 2h7a1 1 0 100-2H3zM3 11a1 1 0 100 2h4a1 1 0 100-2H3zM15 8a1 1 0 10-2 0v5.586l-1.293-1.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L15 13.586V8z"
  }));
}
var ForwardRef188 = React188.forwardRef(SortDescendingIcon);
var SortDescendingIcon_default = ForwardRef188;

// node_modules/@heroicons/react/solid/esm/SparklesIcon.js
var React189 = __toESM(require_react(), 1);
function SparklesIcon(props, svgRef) {
  return React189.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React189.createElement("path", {
    fillRule: "evenodd",
    d: "M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 9.134a1 1 0 010 1.732l-3.354 1.935-1.18 4.455a1 1 0 01-1.933 0L9.854 12.8 6.5 10.866a1 1 0 010-1.732l3.354-1.935 1.18-4.455A1 1 0 0112 2z",
    clipRule: "evenodd"
  }));
}
var ForwardRef189 = React189.forwardRef(SparklesIcon);
var SparklesIcon_default = ForwardRef189;

// node_modules/@heroicons/react/solid/esm/SpeakerphoneIcon.js
var React190 = __toESM(require_react(), 1);
function SpeakerphoneIcon(props, svgRef) {
  return React190.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React190.createElement("path", {
    fillRule: "evenodd",
    d: "M18 3a1 1 0 00-1.447-.894L8.763 6H5a3 3 0 000 6h.28l1.771 5.316A1 1 0 008 18h1a1 1 0 001-1v-4.382l6.553 3.276A1 1 0 0018 15V3z",
    clipRule: "evenodd"
  }));
}
var ForwardRef190 = React190.forwardRef(SpeakerphoneIcon);
var SpeakerphoneIcon_default = ForwardRef190;

// node_modules/@heroicons/react/solid/esm/StarIcon.js
var React191 = __toESM(require_react(), 1);
function StarIcon(props, svgRef) {
  return React191.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React191.createElement("path", {
    d: "M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
  }));
}
var ForwardRef191 = React191.forwardRef(StarIcon);
var StarIcon_default = ForwardRef191;

// node_modules/@heroicons/react/solid/esm/StatusOfflineIcon.js
var React192 = __toESM(require_react(), 1);
function StatusOfflineIcon(props, svgRef) {
  return React192.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React192.createElement("path", {
    d: "M3.707 2.293a1 1 0 00-1.414 1.414l6.921 6.922c.05.062.105.118.168.167l6.91 6.911a1 1 0 001.415-1.414l-.675-.675a9.001 9.001 0 00-.668-11.982A1 1 0 1014.95 5.05a7.002 7.002 0 01.657 9.143l-1.435-1.435a5.002 5.002 0 00-.636-6.294A1 1 0 0012.12 7.88c.924.923 1.12 2.3.587 3.415l-1.992-1.992a.922.922 0 00-.018-.018l-6.99-6.991zM3.238 8.187a1 1 0 00-1.933-.516c-.8 3-.025 6.336 2.331 8.693a1 1 0 001.414-1.415 6.997 6.997 0 01-1.812-6.762zM7.4 11.5a1 1 0 10-1.73 1c.214.371.48.72.795 1.035a1 1 0 001.414-1.414c-.191-.191-.35-.4-.478-.622z"
  }));
}
var ForwardRef192 = React192.forwardRef(StatusOfflineIcon);
var StatusOfflineIcon_default = ForwardRef192;

// node_modules/@heroicons/react/solid/esm/StatusOnlineIcon.js
var React193 = __toESM(require_react(), 1);
function StatusOnlineIcon(props, svgRef) {
  return React193.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React193.createElement("path", {
    fillRule: "evenodd",
    d: "M5.05 3.636a1 1 0 010 1.414 7 7 0 000 9.9 1 1 0 11-1.414 1.414 9 9 0 010-12.728 1 1 0 011.414 0zm9.9 0a1 1 0 011.414 0 9 9 0 010 12.728 1 1 0 11-1.414-1.414 7 7 0 000-9.9 1 1 0 010-1.414zM7.879 6.464a1 1 0 010 1.414 3 3 0 000 4.243 1 1 0 11-1.415 1.414 5 5 0 010-7.07 1 1 0 011.415 0zm4.242 0a1 1 0 011.415 0 5 5 0 010 7.072 1 1 0 01-1.415-1.415 3 3 0 000-4.242 1 1 0 010-1.415zM10 9a1 1 0 011 1v.01a1 1 0 11-2 0V10a1 1 0 011-1z",
    clipRule: "evenodd"
  }));
}
var ForwardRef193 = React193.forwardRef(StatusOnlineIcon);
var StatusOnlineIcon_default = ForwardRef193;

// node_modules/@heroicons/react/solid/esm/StopIcon.js
var React194 = __toESM(require_react(), 1);
function StopIcon(props, svgRef) {
  return React194.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React194.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z",
    clipRule: "evenodd"
  }));
}
var ForwardRef194 = React194.forwardRef(StopIcon);
var StopIcon_default = ForwardRef194;

// node_modules/@heroicons/react/solid/esm/SunIcon.js
var React195 = __toESM(require_react(), 1);
function SunIcon(props, svgRef) {
  return React195.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React195.createElement("path", {
    fillRule: "evenodd",
    d: "M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z",
    clipRule: "evenodd"
  }));
}
var ForwardRef195 = React195.forwardRef(SunIcon);
var SunIcon_default = ForwardRef195;

// node_modules/@heroicons/react/solid/esm/SupportIcon.js
var React196 = __toESM(require_react(), 1);
function SupportIcon(props, svgRef) {
  return React196.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React196.createElement("path", {
    fillRule: "evenodd",
    d: "M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-2 0c0 .993-.241 1.929-.668 2.754l-1.524-1.525a3.997 3.997 0 00.078-2.183l1.562-1.562C15.802 8.249 16 9.1 16 10zm-5.165 3.913l1.58 1.58A5.98 5.98 0 0110 16a5.976 5.976 0 01-2.516-.552l1.562-1.562a4.006 4.006 0 001.789.027zm-4.677-2.796a4.002 4.002 0 01-.041-2.08l-.08.08-1.53-1.533A5.98 5.98 0 004 10c0 .954.223 1.856.619 2.657l1.54-1.54zm1.088-6.45A5.974 5.974 0 0110 4c.954 0 1.856.223 2.657.619l-1.54 1.54a4.002 4.002 0 00-2.346.033L7.246 4.668zM12 10a2 2 0 11-4 0 2 2 0 014 0z",
    clipRule: "evenodd"
  }));
}
var ForwardRef196 = React196.forwardRef(SupportIcon);
var SupportIcon_default = ForwardRef196;

// node_modules/@heroicons/react/solid/esm/SwitchHorizontalIcon.js
var React197 = __toESM(require_react(), 1);
function SwitchHorizontalIcon(props, svgRef) {
  return React197.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React197.createElement("path", {
    d: "M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8zM12 15a1 1 0 100-2H6.414l1.293-1.293a1 1 0 10-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L6.414 15H12z"
  }));
}
var ForwardRef197 = React197.forwardRef(SwitchHorizontalIcon);
var SwitchHorizontalIcon_default = ForwardRef197;

// node_modules/@heroicons/react/solid/esm/SwitchVerticalIcon.js
var React198 = __toESM(require_react(), 1);
function SwitchVerticalIcon(props, svgRef) {
  return React198.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React198.createElement("path", {
    d: "M5 12a1 1 0 102 0V6.414l1.293 1.293a1 1 0 001.414-1.414l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L5 6.414V12zM15 8a1 1 0 10-2 0v5.586l-1.293-1.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L15 13.586V8z"
  }));
}
var ForwardRef198 = React198.forwardRef(SwitchVerticalIcon);
var SwitchVerticalIcon_default = ForwardRef198;

// node_modules/@heroicons/react/solid/esm/TableIcon.js
var React199 = __toESM(require_react(), 1);
function TableIcon(props, svgRef) {
  return React199.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React199.createElement("path", {
    fillRule: "evenodd",
    d: "M5 4a3 3 0 00-3 3v6a3 3 0 003 3h10a3 3 0 003-3V7a3 3 0 00-3-3H5zm-1 9v-1h5v2H5a1 1 0 01-1-1zm7 1h4a1 1 0 001-1v-1h-5v2zm0-4h5V8h-5v2zM9 8H4v2h5V8z",
    clipRule: "evenodd"
  }));
}
var ForwardRef199 = React199.forwardRef(TableIcon);
var TableIcon_default = ForwardRef199;

// node_modules/@heroicons/react/solid/esm/TagIcon.js
var React200 = __toESM(require_react(), 1);
function TagIcon(props, svgRef) {
  return React200.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React200.createElement("path", {
    fillRule: "evenodd",
    d: "M17.707 9.293a1 1 0 010 1.414l-7 7a1 1 0 01-1.414 0l-7-7A.997.997 0 012 10V5a3 3 0 013-3h5c.256 0 .512.098.707.293l7 7zM5 6a1 1 0 100-2 1 1 0 000 2z",
    clipRule: "evenodd"
  }));
}
var ForwardRef200 = React200.forwardRef(TagIcon);
var TagIcon_default = ForwardRef200;

// node_modules/@heroicons/react/solid/esm/TemplateIcon.js
var React201 = __toESM(require_react(), 1);
function TemplateIcon(props, svgRef) {
  return React201.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React201.createElement("path", {
    d: "M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
  }));
}
var ForwardRef201 = React201.forwardRef(TemplateIcon);
var TemplateIcon_default = ForwardRef201;

// node_modules/@heroicons/react/solid/esm/TerminalIcon.js
var React202 = __toESM(require_react(), 1);
function TerminalIcon(props, svgRef) {
  return React202.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React202.createElement("path", {
    fillRule: "evenodd",
    d: "M2 5a2 2 0 012-2h12a2 2 0 012 2v10a2 2 0 01-2 2H4a2 2 0 01-2-2V5zm3.293 1.293a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 01-1.414-1.414L7.586 10 5.293 7.707a1 1 0 010-1.414zM11 12a1 1 0 100 2h3a1 1 0 100-2h-3z",
    clipRule: "evenodd"
  }));
}
var ForwardRef202 = React202.forwardRef(TerminalIcon);
var TerminalIcon_default = ForwardRef202;

// node_modules/@heroicons/react/solid/esm/ThumbDownIcon.js
var React203 = __toESM(require_react(), 1);
function ThumbDownIcon(props, svgRef) {
  return React203.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React203.createElement("path", {
    d: "M18 9.5a1.5 1.5 0 11-3 0v-6a1.5 1.5 0 013 0v6zM14 9.667v-5.43a2 2 0 00-1.105-1.79l-.05-.025A4 4 0 0011.055 2H5.64a2 2 0 00-1.962 1.608l-1.2 6A2 2 0 004.44 12H8v4a2 2 0 002 2 1 1 0 001-1v-.667a4 4 0 01.8-2.4l1.4-1.866a4 4 0 00.8-2.4z"
  }));
}
var ForwardRef203 = React203.forwardRef(ThumbDownIcon);
var ThumbDownIcon_default = ForwardRef203;

// node_modules/@heroicons/react/solid/esm/ThumbUpIcon.js
var React204 = __toESM(require_react(), 1);
function ThumbUpIcon(props, svgRef) {
  return React204.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React204.createElement("path", {
    d: "M2 10.5a1.5 1.5 0 113 0v6a1.5 1.5 0 01-3 0v-6zM6 10.333v5.43a2 2 0 001.106 1.79l.05.025A4 4 0 008.943 18h5.416a2 2 0 001.962-1.608l1.2-6A2 2 0 0015.56 8H12V4a2 2 0 00-2-2 1 1 0 00-1 1v.667a4 4 0 01-.8 2.4L6.8 7.933a4 4 0 00-.8 2.4z"
  }));
}
var ForwardRef204 = React204.forwardRef(ThumbUpIcon);
var ThumbUpIcon_default = ForwardRef204;

// node_modules/@heroicons/react/solid/esm/TicketIcon.js
var React205 = __toESM(require_react(), 1);
function TicketIcon(props, svgRef) {
  return React205.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React205.createElement("path", {
    d: "M2 6a2 2 0 012-2h12a2 2 0 012 2v2a2 2 0 100 4v2a2 2 0 01-2 2H4a2 2 0 01-2-2v-2a2 2 0 100-4V6z"
  }));
}
var ForwardRef205 = React205.forwardRef(TicketIcon);
var TicketIcon_default = ForwardRef205;

// node_modules/@heroicons/react/solid/esm/TranslateIcon.js
var React206 = __toESM(require_react(), 1);
function TranslateIcon(props, svgRef) {
  return React206.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React206.createElement("path", {
    fillRule: "evenodd",
    d: "M7 2a1 1 0 011 1v1h3a1 1 0 110 2H9.578a18.87 18.87 0 01-1.724 4.78c.29.354.596.696.914 1.026a1 1 0 11-1.44 1.389c-.188-.196-.373-.396-.554-.6a19.098 19.098 0 01-3.107 3.567 1 1 0 01-1.334-1.49 17.087 17.087 0 003.13-3.733 18.992 18.992 0 01-1.487-2.494 1 1 0 111.79-.89c.234.47.489.928.764 1.372.417-.934.752-1.913.997-2.927H3a1 1 0 110-2h3V3a1 1 0 011-1zm6 6a1 1 0 01.894.553l2.991 5.982a.869.869 0 01.02.037l.99 1.98a1 1 0 11-1.79.895L15.383 16h-4.764l-.724 1.447a1 1 0 11-1.788-.894l.99-1.98.019-.038 2.99-5.982A1 1 0 0113 8zm-1.382 6h2.764L13 11.236 11.618 14z",
    clipRule: "evenodd"
  }));
}
var ForwardRef206 = React206.forwardRef(TranslateIcon);
var TranslateIcon_default = ForwardRef206;

// node_modules/@heroicons/react/solid/esm/TrashIcon.js
var React207 = __toESM(require_react(), 1);
function TrashIcon(props, svgRef) {
  return React207.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React207.createElement("path", {
    fillRule: "evenodd",
    d: "M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z",
    clipRule: "evenodd"
  }));
}
var ForwardRef207 = React207.forwardRef(TrashIcon);
var TrashIcon_default = ForwardRef207;

// node_modules/@heroicons/react/solid/esm/TrendingDownIcon.js
var React208 = __toESM(require_react(), 1);
function TrendingDownIcon(props, svgRef) {
  return React208.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React208.createElement("path", {
    fillRule: "evenodd",
    d: "M12 13a1 1 0 100 2h5a1 1 0 001-1V9a1 1 0 10-2 0v2.586l-4.293-4.293a1 1 0 00-1.414 0L8 9.586 3.707 5.293a1 1 0 00-1.414 1.414l5 5a1 1 0 001.414 0L11 9.414 14.586 13H12z",
    clipRule: "evenodd"
  }));
}
var ForwardRef208 = React208.forwardRef(TrendingDownIcon);
var TrendingDownIcon_default = ForwardRef208;

// node_modules/@heroicons/react/solid/esm/TrendingUpIcon.js
var React209 = __toESM(require_react(), 1);
function TrendingUpIcon(props, svgRef) {
  return React209.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React209.createElement("path", {
    fillRule: "evenodd",
    d: "M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z",
    clipRule: "evenodd"
  }));
}
var ForwardRef209 = React209.forwardRef(TrendingUpIcon);
var TrendingUpIcon_default = ForwardRef209;

// node_modules/@heroicons/react/solid/esm/TruckIcon.js
var React210 = __toESM(require_react(), 1);
function TruckIcon(props, svgRef) {
  return React210.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React210.createElement("path", {
    d: "M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z"
  }), React210.createElement("path", {
    d: "M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1v-5a1 1 0 00-.293-.707l-2-2A1 1 0 0015 7h-1z"
  }));
}
var ForwardRef210 = React210.forwardRef(TruckIcon);
var TruckIcon_default = ForwardRef210;

// node_modules/@heroicons/react/solid/esm/UploadIcon.js
var React211 = __toESM(require_react(), 1);
function UploadIcon(props, svgRef) {
  return React211.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React211.createElement("path", {
    fillRule: "evenodd",
    d: "M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z",
    clipRule: "evenodd"
  }));
}
var ForwardRef211 = React211.forwardRef(UploadIcon);
var UploadIcon_default = ForwardRef211;

// node_modules/@heroicons/react/solid/esm/UserAddIcon.js
var React212 = __toESM(require_react(), 1);
function UserAddIcon(props, svgRef) {
  return React212.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React212.createElement("path", {
    d: "M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z"
  }));
}
var ForwardRef212 = React212.forwardRef(UserAddIcon);
var UserAddIcon_default = ForwardRef212;

// node_modules/@heroicons/react/solid/esm/UserCircleIcon.js
var React213 = __toESM(require_react(), 1);
function UserCircleIcon(props, svgRef) {
  return React213.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React213.createElement("path", {
    fillRule: "evenodd",
    d: "M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z",
    clipRule: "evenodd"
  }));
}
var ForwardRef213 = React213.forwardRef(UserCircleIcon);
var UserCircleIcon_default = ForwardRef213;

// node_modules/@heroicons/react/solid/esm/UserGroupIcon.js
var React214 = __toESM(require_react(), 1);
function UserGroupIcon(props, svgRef) {
  return React214.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React214.createElement("path", {
    d: "M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"
  }));
}
var ForwardRef214 = React214.forwardRef(UserGroupIcon);
var UserGroupIcon_default = ForwardRef214;

// node_modules/@heroicons/react/solid/esm/UserRemoveIcon.js
var React215 = __toESM(require_react(), 1);
function UserRemoveIcon(props, svgRef) {
  return React215.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React215.createElement("path", {
    d: "M11 6a3 3 0 11-6 0 3 3 0 016 0zM14 17a6 6 0 00-12 0h12zM13 8a1 1 0 100 2h4a1 1 0 100-2h-4z"
  }));
}
var ForwardRef215 = React215.forwardRef(UserRemoveIcon);
var UserRemoveIcon_default = ForwardRef215;

// node_modules/@heroicons/react/solid/esm/UserIcon.js
var React216 = __toESM(require_react(), 1);
function UserIcon(props, svgRef) {
  return React216.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React216.createElement("path", {
    fillRule: "evenodd",
    d: "M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z",
    clipRule: "evenodd"
  }));
}
var ForwardRef216 = React216.forwardRef(UserIcon);
var UserIcon_default = ForwardRef216;

// node_modules/@heroicons/react/solid/esm/UsersIcon.js
var React217 = __toESM(require_react(), 1);
function UsersIcon(props, svgRef) {
  return React217.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React217.createElement("path", {
    d: "M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"
  }));
}
var ForwardRef217 = React217.forwardRef(UsersIcon);
var UsersIcon_default = ForwardRef217;

// node_modules/@heroicons/react/solid/esm/VariableIcon.js
var React218 = __toESM(require_react(), 1);
function VariableIcon(props, svgRef) {
  return React218.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React218.createElement("path", {
    fillRule: "evenodd",
    d: "M4.649 3.084A1 1 0 015.163 4.4 13.95 13.95 0 004 10c0 1.993.416 3.886 1.164 5.6a1 1 0 01-1.832.8A15.95 15.95 0 012 10c0-2.274.475-4.44 1.332-6.4a1 1 0 011.317-.516zM12.96 7a3 3 0 00-2.342 1.126l-.328.41-.111-.279A2 2 0 008.323 7H8a1 1 0 000 2h.323l.532 1.33-1.035 1.295a1 1 0 01-.781.375H7a1 1 0 100 2h.039a3 3 0 002.342-1.126l.328-.41.111.279A2 2 0 0011.677 14H12a1 1 0 100-2h-.323l-.532-1.33 1.035-1.295A1 1 0 0112.961 9H13a1 1 0 100-2h-.039zm1.874-2.6a1 1 0 011.833-.8A15.95 15.95 0 0118 10c0 2.274-.475 4.44-1.332 6.4a1 1 0 11-1.832-.8A13.949 13.949 0 0016 10c0-1.993-.416-3.886-1.165-5.6z",
    clipRule: "evenodd"
  }));
}
var ForwardRef218 = React218.forwardRef(VariableIcon);
var VariableIcon_default = ForwardRef218;

// node_modules/@heroicons/react/solid/esm/VideoCameraIcon.js
var React219 = __toESM(require_react(), 1);
function VideoCameraIcon(props, svgRef) {
  return React219.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React219.createElement("path", {
    d: "M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"
  }));
}
var ForwardRef219 = React219.forwardRef(VideoCameraIcon);
var VideoCameraIcon_default = ForwardRef219;

// node_modules/@heroicons/react/solid/esm/ViewBoardsIcon.js
var React220 = __toESM(require_react(), 1);
function ViewBoardsIcon(props, svgRef) {
  return React220.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React220.createElement("path", {
    d: "M2 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1H3a1 1 0 01-1-1V4zM8 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1H9a1 1 0 01-1-1V4zM15 3a1 1 0 00-1 1v12a1 1 0 001 1h2a1 1 0 001-1V4a1 1 0 00-1-1h-2z"
  }));
}
var ForwardRef220 = React220.forwardRef(ViewBoardsIcon);
var ViewBoardsIcon_default = ForwardRef220;

// node_modules/@heroicons/react/solid/esm/ViewGridAddIcon.js
var React221 = __toESM(require_react(), 1);
function ViewGridAddIcon(props, svgRef) {
  return React221.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React221.createElement("path", {
    d: "M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM14 11a1 1 0 011 1v1h1a1 1 0 110 2h-1v1a1 1 0 11-2 0v-1h-1a1 1 0 110-2h1v-1a1 1 0 011-1z"
  }));
}
var ForwardRef221 = React221.forwardRef(ViewGridAddIcon);
var ViewGridAddIcon_default = ForwardRef221;

// node_modules/@heroicons/react/solid/esm/ViewGridIcon.js
var React222 = __toESM(require_react(), 1);
function ViewGridIcon(props, svgRef) {
  return React222.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React222.createElement("path", {
    d: "M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
  }));
}
var ForwardRef222 = React222.forwardRef(ViewGridIcon);
var ViewGridIcon_default = ForwardRef222;

// node_modules/@heroicons/react/solid/esm/ViewListIcon.js
var React223 = __toESM(require_react(), 1);
function ViewListIcon(props, svgRef) {
  return React223.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React223.createElement("path", {
    fillRule: "evenodd",
    d: "M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z",
    clipRule: "evenodd"
  }));
}
var ForwardRef223 = React223.forwardRef(ViewListIcon);
var ViewListIcon_default = ForwardRef223;

// node_modules/@heroicons/react/solid/esm/VolumeOffIcon.js
var React224 = __toESM(require_react(), 1);
function VolumeOffIcon(props, svgRef) {
  return React224.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React224.createElement("path", {
    fillRule: "evenodd",
    d: "M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM12.293 7.293a1 1 0 011.414 0L15 8.586l1.293-1.293a1 1 0 111.414 1.414L16.414 10l1.293 1.293a1 1 0 01-1.414 1.414L15 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L13.586 10l-1.293-1.293a1 1 0 010-1.414z",
    clipRule: "evenodd"
  }));
}
var ForwardRef224 = React224.forwardRef(VolumeOffIcon);
var VolumeOffIcon_default = ForwardRef224;

// node_modules/@heroicons/react/solid/esm/VolumeUpIcon.js
var React225 = __toESM(require_react(), 1);
function VolumeUpIcon(props, svgRef) {
  return React225.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React225.createElement("path", {
    fillRule: "evenodd",
    d: "M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM14.657 2.929a1 1 0 011.414 0A9.972 9.972 0 0119 10a9.972 9.972 0 01-2.929 7.071 1 1 0 01-1.414-1.414A7.971 7.971 0 0017 10c0-2.21-.894-4.208-2.343-5.657a1 1 0 010-1.414zm-2.829 2.828a1 1 0 011.415 0A5.983 5.983 0 0115 10a5.984 5.984 0 01-1.757 4.243 1 1 0 01-1.415-1.415A3.984 3.984 0 0013 10a3.983 3.983 0 00-1.172-2.828 1 1 0 010-1.415z",
    clipRule: "evenodd"
  }));
}
var ForwardRef225 = React225.forwardRef(VolumeUpIcon);
var VolumeUpIcon_default = ForwardRef225;

// node_modules/@heroicons/react/solid/esm/WifiIcon.js
var React226 = __toESM(require_react(), 1);
function WifiIcon(props, svgRef) {
  return React226.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React226.createElement("path", {
    fillRule: "evenodd",
    d: "M17.778 8.222c-4.296-4.296-11.26-4.296-15.556 0A1 1 0 01.808 6.808c5.076-5.077 13.308-5.077 18.384 0a1 1 0 01-1.414 1.414zM14.95 11.05a7 7 0 00-9.9 0 1 1 0 01-1.414-1.414 9 9 0 0112.728 0 1 1 0 01-1.414 1.414zM12.12 13.88a3 3 0 00-4.242 0 1 1 0 01-1.415-1.415 5 5 0 017.072 0 1 1 0 01-1.415 1.415zM9 16a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z",
    clipRule: "evenodd"
  }));
}
var ForwardRef226 = React226.forwardRef(WifiIcon);
var WifiIcon_default = ForwardRef226;

// node_modules/@heroicons/react/solid/esm/XCircleIcon.js
var React227 = __toESM(require_react(), 1);
function XCircleIcon(props, svgRef) {
  return React227.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React227.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",
    clipRule: "evenodd"
  }));
}
var ForwardRef227 = React227.forwardRef(XCircleIcon);
var XCircleIcon_default = ForwardRef227;

// node_modules/@heroicons/react/solid/esm/XIcon.js
var React228 = __toESM(require_react(), 1);
function XIcon(props, svgRef) {
  return React228.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React228.createElement("path", {
    fillRule: "evenodd",
    d: "M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",
    clipRule: "evenodd"
  }));
}
var ForwardRef228 = React228.forwardRef(XIcon);
var XIcon_default = ForwardRef228;

// node_modules/@heroicons/react/solid/esm/ZoomInIcon.js
var React229 = __toESM(require_react(), 1);
function ZoomInIcon(props, svgRef) {
  return React229.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React229.createElement("path", {
    d: "M5 8a1 1 0 011-1h1V6a1 1 0 012 0v1h1a1 1 0 110 2H9v1a1 1 0 11-2 0V9H6a1 1 0 01-1-1z"
  }), React229.createElement("path", {
    fillRule: "evenodd",
    d: "M2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8zm6-4a4 4 0 100 8 4 4 0 000-8z",
    clipRule: "evenodd"
  }));
}
var ForwardRef229 = React229.forwardRef(ZoomInIcon);
var ZoomInIcon_default = ForwardRef229;

// node_modules/@heroicons/react/solid/esm/ZoomOutIcon.js
var React230 = __toESM(require_react(), 1);
function ZoomOutIcon(props, svgRef) {
  return React230.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    ref: svgRef
  }, props), React230.createElement("path", {
    fillRule: "evenodd",
    d: "M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z",
    clipRule: "evenodd"
  }), React230.createElement("path", {
    fillRule: "evenodd",
    d: "M5 8a1 1 0 011-1h4a1 1 0 110 2H6a1 1 0 01-1-1z",
    clipRule: "evenodd"
  }));
}
var ForwardRef230 = React230.forwardRef(ZoomOutIcon);
var ZoomOutIcon_default = ForwardRef230;
export {
  AcademicCapIcon_default as AcademicCapIcon,
  AdjustmentsIcon_default as AdjustmentsIcon,
  AnnotationIcon_default as AnnotationIcon,
  ArchiveIcon_default as ArchiveIcon,
  ArrowCircleDownIcon_default as ArrowCircleDownIcon,
  ArrowCircleLeftIcon_default as ArrowCircleLeftIcon,
  ArrowCircleRightIcon_default as ArrowCircleRightIcon,
  ArrowCircleUpIcon_default as ArrowCircleUpIcon,
  ArrowDownIcon_default as ArrowDownIcon,
  ArrowLeftIcon_default as ArrowLeftIcon,
  ArrowNarrowDownIcon_default as ArrowNarrowDownIcon,
  ArrowNarrowLeftIcon_default as ArrowNarrowLeftIcon,
  ArrowNarrowRightIcon_default as ArrowNarrowRightIcon,
  ArrowNarrowUpIcon_default as ArrowNarrowUpIcon,
  ArrowRightIcon_default as ArrowRightIcon,
  ArrowSmDownIcon_default as ArrowSmDownIcon,
  ArrowSmLeftIcon_default as ArrowSmLeftIcon,
  ArrowSmRightIcon_default as ArrowSmRightIcon,
  ArrowSmUpIcon_default as ArrowSmUpIcon,
  ArrowUpIcon_default as ArrowUpIcon,
  ArrowsExpandIcon_default as ArrowsExpandIcon,
  AtSymbolIcon_default as AtSymbolIcon,
  BackspaceIcon_default as BackspaceIcon,
  BadgeCheckIcon_default as BadgeCheckIcon,
  BanIcon_default as BanIcon,
  BeakerIcon_default as BeakerIcon,
  BellIcon_default as BellIcon,
  BookOpenIcon_default as BookOpenIcon,
  BookmarkAltIcon_default as BookmarkAltIcon,
  BookmarkIcon_default as BookmarkIcon,
  BriefcaseIcon_default as BriefcaseIcon,
  CakeIcon_default as CakeIcon,
  CalculatorIcon_default as CalculatorIcon,
  CalendarIcon_default as CalendarIcon,
  CameraIcon_default as CameraIcon,
  CashIcon_default as CashIcon,
  ChartBarIcon_default as ChartBarIcon,
  ChartPieIcon_default as ChartPieIcon,
  ChartSquareBarIcon_default as ChartSquareBarIcon,
  ChatAlt2Icon_default as ChatAlt2Icon,
  ChatAltIcon_default as ChatAltIcon,
  ChatIcon_default as ChatIcon,
  CheckCircleIcon_default as CheckCircleIcon,
  CheckIcon_default as CheckIcon,
  ChevronDoubleDownIcon_default as ChevronDoubleDownIcon,
  ChevronDoubleLeftIcon_default as ChevronDoubleLeftIcon,
  ChevronDoubleRightIcon_default as ChevronDoubleRightIcon,
  ChevronDoubleUpIcon_default as ChevronDoubleUpIcon,
  ChevronDownIcon_default as ChevronDownIcon,
  ChevronLeftIcon_default as ChevronLeftIcon,
  ChevronRightIcon_default as ChevronRightIcon,
  ChevronUpIcon_default as ChevronUpIcon,
  ChipIcon_default as ChipIcon,
  ClipboardCheckIcon_default as ClipboardCheckIcon,
  ClipboardCopyIcon_default as ClipboardCopyIcon,
  ClipboardIcon_default as ClipboardIcon,
  ClipboardListIcon_default as ClipboardListIcon,
  ClockIcon_default as ClockIcon,
  CloudDownloadIcon_default as CloudDownloadIcon,
  CloudIcon_default as CloudIcon,
  CloudUploadIcon_default as CloudUploadIcon,
  CodeIcon_default as CodeIcon,
  CogIcon_default as CogIcon,
  CollectionIcon_default as CollectionIcon,
  ColorSwatchIcon_default as ColorSwatchIcon,
  CreditCardIcon_default as CreditCardIcon,
  CubeIcon_default as CubeIcon,
  CubeTransparentIcon_default as CubeTransparentIcon,
  CurrencyBangladeshiIcon_default as CurrencyBangladeshiIcon,
  CurrencyDollarIcon_default as CurrencyDollarIcon,
  CurrencyEuroIcon_default as CurrencyEuroIcon,
  CurrencyPoundIcon_default as CurrencyPoundIcon,
  CurrencyRupeeIcon_default as CurrencyRupeeIcon,
  CurrencyYenIcon_default as CurrencyYenIcon,
  CursorClickIcon_default as CursorClickIcon,
  DatabaseIcon_default as DatabaseIcon,
  DesktopComputerIcon_default as DesktopComputerIcon,
  DeviceMobileIcon_default as DeviceMobileIcon,
  DeviceTabletIcon_default as DeviceTabletIcon,
  DocumentAddIcon_default as DocumentAddIcon,
  DocumentDownloadIcon_default as DocumentDownloadIcon,
  DocumentDuplicateIcon_default as DocumentDuplicateIcon,
  DocumentIcon_default as DocumentIcon,
  DocumentRemoveIcon_default as DocumentRemoveIcon,
  DocumentReportIcon_default as DocumentReportIcon,
  DocumentSearchIcon_default as DocumentSearchIcon,
  DocumentTextIcon_default as DocumentTextIcon,
  DotsCircleHorizontalIcon_default as DotsCircleHorizontalIcon,
  DotsHorizontalIcon_default as DotsHorizontalIcon,
  DotsVerticalIcon_default as DotsVerticalIcon,
  DownloadIcon_default as DownloadIcon,
  DuplicateIcon_default as DuplicateIcon,
  EmojiHappyIcon_default as EmojiHappyIcon,
  EmojiSadIcon_default as EmojiSadIcon,
  ExclamationCircleIcon_default as ExclamationCircleIcon,
  ExclamationIcon_default as ExclamationIcon,
  ExternalLinkIcon_default as ExternalLinkIcon,
  EyeIcon_default as EyeIcon,
  EyeOffIcon_default as EyeOffIcon,
  FastForwardIcon_default as FastForwardIcon,
  FilmIcon_default as FilmIcon,
  FilterIcon_default as FilterIcon,
  FingerPrintIcon_default as FingerPrintIcon,
  FireIcon_default as FireIcon,
  FlagIcon_default as FlagIcon,
  FolderAddIcon_default as FolderAddIcon,
  FolderDownloadIcon_default as FolderDownloadIcon,
  FolderIcon_default as FolderIcon,
  FolderOpenIcon_default as FolderOpenIcon,
  FolderRemoveIcon_default as FolderRemoveIcon,
  GiftIcon_default as GiftIcon,
  GlobeAltIcon_default as GlobeAltIcon,
  GlobeIcon_default as GlobeIcon,
  HandIcon_default as HandIcon,
  HashtagIcon_default as HashtagIcon,
  HeartIcon_default as HeartIcon,
  HomeIcon_default as HomeIcon,
  IdentificationIcon_default as IdentificationIcon,
  InboxIcon_default as InboxIcon,
  InboxInIcon_default as InboxInIcon,
  InformationCircleIcon_default as InformationCircleIcon,
  KeyIcon_default as KeyIcon,
  LibraryIcon_default as LibraryIcon,
  LightBulbIcon_default as LightBulbIcon,
  LightningBoltIcon_default as LightningBoltIcon,
  LinkIcon_default as LinkIcon,
  LocationMarkerIcon_default as LocationMarkerIcon,
  LockClosedIcon_default as LockClosedIcon,
  LockOpenIcon_default as LockOpenIcon,
  LoginIcon_default as LoginIcon,
  LogoutIcon_default as LogoutIcon,
  MailIcon_default as MailIcon,
  MailOpenIcon_default as MailOpenIcon,
  MapIcon_default as MapIcon,
  MenuAlt1Icon_default as MenuAlt1Icon,
  MenuAlt2Icon_default as MenuAlt2Icon,
  MenuAlt3Icon_default as MenuAlt3Icon,
  MenuAlt4Icon_default as MenuAlt4Icon,
  MenuIcon_default as MenuIcon,
  MicrophoneIcon_default as MicrophoneIcon,
  MinusCircleIcon_default as MinusCircleIcon,
  MinusIcon_default as MinusIcon,
  MinusSmIcon_default as MinusSmIcon,
  MoonIcon_default as MoonIcon,
  MusicNoteIcon_default as MusicNoteIcon,
  NewspaperIcon_default as NewspaperIcon,
  OfficeBuildingIcon_default as OfficeBuildingIcon,
  PaperAirplaneIcon_default as PaperAirplaneIcon,
  PaperClipIcon_default as PaperClipIcon,
  PauseIcon_default as PauseIcon,
  PencilAltIcon_default as PencilAltIcon,
  PencilIcon_default as PencilIcon,
  PhoneIcon_default as PhoneIcon,
  PhoneIncomingIcon_default as PhoneIncomingIcon,
  PhoneMissedCallIcon_default as PhoneMissedCallIcon,
  PhoneOutgoingIcon_default as PhoneOutgoingIcon,
  PhotographIcon_default as PhotographIcon,
  PlayIcon_default as PlayIcon,
  PlusCircleIcon_default as PlusCircleIcon,
  PlusIcon_default as PlusIcon,
  PlusSmIcon_default as PlusSmIcon,
  PresentationChartBarIcon_default as PresentationChartBarIcon,
  PresentationChartLineIcon_default as PresentationChartLineIcon,
  PrinterIcon_default as PrinterIcon,
  PuzzleIcon_default as PuzzleIcon,
  QrcodeIcon_default as QrcodeIcon,
  QuestionMarkCircleIcon_default as QuestionMarkCircleIcon,
  ReceiptRefundIcon_default as ReceiptRefundIcon,
  ReceiptTaxIcon_default as ReceiptTaxIcon,
  RefreshIcon_default as RefreshIcon,
  ReplyIcon_default as ReplyIcon,
  RewindIcon_default as RewindIcon,
  RssIcon_default as RssIcon,
  SaveAsIcon_default as SaveAsIcon,
  SaveIcon_default as SaveIcon,
  ScaleIcon_default as ScaleIcon,
  ScissorsIcon_default as ScissorsIcon,
  SearchCircleIcon_default as SearchCircleIcon,
  SearchIcon_default as SearchIcon,
  SelectorIcon_default as SelectorIcon,
  ServerIcon_default as ServerIcon,
  ShareIcon_default as ShareIcon,
  ShieldCheckIcon_default as ShieldCheckIcon,
  ShieldExclamationIcon_default as ShieldExclamationIcon,
  ShoppingBagIcon_default as ShoppingBagIcon,
  ShoppingCartIcon_default as ShoppingCartIcon,
  SortAscendingIcon_default as SortAscendingIcon,
  SortDescendingIcon_default as SortDescendingIcon,
  SparklesIcon_default as SparklesIcon,
  SpeakerphoneIcon_default as SpeakerphoneIcon,
  StarIcon_default as StarIcon,
  StatusOfflineIcon_default as StatusOfflineIcon,
  StatusOnlineIcon_default as StatusOnlineIcon,
  StopIcon_default as StopIcon,
  SunIcon_default as SunIcon,
  SupportIcon_default as SupportIcon,
  SwitchHorizontalIcon_default as SwitchHorizontalIcon,
  SwitchVerticalIcon_default as SwitchVerticalIcon,
  TableIcon_default as TableIcon,
  TagIcon_default as TagIcon,
  TemplateIcon_default as TemplateIcon,
  TerminalIcon_default as TerminalIcon,
  ThumbDownIcon_default as ThumbDownIcon,
  ThumbUpIcon_default as ThumbUpIcon,
  TicketIcon_default as TicketIcon,
  TranslateIcon_default as TranslateIcon,
  TrashIcon_default as TrashIcon,
  TrendingDownIcon_default as TrendingDownIcon,
  TrendingUpIcon_default as TrendingUpIcon,
  TruckIcon_default as TruckIcon,
  UploadIcon_default as UploadIcon,
  UserAddIcon_default as UserAddIcon,
  UserCircleIcon_default as UserCircleIcon,
  UserGroupIcon_default as UserGroupIcon,
  UserIcon_default as UserIcon,
  UserRemoveIcon_default as UserRemoveIcon,
  UsersIcon_default as UsersIcon,
  VariableIcon_default as VariableIcon,
  VideoCameraIcon_default as VideoCameraIcon,
  ViewBoardsIcon_default as ViewBoardsIcon,
  ViewGridAddIcon_default as ViewGridAddIcon,
  ViewGridIcon_default as ViewGridIcon,
  ViewListIcon_default as ViewListIcon,
  VolumeOffIcon_default as VolumeOffIcon,
  VolumeUpIcon_default as VolumeUpIcon,
  WifiIcon_default as WifiIcon,
  XCircleIcon_default as XCircleIcon,
  XIcon_default as XIcon,
  ZoomInIcon_default as ZoomInIcon,
  ZoomOutIcon_default as ZoomOutIcon
};
//# sourceMappingURL=@heroicons_react_solid.js.map
