{"version": 3, "sources": ["../../../../../node_modules/codemirror/addon/hint/show-hint.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/LICENSE\n\n// declare global: DOMRect\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n\n  var HINT_ELEMENT_CLASS        = \"CodeMirror-hint\";\n  var ACTIVE_HINT_ELEMENT_CLASS = \"CodeMirror-hint-active\";\n\n  // This is the old interface, kept around for now to stay\n  // backwards-compatible.\n  CodeMirror.showHint = function(cm, getHints, options) {\n    if (!getHints) return cm.showHint(options);\n    if (options && options.async) getHints.async = true;\n    var newOpts = {hint: getHints};\n    if (options) for (var prop in options) newOpts[prop] = options[prop];\n    return cm.showHint(newOpts);\n  };\n\n  CodeMirror.defineExtension(\"showHint\", function(options) {\n    options = parseOptions(this, this.getCursor(\"start\"), options);\n    var selections = this.listSelections()\n    if (selections.length > 1) return;\n    // By default, don't allow completion when something is selected.\n    // A hint function can have a `supportsSelection` property to\n    // indicate that it can handle selections.\n    if (this.somethingSelected()) {\n      if (!options.hint.supportsSelection) return;\n      // Don't try with cross-line selections\n      for (var i = 0; i < selections.length; i++)\n        if (selections[i].head.line != selections[i].anchor.line) return;\n    }\n\n    if (this.state.completionActive) this.state.completionActive.close();\n    var completion = this.state.completionActive = new Completion(this, options);\n    if (!completion.options.hint) return;\n\n    CodeMirror.signal(this, \"startCompletion\", this);\n    completion.update(true);\n  });\n\n  CodeMirror.defineExtension(\"closeHint\", function() {\n    if (this.state.completionActive) this.state.completionActive.close()\n  })\n\n  function Completion(cm, options) {\n    this.cm = cm;\n    this.options = options;\n    this.widget = null;\n    this.debounce = 0;\n    this.tick = 0;\n    this.startPos = this.cm.getCursor(\"start\");\n    this.startLen = this.cm.getLine(this.startPos.line).length - this.cm.getSelection().length;\n\n    if (this.options.updateOnCursorActivity) {\n      var self = this;\n      cm.on(\"cursorActivity\", this.activityFunc = function() { self.cursorActivity(); });\n    }\n  }\n\n  var requestAnimationFrame = window.requestAnimationFrame || function(fn) {\n    return setTimeout(fn, 1000/60);\n  };\n  var cancelAnimationFrame = window.cancelAnimationFrame || clearTimeout;\n\n  Completion.prototype = {\n    close: function() {\n      if (!this.active()) return;\n      this.cm.state.completionActive = null;\n      this.tick = null;\n      if (this.options.updateOnCursorActivity) {\n        this.cm.off(\"cursorActivity\", this.activityFunc);\n      }\n\n      if (this.widget && this.data) CodeMirror.signal(this.data, \"close\");\n      if (this.widget) this.widget.close();\n      CodeMirror.signal(this.cm, \"endCompletion\", this.cm);\n    },\n\n    active: function() {\n      return this.cm.state.completionActive == this;\n    },\n\n    pick: function(data, i) {\n      var completion = data.list[i], self = this;\n      this.cm.operation(function() {\n        if (completion.hint)\n          completion.hint(self.cm, data, completion);\n        else\n          self.cm.replaceRange(getText(completion), completion.from || data.from,\n                               completion.to || data.to, \"complete\");\n        CodeMirror.signal(data, \"pick\", completion);\n        self.cm.scrollIntoView();\n      });\n      if (this.options.closeOnPick) {\n        this.close();\n      }\n    },\n\n    cursorActivity: function() {\n      if (this.debounce) {\n        cancelAnimationFrame(this.debounce);\n        this.debounce = 0;\n      }\n\n      var identStart = this.startPos;\n      if(this.data) {\n        identStart = this.data.from;\n      }\n\n      var pos = this.cm.getCursor(), line = this.cm.getLine(pos.line);\n      if (pos.line != this.startPos.line || line.length - pos.ch != this.startLen - this.startPos.ch ||\n          pos.ch < identStart.ch || this.cm.somethingSelected() ||\n          (!pos.ch || this.options.closeCharacters.test(line.charAt(pos.ch - 1)))) {\n        this.close();\n      } else {\n        var self = this;\n        this.debounce = requestAnimationFrame(function() {self.update();});\n        if (this.widget) this.widget.disable();\n      }\n    },\n\n    update: function(first) {\n      if (this.tick == null) return\n      var self = this, myTick = ++this.tick\n      fetchHints(this.options.hint, this.cm, this.options, function(data) {\n        if (self.tick == myTick) self.finishUpdate(data, first)\n      })\n    },\n\n    finishUpdate: function(data, first) {\n      if (this.data) CodeMirror.signal(this.data, \"update\");\n\n      var picked = (this.widget && this.widget.picked) || (first && this.options.completeSingle);\n      if (this.widget) this.widget.close();\n\n      this.data = data;\n\n      if (data && data.list.length) {\n        if (picked && data.list.length == 1) {\n          this.pick(data, 0);\n        } else {\n          this.widget = new Widget(this, data);\n          CodeMirror.signal(data, \"shown\");\n        }\n      }\n    }\n  };\n\n  function parseOptions(cm, pos, options) {\n    var editor = cm.options.hintOptions;\n    var out = {};\n    for (var prop in defaultOptions) out[prop] = defaultOptions[prop];\n    if (editor) for (var prop in editor)\n      if (editor[prop] !== undefined) out[prop] = editor[prop];\n    if (options) for (var prop in options)\n      if (options[prop] !== undefined) out[prop] = options[prop];\n    if (out.hint.resolve) out.hint = out.hint.resolve(cm, pos)\n    return out;\n  }\n\n  function getText(completion) {\n    if (typeof completion == \"string\") return completion;\n    else return completion.text;\n  }\n\n  function buildKeyMap(completion, handle) {\n    var baseMap = {\n      Up: function() {handle.moveFocus(-1);},\n      Down: function() {handle.moveFocus(1);},\n      PageUp: function() {handle.moveFocus(-handle.menuSize() + 1, true);},\n      PageDown: function() {handle.moveFocus(handle.menuSize() - 1, true);},\n      Home: function() {handle.setFocus(0);},\n      End: function() {handle.setFocus(handle.length - 1);},\n      Enter: handle.pick,\n      Tab: handle.pick,\n      Esc: handle.close\n    };\n\n    var mac = /Mac/.test(navigator.platform);\n\n    if (mac) {\n      baseMap[\"Ctrl-P\"] = function() {handle.moveFocus(-1);};\n      baseMap[\"Ctrl-N\"] = function() {handle.moveFocus(1);};\n    }\n\n    var custom = completion.options.customKeys;\n    var ourMap = custom ? {} : baseMap;\n    function addBinding(key, val) {\n      var bound;\n      if (typeof val != \"string\")\n        bound = function(cm) { return val(cm, handle); };\n      // This mechanism is deprecated\n      else if (baseMap.hasOwnProperty(val))\n        bound = baseMap[val];\n      else\n        bound = val;\n      ourMap[key] = bound;\n    }\n    if (custom)\n      for (var key in custom) if (custom.hasOwnProperty(key))\n        addBinding(key, custom[key]);\n    var extra = completion.options.extraKeys;\n    if (extra)\n      for (var key in extra) if (extra.hasOwnProperty(key))\n        addBinding(key, extra[key]);\n    return ourMap;\n  }\n\n  function getHintElement(hintsElement, el) {\n    while (el && el != hintsElement) {\n      if (el.nodeName.toUpperCase() === \"LI\" && el.parentNode == hintsElement) return el;\n      el = el.parentNode;\n    }\n  }\n\n  function Widget(completion, data) {\n    this.id = \"cm-complete-\" + Math.floor(Math.random(1e6))\n    this.completion = completion;\n    this.data = data;\n    this.picked = false;\n    var widget = this, cm = completion.cm;\n    var ownerDocument = cm.getInputField().ownerDocument;\n    var parentWindow = ownerDocument.defaultView || ownerDocument.parentWindow;\n\n    var hints = this.hints = ownerDocument.createElement(\"ul\");\n    hints.setAttribute(\"role\", \"listbox\")\n    hints.setAttribute(\"aria-expanded\", \"true\")\n    hints.id = this.id\n    var theme = completion.cm.options.theme;\n    hints.className = \"CodeMirror-hints \" + theme;\n    this.selectedHint = data.selectedHint || 0;\n\n    var completions = data.list;\n    for (var i = 0; i < completions.length; ++i) {\n      var elt = hints.appendChild(ownerDocument.createElement(\"li\")), cur = completions[i];\n      var className = HINT_ELEMENT_CLASS + (i != this.selectedHint ? \"\" : \" \" + ACTIVE_HINT_ELEMENT_CLASS);\n      if (cur.className != null) className = cur.className + \" \" + className;\n      elt.className = className;\n      if (i == this.selectedHint) elt.setAttribute(\"aria-selected\", \"true\")\n      elt.id = this.id + \"-\" + i\n      elt.setAttribute(\"role\", \"option\")\n      if (cur.render) cur.render(elt, data, cur);\n      else elt.appendChild(ownerDocument.createTextNode(cur.displayText || getText(cur)));\n      elt.hintId = i;\n    }\n\n    var container = completion.options.container || ownerDocument.body;\n    var pos = cm.cursorCoords(completion.options.alignWithWord ? data.from : null);\n    var left = pos.left, top = pos.bottom, below = true;\n    var offsetLeft = 0, offsetTop = 0;\n    if (container !== ownerDocument.body) {\n      // We offset the cursor position because left and top are relative to the offsetParent's top left corner.\n      var isContainerPositioned = ['absolute', 'relative', 'fixed'].indexOf(parentWindow.getComputedStyle(container).position) !== -1;\n      var offsetParent = isContainerPositioned ? container : container.offsetParent;\n      var offsetParentPosition = offsetParent.getBoundingClientRect();\n      var bodyPosition = ownerDocument.body.getBoundingClientRect();\n      offsetLeft = (offsetParentPosition.left - bodyPosition.left - offsetParent.scrollLeft);\n      offsetTop = (offsetParentPosition.top - bodyPosition.top - offsetParent.scrollTop);\n    }\n    hints.style.left = (left - offsetLeft) + \"px\";\n    hints.style.top = (top - offsetTop) + \"px\";\n\n    // If we're at the edge of the screen, then we want the menu to appear on the left of the cursor.\n    var winW = parentWindow.innerWidth || Math.max(ownerDocument.body.offsetWidth, ownerDocument.documentElement.offsetWidth);\n    var winH = parentWindow.innerHeight || Math.max(ownerDocument.body.offsetHeight, ownerDocument.documentElement.offsetHeight);\n    container.appendChild(hints);\n    cm.getInputField().setAttribute(\"aria-autocomplete\", \"list\")\n    cm.getInputField().setAttribute(\"aria-owns\", this.id)\n    cm.getInputField().setAttribute(\"aria-activedescendant\", this.id + \"-\" + this.selectedHint)\n\n    var box = completion.options.moveOnOverlap ? hints.getBoundingClientRect() : new DOMRect();\n    var scrolls = completion.options.paddingForScrollbar ? hints.scrollHeight > hints.clientHeight + 1 : false;\n\n    // Compute in the timeout to avoid reflow on init\n    var startScroll;\n    setTimeout(function() { startScroll = cm.getScrollInfo(); });\n\n    var overlapY = box.bottom - winH;\n    if (overlapY > 0) {\n      var height = box.bottom - box.top, curTop = pos.top - (pos.bottom - box.top);\n      if (curTop - height > 0) { // Fits above cursor\n        hints.style.top = (top = pos.top - height - offsetTop) + \"px\";\n        below = false;\n      } else if (height > winH) {\n        hints.style.height = (winH - 5) + \"px\";\n        hints.style.top = (top = pos.bottom - box.top - offsetTop) + \"px\";\n        var cursor = cm.getCursor();\n        if (data.from.ch != cursor.ch) {\n          pos = cm.cursorCoords(cursor);\n          hints.style.left = (left = pos.left - offsetLeft) + \"px\";\n          box = hints.getBoundingClientRect();\n        }\n      }\n    }\n    var overlapX = box.right - winW;\n    if (scrolls) overlapX += cm.display.nativeBarWidth;\n    if (overlapX > 0) {\n      if (box.right - box.left > winW) {\n        hints.style.width = (winW - 5) + \"px\";\n        overlapX -= (box.right - box.left) - winW;\n      }\n      hints.style.left = (left = pos.left - overlapX - offsetLeft) + \"px\";\n    }\n    if (scrolls) for (var node = hints.firstChild; node; node = node.nextSibling)\n      node.style.paddingRight = cm.display.nativeBarWidth + \"px\"\n\n    cm.addKeyMap(this.keyMap = buildKeyMap(completion, {\n      moveFocus: function(n, avoidWrap) { widget.changeActive(widget.selectedHint + n, avoidWrap); },\n      setFocus: function(n) { widget.changeActive(n); },\n      menuSize: function() { return widget.screenAmount(); },\n      length: completions.length,\n      close: function() { completion.close(); },\n      pick: function() { widget.pick(); },\n      data: data\n    }));\n\n    if (completion.options.closeOnUnfocus) {\n      var closingOnBlur;\n      cm.on(\"blur\", this.onBlur = function() { closingOnBlur = setTimeout(function() { completion.close(); }, 100); });\n      cm.on(\"focus\", this.onFocus = function() { clearTimeout(closingOnBlur); });\n    }\n\n    cm.on(\"scroll\", this.onScroll = function() {\n      var curScroll = cm.getScrollInfo(), editor = cm.getWrapperElement().getBoundingClientRect();\n      if (!startScroll) startScroll = cm.getScrollInfo();\n      var newTop = top + startScroll.top - curScroll.top;\n      var point = newTop - (parentWindow.pageYOffset || (ownerDocument.documentElement || ownerDocument.body).scrollTop);\n      if (!below) point += hints.offsetHeight;\n      if (point <= editor.top || point >= editor.bottom) return completion.close();\n      hints.style.top = newTop + \"px\";\n      hints.style.left = (left + startScroll.left - curScroll.left) + \"px\";\n    });\n\n    CodeMirror.on(hints, \"dblclick\", function(e) {\n      var t = getHintElement(hints, e.target || e.srcElement);\n      if (t && t.hintId != null) {widget.changeActive(t.hintId); widget.pick();}\n    });\n\n    CodeMirror.on(hints, \"click\", function(e) {\n      var t = getHintElement(hints, e.target || e.srcElement);\n      if (t && t.hintId != null) {\n        widget.changeActive(t.hintId);\n        if (completion.options.completeOnSingleClick) widget.pick();\n      }\n    });\n\n    CodeMirror.on(hints, \"mousedown\", function() {\n      setTimeout(function(){cm.focus();}, 20);\n    });\n\n    // The first hint doesn't need to be scrolled to on init\n    var selectedHintRange = this.getSelectedHintRange();\n    if (selectedHintRange.from !== 0 || selectedHintRange.to !== 0) {\n      this.scrollToActive();\n    }\n\n    CodeMirror.signal(data, \"select\", completions[this.selectedHint], hints.childNodes[this.selectedHint]);\n    return true;\n  }\n\n  Widget.prototype = {\n    close: function() {\n      if (this.completion.widget != this) return;\n      this.completion.widget = null;\n      if (this.hints.parentNode) this.hints.parentNode.removeChild(this.hints);\n      this.completion.cm.removeKeyMap(this.keyMap);\n      var input = this.completion.cm.getInputField()\n      input.removeAttribute(\"aria-activedescendant\")\n      input.removeAttribute(\"aria-owns\")\n\n      var cm = this.completion.cm;\n      if (this.completion.options.closeOnUnfocus) {\n        cm.off(\"blur\", this.onBlur);\n        cm.off(\"focus\", this.onFocus);\n      }\n      cm.off(\"scroll\", this.onScroll);\n    },\n\n    disable: function() {\n      this.completion.cm.removeKeyMap(this.keyMap);\n      var widget = this;\n      this.keyMap = {Enter: function() { widget.picked = true; }};\n      this.completion.cm.addKeyMap(this.keyMap);\n    },\n\n    pick: function() {\n      this.completion.pick(this.data, this.selectedHint);\n    },\n\n    changeActive: function(i, avoidWrap) {\n      if (i >= this.data.list.length)\n        i = avoidWrap ? this.data.list.length - 1 : 0;\n      else if (i < 0)\n        i = avoidWrap ? 0  : this.data.list.length - 1;\n      if (this.selectedHint == i) return;\n      var node = this.hints.childNodes[this.selectedHint];\n      if (node) {\n        node.className = node.className.replace(\" \" + ACTIVE_HINT_ELEMENT_CLASS, \"\");\n        node.removeAttribute(\"aria-selected\")\n      }\n      node = this.hints.childNodes[this.selectedHint = i];\n      node.className += \" \" + ACTIVE_HINT_ELEMENT_CLASS;\n      node.setAttribute(\"aria-selected\", \"true\")\n      this.completion.cm.getInputField().setAttribute(\"aria-activedescendant\", node.id)\n      this.scrollToActive()\n      CodeMirror.signal(this.data, \"select\", this.data.list[this.selectedHint], node);\n    },\n\n    scrollToActive: function() {\n      var selectedHintRange = this.getSelectedHintRange();\n      var node1 = this.hints.childNodes[selectedHintRange.from];\n      var node2 = this.hints.childNodes[selectedHintRange.to];\n      var firstNode = this.hints.firstChild;\n      if (node1.offsetTop < this.hints.scrollTop)\n        this.hints.scrollTop = node1.offsetTop - firstNode.offsetTop;\n      else if (node2.offsetTop + node2.offsetHeight > this.hints.scrollTop + this.hints.clientHeight)\n        this.hints.scrollTop = node2.offsetTop + node2.offsetHeight - this.hints.clientHeight + firstNode.offsetTop;\n    },\n\n    screenAmount: function() {\n      return Math.floor(this.hints.clientHeight / this.hints.firstChild.offsetHeight) || 1;\n    },\n\n    getSelectedHintRange: function() {\n      var margin = this.completion.options.scrollMargin || 0;\n      return {\n        from: Math.max(0, this.selectedHint - margin),\n        to: Math.min(this.data.list.length - 1, this.selectedHint + margin),\n      };\n    }\n  };\n\n  function applicableHelpers(cm, helpers) {\n    if (!cm.somethingSelected()) return helpers\n    var result = []\n    for (var i = 0; i < helpers.length; i++)\n      if (helpers[i].supportsSelection) result.push(helpers[i])\n    return result\n  }\n\n  function fetchHints(hint, cm, options, callback) {\n    if (hint.async) {\n      hint(cm, callback, options)\n    } else {\n      var result = hint(cm, options)\n      if (result && result.then) result.then(callback)\n      else callback(result)\n    }\n  }\n\n  function resolveAutoHints(cm, pos) {\n    var helpers = cm.getHelpers(pos, \"hint\"), words\n    if (helpers.length) {\n      var resolved = function(cm, callback, options) {\n        var app = applicableHelpers(cm, helpers);\n        function run(i) {\n          if (i == app.length) return callback(null)\n          fetchHints(app[i], cm, options, function(result) {\n            if (result && result.list.length > 0) callback(result)\n            else run(i + 1)\n          })\n        }\n        run(0)\n      }\n      resolved.async = true\n      resolved.supportsSelection = true\n      return resolved\n    } else if (words = cm.getHelper(cm.getCursor(), \"hintWords\")) {\n      return function(cm) { return CodeMirror.hint.fromList(cm, {words: words}) }\n    } else if (CodeMirror.hint.anyword) {\n      return function(cm, options) { return CodeMirror.hint.anyword(cm, options) }\n    } else {\n      return function() {}\n    }\n  }\n\n  CodeMirror.registerHelper(\"hint\", \"auto\", {\n    resolve: resolveAutoHints\n  });\n\n  CodeMirror.registerHelper(\"hint\", \"fromList\", function(cm, options) {\n    var cur = cm.getCursor(), token = cm.getTokenAt(cur)\n    var term, from = CodeMirror.Pos(cur.line, token.start), to = cur\n    if (token.start < cur.ch && /\\w/.test(token.string.charAt(cur.ch - token.start - 1))) {\n      term = token.string.substr(0, cur.ch - token.start)\n    } else {\n      term = \"\"\n      from = cur\n    }\n    var found = [];\n    for (var i = 0; i < options.words.length; i++) {\n      var word = options.words[i];\n      if (word.slice(0, term.length) == term)\n        found.push(word);\n    }\n\n    if (found.length) return {list: found, from: from, to: to};\n  });\n\n  CodeMirror.commands.autocomplete = CodeMirror.showHint;\n\n  var defaultOptions = {\n    hint: CodeMirror.hint.auto,\n    completeSingle: true,\n    alignWithWord: true,\n    closeCharacters: /[\\s()\\[\\]{};:>,]/,\n    closeOnPick: true,\n    closeOnUnfocus: true,\n    updateOnCursorActivity: true,\n    completeOnSingleClick: true,\n    container: null,\n    customKeys: null,\n    extraKeys: null,\n    paddingForScrollbar: true,\n    moveOnOverlap: true,\n  };\n\n  CodeMirror.defineOption(\"hintOptions\", null);\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,GAAC,SAASA,GAAK;AAEXA,MAAIC,GAA+B,CAAA;EAKtC,GAAE,SAASC,GAAY;AAGtB,QAAIC,IAA4B,mBAC5BC,IAA4B;AAIhCF,MAAW,WAAW,SAASG,GAAIC,GAAUC,GAAS;AACpD,UAAI,CAACD;AAAU,eAAOD,EAAG,SAASE,CAAO;AACrCA,WAAWA,EAAQ,UAAOD,EAAS,QAAQ;AAC/C,UAAIE,IAAU,EAAC,MAAMF,EAAQ;AAC7B,UAAIC;AAAS,iBAASE,KAAQF;AAASC,YAAQC,CAAI,IAAIF,EAAQE,CAAI;AACnE,aAAOJ,EAAG,SAASG,CAAO;IAC9B,GAEEN,EAAW,gBAAgB,YAAY,SAASK,GAAS;AACvDA,UAAUG,EAAa,MAAM,KAAK,UAAU,OAAO,GAAGH,CAAO;AAC7D,UAAII,IAAa,KAAK,eAAgB;AACtC,UAAI,EAAAA,EAAW,SAAS,IAIxB;AAAA,YAAI,KAAK,kBAAA,GAAqB;AAC5B,cAAI,CAACJ,EAAQ,KAAK;AAAmB;AAErC,mBAAS,IAAI,GAAG,IAAII,EAAW,QAAQ;AACrC,gBAAIA,EAAW,CAAC,EAAE,KAAK,QAAQA,EAAW,CAAC,EAAE,OAAO;AAAM;QAAA;AAG1D,aAAK,MAAM,oBAAkB,KAAK,MAAM,iBAAiB,MAAA;AAC7D,YAAIC,IAAa,KAAK,MAAM,mBAAmB,IAAIC,EAAW,MAAMN,CAAO;AACtEK,UAAW,QAAQ,SAExBV,EAAW,OAAO,MAAM,mBAAmB,IAAI,GAC/CU,EAAW,OAAO,IAAI;MAAA;IAC1B,CAAG,GAEDV,EAAW,gBAAgB,aAAa,WAAW;AAC7C,WAAK,MAAM,oBAAkB,KAAK,MAAM,iBAAiB,MAAO;IACxE,CAAG;AAED,aAASW,EAAWR,GAAIE,GAAS;AAS/B,UARA,KAAK,KAAKF,GACV,KAAK,UAAUE,GACf,KAAK,SAAS,MACd,KAAK,WAAW,GAChB,KAAK,OAAO,GACZ,KAAK,WAAW,KAAK,GAAG,UAAU,OAAO,GACzC,KAAK,WAAW,KAAK,GAAG,QAAQ,KAAK,SAAS,IAAI,EAAE,SAAS,KAAK,GAAG,aAAY,EAAG,QAEhF,KAAK,QAAQ,wBAAwB;AACvC,YAAIO,IAAO;AACXT,UAAG,GAAG,kBAAkB,KAAK,eAAe,WAAW;AAAES,YAAK,eAAA;QAAiB,CAAE;MAAA;IAEpF;AAbQC,MAAAF,GAAA,YAAA;AAeT,QAAIG,IAAwB,OAAO,yBAAyB,SAASC,GAAI;AACvE,aAAO,WAAWA,GAAI,MAAK,EAAE;IACjC,GACMC,IAAuB,OAAO,wBAAwB;AAE1DL,MAAW,YAAY;MACrB,OAAO,WAAW;AACX,aAAK,OAAM,MAChB,KAAK,GAAG,MAAM,mBAAmB,MACjC,KAAK,OAAO,MACR,KAAK,QAAQ,0BACf,KAAK,GAAG,IAAI,kBAAkB,KAAK,YAAY,GAG7C,KAAK,UAAU,KAAK,QAAMX,EAAW,OAAO,KAAK,MAAM,OAAO,GAC9D,KAAK,UAAQ,KAAK,OAAO,MAAK,GAClCA,EAAW,OAAO,KAAK,IAAI,iBAAiB,KAAK,EAAE;MACpD;MAED,QAAQ,WAAW;AACjB,eAAO,KAAK,GAAG,MAAM,oBAAoB;MAC1C;MAED,MAAM,SAASiB,GAAMC,GAAG;AACtB,YAAIR,IAAaO,EAAK,KAAKC,CAAC,GAAGN,IAAO;AACtC,aAAK,GAAG,UAAU,WAAW;AACvBF,YAAW,OACbA,EAAW,KAAKE,EAAK,IAAIK,GAAMP,CAAU,IAEzCE,EAAK,GAAG;YAAaO,EAAQT,CAAU;YAAGA,EAAW,QAAQO,EAAK;YAC7CP,EAAW,MAAMO,EAAK;YAAI;UAAU,GAC3DjB,EAAW,OAAOiB,GAAM,QAAQP,CAAU,GAC1CE,EAAK,GAAG,eAAA;QAChB,CAAO,GACG,KAAK,QAAQ,eACf,KAAK,MAAK;MAEb;MAED,gBAAgB,WAAW;AACrB,aAAK,aACPI,EAAqB,KAAK,QAAQ,GAClC,KAAK,WAAW;AAGlB,YAAII,IAAa,KAAK;AACnB,aAAK,SACNA,IAAa,KAAK,KAAK;AAGzB,YAAIC,IAAM,KAAK,GAAG,UAAS,GAAIC,IAAO,KAAK,GAAG,QAAQD,EAAI,IAAI;AAC9D,YAAIA,EAAI,QAAQ,KAAK,SAAS,QAAQC,EAAK,SAASD,EAAI,MAAM,KAAK,WAAW,KAAK,SAAS,MACxFA,EAAI,KAAKD,EAAW,MAAM,KAAK,GAAG,kBAAmB,KACpD,CAACC,EAAI,MAAM,KAAK,QAAQ,gBAAgB,KAAKC,EAAK,OAAOD,EAAI,KAAK,CAAC,CAAC;AACvE,eAAK,MAAK;aACL;AACL,cAAIT,IAAO;AACX,eAAK,WAAWE,EAAsB,WAAW;AAACF,cAAK,OAAM;UAAG,CAAC,GAC7D,KAAK,UAAQ,KAAK,OAAO,QAAO;QAAA;MAEvC;MAED,QAAQ,SAASW,GAAO;AACtB,YAAI,KAAK,QAAQ,MACjB;AAAA,cAAIX,IAAO,MAAMY,IAAS,EAAE,KAAK;AACjCC,YAAW,KAAK,QAAQ,MAAM,KAAK,IAAI,KAAK,SAAS,SAASR,GAAM;AAC9DL,cAAK,QAAQY,KAAQZ,EAAK,aAAaK,GAAMM,CAAK;UAC9D,CAAO;QAAA;MACF;MAED,cAAc,SAASN,GAAMM,GAAO;AAC9B,aAAK,QAAMvB,EAAW,OAAO,KAAK,MAAM,QAAQ;AAEpD,YAAI0B,IAAU,KAAK,UAAU,KAAK,OAAO,UAAYH,KAAS,KAAK,QAAQ;AACvE,aAAK,UAAQ,KAAK,OAAO,MAAK,GAElC,KAAK,OAAON,GAERA,KAAQA,EAAK,KAAK,WAChBS,KAAUT,EAAK,KAAK,UAAU,IAChC,KAAK,KAAKA,GAAM,CAAC,KAEjB,KAAK,SAAS,IAAIU,EAAO,MAAMV,CAAI,GACnCjB,EAAW,OAAOiB,GAAM,OAAO;MAGpC;IACL;AAEE,aAAST,EAAaL,GAAIkB,GAAKhB,GAAS;AACtC,UAAIuB,IAASzB,EAAG,QAAQ,aACpB0B,IAAM,CAAA;AACV,eAAStB,KAAQuB;AAAgBD,UAAItB,CAAI,IAAIuB,EAAevB,CAAI;AAChE,UAAIqB;AAAQ,iBAASrB,KAAQqB;AACvBA,YAAOrB,CAAI,MAAM,WAAWsB,EAAItB,CAAI,IAAIqB,EAAOrB,CAAI;AACzD,UAAIF;AAAS,iBAASE,KAAQF;AACxBA,YAAQE,CAAI,MAAM,WAAWsB,EAAItB,CAAI,IAAIF,EAAQE,CAAI;AAC3D,aAAIsB,EAAI,KAAK,YAASA,EAAI,OAAOA,EAAI,KAAK,QAAQ1B,GAAIkB,CAAG,IAClDQ;IACR;AAVQhB,MAAAL,GAAA,cAAA;AAYT,aAASW,EAAQT,GAAY;AAC3B,aAAI,OAAOA,KAAc,WAAiBA,IAC9BA,EAAW;IACxB;AAHQG,MAAAM,GAAA,SAAA;AAKT,aAASY,GAAYrB,GAAYsB,GAAQ;AACvC,UAAIC,IAAU;QACZ,IAAI,WAAW;AAACD,YAAO,UAAU,EAAE;QAAE;QACrC,MAAM,WAAW;AAACA,YAAO,UAAU,CAAC;QAAE;QACtC,QAAQ,WAAW;AAACA,YAAO,UAAU,CAACA,EAAO,SAAA,IAAa,GAAG,IAAI;QAAE;QACnE,UAAU,WAAW;AAACA,YAAO,UAAUA,EAAO,SAAA,IAAa,GAAG,IAAI;QAAE;QACpE,MAAM,WAAW;AAACA,YAAO,SAAS,CAAC;QAAE;QACrC,KAAK,WAAW;AAACA,YAAO,SAASA,EAAO,SAAS,CAAC;QAAE;QACpD,OAAOA,EAAO;QACd,KAAKA,EAAO;QACZ,KAAKA,EAAO;MAClB,GAEQE,IAAM,MAAM,KAAK,UAAU,QAAQ;AAEnCA,YACFD,EAAQ,QAAQ,IAAI,WAAW;AAACD,UAAO,UAAU,EAAE;MAAE,GACrDC,EAAQ,QAAQ,IAAI,WAAW;AAACD,UAAO,UAAU,CAAC;MAAE;AAGtD,UAAIG,IAASzB,EAAW,QAAQ,YAC5B0B,IAASD,IAAS,CAAE,IAAGF;AAC3B,eAASI,EAAWC,GAAKC,GAAK;AAC5B,YAAIC;AACA,eAAOD,KAAO,WAChBC,IAAQ3B,EAAA,SAASV,GAAI;AAAE,iBAAOoC,EAAIpC,GAAI6B,CAAM;QAAA,GAApC,OAAA,IAEDC,EAAQ,eAAeM,CAAG,IACjCC,IAAQP,EAAQM,CAAG,IAEnBC,IAAQD,GACVH,EAAOE,CAAG,IAAIE;MACf;AACD,UAXS3B,EAAAwB,GAAA,YAAA,GAWLF;AACF,iBAASG,KAAOH;AAAYA,YAAO,eAAeG,CAAG,KACnDD,EAAWC,GAAKH,EAAOG,CAAG,CAAC;AAC/B,UAAIG,IAAQ/B,EAAW,QAAQ;AAC/B,UAAI+B;AACF,iBAASH,KAAOG;AAAWA,YAAM,eAAeH,CAAG,KACjDD,EAAWC,GAAKG,EAAMH,CAAG,CAAC;AAC9B,aAAOF;IACR;AAzCQvB,MAAAkB,IAAA,aAAA;AA2CT,aAASW,EAAeC,GAAcC,GAAI;AACxC,aAAOA,KAAMA,KAAMD,KAAc;AAC/B,YAAIC,EAAG,SAAS,YAAA,MAAkB,QAAQA,EAAG,cAAcD;AAAc,iBAAOC;AAChFA,YAAKA,EAAG;MAAA;IAEX;AALQ/B,MAAA6B,GAAA,gBAAA;AAOT,aAASf,EAAOjB,GAAYO,GAAM;AAChC,WAAK,KAAK,iBAAiB,KAAK,MAAM,KAAK,OAAO,GAAG,CAAC,GACtD,KAAK,aAAaP,GAClB,KAAK,OAAOO,GACZ,KAAK,SAAS;AACd,UAAI4B,IAAS,MAAM1C,IAAKO,EAAW,IAC/BoC,IAAgB3C,EAAG,cAAa,EAAG,eACnC4C,IAAeD,EAAc,eAAeA,EAAc,cAE1DE,IAAQ,KAAK,QAAQF,EAAc,cAAc,IAAI;AACzDE,QAAM,aAAa,QAAQ,SAAS,GACpCA,EAAM,aAAa,iBAAiB,MAAM,GAC1CA,EAAM,KAAK,KAAK;AAChB,UAAIC,IAAQvC,EAAW,GAAG,QAAQ;AAClCsC,QAAM,YAAY,sBAAsBC,GACxC,KAAK,eAAehC,EAAK,gBAAgB;AAGzC,eADIiC,IAAcjC,EAAK,MACdC,IAAI,GAAGA,IAAIgC,EAAY,QAAQ,EAAEhC,GAAG;AAC3C,YAAIiC,IAAMH,EAAM,YAAYF,EAAc,cAAc,IAAI,CAAC,GAAGM,IAAMF,EAAYhC,CAAC,GAC/EmC,IAAYpD,KAAsBiB,KAAK,KAAK,eAAe,KAAK,MAAMhB;AACtEkD,UAAI,aAAa,SAAMC,IAAYD,EAAI,YAAY,MAAMC,IAC7DF,EAAI,YAAYE,GACZnC,KAAK,KAAK,gBAAciC,EAAI,aAAa,iBAAiB,MAAM,GACpEA,EAAI,KAAK,KAAK,KAAK,MAAMjC,GACzBiC,EAAI,aAAa,QAAQ,QAAQ,GAC7BC,EAAI,SAAQA,EAAI,OAAOD,GAAKlC,GAAMmC,CAAG,IACpCD,EAAI,YAAYL,EAAc,eAAeM,EAAI,eAAejC,EAAQiC,CAAG,CAAC,CAAC,GAClFD,EAAI,SAASjC;MAAA;AAGf,UAAIoC,IAAY5C,EAAW,QAAQ,aAAaoC,EAAc,MAC1DzB,IAAMlB,EAAG,aAAaO,EAAW,QAAQ,gBAAgBO,EAAK,OAAO,IAAI,GACzEsC,IAAOlC,EAAI,MAAMmC,IAAMnC,EAAI,QAAQoC,IAAQ,MAC3CC,IAAa,GAAGC,IAAY;AAChC,UAAIL,MAAcR,EAAc,MAAM;AAEpC,YAAIc,KAAwB,CAAC,YAAY,YAAY,OAAO,EAAE,QAAQb,EAAa,iBAAiBO,CAAS,EAAE,QAAQ,MAAM,IACzHO,IAAeD,KAAwBN,IAAYA,EAAU,cAC7DQ,IAAuBD,EAAa,sBAAA,GACpCE,IAAejB,EAAc,KAAK,sBAAqB;AAC3DY,YAAcI,EAAqB,OAAOC,EAAa,OAAOF,EAAa,YAC3EF,IAAaG,EAAqB,MAAMC,EAAa,MAAMF,EAAa;MAAA;AAE1Eb,QAAM,MAAM,OAAQO,IAAOG,IAAc,MACzCV,EAAM,MAAM,MAAOQ,IAAMG,IAAa;AAGtC,UAAIK,IAAOjB,EAAa,cAAc,KAAK,IAAID,EAAc,KAAK,aAAaA,EAAc,gBAAgB,WAAW,GACpHmB,IAAOlB,EAAa,eAAe,KAAK,IAAID,EAAc,KAAK,cAAcA,EAAc,gBAAgB,YAAY;AAC3HQ,QAAU,YAAYN,CAAK,GAC3B7C,EAAG,cAAe,EAAC,aAAa,qBAAqB,MAAM,GAC3DA,EAAG,cAAa,EAAG,aAAa,aAAa,KAAK,EAAE,GACpDA,EAAG,cAAA,EAAgB,aAAa,yBAAyB,KAAK,KAAK,MAAM,KAAK,YAAY;AAE1F,UAAI+D,IAAMxD,EAAW,QAAQ,gBAAgBsC,EAAM,sBAAqB,IAAK,IAAI,QAAA,GAC7EmB,IAAUzD,EAAW,QAAQ,sBAAsBsC,EAAM,eAAeA,EAAM,eAAe,IAAI,OAGjGoB;AACJ,iBAAW,WAAW;AAAEA,YAAcjE,EAAG,cAAa;MAAG,CAAE;AAE3D,UAAIkE,KAAWH,EAAI,SAASD;AAC5B,UAAII,KAAW,GAAG;AAChB,YAAIC,IAASJ,EAAI,SAASA,EAAI,KAAKK,KAASlD,EAAI,OAAOA,EAAI,SAAS6C,EAAI;AACxE,YAAIK,KAASD,IAAS;AACpBtB,YAAM,MAAM,OAAOQ,IAAMnC,EAAI,MAAMiD,IAASX,KAAa,MACzDF,IAAQ;iBACCa,IAASL,GAAM;AACxBjB,YAAM,MAAM,SAAUiB,IAAO,IAAK,MAClCjB,EAAM,MAAM,OAAOQ,IAAMnC,EAAI,SAAS6C,EAAI,MAAMP,KAAa;AAC7D,cAAIa,IAASrE,EAAG,UAAA;AACZc,YAAK,KAAK,MAAMuD,EAAO,OACzBnD,IAAMlB,EAAG,aAAaqE,CAAM,GAC5BxB,EAAM,MAAM,QAAQO,IAAOlC,EAAI,OAAOqC,KAAc,MACpDQ,IAAMlB,EAAM,sBAAA;;;AAIlB,UAAIyB,IAAWP,EAAI,QAAQF;AAS3B,UARIG,MAASM,KAAYtE,EAAG,QAAQ,iBAChCsE,IAAW,MACTP,EAAI,QAAQA,EAAI,OAAOF,MACzBhB,EAAM,MAAM,QAASgB,IAAO,IAAK,MACjCS,KAAaP,EAAI,QAAQA,EAAI,OAAQF,IAEvChB,EAAM,MAAM,QAAQO,IAAOlC,EAAI,OAAOoD,IAAWf,KAAc,OAE7DS;AAAS,iBAASO,IAAO1B,EAAM,YAAY0B,GAAMA,IAAOA,EAAK;AAC/DA,YAAK,MAAM,eAAevE,EAAG,QAAQ,iBAAiB;AAYxD,UAVAA,EAAG,UAAU,KAAK,SAAS4B,GAAYrB,GAAY;QACjD,WAAW,SAASiE,GAAGC,GAAW;AAAE/B,YAAO,aAAaA,EAAO,eAAe8B,GAAGC,CAAS;QAAI;QAC9F,UAAU,SAASD,GAAG;AAAE9B,YAAO,aAAa8B,CAAC;QAAI;QACjD,UAAU,WAAW;AAAE,iBAAO9B,EAAO,aAAc;QAAG;QACtD,QAAQK,EAAY;QACpB,OAAO,WAAW;AAAExC,YAAW,MAAO;QAAG;QACzC,MAAM,WAAW;AAAEmC,YAAO,KAAM;QAAG;QACnC,MAAM5B;MACP,CAAA,CAAC,GAEEP,EAAW,QAAQ,gBAAgB;AACrC,YAAImE;AACJ1E,UAAG,GAAG,QAAQ,KAAK,SAAS,WAAW;AAAE0E,cAAgB,WAAW,WAAW;AAAEnE,cAAW,MAAO;UAAG,GAAE,GAAG;QAAE,CAAE,GAC/GP,EAAG,GAAG,SAAS,KAAK,UAAU,WAAW;AAAE,uBAAa0E,CAAa;QAAE,CAAE;MAAA;AAG3E1E,QAAG,GAAG,UAAU,KAAK,WAAW,WAAW;AACzC,YAAI2E,IAAY3E,EAAG,cAAe,GAAEyB,IAASzB,EAAG,kBAAA,EAAoB,sBAAA;AAC/DiE,cAAaA,IAAcjE,EAAG,cAAa;AAChD,YAAI4E,IAASvB,IAAMY,EAAY,MAAMU,EAAU,KAC3CE,IAAQD,KAAUhC,EAAa,gBAAgBD,EAAc,mBAAmBA,EAAc,MAAM;AAExG,YADKW,MAAOuB,KAAShC,EAAM,eACvBgC,KAASpD,EAAO,OAAOoD,KAASpD,EAAO;AAAQ,iBAAOlB,EAAW,MAAA;AACrEsC,UAAM,MAAM,MAAM+B,IAAS,MAC3B/B,EAAM,MAAM,OAAQO,IAAOa,EAAY,OAAOU,EAAU,OAAQ;MACtE,CAAK,GAED9E,EAAW,GAAGgD,GAAO,YAAY,SAASiC,GAAG;AAC3C,YAAIC,IAAIxC,EAAeM,GAAOiC,EAAE,UAAUA,EAAE,UAAU;AAClDC,aAAKA,EAAE,UAAU,SAAOrC,EAAO,aAAaqC,EAAE,MAAM,GAAGrC,EAAO,KAAM;MAC9E,CAAK,GAED7C,EAAW,GAAGgD,GAAO,SAAS,SAASiC,GAAG;AACxC,YAAIC,IAAIxC,EAAeM,GAAOiC,EAAE,UAAUA,EAAE,UAAU;AAClDC,aAAKA,EAAE,UAAU,SACnBrC,EAAO,aAAaqC,EAAE,MAAM,GACxBxE,EAAW,QAAQ,yBAAuBmC,EAAO,KAAI;MAEjE,CAAK,GAED7C,EAAW,GAAGgD,GAAO,aAAa,WAAW;AAC3C,mBAAW,WAAU;AAAC7C,YAAG,MAAK;QAAG,GAAG,EAAE;MAC5C,CAAK;AAGD,UAAIgF,IAAoB,KAAK,qBAAA;AAC7B,cAAIA,EAAkB,SAAS,KAAKA,EAAkB,OAAO,MAC3D,KAAK,eAAc,GAGrBnF,EAAW,OAAOiB,GAAM,UAAUiC,EAAY,KAAK,YAAY,GAAGF,EAAM,WAAW,KAAK,YAAY,CAAC,GAC9F;IACR;AA/IQnC,MAAAc,GAAA,QAAA,GAiJTA,EAAO,YAAY;MACjB,OAAO,WAAW;AAChB,YAAI,KAAK,WAAW,UAAU,MAC9B;AAAA,eAAK,WAAW,SAAS,MACrB,KAAK,MAAM,cAAY,KAAK,MAAM,WAAW,YAAY,KAAK,KAAK,GACvE,KAAK,WAAW,GAAG,aAAa,KAAK,MAAM;AAC3C,cAAIyD,IAAQ,KAAK,WAAW,GAAG,cAAe;AAC9CA,YAAM,gBAAgB,uBAAuB,GAC7CA,EAAM,gBAAgB,WAAW;AAEjC,cAAIjF,IAAK,KAAK,WAAW;AACrB,eAAK,WAAW,QAAQ,mBAC1BA,EAAG,IAAI,QAAQ,KAAK,MAAM,GAC1BA,EAAG,IAAI,SAAS,KAAK,OAAO,IAE9BA,EAAG,IAAI,UAAU,KAAK,QAAQ;QAAA;MAC/B;MAED,SAAS,WAAW;AAClB,aAAK,WAAW,GAAG,aAAa,KAAK,MAAM;AAC3C,YAAI0C,IAAS;AACb,aAAK,SAAS,EAAC,OAAO,WAAW;AAAEA,YAAO,SAAS;QAAK,EAAE,GAC1D,KAAK,WAAW,GAAG,UAAU,KAAK,MAAM;MACzC;MAED,MAAM,WAAW;AACf,aAAK,WAAW,KAAK,KAAK,MAAM,KAAK,YAAY;MAClD;MAED,cAAc,SAAS3B,GAAG0D,GAAW;AAKnC,YAJI1D,KAAK,KAAK,KAAK,KAAK,SACtBA,IAAI0D,IAAY,KAAK,KAAK,KAAK,SAAS,IAAI,IACrC1D,IAAI,MACXA,IAAI0D,IAAY,IAAK,KAAK,KAAK,KAAK,SAAS,IAC3C,KAAK,gBAAgB1D,GACzB;AAAA,cAAIwD,IAAO,KAAK,MAAM,WAAW,KAAK,YAAY;AAC9CA,gBACFA,EAAK,YAAYA,EAAK,UAAU,QAAQ,MAAMxE,GAA2B,EAAE,GAC3EwE,EAAK,gBAAgB,eAAe,IAEtCA,IAAO,KAAK,MAAM,WAAW,KAAK,eAAexD,CAAC,GAClDwD,EAAK,aAAa,MAAMxE,GACxBwE,EAAK,aAAa,iBAAiB,MAAM,GACzC,KAAK,WAAW,GAAG,cAAa,EAAG,aAAa,yBAAyBA,EAAK,EAAE,GAChF,KAAK,eAAgB,GACrB1E,EAAW,OAAO,KAAK,MAAM,UAAU,KAAK,KAAK,KAAK,KAAK,YAAY,GAAG0E,CAAI;QAAA;MAC/E;MAED,gBAAgB,WAAW;AACzB,YAAIS,IAAoB,KAAK,qBAAA,GACzBE,IAAQ,KAAK,MAAM,WAAWF,EAAkB,IAAI,GACpDG,IAAQ,KAAK,MAAM,WAAWH,EAAkB,EAAE,GAClDI,IAAY,KAAK,MAAM;AACvBF,UAAM,YAAY,KAAK,MAAM,YAC/B,KAAK,MAAM,YAAYA,EAAM,YAAYE,EAAU,YAC5CD,EAAM,YAAYA,EAAM,eAAe,KAAK,MAAM,YAAY,KAAK,MAAM,iBAChF,KAAK,MAAM,YAAYA,EAAM,YAAYA,EAAM,eAAe,KAAK,MAAM,eAAeC,EAAU;MACrG;MAED,cAAc,WAAW;AACvB,eAAO,KAAK,MAAM,KAAK,MAAM,eAAe,KAAK,MAAM,WAAW,YAAY,KAAK;MACpF;MAED,sBAAsB,WAAW;AAC/B,YAAIC,IAAS,KAAK,WAAW,QAAQ,gBAAgB;AACrD,eAAO;UACL,MAAM,KAAK,IAAI,GAAG,KAAK,eAAeA,CAAM;UAC5C,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,SAAS,GAAG,KAAK,eAAeA,CAAM;QAC1E;MACK;IACL;AAEE,aAASC,GAAkBtF,GAAIuF,GAAS;AACtC,UAAI,CAACvF,EAAG,kBAAmB;AAAE,eAAOuF;AAEpC,eADIC,IAAS,CAAE,GACNzE,IAAI,GAAGA,IAAIwE,EAAQ,QAAQxE;AAC9BwE,UAAQxE,CAAC,EAAE,qBAAmByE,EAAO,KAAKD,EAAQxE,CAAC,CAAC;AAC1D,aAAOyE;IACR;AANQ9E,MAAA4E,IAAA,mBAAA;AAQT,aAAShE,EAAWmE,GAAMzF,GAAIE,GAASwF,GAAU;AAC/C,UAAID,EAAK;AACPA,UAAKzF,GAAI0F,GAAUxF,CAAO;WACrB;AACL,YAAIsF,IAASC,EAAKzF,GAAIE,CAAO;AACzBsF,aAAUA,EAAO,OAAMA,EAAO,KAAKE,CAAQ,IAC1CA,EAASF,CAAM;MAAA;IAEvB;AARQ9E,MAAAY,GAAA,YAAA;AAUT,aAASqE,GAAiB3F,GAAIkB,GAAK;AACjC,UAAIqE,IAAUvF,EAAG,WAAWkB,GAAK,MAAM,GAAG0E;AAC1C,UAAIL,EAAQ,QAAQ;AAClB,YAAIM,IAAWnF,EAAA,SAASV,GAAI0F,GAAUxF,GAAS;AAC7C,cAAI4F,IAAMR,GAAkBtF,GAAIuF,CAAO;AACvC,mBAASQ,EAAIhF,GAAG;AACd,gBAAIA,KAAK+E,EAAI;AAAQ,qBAAOJ,EAAS,IAAI;AACzCpE,cAAWwE,EAAI/E,CAAC,GAAGf,GAAIE,GAAS,SAASsF,GAAQ;AAC3CA,mBAAUA,EAAO,KAAK,SAAS,IAAGE,EAASF,CAAM,IAChDO,EAAIhF,IAAI,CAAC;YAC1B,CAAW;UACF;AANQL,YAAAqF,GAAA,KAAA,GAOTA,EAAI,CAAC;QACN,GAVc,UAAA;AAWf,eAAAF,EAAS,QAAQ,MACjBA,EAAS,oBAAoB,MACtBA;MAAA;AACF,gBAAID,IAAQ5F,EAAG,UAAUA,EAAG,UAAS,GAAI,WAAW,KAClD,SAASA,GAAI;AAAE,iBAAOH,EAAW,KAAK,SAASG,GAAI,EAAC,OAAO4F,EAAK,CAAC;QAAG,IAClE/F,EAAW,KAAK,UAClB,SAASG,GAAIE,GAAS;AAAE,iBAAOL,EAAW,KAAK,QAAQG,GAAIE,CAAO;QAAG,IAErE,WAAW;QAAE;IAEvB;AAxBQQ,MAAAiF,IAAA,kBAAA,GA0BT9F,EAAW,eAAe,QAAQ,QAAQ;MACxC,SAAS8F;IACb,CAAG,GAED9F,EAAW,eAAe,QAAQ,YAAY,SAASG,GAAIE,GAAS;AAClE,UAAI+C,IAAMjD,EAAG,UAAS,GAAIgG,IAAQhG,EAAG,WAAWiD,CAAG,GAC/CgD,GAAMC,IAAOrG,EAAW,IAAIoD,EAAI,MAAM+C,EAAM,KAAK,GAAGG,IAAKlD;AACzD+C,QAAM,QAAQ/C,EAAI,MAAM,KAAK,KAAK+C,EAAM,OAAO,OAAO/C,EAAI,KAAK+C,EAAM,QAAQ,CAAC,CAAC,IACjFC,IAAOD,EAAM,OAAO,OAAO,GAAG/C,EAAI,KAAK+C,EAAM,KAAK,KAElDC,IAAO,IACPC,IAAOjD;AAGT,eADImD,IAAQ,CAAA,GACHrF,IAAI,GAAGA,IAAIb,EAAQ,MAAM,QAAQa,KAAK;AAC7C,YAAIsF,IAAOnG,EAAQ,MAAMa,CAAC;AACtBsF,UAAK,MAAM,GAAGJ,EAAK,MAAM,KAAKA,KAChCG,EAAM,KAAKC,CAAI;MAAA;AAGnB,UAAID,EAAM;AAAQ,eAAO,EAAC,MAAMA,GAAO,MAAMF,GAAM,IAAIC,EAAE;IAC7D,CAAG,GAEDtG,EAAW,SAAS,eAAeA,EAAW;AAE9C,QAAI8B,IAAiB;MACnB,MAAM9B,EAAW,KAAK;MACtB,gBAAgB;MAChB,eAAe;MACf,iBAAiB;MACjB,aAAa;MACb,gBAAgB;MAChB,wBAAwB;MACxB,uBAAuB;MACvB,WAAW;MACX,YAAY;MACZ,WAAW;MACX,qBAAqB;MACrB,eAAe;IACnB;AAEEA,MAAW,aAAa,eAAe,IAAI;EAC7C,CAAC;;;;;;;;", "names": ["mod", "require$$0", "CodeMirror", "HINT_ELEMENT_CLASS", "ACTIVE_HINT_ELEMENT_CLASS", "cm", "getHints", "options", "newOpts", "prop", "parseOptions", "selections", "completion", "Completion", "self", "__name", "requestAnimationFrame", "fn", "cancelAnimationFrame", "data", "i", "getText", "identStart", "pos", "line", "first", "myTick", "fetchHints", "picked", "Widget", "editor", "out", "defaultOptions", "buildKeyMap", "handle", "baseMap", "mac", "custom", "ourMap", "addBinding", "key", "val", "bound", "extra", "getHintElement", "hintsElement", "el", "widget", "ownerDocument", "parentWindow", "hints", "theme", "completions", "elt", "cur", "className", "container", "left", "top", "below", "offsetLeft", "offsetTop", "isContainerPositioned", "offsetParent", "offsetParentPosition", "bodyPosition", "winW", "winH", "box", "scrolls", "startScroll", "overlapY", "height", "curTop", "cursor", "overlapX", "node", "n", "avoidWrap", "closingOnBlur", "curScroll", "newTop", "point", "e", "t", "selected<PERSON><PERSON><PERSON><PERSON><PERSON>", "input", "node1", "node2", "firstNode", "margin", "applicableHelpers", "helpers", "result", "hint", "callback", "resolveAutoHints", "words", "resolved", "app", "run", "token", "term", "from", "to", "found", "word"]}