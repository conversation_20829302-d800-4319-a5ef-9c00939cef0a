{"version": 3, "sources": ["../../../../../@graphiql/react/dist/dialog.es.js"], "sourcesContent": ["var s = Object.defineProperty;\nvar n = (e, o) => s(e, \"name\", { value: o, configurable: !0 });\nimport { g } from \"./codemirror.es2.js\";\nimport { r as l } from \"./dialog.es2.js\";\nfunction c(e, o) {\n  for (var a = 0; a < o.length; a++) {\n    const r = o[a];\n    if (typeof r != \"string\" && !Array.isArray(r)) {\n      for (const t in r)\n        if (t !== \"default\" && !(t in e)) {\n          const i = Object.getOwnPropertyDescriptor(r, t);\n          i && Object.defineProperty(e, t, i.get ? i : {\n            enumerable: !0,\n            get: () => r[t]\n          });\n        }\n    }\n  }\n  return Object.freeze(Object.defineProperty(e, Symbol.toStringTag, { value: \"Module\" }));\n}\nn(c, \"_mergeNamespaces\");\nvar f = l();\nconst p = /* @__PURE__ */ g(f), y = /* @__PURE__ */ c({\n  __proto__: null,\n  default: p\n}, [f]);\nexport {\n  y as d\n};\n//# sourceMappingURL=dialog.es.js.map\n"], "mappings": ";;;;;;;;;AAAA,IAAI,IAAI,OAAO;AACf,IAAI,IAAI,CAAC,GAAG,MAAM,EAAE,GAAG,QAAQ,EAAE,OAAO,GAAG,cAAc,KAAG,CAAC;AAG7D,SAAS,EAAE,GAAG,GAAG;AACf,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,UAAM,IAAI,EAAE,CAAC;AACb,QAAI,OAAO,KAAK,YAAY,CAAC,MAAM,QAAQ,CAAC,GAAG;AAC7C,iBAAW,KAAK;AACd,YAAI,MAAM,aAAa,EAAE,KAAK,IAAI;AAChC,gBAAM,IAAI,OAAO,yBAAyB,GAAG,CAAC;AAC9C,eAAK,OAAO,eAAe,GAAG,GAAG,EAAE,MAAM,IAAI;AAAA,YAC3C,YAAY;AAAA,YACZ,KAAK,MAAM,EAAE,CAAC;AAAA,UAChB,CAAC;AAAA,QACH;AAAA,IACJ;AAAA,EACF;AACA,SAAO,OAAO,OAAO,OAAO,eAAe,GAAG,OAAO,aAAa,EAAE,OAAO,SAAS,CAAC,CAAC;AACxF;AACA,EAAE,GAAG,kBAAkB;AACvB,IAAI,IAAI,EAAE;AACV,IAAM,IAAoB,GAAE,CAAC;AAA7B,IAAgC,IAAoB,EAAE;AAAA,EACpD,WAAW;AAAA,EACX,SAAS;AACX,GAAG,CAAC,CAAC,CAAC;", "names": []}