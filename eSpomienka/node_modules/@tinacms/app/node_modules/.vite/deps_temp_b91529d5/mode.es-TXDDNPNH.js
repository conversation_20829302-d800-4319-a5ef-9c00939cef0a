import {
  r
} from "./chunk-A4B2V6D5.js";
import {
  d
} from "./chunk-WIXYZC3K.js";
import "./chunk-MEKKV4OY.js";
import {
  LexRules,
  ParseRules,
  isIgnored,
  onlineParser
} from "./chunk-4LQY6QSN.js";
import "./chunk-HD22INE4.js";
import "./chunk-AUZ3RYOM.js";

// node_modules/@graphiql/react/dist/mode.es.js
var a = Object.defineProperty;
var t = (e, r2) => a(e, "name", { value: r2, configurable: true });
var m = t((e) => {
  const r2 = onlineParser({
    eatWhitespace: (o) => o.eatWhile(isIgnored),
    lexRules: LexRules,
    parseRules: ParseRules,
    editorConfig: { tabSize: e.tabSize }
  });
  return {
    config: e,
    startState: r2.startState,
    token: r2.token,
    indent: r,
    electricInput: /^\s*[})\]]/,
    fold: "brace",
    lineComment: "#",
    closeBrackets: {
      pairs: '()[]{}""',
      explode: "()[]{}"
    }
  };
}, "graphqlModeFactory");
d.defineMode("graphql", m);
//# sourceMappingURL=mode.es-TXDDNPNH.js.map
