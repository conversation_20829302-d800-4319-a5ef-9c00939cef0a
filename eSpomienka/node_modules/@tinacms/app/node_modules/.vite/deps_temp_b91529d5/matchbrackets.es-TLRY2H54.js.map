{"version": 3, "sources": ["../../../../../@graphiql/react/dist/matchbrackets.es.js"], "sourcesContent": ["var f = Object.defineProperty;\nvar s = (e, o) => f(e, \"name\", { value: o, configurable: !0 });\nimport { g as i } from \"./codemirror.es2.js\";\nimport { r as m } from \"./matchbrackets.es2.js\";\nfunction p(e, o) {\n  for (var a = 0; a < o.length; a++) {\n    const t = o[a];\n    if (typeof t != \"string\" && !Array.isArray(t)) {\n      for (const r in t)\n        if (r !== \"default\" && !(r in e)) {\n          const c = Object.getOwnPropertyDescriptor(t, r);\n          c && Object.defineProperty(e, r, c.get ? c : {\n            enumerable: !0,\n            get: () => t[r]\n          });\n        }\n    }\n  }\n  return Object.freeze(Object.defineProperty(e, Symbol.toStringTag, { value: \"Module\" }));\n}\ns(p, \"_mergeNamespaces\");\nvar n = m();\nconst u = /* @__PURE__ */ i(n), y = /* @__PURE__ */ p({\n  __proto__: null,\n  default: u\n}, [n]);\nexport {\n  y as m\n};\n//# sourceMappingURL=matchbrackets.es.js.map\n"], "mappings": ";;;;;;;;;AAAA,IAAI,IAAI,OAAO;AACf,IAAI,IAAI,CAAC,GAAG,MAAM,EAAE,GAAG,QAAQ,EAAE,OAAO,GAAG,cAAc,KAAG,CAAC;AAG7D,SAAS,EAAE,GAAG,GAAG;AACf,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,UAAM,IAAI,EAAE,CAAC;AACb,QAAI,OAAO,KAAK,YAAY,CAAC,MAAM,QAAQ,CAAC,GAAG;AAC7C,iBAAW,KAAK;AACd,YAAI,MAAM,aAAa,EAAE,KAAK,IAAI;AAChC,gBAAM,IAAI,OAAO,yBAAyB,GAAG,CAAC;AAC9C,eAAK,OAAO,eAAe,GAAG,GAAG,EAAE,MAAM,IAAI;AAAA,YAC3C,YAAY;AAAA,YACZ,KAAK,MAAM,EAAE,CAAC;AAAA,UAChB,CAAC;AAAA,QACH;AAAA,IACJ;AAAA,EACF;AACA,SAAO,OAAO,OAAO,OAAO,eAAe,GAAG,OAAO,aAAa,EAAE,OAAO,SAAS,CAAC,CAAC;AACxF;AACA,EAAE,GAAG,kBAAkB;AACvB,IAAI,IAAI,EAAE;AACV,IAAM,IAAoB,GAAE,CAAC;AAA7B,IAAgC,IAAoB,EAAE;AAAA,EACpD,WAAW;AAAA,EACX,SAAS;AACX,GAAG,CAAC,CAAC,CAAC;", "names": []}