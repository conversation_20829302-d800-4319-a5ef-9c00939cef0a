import {
  k
} from "./chunk-7ARKPMCG.js";
import {
  K
} from "./chunk-4WPFA5CA.js";
import {
  cu,
  hu
} from "./chunk-MEKKV4OY.js";
import "./chunk-AUZ3RYOM.js";

// node_modules/@graphiql/react/dist/search.es.js
var V = Object.defineProperty;
var a = (S, O) => V(S, "name", { value: O, configurable: true });
function B(S, O) {
  for (var i = 0; i < O.length; i++) {
    const y = O[i];
    if (typeof y != "string" && !Array.isArray(y)) {
      for (const v in y)
        if (v !== "default" && !(v in S)) {
          const h = Object.getOwnPropertyDescriptor(y, v);
          h && Object.defineProperty(S, v, h.get ? h : {
            enumerable: true,
            get: () => y[v]
          });
        }
    }
  }
  return Object.freeze(Object.defineProperty(S, Symbol.toStringTag, { value: "Module" }));
}
a(B, "_mergeNamespaces");
var W = { exports: {} };
(function(S, O) {
  (function(i) {
    i(cu(), K(), k());
  })(function(i) {
    i.defineOption("search", { bottom: false });
    function y(e, n) {
      return typeof e == "string" ? e = new RegExp(e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g, "\\$&"), n ? "gi" : "g") : e.global || (e = new RegExp(e.source, e.ignoreCase ? "gi" : "g")), { token: function(t) {
        e.lastIndex = t.pos;
        var r = e.exec(t.string);
        if (r && r.index == t.pos)
          return t.pos += r[0].length || 1, "searching";
        r ? t.pos = r.index : t.skipToEnd();
      } };
    }
    a(y, "searchOverlay");
    function v() {
      this.posFrom = this.posTo = this.lastQuery = this.query = null, this.overlay = null;
    }
    a(v, "SearchState");
    function h(e) {
      return e.state.search || (e.state.search = new v());
    }
    a(h, "getSearchState");
    function w(e) {
      return typeof e == "string" && e == e.toLowerCase();
    }
    a(w, "queryCaseInsensitive");
    function m(e, n, t) {
      return e.getSearchCursor(n, t, { caseFold: w(n), multiline: true });
    }
    a(m, "getSearchCursor");
    function $(e, n, t, r, o) {
      e.openDialog(n, r, {
        value: t,
        selectValueOnOpen: true,
        closeOnEnter: false,
        onClose: function() {
          N(e);
        },
        onKeyDown: o,
        bottom: e.options.search.bottom
      });
    }
    a($, "persistentDialog");
    function R(e, n, t, r, o) {
      e.openDialog ? e.openDialog(n, o, { value: r, selectValueOnOpen: true, bottom: e.options.search.bottom }) : o(prompt(t, r));
    }
    a(R, "dialog");
    function j(e, n, t, r) {
      e.openConfirm ? e.openConfirm(n, r) : confirm(t) && r[0]();
    }
    a(j, "confirmDialog");
    function C(e) {
      return e.replace(/\\([nrt\\])/g, function(n, t) {
        return t == "n" ? `
` : t == "r" ? "\r" : t == "t" ? "	" : t == "\\" ? "\\" : n;
      });
    }
    a(C, "parseString");
    function T(e) {
      var n = e.match(/^\/(.*)\/([a-z]*)$/);
      if (n)
        try {
          e = new RegExp(n[1], n[2].indexOf("i") == -1 ? "" : "i");
        } catch {
        }
      else
        e = C(e);
      return (typeof e == "string" ? e == "" : e.test("")) && (e = /x^/), e;
    }
    a(T, "parseQuery");
    function D(e, n, t) {
      n.queryText = t, n.query = T(t), e.removeOverlay(n.overlay, w(n.query)), n.overlay = y(n.query, w(n.query)), e.addOverlay(n.overlay), e.showMatchesOnScrollbar && (n.annotate && (n.annotate.clear(), n.annotate = null), n.annotate = e.showMatchesOnScrollbar(n.query, w(n.query)));
    }
    a(D, "startSearch");
    function b(e, n, t, r) {
      var o = h(e);
      if (o.query)
        return P(e, n);
      var s = e.getSelection() || o.lastQuery;
      if (s instanceof RegExp && s.source == "x^" && (s = null), t && e.openDialog) {
        var c = null, p = a(function(f, x) {
          i.e_stop(x), f && (f != o.queryText && (D(e, o, f), o.posFrom = o.posTo = e.getCursor()), c && (c.style.opacity = 1), P(e, x.shiftKey, function(d, g) {
            var u;
            g.line < 3 && document.querySelector && (u = e.display.wrapper.querySelector(".CodeMirror-dialog")) && u.getBoundingClientRect().bottom - 4 > e.cursorCoords(g, "window").top && ((c = u).style.opacity = 0.4);
          }));
        }, "searchNext");
        $(e, _(e), s, p, function(f, x) {
          var d = i.keyName(f), g = e.getOption("extraKeys"), u = g && g[d] || i.keyMap[e.getOption("keyMap")][d];
          u == "findNext" || u == "findPrev" || u == "findPersistentNext" || u == "findPersistentPrev" ? (i.e_stop(f), D(e, h(e), x), e.execCommand(u)) : (u == "find" || u == "findPersistent") && (i.e_stop(f), p(x, f));
        }), r && s && (D(e, o, s), P(e, n));
      } else
        R(e, _(e), "Search for:", s, function(f) {
          f && !o.query && e.operation(function() {
            D(e, o, f), o.posFrom = o.posTo = e.getCursor(), P(e, n);
          });
        });
    }
    a(b, "doSearch");
    function P(e, n, t) {
      e.operation(function() {
        var r = h(e), o = m(e, r.query, n ? r.posFrom : r.posTo);
        !o.find(n) && (o = m(e, r.query, n ? i.Pos(e.lastLine()) : i.Pos(e.firstLine(), 0)), !o.find(n)) || (e.setSelection(o.from(), o.to()), e.scrollIntoView({ from: o.from(), to: o.to() }, 20), r.posFrom = o.from(), r.posTo = o.to(), t && t(o.from(), o.to()));
      });
    }
    a(P, "findNext");
    function N(e) {
      e.operation(function() {
        var n = h(e);
        n.lastQuery = n.query, n.query && (n.query = n.queryText = null, e.removeOverlay(n.overlay), n.annotate && (n.annotate.clear(), n.annotate = null));
      });
    }
    a(N, "clearSearch");
    function l(e, n) {
      var t = e ? document.createElement(e) : document.createDocumentFragment();
      for (var r in n)
        t[r] = n[r];
      for (var o = 2; o < arguments.length; o++) {
        var s = arguments[o];
        t.appendChild(typeof s == "string" ? document.createTextNode(s) : s);
      }
      return t;
    }
    a(l, "el");
    function _(e) {
      return l(
        "",
        null,
        l("span", { className: "CodeMirror-search-label" }, e.phrase("Search:")),
        " ",
        l("input", { type: "text", style: "width: 10em", className: "CodeMirror-search-field" }),
        " ",
        l(
          "span",
          { style: "color: #888", className: "CodeMirror-search-hint" },
          e.phrase("(Use /re/ syntax for regexp search)")
        )
      );
    }
    a(_, "getQueryDialog");
    function k2(e) {
      return l(
        "",
        null,
        " ",
        l("input", { type: "text", style: "width: 10em", className: "CodeMirror-search-field" }),
        " ",
        l(
          "span",
          { style: "color: #888", className: "CodeMirror-search-hint" },
          e.phrase("(Use /re/ syntax for regexp search)")
        )
      );
    }
    a(k2, "getReplaceQueryDialog");
    function A(e) {
      return l(
        "",
        null,
        l("span", { className: "CodeMirror-search-label" }, e.phrase("With:")),
        " ",
        l("input", { type: "text", style: "width: 10em", className: "CodeMirror-search-field" })
      );
    }
    a(A, "getReplacementQueryDialog");
    function I(e) {
      return l(
        "",
        null,
        l("span", { className: "CodeMirror-search-label" }, e.phrase("Replace?")),
        " ",
        l("button", {}, e.phrase("Yes")),
        " ",
        l("button", {}, e.phrase("No")),
        " ",
        l("button", {}, e.phrase("All")),
        " ",
        l("button", {}, e.phrase("Stop"))
      );
    }
    a(I, "getDoReplaceConfirm");
    function E(e, n, t) {
      e.operation(function() {
        for (var r = m(e, n); r.findNext(); )
          if (typeof n != "string") {
            var o = e.getRange(r.from(), r.to()).match(n);
            r.replace(t.replace(/\$(\d)/g, function(s, c) {
              return o[c];
            }));
          } else
            r.replace(t);
      });
    }
    a(E, "replaceAll");
    function F(e, n) {
      if (!e.getOption("readOnly")) {
        var t = e.getSelection() || h(e).lastQuery, r = n ? e.phrase("Replace all:") : e.phrase("Replace:"), o = l(
          "",
          null,
          l("span", { className: "CodeMirror-search-label" }, r),
          k2(e)
        );
        R(e, o, r, t, function(s) {
          s && (s = T(s), R(e, A(e), e.phrase("Replace with:"), "", function(c) {
            if (c = C(c), n)
              E(e, s, c);
            else {
              N(e);
              var p = m(e, s, e.getCursor("from")), f = a(function() {
                var d = p.from(), g;
                !(g = p.findNext()) && (p = m(e, s), !(g = p.findNext()) || d && p.from().line == d.line && p.from().ch == d.ch) || (e.setSelection(p.from(), p.to()), e.scrollIntoView({ from: p.from(), to: p.to() }), j(
                  e,
                  I(e),
                  e.phrase("Replace?"),
                  [
                    function() {
                      x(g);
                    },
                    f,
                    function() {
                      E(e, s, c);
                    }
                  ]
                ));
              }, "advance"), x = a(function(d) {
                p.replace(typeof s == "string" ? c : c.replace(/\$(\d)/g, function(g, u) {
                  return d[u];
                })), f();
              }, "doReplace");
              f();
            }
          }));
        });
      }
    }
    a(F, "replace"), i.commands.find = function(e) {
      N(e), b(e);
    }, i.commands.findPersistent = function(e) {
      N(e), b(e, false, true);
    }, i.commands.findPersistentNext = function(e) {
      b(e, false, true, true);
    }, i.commands.findPersistentPrev = function(e) {
      b(e, true, true, true);
    }, i.commands.findNext = b, i.commands.findPrev = function(e) {
      b(e, true);
    }, i.commands.clearSearch = N, i.commands.replace = F, i.commands.replaceAll = function(e) {
      F(e, true);
    };
  });
})();
var Q = W.exports;
var Y = hu(Q);
var Z = B({
  __proto__: null,
  default: Y
}, [Q]);
export {
  Z as s
};
//# sourceMappingURL=search.es-IKQJJVAQ.js.map
