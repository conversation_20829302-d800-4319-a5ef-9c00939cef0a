{"version": 3, "sources": ["../../../../../@graphiql/react/dist/searchcursor.es.js"], "sourcesContent": ["var f = Object.defineProperty;\nvar a = (r, o) => f(r, \"name\", { value: o, configurable: !0 });\nimport { g as u } from \"./codemirror.es2.js\";\nimport { r as i } from \"./searchcursor.es2.js\";\nfunction p(r, o) {\n  for (var s = 0; s < o.length; s++) {\n    const e = o[s];\n    if (typeof e != \"string\" && !Array.isArray(e)) {\n      for (const t in e)\n        if (t !== \"default\" && !(t in r)) {\n          const c = Object.getOwnPropertyDescriptor(e, t);\n          c && Object.defineProperty(r, t, c.get ? c : {\n            enumerable: !0,\n            get: () => e[t]\n          });\n        }\n    }\n  }\n  return Object.freeze(Object.defineProperty(r, Symbol.toStringTag, { value: \"Module\" }));\n}\na(p, \"_mergeNamespaces\");\nvar n = i();\nconst g = /* @__PURE__ */ u(n), b = /* @__PURE__ */ p({\n  __proto__: null,\n  default: g\n}, [n]);\nexport {\n  b as s\n};\n//# sourceMappingURL=searchcursor.es.js.map\n"], "mappings": ";;;;;;;;;AAAA,IAAI,IAAI,OAAO;AACf,IAAI,IAAI,CAAC,GAAG,MAAM,EAAE,GAAG,QAAQ,EAAE,OAAO,GAAG,cAAc,KAAG,CAAC;AAG7D,SAAS,EAAE,GAAG,GAAG;AACf,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,UAAM,IAAI,EAAE,CAAC;AACb,QAAI,OAAO,KAAK,YAAY,CAAC,MAAM,QAAQ,CAAC,GAAG;AAC7C,iBAAW,KAAK;AACd,YAAI,MAAM,aAAa,EAAE,KAAK,IAAI;AAChC,gBAAM,IAAI,OAAO,yBAAyB,GAAG,CAAC;AAC9C,eAAK,OAAO,eAAe,GAAG,GAAG,EAAE,MAAM,IAAI;AAAA,YAC3C,YAAY;AAAA,YACZ,KAAK,MAAM,EAAE,CAAC;AAAA,UAChB,CAAC;AAAA,QACH;AAAA,IACJ;AAAA,EACF;AACA,SAAO,OAAO,OAAO,OAAO,eAAe,GAAG,OAAO,aAAa,EAAE,OAAO,SAAS,CAAC,CAAC;AACxF;AACA,EAAE,GAAG,kBAAkB;AACvB,IAAI,IAAI,EAAE;AACV,IAAM,IAAoB,GAAE,CAAC;AAA7B,IAAgC,IAAoB,EAAE;AAAA,EACpD,WAAW;AAAA,EACX,SAAS;AACX,GAAG,CAAC,CAAC,CAAC;", "names": []}