import {
  cu,
  hu
} from "./chunk-MEKKV4OY.js";
import "./chunk-AUZ3RYOM.js";

// node_modules/@graphiql/react/dist/closebrackets.es.js
var N = Object.defineProperty;
var f = (S, P) => N(S, "name", { value: P, configurable: true });
function J(S, P) {
  for (var a = 0; a < P.length; a++) {
    const c = P[a];
    if (typeof c != "string" && !Array.isArray(c)) {
      for (const i in c)
        if (i !== "default" && !(i in S)) {
          const v = Object.getOwnPropertyDescriptor(c, i);
          v && Object.defineProperty(S, i, v.get ? v : {
            enumerable: true,
            get: () => c[i]
          });
        }
    }
  }
  return Object.freeze(Object.defineProperty(S, Symbol.toStringTag, { value: "Module" }));
}
f(J, "_mergeNamespaces");
var Q = { exports: {} };
(function(S, P) {
  (function(a) {
    a(cu());
  })(function(a) {
    var c = {
      pairs: `()[]{}''""`,
      closeBefore: `)]}'":;>`,
      triples: "",
      explode: "[]{}"
    }, i = a.Pos;
    a.defineOption("autoCloseBrackets", false, function(e, t, n) {
      n && n != a.Init && (e.removeKeyMap(y), e.state.closeBrackets = null), t && (_(v(t, "pairs")), e.state.closeBrackets = t, e.addKeyMap(y));
    });
    function v(e, t) {
      return t == "pairs" && typeof e == "string" ? e : typeof e == "object" && e[t] != null ? e[t] : c[t];
    }
    f(v, "getOption");
    var y = { Backspace: L, Enter: W };
    function _(e) {
      for (var t = 0; t < e.length; t++) {
        var n = e.charAt(t), s = "'" + n + "'";
        y[s] || (y[s] = K(n));
      }
    }
    f(_, "ensureBound"), _(c.pairs + "`");
    function K(e) {
      return function(t) {
        return q(t, e);
      };
    }
    f(K, "handler");
    function B(e) {
      var t = e.state.closeBrackets;
      if (!t || t.override)
        return t;
      var n = e.getModeAt(e.getCursor());
      return n.closeBrackets || t;
    }
    f(B, "getConfig");
    function L(e) {
      var t = B(e);
      if (!t || e.getOption("disableInput"))
        return a.Pass;
      for (var n = v(t, "pairs"), s = e.listSelections(), r = 0; r < s.length; r++) {
        if (!s[r].empty())
          return a.Pass;
        var h = w(e, s[r].head);
        if (!h || n.indexOf(h) % 2 != 0)
          return a.Pass;
      }
      for (var r = s.length - 1; r >= 0; r--) {
        var o = s[r].head;
        e.replaceRange("", i(o.line, o.ch - 1), i(o.line, o.ch + 1), "+delete");
      }
    }
    f(L, "handleBackspace");
    function W(e) {
      var t = B(e), n = t && v(t, "explode");
      if (!n || e.getOption("disableInput"))
        return a.Pass;
      for (var s = e.listSelections(), r = 0; r < s.length; r++) {
        if (!s[r].empty())
          return a.Pass;
        var h = w(e, s[r].head);
        if (!h || n.indexOf(h) % 2 != 0)
          return a.Pass;
      }
      e.operation(function() {
        var o = e.lineSeparator() || `
`;
        e.replaceSelection(o + o, null), O(e, -1), s = e.listSelections();
        for (var g = 0; g < s.length; g++) {
          var A = s[g].head.line;
          e.indentLine(A, null, true), e.indentLine(A + 1, null, true);
        }
      });
    }
    f(W, "handleEnter");
    function O(e, t) {
      for (var n = [], s = e.listSelections(), r = 0, h = 0; h < s.length; h++) {
        var o = s[h];
        o.head == e.getCursor() && (r = h);
        var g = o.head.ch || t > 0 ? { line: o.head.line, ch: o.head.ch + t } : { line: o.head.line - 1 };
        n.push({ anchor: g, head: g });
      }
      e.setSelections(n, r);
    }
    f(O, "moveSel");
    function $(e) {
      var t = a.cmpPos(e.anchor, e.head) > 0;
      return {
        anchor: new i(e.anchor.line, e.anchor.ch + (t ? -1 : 1)),
        head: new i(e.head.line, e.head.ch + (t ? 1 : -1))
      };
    }
    f($, "contractSelection");
    function q(e, t) {
      var n = B(e);
      if (!n || e.getOption("disableInput"))
        return a.Pass;
      var s = v(n, "pairs"), r = s.indexOf(t);
      if (r == -1)
        return a.Pass;
      for (var h = v(n, "closeBefore"), o = v(n, "triples"), g = s.charAt(r + 1) == t, A = e.listSelections(), R = r % 2 == 0, b, j = 0; j < A.length; j++) {
        var I = A[j], l = I.head, p, x = e.getRange(l, i(l.line, l.ch + 1));
        if (R && !I.empty())
          p = "surround";
        else if ((g || !R) && x == t)
          g && z(e, l) ? p = "both" : o.indexOf(t) >= 0 && e.getRange(l, i(l.line, l.ch + 3)) == t + t + t ? p = "skipThree" : p = "skip";
        else if (g && l.ch > 1 && o.indexOf(t) >= 0 && e.getRange(i(l.line, l.ch - 2), l) == t + t) {
          if (l.ch > 2 && /\bstring/.test(e.getTokenTypeAt(i(l.line, l.ch - 2))))
            return a.Pass;
          p = "addFour";
        } else if (g) {
          var F = l.ch == 0 ? " " : e.getRange(i(l.line, l.ch - 1), l);
          if (!a.isWordChar(x) && F != t && !a.isWordChar(F))
            p = "both";
          else
            return a.Pass;
        } else if (R && (x.length === 0 || /\s/.test(x) || h.indexOf(x) > -1))
          p = "both";
        else
          return a.Pass;
        if (!b)
          b = p;
        else if (b != p)
          return a.Pass;
      }
      var k = r % 2 ? s.charAt(r - 1) : t, E = r % 2 ? t : s.charAt(r + 1);
      e.operation(function() {
        if (b == "skip")
          O(e, 1);
        else if (b == "skipThree")
          O(e, 3);
        else if (b == "surround") {
          for (var u = e.getSelections(), d = 0; d < u.length; d++)
            u[d] = k + u[d] + E;
          e.replaceSelections(u, "around"), u = e.listSelections().slice();
          for (var d = 0; d < u.length; d++)
            u[d] = $(u[d]);
          e.setSelections(u);
        } else
          b == "both" ? (e.replaceSelection(k + E, null), e.triggerElectric(k + E), O(e, -1)) : b == "addFour" && (e.replaceSelection(k + k + k + k, "before"), O(e, 1));
      });
    }
    f(q, "handleChar");
    function w(e, t) {
      var n = e.getRange(
        i(t.line, t.ch - 1),
        i(t.line, t.ch + 1)
      );
      return n.length == 2 ? n : null;
    }
    f(w, "charsAround");
    function z(e, t) {
      var n = e.getTokenAt(i(t.line, t.ch + 1));
      return /\bstring/.test(n.type) && n.start == t.ch && (t.ch == 0 || !/\bstring/.test(e.getTokenTypeAt(t)));
    }
    f(z, "stringStartsAfter");
  });
})();
var D = Q.exports;
var T = hu(D);
var X = J({
  __proto__: null,
  default: T
}, [D]);
export {
  X as c
};
//# sourceMappingURL=closebrackets.es-RGN7SUNA.js.map
