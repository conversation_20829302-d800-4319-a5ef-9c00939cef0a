{"version": 3, "sources": ["../../../../../node_modules/codemirror/addon/lint/lint.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n  var GUTTER_ID = \"CodeMirror-lint-markers\";\n  var LINT_LINE_ID = \"CodeMirror-lint-line-\";\n\n  function showTooltip(cm, e, content) {\n    var tt = document.createElement(\"div\");\n    tt.className = \"CodeMirror-lint-tooltip cm-s-\" + cm.options.theme;\n    tt.appendChild(content.cloneNode(true));\n    if (cm.state.lint.options.selfContain)\n      cm.getWrapperElement().appendChild(tt);\n    else\n      document.body.appendChild(tt);\n\n    function position(e) {\n      if (!tt.parentNode) return CodeMirror.off(document, \"mousemove\", position);\n      tt.style.top = Math.max(0, e.clientY - tt.offsetHeight - 5) + \"px\";\n      tt.style.left = (e.clientX + 5) + \"px\";\n    }\n    CodeMirror.on(document, \"mousemove\", position);\n    position(e);\n    if (tt.style.opacity != null) tt.style.opacity = 1;\n    return tt;\n  }\n  function rm(elt) {\n    if (elt.parentNode) elt.parentNode.removeChild(elt);\n  }\n  function hideTooltip(tt) {\n    if (!tt.parentNode) return;\n    if (tt.style.opacity == null) rm(tt);\n    tt.style.opacity = 0;\n    setTimeout(function() { rm(tt); }, 600);\n  }\n\n  function showTooltipFor(cm, e, content, node) {\n    var tooltip = showTooltip(cm, e, content);\n    function hide() {\n      CodeMirror.off(node, \"mouseout\", hide);\n      if (tooltip) { hideTooltip(tooltip); tooltip = null; }\n    }\n    var poll = setInterval(function() {\n      if (tooltip) for (var n = node;; n = n.parentNode) {\n        if (n && n.nodeType == 11) n = n.host;\n        if (n == document.body) return;\n        if (!n) { hide(); break; }\n      }\n      if (!tooltip) return clearInterval(poll);\n    }, 400);\n    CodeMirror.on(node, \"mouseout\", hide);\n  }\n\n  function LintState(cm, conf, hasGutter) {\n    this.marked = [];\n    if (conf instanceof Function) conf = {getAnnotations: conf};\n    if (!conf || conf === true) conf = {};\n    this.options = {};\n    this.linterOptions = conf.options || {};\n    for (var prop in defaults) this.options[prop] = defaults[prop];\n    for (var prop in conf) {\n      if (defaults.hasOwnProperty(prop)) {\n        if (conf[prop] != null) this.options[prop] = conf[prop];\n      } else if (!conf.options) {\n        this.linterOptions[prop] = conf[prop];\n      }\n    }\n    this.timeout = null;\n    this.hasGutter = hasGutter;\n    this.onMouseOver = function(e) { onMouseOver(cm, e); };\n    this.waitingFor = 0\n  }\n\n  var defaults = {\n    highlightLines: false,\n    tooltips: true,\n    delay: 500,\n    lintOnChange: true,\n    getAnnotations: null,\n    async: false,\n    selfContain: null,\n    formatAnnotation: null,\n    onUpdateLinting: null\n  }\n\n  function clearMarks(cm) {\n    var state = cm.state.lint;\n    if (state.hasGutter) cm.clearGutter(GUTTER_ID);\n    if (state.options.highlightLines) clearErrorLines(cm);\n    for (var i = 0; i < state.marked.length; ++i)\n      state.marked[i].clear();\n    state.marked.length = 0;\n  }\n\n  function clearErrorLines(cm) {\n    cm.eachLine(function(line) {\n      var has = line.wrapClass && /\\bCodeMirror-lint-line-\\w+\\b/.exec(line.wrapClass);\n      if (has) cm.removeLineClass(line, \"wrap\", has[0]);\n    })\n  }\n\n  function makeMarker(cm, labels, severity, multiple, tooltips) {\n    var marker = document.createElement(\"div\"), inner = marker;\n    marker.className = \"CodeMirror-lint-marker CodeMirror-lint-marker-\" + severity;\n    if (multiple) {\n      inner = marker.appendChild(document.createElement(\"div\"));\n      inner.className = \"CodeMirror-lint-marker CodeMirror-lint-marker-multiple\";\n    }\n\n    if (tooltips != false) CodeMirror.on(inner, \"mouseover\", function(e) {\n      showTooltipFor(cm, e, labels, inner);\n    });\n\n    return marker;\n  }\n\n  function getMaxSeverity(a, b) {\n    if (a == \"error\") return a;\n    else return b;\n  }\n\n  function groupByLine(annotations) {\n    var lines = [];\n    for (var i = 0; i < annotations.length; ++i) {\n      var ann = annotations[i], line = ann.from.line;\n      (lines[line] || (lines[line] = [])).push(ann);\n    }\n    return lines;\n  }\n\n  function annotationTooltip(ann) {\n    var severity = ann.severity;\n    if (!severity) severity = \"error\";\n    var tip = document.createElement(\"div\");\n    tip.className = \"CodeMirror-lint-message CodeMirror-lint-message-\" + severity;\n    if (typeof ann.messageHTML != 'undefined') {\n      tip.innerHTML = ann.messageHTML;\n    } else {\n      tip.appendChild(document.createTextNode(ann.message));\n    }\n    return tip;\n  }\n\n  function lintAsync(cm, getAnnotations) {\n    var state = cm.state.lint\n    var id = ++state.waitingFor\n    function abort() {\n      id = -1\n      cm.off(\"change\", abort)\n    }\n    cm.on(\"change\", abort)\n    getAnnotations(cm.getValue(), function(annotations, arg2) {\n      cm.off(\"change\", abort)\n      if (state.waitingFor != id) return\n      if (arg2 && annotations instanceof CodeMirror) annotations = arg2\n      cm.operation(function() {updateLinting(cm, annotations)})\n    }, state.linterOptions, cm);\n  }\n\n  function startLinting(cm) {\n    var state = cm.state.lint;\n    if (!state) return;\n    var options = state.options;\n    /*\n     * Passing rules in `options` property prevents JSHint (and other linters) from complaining\n     * about unrecognized rules like `onUpdateLinting`, `delay`, `lintOnChange`, etc.\n     */\n    var getAnnotations = options.getAnnotations || cm.getHelper(CodeMirror.Pos(0, 0), \"lint\");\n    if (!getAnnotations) return;\n    if (options.async || getAnnotations.async) {\n      lintAsync(cm, getAnnotations)\n    } else {\n      var annotations = getAnnotations(cm.getValue(), state.linterOptions, cm);\n      if (!annotations) return;\n      if (annotations.then) annotations.then(function(issues) {\n        cm.operation(function() {updateLinting(cm, issues)})\n      });\n      else cm.operation(function() {updateLinting(cm, annotations)})\n    }\n  }\n\n  function updateLinting(cm, annotationsNotSorted) {\n    var state = cm.state.lint;\n    if (!state) return;\n    var options = state.options;\n    clearMarks(cm);\n\n    var annotations = groupByLine(annotationsNotSorted);\n\n    for (var line = 0; line < annotations.length; ++line) {\n      var anns = annotations[line];\n      if (!anns) continue;\n\n      // filter out duplicate messages\n      var message = [];\n      anns = anns.filter(function(item) { return message.indexOf(item.message) > -1 ? false : message.push(item.message) });\n\n      var maxSeverity = null;\n      var tipLabel = state.hasGutter && document.createDocumentFragment();\n\n      for (var i = 0; i < anns.length; ++i) {\n        var ann = anns[i];\n        var severity = ann.severity;\n        if (!severity) severity = \"error\";\n        maxSeverity = getMaxSeverity(maxSeverity, severity);\n\n        if (options.formatAnnotation) ann = options.formatAnnotation(ann);\n        if (state.hasGutter) tipLabel.appendChild(annotationTooltip(ann));\n\n        if (ann.to) state.marked.push(cm.markText(ann.from, ann.to, {\n          className: \"CodeMirror-lint-mark CodeMirror-lint-mark-\" + severity,\n          __annotation: ann\n        }));\n      }\n      // use original annotations[line] to show multiple messages\n      if (state.hasGutter)\n        cm.setGutterMarker(line, GUTTER_ID, makeMarker(cm, tipLabel, maxSeverity, annotations[line].length > 1,\n                                                       options.tooltips));\n\n      if (options.highlightLines)\n        cm.addLineClass(line, \"wrap\", LINT_LINE_ID + maxSeverity);\n    }\n    if (options.onUpdateLinting) options.onUpdateLinting(annotationsNotSorted, annotations, cm);\n  }\n\n  function onChange(cm) {\n    var state = cm.state.lint;\n    if (!state) return;\n    clearTimeout(state.timeout);\n    state.timeout = setTimeout(function(){startLinting(cm);}, state.options.delay);\n  }\n\n  function popupTooltips(cm, annotations, e) {\n    var target = e.target || e.srcElement;\n    var tooltip = document.createDocumentFragment();\n    for (var i = 0; i < annotations.length; i++) {\n      var ann = annotations[i];\n      tooltip.appendChild(annotationTooltip(ann));\n    }\n    showTooltipFor(cm, e, tooltip, target);\n  }\n\n  function onMouseOver(cm, e) {\n    var target = e.target || e.srcElement;\n    if (!/\\bCodeMirror-lint-mark-/.test(target.className)) return;\n    var box = target.getBoundingClientRect(), x = (box.left + box.right) / 2, y = (box.top + box.bottom) / 2;\n    var spans = cm.findMarksAt(cm.coordsChar({left: x, top: y}, \"client\"));\n\n    var annotations = [];\n    for (var i = 0; i < spans.length; ++i) {\n      var ann = spans[i].__annotation;\n      if (ann) annotations.push(ann);\n    }\n    if (annotations.length) popupTooltips(cm, annotations, e);\n  }\n\n  CodeMirror.defineOption(\"lint\", false, function(cm, val, old) {\n    if (old && old != CodeMirror.Init) {\n      clearMarks(cm);\n      if (cm.state.lint.options.lintOnChange !== false)\n        cm.off(\"change\", onChange);\n      CodeMirror.off(cm.getWrapperElement(), \"mouseover\", cm.state.lint.onMouseOver);\n      clearTimeout(cm.state.lint.timeout);\n      delete cm.state.lint;\n    }\n\n    if (val) {\n      var gutters = cm.getOption(\"gutters\"), hasLintGutter = false;\n      for (var i = 0; i < gutters.length; ++i) if (gutters[i] == GUTTER_ID) hasLintGutter = true;\n      var state = cm.state.lint = new LintState(cm, val, hasLintGutter);\n      if (state.options.lintOnChange)\n        cm.on(\"change\", onChange);\n      if (state.options.tooltips != false && state.options.tooltips != \"gutter\")\n        CodeMirror.on(cm.getWrapperElement(), \"mouseover\", state.onMouseOver);\n\n      startLinting(cm);\n    }\n  });\n\n  CodeMirror.defineExtension(\"performLint\", function() {\n    startLinting(this);\n  });\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,GAAC,SAASA,GAAK;AAEXA,MAAIC,GAA+B,CAAA;EAKtC,GAAE,SAASC,GAAY;AAEtB,QAAIC,IAAY,2BACZC,IAAe;AAEnB,aAASC,EAAYC,GAAI,GAAGC,GAAS;AACnC,UAAIC,IAAK,SAAS,cAAc,KAAK;AACrCA,QAAG,YAAY,kCAAkCF,EAAG,QAAQ,OAC5DE,EAAG,YAAYD,EAAQ,UAAU,IAAI,CAAC,GAClCD,EAAG,MAAM,KAAK,QAAQ,cACxBA,EAAG,kBAAiB,EAAG,YAAYE,CAAE,IAErC,SAAS,KAAK,YAAYA,CAAE;AAE9B,eAASC,EAASC,GAAG;AACnB,YAAI,CAACF,EAAG;AAAY,iBAAON,EAAW,IAAI,UAAU,aAAaO,CAAQ;AACzED,UAAG,MAAM,MAAM,KAAK,IAAI,GAAGE,EAAE,UAAUF,EAAG,eAAe,CAAC,IAAI,MAC9DA,EAAG,MAAM,OAAQE,EAAE,UAAU,IAAK;MACnC;AAJQ,aAAAC,EAAAF,GAAA,UAAA,GAKTP,EAAW,GAAG,UAAU,aAAaO,CAAQ,GAC7CA,EAAS,CAAC,GACND,EAAG,MAAM,WAAW,SAAMA,EAAG,MAAM,UAAU,IAC1CA;IACR;AAlBQG,MAAAN,GAAA,aAAA;AAmBT,aAASO,EAAGC,GAAK;AACXA,QAAI,cAAYA,EAAI,WAAW,YAAYA,CAAG;IACnD;AAFQF,MAAAC,GAAA,IAAA;AAGT,aAASE,EAAYN,GAAI;AAClBA,QAAG,eACJA,EAAG,MAAM,WAAW,QAAMI,EAAGJ,CAAE,GACnCA,EAAG,MAAM,UAAU,GACnB,WAAW,WAAW;AAAEI,UAAGJ,CAAE;MAAE,GAAI,GAAG;IACvC;AALQG,MAAAG,GAAA,aAAA;AAOT,aAASC,EAAeT,GAAI,GAAGC,GAASS,GAAM;AAC5C,UAAIC,IAAUZ,EAAYC,GAAI,GAAGC,CAAO;AACxC,eAASW,IAAO;AACdhB,UAAW,IAAIc,GAAM,YAAYE,CAAI,GACjCD,MAAWH,EAAYG,CAAO,GAAGA,IAAU;MAChD;AAHQN,QAAAO,GAAA,MAAA;AAIT,UAAIC,IAAO,YAAY,WAAW;AAChC,YAAIF;AAAS,mBAASG,IAAIJ,KAAOI,IAAIA,EAAE,YAAY;AAEjD,gBADIA,KAAKA,EAAE,YAAY,OAAIA,IAAIA,EAAE,OAC7BA,KAAK,SAAS;AAAM;AACxB,gBAAI,CAACA,GAAG;AAAEF,gBAAM;AAAE;YAAA;UAAA;AAEpB,YAAI,CAACD;AAAS,iBAAO,cAAcE,CAAI;MACxC,GAAE,GAAG;AACNjB,QAAW,GAAGc,GAAM,YAAYE,CAAI;IACrC;AAfQP,MAAAI,GAAA,gBAAA;AAiBT,aAASM,EAAUf,GAAIgB,GAAMC,GAAW;AACtC,WAAK,SAAS,CAAA,GACVD,aAAgB,aAAUA,IAAO,EAAC,gBAAgBA,EAAI,KACtD,CAACA,KAAQA,MAAS,UAAMA,IAAO,CAAA,IACnC,KAAK,UAAU,CAAA,GACf,KAAK,gBAAgBA,EAAK,WAAW,CAAA;AACrC,eAASE,KAAQC;AAAU,aAAK,QAAQD,CAAI,IAAIC,EAASD,CAAI;AAC7D,eAASA,KAAQF;AACXG,UAAS,eAAeD,CAAI,IAC1BF,EAAKE,CAAI,KAAK,SAAM,KAAK,QAAQA,CAAI,IAAIF,EAAKE,CAAI,KAC5CF,EAAK,YACf,KAAK,cAAcE,CAAI,IAAIF,EAAKE,CAAI;AAGxC,WAAK,UAAU,MACf,KAAK,YAAYD,GACjB,KAAK,cAAc,SAASb,GAAG;AAAEgB,UAAYpB,GAAII,CAAC;MAAA,GAClD,KAAK,aAAa;IACnB;AAlBQC,MAAAU,GAAA,WAAA;AAoBT,QAAII,IAAW;MACb,gBAAgB;MAChB,UAAU;MACV,OAAO;MACP,cAAc;MACd,gBAAgB;MAChB,OAAO;MACP,aAAa;MACb,kBAAkB;MAClB,iBAAiB;IAClB;AAED,aAASE,EAAWrB,GAAI;AACtB,UAAIsB,IAAQtB,EAAG,MAAM;AACjBsB,QAAM,aAAWtB,EAAG,YAAYH,CAAS,GACzCyB,EAAM,QAAQ,kBAAgBC,EAAgBvB,CAAE;AACpD,eAASwB,IAAI,GAAGA,IAAIF,EAAM,OAAO,QAAQ,EAAEE;AACzCF,UAAM,OAAOE,CAAC,EAAE,MAAK;AACvBF,QAAM,OAAO,SAAS;IACvB;AAPQjB,MAAAgB,GAAA,YAAA;AAST,aAASE,EAAgBvB,GAAI;AAC3BA,QAAG,SAAS,SAASyB,GAAM;AACzB,YAAIC,IAAMD,EAAK,aAAa,+BAA+B,KAAKA,EAAK,SAAS;AAC1EC,aAAK1B,EAAG,gBAAgByB,GAAM,QAAQC,EAAI,CAAC,CAAC;MACtD,CAAK;IACF;AALQrB,MAAAkB,GAAA,iBAAA;AAOT,aAASI,EAAW3B,GAAI4B,GAAQC,GAAUC,GAAUC,GAAU;AAC5D,UAAIC,IAAS,SAAS,cAAc,KAAK,GAAGC,IAAQD;AACpD,aAAAA,EAAO,YAAY,mDAAmDH,GAClEC,MACFG,IAAQD,EAAO,YAAY,SAAS,cAAc,KAAK,CAAC,GACxDC,EAAM,YAAY,2DAGhBF,KAAY,SAAOnC,EAAW,GAAGqC,GAAO,aAAa,SAAS7B,GAAG;AACnEK,UAAeT,GAAII,GAAGwB,GAAQK,CAAK;MACzC,CAAK,GAEMD;IACR;AAbQ3B,MAAAsB,GAAA,YAAA;AAeT,aAASO,EAAeC,GAAGC,GAAG;AAC5B,aAAID,KAAK,UAAgBA,IACbC;IACb;AAHQ/B,MAAA6B,GAAA,gBAAA;AAKT,aAASG,EAAYC,GAAa;AAEhC,eADIC,IAAQ,CAAA,GACHf,IAAI,GAAGA,IAAIc,EAAY,QAAQ,EAAEd,GAAG;AAC3C,YAAIgB,IAAMF,EAAYd,CAAC,GAAGC,IAAOe,EAAI,KAAK;AAC1C,SAACD,EAAMd,CAAI,MAAMc,EAAMd,CAAI,IAAI,CAAA,IAAK,KAAKe,CAAG;MAAA;AAE9C,aAAOD;IACR;AAPQlC,MAAAgC,GAAA,aAAA;AAST,aAASI,EAAkBD,GAAK;AAC9B,UAAIX,IAAWW,EAAI;AACdX,YAAUA,IAAW;AAC1B,UAAIa,IAAM,SAAS,cAAc,KAAK;AACtC,aAAAA,EAAI,YAAY,qDAAqDb,GACjE,OAAOW,EAAI,cAAe,MAC5BE,EAAI,YAAYF,EAAI,cAEpBE,EAAI,YAAY,SAAS,eAAeF,EAAI,OAAO,CAAC,GAE/CE;IACR;AAXQrC,MAAAoC,GAAA,mBAAA;AAaT,aAASE,EAAU3C,GAAI4C,GAAgB;AACrC,UAAItB,IAAQtB,EAAG,MAAM,MACjB6C,IAAK,EAAEvB,EAAM;AACjB,eAASwB,IAAQ;AACfD,YAAK,IACL7C,EAAG,IAAI,UAAU8C,CAAK;MACvB;AAHQzC,QAAAyC,GAAA,OAAA,GAIT9C,EAAG,GAAG,UAAU8C,CAAK,GACrBF,EAAe5C,EAAG,SAAU,GAAE,SAASsC,GAAaS,GAAM;AACxD/C,UAAG,IAAI,UAAU8C,CAAK,GAClBxB,EAAM,cAAcuB,MACpBE,KAAQT,aAAuB1C,MAAY0C,IAAcS,IAC7D/C,EAAG,UAAU,WAAW;AAACgD,YAAchD,GAAIsC,CAAW;QAAC,CAAC;MAC9D,GAAOhB,EAAM,eAAetB,CAAE;IAC3B;AAdQK,MAAAsC,GAAA,WAAA;AAgBT,aAASM,EAAajD,GAAI;AACxB,UAAIsB,IAAQtB,EAAG,MAAM;AACrB,UAAKsB,GACL;AAAA,YAAI4B,IAAU5B,EAAM,SAKhBsB,IAAiBM,EAAQ,kBAAkBlD,EAAG,UAAUJ,EAAW,IAAI,GAAG,CAAC,GAAG,MAAM;AACxF,YAAKgD;AACL,cAAIM,EAAQ,SAASN,EAAe;AAClCD,cAAU3C,GAAI4C,CAAc;eACvB;AACL,gBAAIN,IAAcM,EAAe5C,EAAG,SAAU,GAAEsB,EAAM,eAAetB,CAAE;AACvE,gBAAI,CAACsC;AAAa;AACdA,cAAY,OAAMA,EAAY,KAAK,SAASa,GAAQ;AACtDnD,gBAAG,UAAU,WAAW;AAACgD,kBAAchD,GAAImD,CAAM;cAAC,CAAC;YAC3D,CAAO,IACInD,EAAG,UAAU,WAAW;AAACgD,gBAAchD,GAAIsC,CAAW;YAAC,CAAC;UAAA;MAAA;IAEhE;AApBQjC,MAAA4C,GAAA,cAAA;AAsBT,aAASD,EAAchD,GAAIoD,GAAsB;AAC/C,UAAI9B,IAAQtB,EAAG,MAAM;AACrB,UAAKsB,GACL;AAAA,YAAI4B,IAAU5B,EAAM;AACpBD,UAAWrB,CAAE;AAIb,iBAFIsC,IAAcD,EAAYe,CAAoB,GAEzC3B,IAAO,GAAGA,IAAOa,EAAY,QAAQ,EAAEb,GAAM;AACpD,cAAI4B,IAAOf,EAAYb,CAAI;AAC3B,cAAK4B,GAGL;AAAA,gBAAIC,IAAU,CAAA;AACdD,gBAAOA,EAAK,OAAO,SAASE,GAAM;AAAE,qBAAOD,EAAQ,QAAQC,EAAK,OAAO,IAAI,KAAK,QAAQD,EAAQ,KAAKC,EAAK,OAAO;YAAC,CAAE;AAKpH,qBAHIC,IAAc,MACdC,IAAWnC,EAAM,aAAa,SAAS,uBAAsB,GAExDE,IAAI,GAAGA,IAAI6B,EAAK,QAAQ,EAAE7B,GAAG;AACpC,kBAAIgB,IAAMa,EAAK7B,CAAC,GACZK,IAAWW,EAAI;AACdX,oBAAUA,IAAW,UAC1B2B,IAActB,EAAesB,GAAa3B,CAAQ,GAE9CqB,EAAQ,qBAAkBV,IAAMU,EAAQ,iBAAiBV,CAAG,IAC5DlB,EAAM,aAAWmC,EAAS,YAAYhB,EAAkBD,CAAG,CAAC,GAE5DA,EAAI,MAAIlB,EAAM,OAAO,KAAKtB,EAAG,SAASwC,EAAI,MAAMA,EAAI,IAAI;gBAC1D,WAAW,+CAA+CX;gBAC1D,cAAcW;cACf,CAAA,CAAC;YAAA;AAGAlB,cAAM,aACRtB,EAAG,gBAAgByB,GAAM5B,GAAW8B;cAAW3B;cAAIyD;cAAUD;cAAalB,EAAYb,CAAI,EAAE,SAAS;cACtDyB,EAAQ;YAAQ,CAAC,GAE9DA,EAAQ,kBACVlD,EAAG,aAAayB,GAAM,QAAQ3B,IAAe0D,CAAW;UAAA;QAAA;AAExDN,UAAQ,mBAAiBA,EAAQ,gBAAgBE,GAAsBd,GAAatC,CAAE;MAAA;IAC3F;AA1CQK,MAAA2C,GAAA,eAAA;AA4CT,aAASU,EAAS1D,GAAI;AACpB,UAAIsB,IAAQtB,EAAG,MAAM;AAChBsB,YACL,aAAaA,EAAM,OAAO,GAC1BA,EAAM,UAAU,WAAW,WAAU;AAAC2B,UAAajD,CAAE;MAAE,GAAGsB,EAAM,QAAQ,KAAK;IAC9E;AALQjB,MAAAqD,GAAA,UAAA;AAOT,aAASC,EAAc3D,GAAIsC,GAAalC,GAAG;AAGzC,eAFIwD,IAASxD,EAAE,UAAUA,EAAE,YACvBO,IAAU,SAAS,uBAAA,GACd,IAAI,GAAG,IAAI2B,EAAY,QAAQ,KAAK;AAC3C,YAAIE,IAAMF,EAAY,CAAC;AACvB3B,UAAQ,YAAY8B,EAAkBD,CAAG,CAAC;MAAA;AAE5C/B,QAAeT,GAAII,GAAGO,GAASiD,CAAM;IACtC;AARQvD,MAAAsD,GAAA,eAAA;AAUT,aAASvC,EAAYpB,GAAI,GAAG;AAC1B,UAAI4D,IAAS,EAAE,UAAU,EAAE;AAC3B,UAAK,0BAA0B,KAAKA,EAAO,SAAS,GAKpD;AAAA,iBAJIC,IAAMD,EAAO,sBAAqB,GAAIE,KAAKD,EAAI,OAAOA,EAAI,SAAS,GAAGE,KAAKF,EAAI,MAAMA,EAAI,UAAU,GACnGG,IAAQhE,EAAG,YAAYA,EAAG,WAAW,EAAC,MAAM8D,GAAG,KAAKC,EAAC,GAAG,QAAQ,CAAC,GAEjEzB,IAAc,CAAA,GACTd,IAAI,GAAGA,IAAIwC,EAAM,QAAQ,EAAExC,GAAG;AACrC,cAAIgB,IAAMwB,EAAMxC,CAAC,EAAE;AACfgB,eAAKF,EAAY,KAAKE,CAAG;QAAA;AAE3BF,UAAY,UAAQqB,EAAc3D,GAAIsC,GAAa,CAAC;MAAA;IACzD;AAZQjC,MAAAe,GAAA,aAAA,GAcTxB,EAAW,aAAa,QAAQ,OAAO,SAASI,GAAIiE,GAAKC,GAAK;AAU5D,UATIA,KAAOA,KAAOtE,EAAW,SAC3ByB,EAAWrB,CAAE,GACTA,EAAG,MAAM,KAAK,QAAQ,iBAAiB,SACzCA,EAAG,IAAI,UAAU0D,CAAQ,GAC3B9D,EAAW,IAAII,EAAG,kBAAmB,GAAE,aAAaA,EAAG,MAAM,KAAK,WAAW,GAC7E,aAAaA,EAAG,MAAM,KAAK,OAAO,GAClC,OAAOA,EAAG,MAAM,OAGdiE,GAAK;AAEP,iBADIE,IAAUnE,EAAG,UAAU,SAAS,GAAGoE,IAAgB,OAC9C,IAAI,GAAG,IAAID,EAAQ,QAAQ,EAAE;AAAOA,YAAQ,CAAC,KAAKtE,MAAWuE,IAAgB;AACtF,YAAI9C,IAAQtB,EAAG,MAAM,OAAO,IAAIe,EAAUf,GAAIiE,GAAKG,CAAa;AAC5D9C,UAAM,QAAQ,gBAChBtB,EAAG,GAAG,UAAU0D,CAAQ,GACtBpC,EAAM,QAAQ,YAAY,SAASA,EAAM,QAAQ,YAAY,YAC/D1B,EAAW,GAAGI,EAAG,kBAAmB,GAAE,aAAasB,EAAM,WAAW,GAEtE2B,EAAajD,CAAE;MAAA;IAErB,CAAG,GAEDJ,EAAW,gBAAgB,eAAe,WAAW;AACnDqD,QAAa,IAAI;IACrB,CAAG;EACH,CAAC;;;;;;;;", "names": ["mod", "require$$0", "CodeMirror", "GUTTER_ID", "LINT_LINE_ID", "showTooltip", "cm", "content", "tt", "position", "e", "__name", "rm", "elt", "hideTooltip", "showTooltipFor", "node", "tooltip", "hide", "poll", "n", "LintState", "conf", "<PERSON><PERSON><PERSON>", "prop", "defaults", "onMouseOver", "clearMarks", "state", "clearErrorLines", "i", "line", "has", "make<PERSON><PERSON><PERSON>", "labels", "severity", "multiple", "tooltips", "marker", "inner", "getMaxSeverity", "a", "b", "groupByLine", "annotations", "lines", "ann", "annotationTooltip", "tip", "lintAsync", "getAnnotations", "id", "abort", "arg2", "updateLinting", "startLinting", "options", "issues", "annotationsNotSorted", "anns", "message", "item", "maxSeverity", "tipLabel", "onChange", "popupTooltips", "target", "box", "x", "y", "spans", "val", "old", "gutters", "has<PERSON>int<PERSON>utter"]}