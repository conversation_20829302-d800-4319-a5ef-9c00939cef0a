{"version": 3, "sources": ["../../../../../node_modules/codemirror/addon/fold/foldcode.js", "../../../../../node_modules/codemirror/addon/fold/foldgutter.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n\n  function doFold(cm, pos, options, force) {\n    if (options && options.call) {\n      var finder = options;\n      options = null;\n    } else {\n      var finder = getOption(cm, options, \"rangeFinder\");\n    }\n    if (typeof pos == \"number\") pos = CodeMirror.Pos(pos, 0);\n    var minSize = getOption(cm, options, \"minFoldSize\");\n\n    function getRange(allowFolded) {\n      var range = finder(cm, pos);\n      if (!range || range.to.line - range.from.line < minSize) return null;\n      if (force === \"fold\") return range;\n\n      var marks = cm.findMarksAt(range.from);\n      for (var i = 0; i < marks.length; ++i) {\n        if (marks[i].__isFold) {\n          if (!allowFolded) return null;\n          range.cleared = true;\n          marks[i].clear();\n        }\n      }\n      return range;\n    }\n\n    var range = getRange(true);\n    if (getOption(cm, options, \"scanUp\")) while (!range && pos.line > cm.firstLine()) {\n      pos = CodeMirror.Pos(pos.line - 1, 0);\n      range = getRange(false);\n    }\n    if (!range || range.cleared || force === \"unfold\") return;\n\n    var myWidget = makeWidget(cm, options, range);\n    CodeMirror.on(myWidget, \"mousedown\", function(e) {\n      myRange.clear();\n      CodeMirror.e_preventDefault(e);\n    });\n    var myRange = cm.markText(range.from, range.to, {\n      replacedWith: myWidget,\n      clearOnEnter: getOption(cm, options, \"clearOnEnter\"),\n      __isFold: true\n    });\n    myRange.on(\"clear\", function(from, to) {\n      CodeMirror.signal(cm, \"unfold\", cm, from, to);\n    });\n    CodeMirror.signal(cm, \"fold\", cm, range.from, range.to);\n  }\n\n  function makeWidget(cm, options, range) {\n    var widget = getOption(cm, options, \"widget\");\n\n    if (typeof widget == \"function\") {\n      widget = widget(range.from, range.to);\n    }\n\n    if (typeof widget == \"string\") {\n      var text = document.createTextNode(widget);\n      widget = document.createElement(\"span\");\n      widget.appendChild(text);\n      widget.className = \"CodeMirror-foldmarker\";\n    } else if (widget) {\n      widget = widget.cloneNode(true)\n    }\n    return widget;\n  }\n\n  // Clumsy backwards-compatible interface\n  CodeMirror.newFoldFunction = function(rangeFinder, widget) {\n    return function(cm, pos) { doFold(cm, pos, {rangeFinder: rangeFinder, widget: widget}); };\n  };\n\n  // New-style interface\n  CodeMirror.defineExtension(\"foldCode\", function(pos, options, force) {\n    doFold(this, pos, options, force);\n  });\n\n  CodeMirror.defineExtension(\"isFolded\", function(pos) {\n    var marks = this.findMarksAt(pos);\n    for (var i = 0; i < marks.length; ++i)\n      if (marks[i].__isFold) return true;\n  });\n\n  CodeMirror.commands.toggleFold = function(cm) {\n    cm.foldCode(cm.getCursor());\n  };\n  CodeMirror.commands.fold = function(cm) {\n    cm.foldCode(cm.getCursor(), null, \"fold\");\n  };\n  CodeMirror.commands.unfold = function(cm) {\n    cm.foldCode(cm.getCursor(), { scanUp: false }, \"unfold\");\n  };\n  CodeMirror.commands.foldAll = function(cm) {\n    cm.operation(function() {\n      for (var i = cm.firstLine(), e = cm.lastLine(); i <= e; i++)\n        cm.foldCode(CodeMirror.Pos(i, 0), { scanUp: false }, \"fold\");\n    });\n  };\n  CodeMirror.commands.unfoldAll = function(cm) {\n    cm.operation(function() {\n      for (var i = cm.firstLine(), e = cm.lastLine(); i <= e; i++)\n        cm.foldCode(CodeMirror.Pos(i, 0), { scanUp: false }, \"unfold\");\n    });\n  };\n\n  CodeMirror.registerHelper(\"fold\", \"combine\", function() {\n    var funcs = Array.prototype.slice.call(arguments, 0);\n    return function(cm, start) {\n      for (var i = 0; i < funcs.length; ++i) {\n        var found = funcs[i](cm, start);\n        if (found) return found;\n      }\n    };\n  });\n\n  CodeMirror.registerHelper(\"fold\", \"auto\", function(cm, start) {\n    var helpers = cm.getHelpers(start, \"fold\");\n    for (var i = 0; i < helpers.length; i++) {\n      var cur = helpers[i](cm, start);\n      if (cur) return cur;\n    }\n  });\n\n  var defaultOptions = {\n    rangeFinder: CodeMirror.fold.auto,\n    widget: \"\\u2194\",\n    minFoldSize: 0,\n    scanUp: false,\n    clearOnEnter: true\n  };\n\n  CodeMirror.defineOption(\"foldOptions\", null);\n\n  function getOption(cm, options, name) {\n    if (options && options[name] !== undefined)\n      return options[name];\n    var editorOptions = cm.options.foldOptions;\n    if (editorOptions && editorOptions[name] !== undefined)\n      return editorOptions[name];\n    return defaultOptions[name];\n  }\n\n  CodeMirror.defineExtension(\"foldOption\", function(options, name) {\n    return getOption(this, options, name);\n  });\n});\n", "// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"), require(\"./foldcode\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\", \"./foldcode\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n\n  CodeMirror.defineOption(\"foldGutter\", false, function(cm, val, old) {\n    if (old && old != CodeMirror.Init) {\n      cm.clearGutter(cm.state.foldGutter.options.gutter);\n      cm.state.foldGutter = null;\n      cm.off(\"gutterClick\", onGutterClick);\n      cm.off(\"changes\", onChange);\n      cm.off(\"viewportChange\", onViewportChange);\n      cm.off(\"fold\", onFold);\n      cm.off(\"unfold\", onFold);\n      cm.off(\"swapDoc\", onChange);\n    }\n    if (val) {\n      cm.state.foldGutter = new State(parseOptions(val));\n      updateInViewport(cm);\n      cm.on(\"gutterClick\", onGutterClick);\n      cm.on(\"changes\", onChange);\n      cm.on(\"viewportChange\", onViewportChange);\n      cm.on(\"fold\", onFold);\n      cm.on(\"unfold\", onFold);\n      cm.on(\"swapDoc\", onChange);\n    }\n  });\n\n  var Pos = CodeMirror.Pos;\n\n  function State(options) {\n    this.options = options;\n    this.from = this.to = 0;\n  }\n\n  function parseOptions(opts) {\n    if (opts === true) opts = {};\n    if (opts.gutter == null) opts.gutter = \"CodeMirror-foldgutter\";\n    if (opts.indicatorOpen == null) opts.indicatorOpen = \"CodeMirror-foldgutter-open\";\n    if (opts.indicatorFolded == null) opts.indicatorFolded = \"CodeMirror-foldgutter-folded\";\n    return opts;\n  }\n\n  function isFolded(cm, line) {\n    var marks = cm.findMarks(Pos(line, 0), Pos(line + 1, 0));\n    for (var i = 0; i < marks.length; ++i) {\n      if (marks[i].__isFold) {\n        var fromPos = marks[i].find(-1);\n        if (fromPos && fromPos.line === line)\n          return marks[i];\n      }\n    }\n  }\n\n  function marker(spec) {\n    if (typeof spec == \"string\") {\n      var elt = document.createElement(\"div\");\n      elt.className = spec + \" CodeMirror-guttermarker-subtle\";\n      return elt;\n    } else {\n      return spec.cloneNode(true);\n    }\n  }\n\n  function updateFoldInfo(cm, from, to) {\n    var opts = cm.state.foldGutter.options, cur = from - 1;\n    var minSize = cm.foldOption(opts, \"minFoldSize\");\n    var func = cm.foldOption(opts, \"rangeFinder\");\n    // we can reuse the built-in indicator element if its className matches the new state\n    var clsFolded = typeof opts.indicatorFolded == \"string\" && classTest(opts.indicatorFolded);\n    var clsOpen = typeof opts.indicatorOpen == \"string\" && classTest(opts.indicatorOpen);\n    cm.eachLine(from, to, function(line) {\n      ++cur;\n      var mark = null;\n      var old = line.gutterMarkers;\n      if (old) old = old[opts.gutter];\n      if (isFolded(cm, cur)) {\n        if (clsFolded && old && clsFolded.test(old.className)) return;\n        mark = marker(opts.indicatorFolded);\n      } else {\n        var pos = Pos(cur, 0);\n        var range = func && func(cm, pos);\n        if (range && range.to.line - range.from.line >= minSize) {\n          if (clsOpen && old && clsOpen.test(old.className)) return;\n          mark = marker(opts.indicatorOpen);\n        }\n      }\n      if (!mark && !old) return;\n      cm.setGutterMarker(line, opts.gutter, mark);\n    });\n  }\n\n  // copied from CodeMirror/src/util/dom.js\n  function classTest(cls) { return new RegExp(\"(^|\\\\s)\" + cls + \"(?:$|\\\\s)\\\\s*\") }\n\n  function updateInViewport(cm) {\n    var vp = cm.getViewport(), state = cm.state.foldGutter;\n    if (!state) return;\n    cm.operation(function() {\n      updateFoldInfo(cm, vp.from, vp.to);\n    });\n    state.from = vp.from; state.to = vp.to;\n  }\n\n  function onGutterClick(cm, line, gutter) {\n    var state = cm.state.foldGutter;\n    if (!state) return;\n    var opts = state.options;\n    if (gutter != opts.gutter) return;\n    var folded = isFolded(cm, line);\n    if (folded) folded.clear();\n    else cm.foldCode(Pos(line, 0), opts);\n  }\n\n  function onChange(cm) {\n    var state = cm.state.foldGutter;\n    if (!state) return;\n    var opts = state.options;\n    state.from = state.to = 0;\n    clearTimeout(state.changeUpdate);\n    state.changeUpdate = setTimeout(function() { updateInViewport(cm); }, opts.foldOnChangeTimeSpan || 600);\n  }\n\n  function onViewportChange(cm) {\n    var state = cm.state.foldGutter;\n    if (!state) return;\n    var opts = state.options;\n    clearTimeout(state.changeUpdate);\n    state.changeUpdate = setTimeout(function() {\n      var vp = cm.getViewport();\n      if (state.from == state.to || vp.from - state.to > 20 || state.from - vp.to > 20) {\n        updateInViewport(cm);\n      } else {\n        cm.operation(function() {\n          if (vp.from < state.from) {\n            updateFoldInfo(cm, vp.from, state.from);\n            state.from = vp.from;\n          }\n          if (vp.to > state.to) {\n            updateFoldInfo(cm, state.to, vp.to);\n            state.to = vp.to;\n          }\n        });\n      }\n    }, opts.updateViewportTimeSpan || 400);\n  }\n\n  function onFold(cm, from) {\n    var state = cm.state.foldGutter;\n    if (!state) return;\n    var line = from.line;\n    if (line >= state.from && line < state.to)\n      updateFoldInfo(cm, line, line + 1);\n  }\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,KAAC,SAASA,GAAK;AAEXA,QAAIC,GAA+B,CAAA;IAKtC,GAAE,SAASC,GAAY;AAGtB,eAASC,EAAOC,GAAIC,GAAKC,GAASC,GAAO;AACvC,YAAID,KAAWA,EAAQ,MAAM;AAC3B,cAAIE,IAASF;AACbA,cAAU;QAAA;AAEV,cAAIE,IAASC,EAAUL,GAAIE,GAAS,aAAa;AAE/C,eAAOD,KAAO,aAAUA,IAAMH,EAAW,IAAIG,GAAK,CAAC;AACvD,YAAIK,IAAUD,EAAUL,GAAIE,GAAS,aAAa;AAElD,iBAASK,EAASC,GAAa;AAC7B,cAAIC,IAAQL,EAAOJ,GAAIC,CAAG;AAC1B,cAAI,CAACQ,KAASA,EAAM,GAAG,OAAOA,EAAM,KAAK,OAAOH;AAAS,mBAAO;AAChE,cAAIH,MAAU;AAAQ,mBAAOM;AAG7B,mBADIC,IAAQV,EAAG,YAAYS,EAAM,IAAI,GAC5BE,IAAI,GAAGA,IAAID,EAAM,QAAQ,EAAEC;AAClC,gBAAID,EAAMC,CAAC,EAAE,UAAU;AACrB,kBAAI,CAACH;AAAa,uBAAO;AACzBC,gBAAM,UAAU,MAChBC,EAAMC,CAAC,EAAE,MAAA;;AAGb,iBAAOF;QACR;AAdQG,UAAAL,GAAA,UAAA;AAgBT,YAAIE,IAAQF,EAAS,IAAI;AACzB,YAAIF,EAAUL,GAAIE,GAAS,QAAQ;AAAG,iBAAO,CAACO,KAASR,EAAI,OAAOD,EAAG,UAAS;AAC5EC,gBAAMH,EAAW,IAAIG,EAAI,OAAO,GAAG,CAAC,GACpCQ,IAAQF,EAAS,KAAK;AAExB,YAAI,EAAA,CAACE,KAASA,EAAM,WAAWN,MAAU,WAEzC;AAAA,cAAIU,IAAWC,EAAWd,GAAIE,GAASO,CAAK;AAC5CX,YAAW,GAAGe,GAAU,aAAa,SAASE,GAAG;AAC/CC,cAAQ,MAAK,GACblB,EAAW,iBAAiBiB,CAAC;UACnC,CAAK;AACD,cAAIC,IAAUhB,EAAG,SAASS,EAAM,MAAMA,EAAM,IAAI;YAC9C,cAAcI;YACd,cAAcR,EAAUL,GAAIE,GAAS,cAAc;YACnD,UAAU;UAChB,CAAK;AACDc,YAAQ,GAAG,SAAS,SAASC,GAAMC,GAAI;AACrCpB,cAAW,OAAOE,GAAI,UAAUA,GAAIiB,GAAMC,CAAE;UAClD,CAAK,GACDpB,EAAW,OAAOE,GAAI,QAAQA,GAAIS,EAAM,MAAMA,EAAM,EAAE;QAAA;MACvD;AA/CQG,QAAAb,GAAA,QAAA;AAiDT,eAASe,EAAWd,GAAIE,GAASO,GAAO;AACtC,YAAIU,IAASd,EAAUL,GAAIE,GAAS,QAAQ;AAM5C,YAJI,OAAOiB,KAAU,eACnBA,IAASA,EAAOV,EAAM,MAAMA,EAAM,EAAE,IAGlC,OAAOU,KAAU,UAAU;AAC7B,cAAIC,IAAO,SAAS,eAAeD,CAAM;AACzCA,cAAS,SAAS,cAAc,MAAM,GACtCA,EAAO,YAAYC,CAAI,GACvBD,EAAO,YAAY;QAAA;AACVA,gBACTA,IAASA,EAAO,UAAU,IAAI;AAEhC,eAAOA;MACR;AAhBQP,QAAAE,GAAA,YAAA,GAmBThB,EAAW,kBAAkB,SAASuB,GAAaF,GAAQ;AACzD,eAAO,SAASnB,GAAIC,GAAK;AAAEF,YAAOC,GAAIC,GAAK,EAAC,aAAaoB,GAAa,QAAQF,EAAM,CAAC;QAAE;MAC3F,GAGErB,EAAW,gBAAgB,YAAY,SAASG,GAAKC,GAASC,GAAO;AACnEJ,UAAO,MAAME,GAAKC,GAASC,CAAK;MACpC,CAAG,GAEDL,EAAW,gBAAgB,YAAY,SAASG,GAAK;AAEnD,iBADIS,IAAQ,KAAK,YAAYT,CAAG,GACvBU,IAAI,GAAGA,IAAID,EAAM,QAAQ,EAAEC;AAClC,cAAID,EAAMC,CAAC,EAAE;AAAU,mBAAO;MACpC,CAAG,GAEDb,EAAW,SAAS,aAAa,SAASE,GAAI;AAC5CA,UAAG,SAASA,EAAG,UAAW,CAAA;MAC9B,GACEF,EAAW,SAAS,OAAO,SAASE,GAAI;AACtCA,UAAG,SAASA,EAAG,UAAS,GAAI,MAAM,MAAM;MAC5C,GACEF,EAAW,SAAS,SAAS,SAASE,GAAI;AACxCA,UAAG,SAASA,EAAG,UAAS,GAAI,EAAE,QAAQ,MAAA,GAAS,QAAQ;MAC3D,GACEF,EAAW,SAAS,UAAU,SAASE,GAAI;AACzCA,UAAG,UAAU,WAAW;AACtB,mBAASW,IAAIX,EAAG,UAAA,GAAae,IAAIf,EAAG,SAAU,GAAEW,KAAKI,GAAGJ;AACtDX,cAAG,SAASF,EAAW,IAAIa,GAAG,CAAC,GAAG,EAAE,QAAQ,MAAO,GAAE,MAAM;QACnE,CAAK;MACL,GACEb,EAAW,SAAS,YAAY,SAASE,GAAI;AAC3CA,UAAG,UAAU,WAAW;AACtB,mBAASW,IAAIX,EAAG,UAAA,GAAae,IAAIf,EAAG,SAAU,GAAEW,KAAKI,GAAGJ;AACtDX,cAAG,SAASF,EAAW,IAAIa,GAAG,CAAC,GAAG,EAAE,QAAQ,MAAO,GAAE,QAAQ;QACrE,CAAK;MACL,GAEEb,EAAW,eAAe,QAAQ,WAAW,WAAW;AACtD,YAAIwB,IAAQ,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC;AACnD,eAAO,SAAStB,GAAIuB,GAAO;AACzB,mBAASZ,IAAI,GAAGA,IAAIW,EAAM,QAAQ,EAAEX,GAAG;AACrC,gBAAIa,IAAQF,EAAMX,CAAC,EAAEX,GAAIuB,CAAK;AAC9B,gBAAIC;AAAO,qBAAOA;UAAA;QAE1B;MACA,CAAG,GAED1B,EAAW,eAAe,QAAQ,QAAQ,SAASE,GAAIuB,GAAO;AAE5D,iBADIE,IAAUzB,EAAG,WAAWuB,GAAO,MAAM,GAChCZ,IAAI,GAAGA,IAAIc,EAAQ,QAAQd,KAAK;AACvC,cAAIe,IAAMD,EAAQd,CAAC,EAAEX,GAAIuB,CAAK;AAC9B,cAAIG;AAAK,mBAAOA;QAAA;MAEtB,CAAG;AAED,UAAIC,IAAiB;QACnB,aAAa7B,EAAW,KAAK;QAC7B,QAAQ;QACR,aAAa;QACb,QAAQ;QACR,cAAc;MAClB;AAEEA,QAAW,aAAa,eAAe,IAAI;AAE3C,eAASO,EAAUL,GAAIE,GAAS0B,GAAM;AACpC,YAAI1B,KAAWA,EAAQ0B,CAAI,MAAM;AAC/B,iBAAO1B,EAAQ0B,CAAI;AACrB,YAAIC,IAAgB7B,EAAG,QAAQ;AAC/B,eAAI6B,KAAiBA,EAAcD,CAAI,MAAM,SACpCC,EAAcD,CAAI,IACpBD,EAAeC,CAAI;MAC3B;AAPQhB,QAAAP,GAAA,WAAA,GASTP,EAAW,gBAAgB,cAAc,SAASI,GAAS0B,GAAM;AAC/D,eAAOvB,EAAU,MAAMH,GAAS0B,CAAI;MACxC,CAAG;IACH,CAAC;EAAA,EAAA,IAAA,EAAA;;;;AC3JD,GAAC,SAAShC,GAAK;AAEXA,MAAIC,GAAA,GAAiCiC,EAAA,CAAqB;EAK7D,GAAE,SAAShC,GAAY;AAGtBA,MAAW,aAAa,cAAc,OAAO,SAASE,GAAI+B,GAAKC,GAAK;AAC9DA,WAAOA,KAAOlC,EAAW,SAC3BE,EAAG,YAAYA,EAAG,MAAM,WAAW,QAAQ,MAAM,GACjDA,EAAG,MAAM,aAAa,MACtBA,EAAG,IAAI,eAAeiC,CAAa,GACnCjC,EAAG,IAAI,WAAWkC,CAAQ,GAC1BlC,EAAG,IAAI,kBAAkBmC,CAAgB,GACzCnC,EAAG,IAAI,QAAQoC,CAAM,GACrBpC,EAAG,IAAI,UAAUoC,CAAM,GACvBpC,EAAG,IAAI,WAAWkC,CAAQ,IAExBH,MACF/B,EAAG,MAAM,aAAa,IAAIqC,EAAMC,EAAaP,CAAG,CAAC,GACjDQ,EAAiBvC,CAAE,GACnBA,EAAG,GAAG,eAAeiC,CAAa,GAClCjC,EAAG,GAAG,WAAWkC,CAAQ,GACzBlC,EAAG,GAAG,kBAAkBmC,CAAgB,GACxCnC,EAAG,GAAG,QAAQoC,CAAM,GACpBpC,EAAG,GAAG,UAAUoC,CAAM,GACtBpC,EAAG,GAAG,WAAWkC,CAAQ;IAE/B,CAAG;AAED,QAAIM,IAAM1C,EAAW;AAErB,aAASuC,EAAMnC,GAAS;AACtB,WAAK,UAAUA,GACf,KAAK,OAAO,KAAK,KAAK;IACvB;AAHQU,MAAAyB,GAAA,OAAA;AAKT,aAASC,EAAaG,GAAM;AAC1B,aAAIA,MAAS,SAAMA,IAAO,CAAA,IACtBA,EAAK,UAAU,SAAMA,EAAK,SAAS,0BACnCA,EAAK,iBAAiB,SAAMA,EAAK,gBAAgB,+BACjDA,EAAK,mBAAmB,SAAMA,EAAK,kBAAkB,iCAClDA;IACR;AANQ7B,MAAA0B,GAAA,cAAA;AAQT,aAASI,EAAS1C,GAAI2C,GAAM;AAE1B,eADIjC,IAAQV,EAAG,UAAUwC,EAAIG,GAAM,CAAC,GAAGH,EAAIG,IAAO,GAAG,CAAC,CAAC,GAC9ChC,IAAI,GAAGA,IAAID,EAAM,QAAQ,EAAEC;AAClC,YAAID,EAAMC,CAAC,EAAE,UAAU;AACrB,cAAIiC,IAAUlC,EAAMC,CAAC,EAAE,KAAK,EAAE;AAC9B,cAAIiC,KAAWA,EAAQ,SAASD;AAC9B,mBAAOjC,EAAMC,CAAC;QAAA;IAGrB;AATQC,MAAA8B,GAAA,UAAA;AAWT,aAASG,EAAOC,GAAM;AACpB,UAAI,OAAOA,KAAQ,UAAU;AAC3B,YAAIC,IAAM,SAAS,cAAc,KAAK;AACtC,eAAAA,EAAI,YAAYD,IAAO,mCAChBC;MAAA;AAEP,eAAOD,EAAK,UAAU,IAAI;IAE7B;AARQlC,MAAAiC,GAAA,QAAA;AAUT,aAASG,EAAehD,GAAIiB,GAAMC,GAAI;AACpC,UAAIuB,IAAOzC,EAAG,MAAM,WAAW,SAAS0B,IAAMT,IAAO,GACjDX,IAAUN,EAAG,WAAWyC,GAAM,aAAa,GAC3CQ,IAAOjD,EAAG,WAAWyC,GAAM,aAAa,GAExCS,IAAY,OAAOT,EAAK,mBAAmB,YAAYU,EAAUV,EAAK,eAAe,GACrFW,IAAU,OAAOX,EAAK,iBAAiB,YAAYU,EAAUV,EAAK,aAAa;AACnFzC,QAAG,SAASiB,GAAMC,GAAI,SAASyB,GAAM;AACnC,UAAEjB;AACF,YAAI2B,IAAO,MACPrB,IAAMW,EAAK;AAEf,YADIX,MAAKA,IAAMA,EAAIS,EAAK,MAAM,IAC1BC,EAAS1C,GAAI0B,CAAG,GAAG;AACrB,cAAIwB,KAAalB,KAAOkB,EAAU,KAAKlB,EAAI,SAAS;AAAG;AACvDqB,cAAOR,EAAOJ,EAAK,eAAe;QAAA,OAC7B;AACL,cAAIxC,IAAMuC,EAAId,GAAK,CAAC,GAChBjB,IAAQwC,KAAQA,EAAKjD,GAAIC,CAAG;AAChC,cAAIQ,KAASA,EAAM,GAAG,OAAOA,EAAM,KAAK,QAAQH,GAAS;AACvD,gBAAI8C,KAAWpB,KAAOoB,EAAQ,KAAKpB,EAAI,SAAS;AAAG;AACnDqB,gBAAOR,EAAOJ,EAAK,aAAa;UAAA;QAAA;AAGhC,SAACY,KAAQ,CAACrB,KACdhC,EAAG,gBAAgB2C,GAAMF,EAAK,QAAQY,CAAI;MAChD,CAAK;IACF;AA1BQzC,MAAAoC,GAAA,gBAAA;AA6BT,aAASG,EAAUG,GAAK;AAAE,aAAO,IAAI,OAAO,YAAYA,IAAM,eAAe;IAAG;AAAvE1C,MAAAuC,GAAA,WAAA;AAET,aAASZ,EAAiBvC,GAAI;AAC5B,UAAIuD,IAAKvD,EAAG,YAAW,GAAIwD,IAAQxD,EAAG,MAAM;AACvCwD,YACLxD,EAAG,UAAU,WAAW;AACtBgD,UAAehD,GAAIuD,EAAG,MAAMA,EAAG,EAAE;MACvC,CAAK,GACDC,EAAM,OAAOD,EAAG,MAAMC,EAAM,KAAKD,EAAG;IACrC;AAPQ3C,MAAA2B,GAAA,kBAAA;AAST,aAASN,EAAcjC,GAAI2C,GAAMc,GAAQ;AACvC,UAAID,IAAQxD,EAAG,MAAM;AACrB,UAAKwD,GACL;AAAA,YAAIf,IAAOe,EAAM;AACjB,YAAIC,KAAUhB,EAAK,QACnB;AAAA,cAAIiB,IAAShB,EAAS1C,GAAI2C,CAAI;AAC1Be,cAAQA,EAAO,MAAA,IACd1D,EAAG,SAASwC,EAAIG,GAAM,CAAC,GAAGF,CAAI;QAAA;MAAA;IACpC;AARQ7B,MAAAqB,GAAA,eAAA;AAUT,aAASC,EAASlC,GAAI;AACpB,UAAIwD,IAAQxD,EAAG,MAAM;AACrB,UAAKwD,GACL;AAAA,YAAIf,IAAOe,EAAM;AACjBA,UAAM,OAAOA,EAAM,KAAK,GACxB,aAAaA,EAAM,YAAY,GAC/BA,EAAM,eAAe,WAAW,WAAW;AAAEjB,YAAiBvC,CAAE;QAAA,GAAMyC,EAAK,wBAAwB,GAAG;MAAA;IACvG;AAPQ7B,MAAAsB,GAAA,UAAA;AAST,aAASC,EAAiBnC,GAAI;AAC5B,UAAIwD,IAAQxD,EAAG,MAAM;AACrB,UAAKwD,GACL;AAAA,YAAIf,IAAOe,EAAM;AACjB,qBAAaA,EAAM,YAAY,GAC/BA,EAAM,eAAe,WAAW,WAAW;AACzC,cAAID,IAAKvD,EAAG,YAAA;AACRwD,YAAM,QAAQA,EAAM,MAAMD,EAAG,OAAOC,EAAM,KAAK,MAAMA,EAAM,OAAOD,EAAG,KAAK,KAC5EhB,EAAiBvC,CAAE,IAEnBA,EAAG,UAAU,WAAW;AAClBuD,cAAG,OAAOC,EAAM,SAClBR,EAAehD,GAAIuD,EAAG,MAAMC,EAAM,IAAI,GACtCA,EAAM,OAAOD,EAAG,OAEdA,EAAG,KAAKC,EAAM,OAChBR,EAAehD,GAAIwD,EAAM,IAAID,EAAG,EAAE,GAClCC,EAAM,KAAKD,EAAG;UAE1B,CAAS;QAET,GAAOd,EAAK,0BAA0B,GAAG;MAAA;IACtC;AAtBQ7B,MAAAuB,GAAA,kBAAA;AAwBT,aAASC,EAAOpC,GAAIiB,GAAM;AACxB,UAAIuC,IAAQxD,EAAG,MAAM;AACrB,UAAKwD,GACL;AAAA,YAAIb,IAAO1B,EAAK;AACZ0B,aAAQa,EAAM,QAAQb,IAAOa,EAAM,MACrCR,EAAehD,GAAI2C,GAAMA,IAAO,CAAC;MAAA;IACpC;AANQ/B,MAAAwB,GAAA,QAAA;EAOX,CAAC;;;;;;;;", "names": ["mod", "require$$0", "CodeMirror", "do<PERSON><PERSON>", "cm", "pos", "options", "force", "finder", "getOption", "minSize", "getRange", "allowFolded", "range", "marks", "i", "__name", "myWidget", "makeWidget", "e", "myRange", "from", "to", "widget", "text", "rangeFinder", "funcs", "start", "found", "helpers", "cur", "defaultOptions", "name", "editorOptions", "require$$1", "val", "old", "onGutterClick", "onChange", "onViewportChange", "onFold", "State", "parseOptions", "updateInViewport", "Pos", "opts", "isFolded", "line", "fromPos", "marker", "spec", "elt", "updateFoldInfo", "func", "clsFolded", "classTest", "clsOpen", "mark", "cls", "vp", "state", "gutter", "folded"]}