{"version": 3, "sources": ["../../../../../@graphiql/codemirror-graphql/esm/variables/mode.js"], "sourcesContent": ["import CodeMirror from 'codemirror';\nimport { list, t, onlineParser, opt, p, } from 'graphql-language-service';\nimport indent from '../utils/mode-indent';\nCodeMirror.defineMode('graphql-variables', config => {\n    const parser = onlineParser({\n        eatWhitespace: stream => stream.eatSpace(),\n        lexRules: LexRules,\n        parseRules: ParseRules,\n        editorConfig: { tabSize: config.tabSize },\n    });\n    return {\n        config,\n        startState: parser.startState,\n        token: parser.token,\n        indent,\n        electricInput: /^\\s*[}\\]]/,\n        fold: 'brace',\n        closeBrackets: {\n            pairs: '[]{}\"\"',\n            explode: '[]{}',\n        },\n    };\n});\nconst LexRules = {\n    Punctuation: /^\\[|]|\\{|\\}|:|,/,\n    Number: /^-?(?:0|(?:[1-9][0-9]*))(?:\\.[0-9]*)?(?:[eE][+-]?[0-9]+)?/,\n    String: /^\"(?:[^\"\\\\]|\\\\(?:\"|\\/|\\\\|b|f|n|r|t|u[0-9a-fA-F]{4}))*\"?/,\n    Keyword: /^true|false|null/,\n};\nconst ParseRules = {\n    Document: [p('{'), list('Variable', opt(p(','))), p('}')],\n    Variable: [namedKey('variable'), p(':'), 'Value'],\n    Value(token) {\n        switch (token.kind) {\n            case 'Number':\n                return 'NumberValue';\n            case 'String':\n                return 'StringValue';\n            case 'Punctuation':\n                switch (token.value) {\n                    case '[':\n                        return 'ListValue';\n                    case '{':\n                        return 'ObjectValue';\n                }\n                return null;\n            case 'Keyword':\n                switch (token.value) {\n                    case 'true':\n                    case 'false':\n                        return 'BooleanValue';\n                    case 'null':\n                        return 'NullValue';\n                }\n                return null;\n        }\n    },\n    NumberValue: [t('Number', 'number')],\n    StringValue: [t('String', 'string')],\n    BooleanValue: [t('Keyword', 'builtin')],\n    NullValue: [t('Keyword', 'keyword')],\n    ListValue: [p('['), list('Value', opt(p(','))), p(']')],\n    ObjectValue: [p('{'), list('ObjectField', opt(p(','))), p('}')],\n    ObjectField: [namedKey('attribute'), p(':'), 'Value'],\n};\nfunction namedKey(style) {\n    return {\n        style,\n        match: (token) => token.kind === 'String',\n        update(state, token) {\n            state.name = token.value.slice(1, -1);\n        },\n    };\n}\n//# sourceMappingURL=mode.js.map"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAGAA,EAAW,WAAW,qBAAqB,CAAAC,OAAU;AACjD,QAAMC,KAASC,aAAa;IACxB,eAAe,CAAAC,MAAUA,EAAO,SAAU;IAC1C,UAAUC;IACV,YAAYC;IACZ,cAAc,EAAE,SAASL,GAAO,QAAS;EACjD,CAAK;AACD,SAAO;IACH,QAAAA;IACA,YAAYC,GAAO;IACnB,OAAOA,GAAO;IACd,QAAAK;IACA,eAAe;IACf,MAAM;IACN,eAAe;MACX,OAAO;MACP,SAAS;IACZ;EACT;AACA,CAAC;AACD,IAAMF,IAAW;EACb,aAAa;EACb,QAAQ;EACR,QAAQ;EACR,SAAS;AACb;AALA,IAMMC,IAAa;EACf,UAAU,CAACE,EAAE,GAAG,GAAGC,KAAK,YAAYC,IAAIF,EAAE,GAAG,CAAC,CAAC,GAAGA,EAAE,GAAG,CAAC;EACxD,UAAU,CAACG,EAAS,UAAU,GAAGH,EAAE,GAAG,GAAG,OAAO;EAChD,MAAMI,IAAO;AACT,YAAQA,GAAM,MAAI;MACd,KAAK;AACD,eAAO;MACX,KAAK;AACD,eAAO;MACX,KAAK;AACD,gBAAQA,GAAM,OAAK;UACf,KAAK;AACD,mBAAO;UACX,KAAK;AACD,mBAAO;QACd;AACD,eAAO;MACX,KAAK;AACD,gBAAQA,GAAM,OAAK;UACf,KAAK;UACL,KAAK;AACD,mBAAO;UACX,KAAK;AACD,mBAAO;QACd;AACD,eAAO;IACd;EACJ;EACD,aAAa,CAACC,EAAE,UAAU,QAAQ,CAAC;EACnC,aAAa,CAACA,EAAE,UAAU,QAAQ,CAAC;EACnC,cAAc,CAACA,EAAE,WAAW,SAAS,CAAC;EACtC,WAAW,CAACA,EAAE,WAAW,SAAS,CAAC;EACnC,WAAW,CAACL,EAAE,GAAG,GAAGC,KAAK,SAASC,IAAIF,EAAE,GAAG,CAAC,CAAC,GAAGA,EAAE,GAAG,CAAC;EACtD,aAAa,CAACA,EAAE,GAAG,GAAGC,KAAK,eAAeC,IAAIF,EAAE,GAAG,CAAC,CAAC,GAAGA,EAAE,GAAG,CAAC;EAC9D,aAAa,CAACG,EAAS,WAAW,GAAGH,EAAE,GAAG,GAAG,OAAO;AACxD;AACA,SAASG,EAASG,IAAO;AACrB,SAAO;IACH,OAAAA;IACA,OAAO,CAACF,OAAUA,GAAM,SAAS;IACjC,OAAOG,IAAOH,GAAO;AACjBG,MAAAA,GAAM,OAAOH,EAAM,MAAM,MAAM,GAAG,EAAE;IACvC;EACT;AACA;AARSI,EAAAL,GAAA,UAAA;", "names": ["CodeMirror", "config", "parser", "onlineParser", "stream", "LexRules", "ParseRules", "indent", "p", "list", "opt", "<PERSON><PERSON><PERSON>", "token", "t", "style", "state", "__name"]}