{"version": 3, "sources": ["../../../../../@graphiql/codemirror-graphql/esm/utils/forEachState.js"], "sourcesContent": ["export default function forEachState(stack, fn) {\n    const reverseStateStack = [];\n    let state = stack;\n    while (state === null || state === void 0 ? void 0 : state.kind) {\n        reverseStateStack.push(state);\n        state = state.prevState;\n    }\n    for (let i = reverseStateStack.length - 1; i >= 0; i--) {\n        fn(reverseStateStack[i]);\n    }\n}\n//# sourceMappingURL=forEachState.js.map"], "mappings": ";;;AAAe,SAASA,EAAaC,GAAOC,GAAI;AAC5C,QAAMC,IAAoB,CAAA;AAC1B,MAAIC,IAAQH;AACZ,SAAOG,KAAU,QAAoCA,EAAM;AACvDD,MAAkB,KAAKC,CAAK,GAC5BA,IAAQA,EAAM;AAElB,WAAS,IAAID,EAAkB,SAAS,GAAG,KAAK,GAAG;AAC/CD,MAAGC,EAAkB,CAAC,CAAC;AAE/B;AAVwBE,EAAAL,GAAA,cAAA;", "names": ["forEachState", "stack", "fn", "reverseStateStack", "state", "__name"]}