{"version": 3, "sources": ["../../../../../node_modules/codemirror/mode/javascript/javascript.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n\"use strict\";\n\nCodeMirror.defineMode(\"javascript\", function(config, parserConfig) {\n  var indentUnit = config.indentUnit;\n  var statementIndent = parserConfig.statementIndent;\n  var jsonldMode = parserConfig.jsonld;\n  var jsonMode = parserConfig.json || jsonldMode;\n  var trackScope = parserConfig.trackScope !== false\n  var isTS = parserConfig.typescript;\n  var wordRE = parserConfig.wordCharacters || /[\\w$\\xa1-\\uffff]/;\n\n  // Tokenizer\n\n  var keywords = function(){\n    function kw(type) {return {type: type, style: \"keyword\"};}\n    var A = kw(\"keyword a\"), B = kw(\"keyword b\"), C = kw(\"keyword c\"), D = kw(\"keyword d\");\n    var operator = kw(\"operator\"), atom = {type: \"atom\", style: \"atom\"};\n\n    return {\n      \"if\": kw(\"if\"), \"while\": A, \"with\": A, \"else\": B, \"do\": B, \"try\": B, \"finally\": B,\n      \"return\": D, \"break\": D, \"continue\": D, \"new\": kw(\"new\"), \"delete\": C, \"void\": C, \"throw\": C,\n      \"debugger\": kw(\"debugger\"), \"var\": kw(\"var\"), \"const\": kw(\"var\"), \"let\": kw(\"var\"),\n      \"function\": kw(\"function\"), \"catch\": kw(\"catch\"),\n      \"for\": kw(\"for\"), \"switch\": kw(\"switch\"), \"case\": kw(\"case\"), \"default\": kw(\"default\"),\n      \"in\": operator, \"typeof\": operator, \"instanceof\": operator,\n      \"true\": atom, \"false\": atom, \"null\": atom, \"undefined\": atom, \"NaN\": atom, \"Infinity\": atom,\n      \"this\": kw(\"this\"), \"class\": kw(\"class\"), \"super\": kw(\"atom\"),\n      \"yield\": C, \"export\": kw(\"export\"), \"import\": kw(\"import\"), \"extends\": C,\n      \"await\": C\n    };\n  }();\n\n  var isOperatorChar = /[+\\-*&%=<>!?|~^@]/;\n  var isJsonldKeyword = /^@(context|id|value|language|type|container|list|set|reverse|index|base|vocab|graph)\"/;\n\n  function readRegexp(stream) {\n    var escaped = false, next, inSet = false;\n    while ((next = stream.next()) != null) {\n      if (!escaped) {\n        if (next == \"/\" && !inSet) return;\n        if (next == \"[\") inSet = true;\n        else if (inSet && next == \"]\") inSet = false;\n      }\n      escaped = !escaped && next == \"\\\\\";\n    }\n  }\n\n  // Used as scratch variables to communicate multiple values without\n  // consing up tons of objects.\n  var type, content;\n  function ret(tp, style, cont) {\n    type = tp; content = cont;\n    return style;\n  }\n  function tokenBase(stream, state) {\n    var ch = stream.next();\n    if (ch == '\"' || ch == \"'\") {\n      state.tokenize = tokenString(ch);\n      return state.tokenize(stream, state);\n    } else if (ch == \".\" && stream.match(/^\\d[\\d_]*(?:[eE][+\\-]?[\\d_]+)?/)) {\n      return ret(\"number\", \"number\");\n    } else if (ch == \".\" && stream.match(\"..\")) {\n      return ret(\"spread\", \"meta\");\n    } else if (/[\\[\\]{}\\(\\),;\\:\\.]/.test(ch)) {\n      return ret(ch);\n    } else if (ch == \"=\" && stream.eat(\">\")) {\n      return ret(\"=>\", \"operator\");\n    } else if (ch == \"0\" && stream.match(/^(?:x[\\dA-Fa-f_]+|o[0-7_]+|b[01_]+)n?/)) {\n      return ret(\"number\", \"number\");\n    } else if (/\\d/.test(ch)) {\n      stream.match(/^[\\d_]*(?:n|(?:\\.[\\d_]*)?(?:[eE][+\\-]?[\\d_]+)?)?/);\n      return ret(\"number\", \"number\");\n    } else if (ch == \"/\") {\n      if (stream.eat(\"*\")) {\n        state.tokenize = tokenComment;\n        return tokenComment(stream, state);\n      } else if (stream.eat(\"/\")) {\n        stream.skipToEnd();\n        return ret(\"comment\", \"comment\");\n      } else if (expressionAllowed(stream, state, 1)) {\n        readRegexp(stream);\n        stream.match(/^\\b(([gimyus])(?![gimyus]*\\2))+\\b/);\n        return ret(\"regexp\", \"string-2\");\n      } else {\n        stream.eat(\"=\");\n        return ret(\"operator\", \"operator\", stream.current());\n      }\n    } else if (ch == \"`\") {\n      state.tokenize = tokenQuasi;\n      return tokenQuasi(stream, state);\n    } else if (ch == \"#\" && stream.peek() == \"!\") {\n      stream.skipToEnd();\n      return ret(\"meta\", \"meta\");\n    } else if (ch == \"#\" && stream.eatWhile(wordRE)) {\n      return ret(\"variable\", \"property\")\n    } else if (ch == \"<\" && stream.match(\"!--\") ||\n               (ch == \"-\" && stream.match(\"->\") && !/\\S/.test(stream.string.slice(0, stream.start)))) {\n      stream.skipToEnd()\n      return ret(\"comment\", \"comment\")\n    } else if (isOperatorChar.test(ch)) {\n      if (ch != \">\" || !state.lexical || state.lexical.type != \">\") {\n        if (stream.eat(\"=\")) {\n          if (ch == \"!\" || ch == \"=\") stream.eat(\"=\")\n        } else if (/[<>*+\\-|&?]/.test(ch)) {\n          stream.eat(ch)\n          if (ch == \">\") stream.eat(ch)\n        }\n      }\n      if (ch == \"?\" && stream.eat(\".\")) return ret(\".\")\n      return ret(\"operator\", \"operator\", stream.current());\n    } else if (wordRE.test(ch)) {\n      stream.eatWhile(wordRE);\n      var word = stream.current()\n      if (state.lastType != \".\") {\n        if (keywords.propertyIsEnumerable(word)) {\n          var kw = keywords[word]\n          return ret(kw.type, kw.style, word)\n        }\n        if (word == \"async\" && stream.match(/^(\\s|\\/\\*([^*]|\\*(?!\\/))*?\\*\\/)*[\\[\\(\\w]/, false))\n          return ret(\"async\", \"keyword\", word)\n      }\n      return ret(\"variable\", \"variable\", word)\n    }\n  }\n\n  function tokenString(quote) {\n    return function(stream, state) {\n      var escaped = false, next;\n      if (jsonldMode && stream.peek() == \"@\" && stream.match(isJsonldKeyword)){\n        state.tokenize = tokenBase;\n        return ret(\"jsonld-keyword\", \"meta\");\n      }\n      while ((next = stream.next()) != null) {\n        if (next == quote && !escaped) break;\n        escaped = !escaped && next == \"\\\\\";\n      }\n      if (!escaped) state.tokenize = tokenBase;\n      return ret(\"string\", \"string\");\n    };\n  }\n\n  function tokenComment(stream, state) {\n    var maybeEnd = false, ch;\n    while (ch = stream.next()) {\n      if (ch == \"/\" && maybeEnd) {\n        state.tokenize = tokenBase;\n        break;\n      }\n      maybeEnd = (ch == \"*\");\n    }\n    return ret(\"comment\", \"comment\");\n  }\n\n  function tokenQuasi(stream, state) {\n    var escaped = false, next;\n    while ((next = stream.next()) != null) {\n      if (!escaped && (next == \"`\" || next == \"$\" && stream.eat(\"{\"))) {\n        state.tokenize = tokenBase;\n        break;\n      }\n      escaped = !escaped && next == \"\\\\\";\n    }\n    return ret(\"quasi\", \"string-2\", stream.current());\n  }\n\n  var brackets = \"([{}])\";\n  // This is a crude lookahead trick to try and notice that we're\n  // parsing the argument patterns for a fat-arrow function before we\n  // actually hit the arrow token. It only works if the arrow is on\n  // the same line as the arguments and there's no strange noise\n  // (comments) in between. Fallback is to only notice when we hit the\n  // arrow, and not declare the arguments as locals for the arrow\n  // body.\n  function findFatArrow(stream, state) {\n    if (state.fatArrowAt) state.fatArrowAt = null;\n    var arrow = stream.string.indexOf(\"=>\", stream.start);\n    if (arrow < 0) return;\n\n    if (isTS) { // Try to skip TypeScript return type declarations after the arguments\n      var m = /:\\s*(?:\\w+(?:<[^>]*>|\\[\\])?|\\{[^}]*\\})\\s*$/.exec(stream.string.slice(stream.start, arrow))\n      if (m) arrow = m.index\n    }\n\n    var depth = 0, sawSomething = false;\n    for (var pos = arrow - 1; pos >= 0; --pos) {\n      var ch = stream.string.charAt(pos);\n      var bracket = brackets.indexOf(ch);\n      if (bracket >= 0 && bracket < 3) {\n        if (!depth) { ++pos; break; }\n        if (--depth == 0) { if (ch == \"(\") sawSomething = true; break; }\n      } else if (bracket >= 3 && bracket < 6) {\n        ++depth;\n      } else if (wordRE.test(ch)) {\n        sawSomething = true;\n      } else if (/[\"'\\/`]/.test(ch)) {\n        for (;; --pos) {\n          if (pos == 0) return\n          var next = stream.string.charAt(pos - 1)\n          if (next == ch && stream.string.charAt(pos - 2) != \"\\\\\") { pos--; break }\n        }\n      } else if (sawSomething && !depth) {\n        ++pos;\n        break;\n      }\n    }\n    if (sawSomething && !depth) state.fatArrowAt = pos;\n  }\n\n  // Parser\n\n  var atomicTypes = {\"atom\": true, \"number\": true, \"variable\": true, \"string\": true,\n                     \"regexp\": true, \"this\": true, \"import\": true, \"jsonld-keyword\": true};\n\n  function JSLexical(indented, column, type, align, prev, info) {\n    this.indented = indented;\n    this.column = column;\n    this.type = type;\n    this.prev = prev;\n    this.info = info;\n    if (align != null) this.align = align;\n  }\n\n  function inScope(state, varname) {\n    if (!trackScope) return false\n    for (var v = state.localVars; v; v = v.next)\n      if (v.name == varname) return true;\n    for (var cx = state.context; cx; cx = cx.prev) {\n      for (var v = cx.vars; v; v = v.next)\n        if (v.name == varname) return true;\n    }\n  }\n\n  function parseJS(state, style, type, content, stream) {\n    var cc = state.cc;\n    // Communicate our context to the combinators.\n    // (Less wasteful than consing up a hundred closures on every call.)\n    cx.state = state; cx.stream = stream; cx.marked = null, cx.cc = cc; cx.style = style;\n\n    if (!state.lexical.hasOwnProperty(\"align\"))\n      state.lexical.align = true;\n\n    while(true) {\n      var combinator = cc.length ? cc.pop() : jsonMode ? expression : statement;\n      if (combinator(type, content)) {\n        while(cc.length && cc[cc.length - 1].lex)\n          cc.pop()();\n        if (cx.marked) return cx.marked;\n        if (type == \"variable\" && inScope(state, content)) return \"variable-2\";\n        return style;\n      }\n    }\n  }\n\n  // Combinator utils\n\n  var cx = {state: null, column: null, marked: null, cc: null};\n  function pass() {\n    for (var i = arguments.length - 1; i >= 0; i--) cx.cc.push(arguments[i]);\n  }\n  function cont() {\n    pass.apply(null, arguments);\n    return true;\n  }\n  function inList(name, list) {\n    for (var v = list; v; v = v.next) if (v.name == name) return true\n    return false;\n  }\n  function register(varname) {\n    var state = cx.state;\n    cx.marked = \"def\";\n    if (!trackScope) return\n    if (state.context) {\n      if (state.lexical.info == \"var\" && state.context && state.context.block) {\n        // FIXME function decls are also not block scoped\n        var newContext = registerVarScoped(varname, state.context)\n        if (newContext != null) {\n          state.context = newContext\n          return\n        }\n      } else if (!inList(varname, state.localVars)) {\n        state.localVars = new Var(varname, state.localVars)\n        return\n      }\n    }\n    // Fall through means this is global\n    if (parserConfig.globalVars && !inList(varname, state.globalVars))\n      state.globalVars = new Var(varname, state.globalVars)\n  }\n  function registerVarScoped(varname, context) {\n    if (!context) {\n      return null\n    } else if (context.block) {\n      var inner = registerVarScoped(varname, context.prev)\n      if (!inner) return null\n      if (inner == context.prev) return context\n      return new Context(inner, context.vars, true)\n    } else if (inList(varname, context.vars)) {\n      return context\n    } else {\n      return new Context(context.prev, new Var(varname, context.vars), false)\n    }\n  }\n\n  function isModifier(name) {\n    return name == \"public\" || name == \"private\" || name == \"protected\" || name == \"abstract\" || name == \"readonly\"\n  }\n\n  // Combinators\n\n  function Context(prev, vars, block) { this.prev = prev; this.vars = vars; this.block = block }\n  function Var(name, next) { this.name = name; this.next = next }\n\n  var defaultVars = new Var(\"this\", new Var(\"arguments\", null))\n  function pushcontext() {\n    cx.state.context = new Context(cx.state.context, cx.state.localVars, false)\n    cx.state.localVars = defaultVars\n  }\n  function pushblockcontext() {\n    cx.state.context = new Context(cx.state.context, cx.state.localVars, true)\n    cx.state.localVars = null\n  }\n  pushcontext.lex = pushblockcontext.lex = true\n  function popcontext() {\n    cx.state.localVars = cx.state.context.vars\n    cx.state.context = cx.state.context.prev\n  }\n  popcontext.lex = true\n  function pushlex(type, info) {\n    var result = function() {\n      var state = cx.state, indent = state.indented;\n      if (state.lexical.type == \"stat\") indent = state.lexical.indented;\n      else for (var outer = state.lexical; outer && outer.type == \")\" && outer.align; outer = outer.prev)\n        indent = outer.indented;\n      state.lexical = new JSLexical(indent, cx.stream.column(), type, null, state.lexical, info);\n    };\n    result.lex = true;\n    return result;\n  }\n  function poplex() {\n    var state = cx.state;\n    if (state.lexical.prev) {\n      if (state.lexical.type == \")\")\n        state.indented = state.lexical.indented;\n      state.lexical = state.lexical.prev;\n    }\n  }\n  poplex.lex = true;\n\n  function expect(wanted) {\n    function exp(type) {\n      if (type == wanted) return cont();\n      else if (wanted == \";\" || type == \"}\" || type == \")\" || type == \"]\") return pass();\n      else return cont(exp);\n    };\n    return exp;\n  }\n\n  function statement(type, value) {\n    if (type == \"var\") return cont(pushlex(\"vardef\", value), vardef, expect(\";\"), poplex);\n    if (type == \"keyword a\") return cont(pushlex(\"form\"), parenExpr, statement, poplex);\n    if (type == \"keyword b\") return cont(pushlex(\"form\"), statement, poplex);\n    if (type == \"keyword d\") return cx.stream.match(/^\\s*$/, false) ? cont() : cont(pushlex(\"stat\"), maybeexpression, expect(\";\"), poplex);\n    if (type == \"debugger\") return cont(expect(\";\"));\n    if (type == \"{\") return cont(pushlex(\"}\"), pushblockcontext, block, poplex, popcontext);\n    if (type == \";\") return cont();\n    if (type == \"if\") {\n      if (cx.state.lexical.info == \"else\" && cx.state.cc[cx.state.cc.length - 1] == poplex)\n        cx.state.cc.pop()();\n      return cont(pushlex(\"form\"), parenExpr, statement, poplex, maybeelse);\n    }\n    if (type == \"function\") return cont(functiondef);\n    if (type == \"for\") return cont(pushlex(\"form\"), pushblockcontext, forspec, statement, popcontext, poplex);\n    if (type == \"class\" || (isTS && value == \"interface\")) {\n      cx.marked = \"keyword\"\n      return cont(pushlex(\"form\", type == \"class\" ? type : value), className, poplex)\n    }\n    if (type == \"variable\") {\n      if (isTS && value == \"declare\") {\n        cx.marked = \"keyword\"\n        return cont(statement)\n      } else if (isTS && (value == \"module\" || value == \"enum\" || value == \"type\") && cx.stream.match(/^\\s*\\w/, false)) {\n        cx.marked = \"keyword\"\n        if (value == \"enum\") return cont(enumdef);\n        else if (value == \"type\") return cont(typename, expect(\"operator\"), typeexpr, expect(\";\"));\n        else return cont(pushlex(\"form\"), pattern, expect(\"{\"), pushlex(\"}\"), block, poplex, poplex)\n      } else if (isTS && value == \"namespace\") {\n        cx.marked = \"keyword\"\n        return cont(pushlex(\"form\"), expression, statement, poplex)\n      } else if (isTS && value == \"abstract\") {\n        cx.marked = \"keyword\"\n        return cont(statement)\n      } else {\n        return cont(pushlex(\"stat\"), maybelabel);\n      }\n    }\n    if (type == \"switch\") return cont(pushlex(\"form\"), parenExpr, expect(\"{\"), pushlex(\"}\", \"switch\"), pushblockcontext,\n                                      block, poplex, poplex, popcontext);\n    if (type == \"case\") return cont(expression, expect(\":\"));\n    if (type == \"default\") return cont(expect(\":\"));\n    if (type == \"catch\") return cont(pushlex(\"form\"), pushcontext, maybeCatchBinding, statement, poplex, popcontext);\n    if (type == \"export\") return cont(pushlex(\"stat\"), afterExport, poplex);\n    if (type == \"import\") return cont(pushlex(\"stat\"), afterImport, poplex);\n    if (type == \"async\") return cont(statement)\n    if (value == \"@\") return cont(expression, statement)\n    return pass(pushlex(\"stat\"), expression, expect(\";\"), poplex);\n  }\n  function maybeCatchBinding(type) {\n    if (type == \"(\") return cont(funarg, expect(\")\"))\n  }\n  function expression(type, value) {\n    return expressionInner(type, value, false);\n  }\n  function expressionNoComma(type, value) {\n    return expressionInner(type, value, true);\n  }\n  function parenExpr(type) {\n    if (type != \"(\") return pass()\n    return cont(pushlex(\")\"), maybeexpression, expect(\")\"), poplex)\n  }\n  function expressionInner(type, value, noComma) {\n    if (cx.state.fatArrowAt == cx.stream.start) {\n      var body = noComma ? arrowBodyNoComma : arrowBody;\n      if (type == \"(\") return cont(pushcontext, pushlex(\")\"), commasep(funarg, \")\"), poplex, expect(\"=>\"), body, popcontext);\n      else if (type == \"variable\") return pass(pushcontext, pattern, expect(\"=>\"), body, popcontext);\n    }\n\n    var maybeop = noComma ? maybeoperatorNoComma : maybeoperatorComma;\n    if (atomicTypes.hasOwnProperty(type)) return cont(maybeop);\n    if (type == \"function\") return cont(functiondef, maybeop);\n    if (type == \"class\" || (isTS && value == \"interface\")) { cx.marked = \"keyword\"; return cont(pushlex(\"form\"), classExpression, poplex); }\n    if (type == \"keyword c\" || type == \"async\") return cont(noComma ? expressionNoComma : expression);\n    if (type == \"(\") return cont(pushlex(\")\"), maybeexpression, expect(\")\"), poplex, maybeop);\n    if (type == \"operator\" || type == \"spread\") return cont(noComma ? expressionNoComma : expression);\n    if (type == \"[\") return cont(pushlex(\"]\"), arrayLiteral, poplex, maybeop);\n    if (type == \"{\") return contCommasep(objprop, \"}\", null, maybeop);\n    if (type == \"quasi\") return pass(quasi, maybeop);\n    if (type == \"new\") return cont(maybeTarget(noComma));\n    return cont();\n  }\n  function maybeexpression(type) {\n    if (type.match(/[;\\}\\)\\],]/)) return pass();\n    return pass(expression);\n  }\n\n  function maybeoperatorComma(type, value) {\n    if (type == \",\") return cont(maybeexpression);\n    return maybeoperatorNoComma(type, value, false);\n  }\n  function maybeoperatorNoComma(type, value, noComma) {\n    var me = noComma == false ? maybeoperatorComma : maybeoperatorNoComma;\n    var expr = noComma == false ? expression : expressionNoComma;\n    if (type == \"=>\") return cont(pushcontext, noComma ? arrowBodyNoComma : arrowBody, popcontext);\n    if (type == \"operator\") {\n      if (/\\+\\+|--/.test(value) || isTS && value == \"!\") return cont(me);\n      if (isTS && value == \"<\" && cx.stream.match(/^([^<>]|<[^<>]*>)*>\\s*\\(/, false))\n        return cont(pushlex(\">\"), commasep(typeexpr, \">\"), poplex, me);\n      if (value == \"?\") return cont(expression, expect(\":\"), expr);\n      return cont(expr);\n    }\n    if (type == \"quasi\") { return pass(quasi, me); }\n    if (type == \";\") return;\n    if (type == \"(\") return contCommasep(expressionNoComma, \")\", \"call\", me);\n    if (type == \".\") return cont(property, me);\n    if (type == \"[\") return cont(pushlex(\"]\"), maybeexpression, expect(\"]\"), poplex, me);\n    if (isTS && value == \"as\") { cx.marked = \"keyword\"; return cont(typeexpr, me) }\n    if (type == \"regexp\") {\n      cx.state.lastType = cx.marked = \"operator\"\n      cx.stream.backUp(cx.stream.pos - cx.stream.start - 1)\n      return cont(expr)\n    }\n  }\n  function quasi(type, value) {\n    if (type != \"quasi\") return pass();\n    if (value.slice(value.length - 2) != \"${\") return cont(quasi);\n    return cont(maybeexpression, continueQuasi);\n  }\n  function continueQuasi(type) {\n    if (type == \"}\") {\n      cx.marked = \"string-2\";\n      cx.state.tokenize = tokenQuasi;\n      return cont(quasi);\n    }\n  }\n  function arrowBody(type) {\n    findFatArrow(cx.stream, cx.state);\n    return pass(type == \"{\" ? statement : expression);\n  }\n  function arrowBodyNoComma(type) {\n    findFatArrow(cx.stream, cx.state);\n    return pass(type == \"{\" ? statement : expressionNoComma);\n  }\n  function maybeTarget(noComma) {\n    return function(type) {\n      if (type == \".\") return cont(noComma ? targetNoComma : target);\n      else if (type == \"variable\" && isTS) return cont(maybeTypeArgs, noComma ? maybeoperatorNoComma : maybeoperatorComma)\n      else return pass(noComma ? expressionNoComma : expression);\n    };\n  }\n  function target(_, value) {\n    if (value == \"target\") { cx.marked = \"keyword\"; return cont(maybeoperatorComma); }\n  }\n  function targetNoComma(_, value) {\n    if (value == \"target\") { cx.marked = \"keyword\"; return cont(maybeoperatorNoComma); }\n  }\n  function maybelabel(type) {\n    if (type == \":\") return cont(poplex, statement);\n    return pass(maybeoperatorComma, expect(\";\"), poplex);\n  }\n  function property(type) {\n    if (type == \"variable\") {cx.marked = \"property\"; return cont();}\n  }\n  function objprop(type, value) {\n    if (type == \"async\") {\n      cx.marked = \"property\";\n      return cont(objprop);\n    } else if (type == \"variable\" || cx.style == \"keyword\") {\n      cx.marked = \"property\";\n      if (value == \"get\" || value == \"set\") return cont(getterSetter);\n      var m // Work around fat-arrow-detection complication for detecting typescript typed arrow params\n      if (isTS && cx.state.fatArrowAt == cx.stream.start && (m = cx.stream.match(/^\\s*:\\s*/, false)))\n        cx.state.fatArrowAt = cx.stream.pos + m[0].length\n      return cont(afterprop);\n    } else if (type == \"number\" || type == \"string\") {\n      cx.marked = jsonldMode ? \"property\" : (cx.style + \" property\");\n      return cont(afterprop);\n    } else if (type == \"jsonld-keyword\") {\n      return cont(afterprop);\n    } else if (isTS && isModifier(value)) {\n      cx.marked = \"keyword\"\n      return cont(objprop)\n    } else if (type == \"[\") {\n      return cont(expression, maybetype, expect(\"]\"), afterprop);\n    } else if (type == \"spread\") {\n      return cont(expressionNoComma, afterprop);\n    } else if (value == \"*\") {\n      cx.marked = \"keyword\";\n      return cont(objprop);\n    } else if (type == \":\") {\n      return pass(afterprop)\n    }\n  }\n  function getterSetter(type) {\n    if (type != \"variable\") return pass(afterprop);\n    cx.marked = \"property\";\n    return cont(functiondef);\n  }\n  function afterprop(type) {\n    if (type == \":\") return cont(expressionNoComma);\n    if (type == \"(\") return pass(functiondef);\n  }\n  function commasep(what, end, sep) {\n    function proceed(type, value) {\n      if (sep ? sep.indexOf(type) > -1 : type == \",\") {\n        var lex = cx.state.lexical;\n        if (lex.info == \"call\") lex.pos = (lex.pos || 0) + 1;\n        return cont(function(type, value) {\n          if (type == end || value == end) return pass()\n          return pass(what)\n        }, proceed);\n      }\n      if (type == end || value == end) return cont();\n      if (sep && sep.indexOf(\";\") > -1) return pass(what)\n      return cont(expect(end));\n    }\n    return function(type, value) {\n      if (type == end || value == end) return cont();\n      return pass(what, proceed);\n    };\n  }\n  function contCommasep(what, end, info) {\n    for (var i = 3; i < arguments.length; i++)\n      cx.cc.push(arguments[i]);\n    return cont(pushlex(end, info), commasep(what, end), poplex);\n  }\n  function block(type) {\n    if (type == \"}\") return cont();\n    return pass(statement, block);\n  }\n  function maybetype(type, value) {\n    if (isTS) {\n      if (type == \":\") return cont(typeexpr);\n      if (value == \"?\") return cont(maybetype);\n    }\n  }\n  function maybetypeOrIn(type, value) {\n    if (isTS && (type == \":\" || value == \"in\")) return cont(typeexpr)\n  }\n  function mayberettype(type) {\n    if (isTS && type == \":\") {\n      if (cx.stream.match(/^\\s*\\w+\\s+is\\b/, false)) return cont(expression, isKW, typeexpr)\n      else return cont(typeexpr)\n    }\n  }\n  function isKW(_, value) {\n    if (value == \"is\") {\n      cx.marked = \"keyword\"\n      return cont()\n    }\n  }\n  function typeexpr(type, value) {\n    if (value == \"keyof\" || value == \"typeof\" || value == \"infer\" || value == \"readonly\") {\n      cx.marked = \"keyword\"\n      return cont(value == \"typeof\" ? expressionNoComma : typeexpr)\n    }\n    if (type == \"variable\" || value == \"void\") {\n      cx.marked = \"type\"\n      return cont(afterType)\n    }\n    if (value == \"|\" || value == \"&\") return cont(typeexpr)\n    if (type == \"string\" || type == \"number\" || type == \"atom\") return cont(afterType);\n    if (type == \"[\") return cont(pushlex(\"]\"), commasep(typeexpr, \"]\", \",\"), poplex, afterType)\n    if (type == \"{\") return cont(pushlex(\"}\"), typeprops, poplex, afterType)\n    if (type == \"(\") return cont(commasep(typearg, \")\"), maybeReturnType, afterType)\n    if (type == \"<\") return cont(commasep(typeexpr, \">\"), typeexpr)\n    if (type == \"quasi\") { return pass(quasiType, afterType); }\n  }\n  function maybeReturnType(type) {\n    if (type == \"=>\") return cont(typeexpr)\n  }\n  function typeprops(type) {\n    if (type.match(/[\\}\\)\\]]/)) return cont()\n    if (type == \",\" || type == \";\") return cont(typeprops)\n    return pass(typeprop, typeprops)\n  }\n  function typeprop(type, value) {\n    if (type == \"variable\" || cx.style == \"keyword\") {\n      cx.marked = \"property\"\n      return cont(typeprop)\n    } else if (value == \"?\" || type == \"number\" || type == \"string\") {\n      return cont(typeprop)\n    } else if (type == \":\") {\n      return cont(typeexpr)\n    } else if (type == \"[\") {\n      return cont(expect(\"variable\"), maybetypeOrIn, expect(\"]\"), typeprop)\n    } else if (type == \"(\") {\n      return pass(functiondecl, typeprop)\n    } else if (!type.match(/[;\\}\\)\\],]/)) {\n      return cont()\n    }\n  }\n  function quasiType(type, value) {\n    if (type != \"quasi\") return pass();\n    if (value.slice(value.length - 2) != \"${\") return cont(quasiType);\n    return cont(typeexpr, continueQuasiType);\n  }\n  function continueQuasiType(type) {\n    if (type == \"}\") {\n      cx.marked = \"string-2\";\n      cx.state.tokenize = tokenQuasi;\n      return cont(quasiType);\n    }\n  }\n  function typearg(type, value) {\n    if (type == \"variable\" && cx.stream.match(/^\\s*[?:]/, false) || value == \"?\") return cont(typearg)\n    if (type == \":\") return cont(typeexpr)\n    if (type == \"spread\") return cont(typearg)\n    return pass(typeexpr)\n  }\n  function afterType(type, value) {\n    if (value == \"<\") return cont(pushlex(\">\"), commasep(typeexpr, \">\"), poplex, afterType)\n    if (value == \"|\" || type == \".\" || value == \"&\") return cont(typeexpr)\n    if (type == \"[\") return cont(typeexpr, expect(\"]\"), afterType)\n    if (value == \"extends\" || value == \"implements\") { cx.marked = \"keyword\"; return cont(typeexpr) }\n    if (value == \"?\") return cont(typeexpr, expect(\":\"), typeexpr)\n  }\n  function maybeTypeArgs(_, value) {\n    if (value == \"<\") return cont(pushlex(\">\"), commasep(typeexpr, \">\"), poplex, afterType)\n  }\n  function typeparam() {\n    return pass(typeexpr, maybeTypeDefault)\n  }\n  function maybeTypeDefault(_, value) {\n    if (value == \"=\") return cont(typeexpr)\n  }\n  function vardef(_, value) {\n    if (value == \"enum\") {cx.marked = \"keyword\"; return cont(enumdef)}\n    return pass(pattern, maybetype, maybeAssign, vardefCont);\n  }\n  function pattern(type, value) {\n    if (isTS && isModifier(value)) { cx.marked = \"keyword\"; return cont(pattern) }\n    if (type == \"variable\") { register(value); return cont(); }\n    if (type == \"spread\") return cont(pattern);\n    if (type == \"[\") return contCommasep(eltpattern, \"]\");\n    if (type == \"{\") return contCommasep(proppattern, \"}\");\n  }\n  function proppattern(type, value) {\n    if (type == \"variable\" && !cx.stream.match(/^\\s*:/, false)) {\n      register(value);\n      return cont(maybeAssign);\n    }\n    if (type == \"variable\") cx.marked = \"property\";\n    if (type == \"spread\") return cont(pattern);\n    if (type == \"}\") return pass();\n    if (type == \"[\") return cont(expression, expect(']'), expect(':'), proppattern);\n    return cont(expect(\":\"), pattern, maybeAssign);\n  }\n  function eltpattern() {\n    return pass(pattern, maybeAssign)\n  }\n  function maybeAssign(_type, value) {\n    if (value == \"=\") return cont(expressionNoComma);\n  }\n  function vardefCont(type) {\n    if (type == \",\") return cont(vardef);\n  }\n  function maybeelse(type, value) {\n    if (type == \"keyword b\" && value == \"else\") return cont(pushlex(\"form\", \"else\"), statement, poplex);\n  }\n  function forspec(type, value) {\n    if (value == \"await\") return cont(forspec);\n    if (type == \"(\") return cont(pushlex(\")\"), forspec1, poplex);\n  }\n  function forspec1(type) {\n    if (type == \"var\") return cont(vardef, forspec2);\n    if (type == \"variable\") return cont(forspec2);\n    return pass(forspec2)\n  }\n  function forspec2(type, value) {\n    if (type == \")\") return cont()\n    if (type == \";\") return cont(forspec2)\n    if (value == \"in\" || value == \"of\") { cx.marked = \"keyword\"; return cont(expression, forspec2) }\n    return pass(expression, forspec2)\n  }\n  function functiondef(type, value) {\n    if (value == \"*\") {cx.marked = \"keyword\"; return cont(functiondef);}\n    if (type == \"variable\") {register(value); return cont(functiondef);}\n    if (type == \"(\") return cont(pushcontext, pushlex(\")\"), commasep(funarg, \")\"), poplex, mayberettype, statement, popcontext);\n    if (isTS && value == \"<\") return cont(pushlex(\">\"), commasep(typeparam, \">\"), poplex, functiondef)\n  }\n  function functiondecl(type, value) {\n    if (value == \"*\") {cx.marked = \"keyword\"; return cont(functiondecl);}\n    if (type == \"variable\") {register(value); return cont(functiondecl);}\n    if (type == \"(\") return cont(pushcontext, pushlex(\")\"), commasep(funarg, \")\"), poplex, mayberettype, popcontext);\n    if (isTS && value == \"<\") return cont(pushlex(\">\"), commasep(typeparam, \">\"), poplex, functiondecl)\n  }\n  function typename(type, value) {\n    if (type == \"keyword\" || type == \"variable\") {\n      cx.marked = \"type\"\n      return cont(typename)\n    } else if (value == \"<\") {\n      return cont(pushlex(\">\"), commasep(typeparam, \">\"), poplex)\n    }\n  }\n  function funarg(type, value) {\n    if (value == \"@\") cont(expression, funarg)\n    if (type == \"spread\") return cont(funarg);\n    if (isTS && isModifier(value)) { cx.marked = \"keyword\"; return cont(funarg); }\n    if (isTS && type == \"this\") return cont(maybetype, maybeAssign)\n    return pass(pattern, maybetype, maybeAssign);\n  }\n  function classExpression(type, value) {\n    // Class expressions may have an optional name.\n    if (type == \"variable\") return className(type, value);\n    return classNameAfter(type, value);\n  }\n  function className(type, value) {\n    if (type == \"variable\") {register(value); return cont(classNameAfter);}\n  }\n  function classNameAfter(type, value) {\n    if (value == \"<\") return cont(pushlex(\">\"), commasep(typeparam, \">\"), poplex, classNameAfter)\n    if (value == \"extends\" || value == \"implements\" || (isTS && type == \",\")) {\n      if (value == \"implements\") cx.marked = \"keyword\";\n      return cont(isTS ? typeexpr : expression, classNameAfter);\n    }\n    if (type == \"{\") return cont(pushlex(\"}\"), classBody, poplex);\n  }\n  function classBody(type, value) {\n    if (type == \"async\" ||\n        (type == \"variable\" &&\n         (value == \"static\" || value == \"get\" || value == \"set\" || (isTS && isModifier(value))) &&\n         cx.stream.match(/^\\s+[\\w$\\xa1-\\uffff]/, false))) {\n      cx.marked = \"keyword\";\n      return cont(classBody);\n    }\n    if (type == \"variable\" || cx.style == \"keyword\") {\n      cx.marked = \"property\";\n      return cont(classfield, classBody);\n    }\n    if (type == \"number\" || type == \"string\") return cont(classfield, classBody);\n    if (type == \"[\")\n      return cont(expression, maybetype, expect(\"]\"), classfield, classBody)\n    if (value == \"*\") {\n      cx.marked = \"keyword\";\n      return cont(classBody);\n    }\n    if (isTS && type == \"(\") return pass(functiondecl, classBody)\n    if (type == \";\" || type == \",\") return cont(classBody);\n    if (type == \"}\") return cont();\n    if (value == \"@\") return cont(expression, classBody)\n  }\n  function classfield(type, value) {\n    if (value == \"!\") return cont(classfield)\n    if (value == \"?\") return cont(classfield)\n    if (type == \":\") return cont(typeexpr, maybeAssign)\n    if (value == \"=\") return cont(expressionNoComma)\n    var context = cx.state.lexical.prev, isInterface = context && context.info == \"interface\"\n    return pass(isInterface ? functiondecl : functiondef)\n  }\n  function afterExport(type, value) {\n    if (value == \"*\") { cx.marked = \"keyword\"; return cont(maybeFrom, expect(\";\")); }\n    if (value == \"default\") { cx.marked = \"keyword\"; return cont(expression, expect(\";\")); }\n    if (type == \"{\") return cont(commasep(exportField, \"}\"), maybeFrom, expect(\";\"));\n    return pass(statement);\n  }\n  function exportField(type, value) {\n    if (value == \"as\") { cx.marked = \"keyword\"; return cont(expect(\"variable\")); }\n    if (type == \"variable\") return pass(expressionNoComma, exportField);\n  }\n  function afterImport(type) {\n    if (type == \"string\") return cont();\n    if (type == \"(\") return pass(expression);\n    if (type == \".\") return pass(maybeoperatorComma);\n    return pass(importSpec, maybeMoreImports, maybeFrom);\n  }\n  function importSpec(type, value) {\n    if (type == \"{\") return contCommasep(importSpec, \"}\");\n    if (type == \"variable\") register(value);\n    if (value == \"*\") cx.marked = \"keyword\";\n    return cont(maybeAs);\n  }\n  function maybeMoreImports(type) {\n    if (type == \",\") return cont(importSpec, maybeMoreImports)\n  }\n  function maybeAs(_type, value) {\n    if (value == \"as\") { cx.marked = \"keyword\"; return cont(importSpec); }\n  }\n  function maybeFrom(_type, value) {\n    if (value == \"from\") { cx.marked = \"keyword\"; return cont(expression); }\n  }\n  function arrayLiteral(type) {\n    if (type == \"]\") return cont();\n    return pass(commasep(expressionNoComma, \"]\"));\n  }\n  function enumdef() {\n    return pass(pushlex(\"form\"), pattern, expect(\"{\"), pushlex(\"}\"), commasep(enummember, \"}\"), poplex, poplex)\n  }\n  function enummember() {\n    return pass(pattern, maybeAssign);\n  }\n\n  function isContinuedStatement(state, textAfter) {\n    return state.lastType == \"operator\" || state.lastType == \",\" ||\n      isOperatorChar.test(textAfter.charAt(0)) ||\n      /[,.]/.test(textAfter.charAt(0));\n  }\n\n  function expressionAllowed(stream, state, backUp) {\n    return state.tokenize == tokenBase &&\n      /^(?:operator|sof|keyword [bcd]|case|new|export|default|spread|[\\[{}\\(,;:]|=>)$/.test(state.lastType) ||\n      (state.lastType == \"quasi\" && /\\{\\s*$/.test(stream.string.slice(0, stream.pos - (backUp || 0))))\n  }\n\n  // Interface\n\n  return {\n    startState: function(basecolumn) {\n      var state = {\n        tokenize: tokenBase,\n        lastType: \"sof\",\n        cc: [],\n        lexical: new JSLexical((basecolumn || 0) - indentUnit, 0, \"block\", false),\n        localVars: parserConfig.localVars,\n        context: parserConfig.localVars && new Context(null, null, false),\n        indented: basecolumn || 0\n      };\n      if (parserConfig.globalVars && typeof parserConfig.globalVars == \"object\")\n        state.globalVars = parserConfig.globalVars;\n      return state;\n    },\n\n    token: function(stream, state) {\n      if (stream.sol()) {\n        if (!state.lexical.hasOwnProperty(\"align\"))\n          state.lexical.align = false;\n        state.indented = stream.indentation();\n        findFatArrow(stream, state);\n      }\n      if (state.tokenize != tokenComment && stream.eatSpace()) return null;\n      var style = state.tokenize(stream, state);\n      if (type == \"comment\") return style;\n      state.lastType = type == \"operator\" && (content == \"++\" || content == \"--\") ? \"incdec\" : type;\n      return parseJS(state, style, type, content, stream);\n    },\n\n    indent: function(state, textAfter) {\n      if (state.tokenize == tokenComment || state.tokenize == tokenQuasi) return CodeMirror.Pass;\n      if (state.tokenize != tokenBase) return 0;\n      var firstChar = textAfter && textAfter.charAt(0), lexical = state.lexical, top\n      // Kludge to prevent 'maybelse' from blocking lexical scope pops\n      if (!/^\\s*else\\b/.test(textAfter)) for (var i = state.cc.length - 1; i >= 0; --i) {\n        var c = state.cc[i];\n        if (c == poplex) lexical = lexical.prev;\n        else if (c != maybeelse && c != popcontext) break;\n      }\n      while ((lexical.type == \"stat\" || lexical.type == \"form\") &&\n             (firstChar == \"}\" || ((top = state.cc[state.cc.length - 1]) &&\n                                   (top == maybeoperatorComma || top == maybeoperatorNoComma) &&\n                                   !/^[,\\.=+\\-*:?[\\(]/.test(textAfter))))\n        lexical = lexical.prev;\n      if (statementIndent && lexical.type == \")\" && lexical.prev.type == \"stat\")\n        lexical = lexical.prev;\n      var type = lexical.type, closing = firstChar == type;\n\n      if (type == \"vardef\") return lexical.indented + (state.lastType == \"operator\" || state.lastType == \",\" ? lexical.info.length + 1 : 0);\n      else if (type == \"form\" && firstChar == \"{\") return lexical.indented;\n      else if (type == \"form\") return lexical.indented + indentUnit;\n      else if (type == \"stat\")\n        return lexical.indented + (isContinuedStatement(state, textAfter) ? statementIndent || indentUnit : 0);\n      else if (lexical.info == \"switch\" && !closing && parserConfig.doubleIndentSwitch != false)\n        return lexical.indented + (/^(?:case|default)\\b/.test(textAfter) ? indentUnit : 2 * indentUnit);\n      else if (lexical.align) return lexical.column + (closing ? 0 : 1);\n      else return lexical.indented + (closing ? 0 : indentUnit);\n    },\n\n    electricInput: /^\\s*(?:case .*?:|default:|\\{|\\})$/,\n    blockCommentStart: jsonMode ? null : \"/*\",\n    blockCommentEnd: jsonMode ? null : \"*/\",\n    blockCommentContinue: jsonMode ? null : \" * \",\n    lineComment: jsonMode ? null : \"//\",\n    fold: \"brace\",\n    closeBrackets: \"()[]{}''\\\"\\\"``\",\n\n    helperType: jsonMode ? \"json\" : \"javascript\",\n    jsonldMode: jsonldMode,\n    jsonMode: jsonMode,\n\n    expressionAllowed: expressionAllowed,\n\n    skipExpression: function(state) {\n      parseJS(state, \"atom\", \"atom\", \"true\", new CodeMirror.StringStream(\"\", 2, null))\n    }\n  };\n});\n\nCodeMirror.registerHelper(\"wordChars\", \"javascript\", /[\\w$]/);\n\nCodeMirror.defineMIME(\"text/javascript\", \"javascript\");\nCodeMirror.defineMIME(\"text/ecmascript\", \"javascript\");\nCodeMirror.defineMIME(\"application/javascript\", \"javascript\");\nCodeMirror.defineMIME(\"application/x-javascript\", \"javascript\");\nCodeMirror.defineMIME(\"application/ecmascript\", \"javascript\");\nCodeMirror.defineMIME(\"application/json\", { name: \"javascript\", json: true });\nCodeMirror.defineMIME(\"application/x-json\", { name: \"javascript\", json: true });\nCodeMirror.defineMIME(\"application/manifest+json\", { name: \"javascript\", json: true })\nCodeMirror.defineMIME(\"application/ld+json\", { name: \"javascript\", jsonld: true });\nCodeMirror.defineMIME(\"text/typescript\", { name: \"javascript\", typescript: true });\nCodeMirror.defineMIME(\"application/typescript\", { name: \"javascript\", typescript: true });\n\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,GAAC,SAASA,GAAK;AAEXA,MAAIC,GAA+B,CAAA;EAKtC,GAAE,SAASC,GAAY;AAGxBA,MAAW,WAAW,cAAc,SAASC,GAAQC,GAAc;AACjE,UAAIC,IAAaF,EAAO,YACpBG,KAAkBF,EAAa,iBAC/BG,KAAaH,EAAa,QAC1BI,IAAWJ,EAAa,QAAQG,IAChCE,KAAaL,EAAa,eAAe,OACzCM,IAAON,EAAa,YACpBO,KAASP,EAAa,kBAAkB,oBAIxCQ,KAAW,WAAU;AACvB,iBAASC,EAAGC,GAAM;AAAC,iBAAO,EAAC,MAAMA,GAAM,OAAO,UAAS;QAAE;AAAhDC,UAAAF,GAAA,IAAA;AACT,YAAIG,IAAIH,EAAG,WAAW,GAAGI,IAAIJ,EAAG,WAAW,GAAGK,IAAIL,EAAG,WAAW,GAAGM,IAAIN,EAAG,WAAW,GACjFO,IAAWP,EAAG,UAAU,GAAGQ,IAAO,EAAC,MAAM,QAAQ,OAAO,OAAM;AAElE,eAAO;UACL,IAAMR,EAAG,IAAI;UAAG,OAASG;UAAG,MAAQA;UAAG,MAAQC;UAAG,IAAMA;UAAG,KAAOA;UAAG,SAAWA;UAChF,QAAUE;UAAG,OAASA;UAAG,UAAYA;UAAG,KAAON,EAAG,KAAK;UAAG,QAAUK;UAAG,MAAQA;UAAG,OAASA;UAC3F,UAAYL,EAAG,UAAU;UAAG,KAAOA,EAAG,KAAK;UAAG,OAASA,EAAG,KAAK;UAAG,KAAOA,EAAG,KAAK;UACjF,UAAYA,EAAG,UAAU;UAAG,OAASA,EAAG,OAAO;UAC/C,KAAOA,EAAG,KAAK;UAAG,QAAUA,EAAG,QAAQ;UAAG,MAAQA,EAAG,MAAM;UAAG,SAAWA,EAAG,SAAS;UACrF,IAAMO;UAAU,QAAUA;UAAU,YAAcA;UAClD,MAAQC;UAAM,OAASA;UAAM,MAAQA;UAAM,WAAaA;UAAM,KAAOA;UAAM,UAAYA;UACvF,MAAQR,EAAG,MAAM;UAAG,OAASA,EAAG,OAAO;UAAG,OAASA,EAAG,MAAM;UAC5D,OAASK;UAAG,QAAUL,EAAG,QAAQ;UAAG,QAAUA,EAAG,QAAQ;UAAG,SAAWK;UACvE,OAASA;QACf;MACA,EAAA,GAEMI,KAAiB,qBACjBC,KAAkB;AAEtB,eAASC,GAAWC,GAAQ;AAE1B,iBADIC,IAAU,OAAOC,GAAMC,IAAQ,QAC3BD,IAAOF,EAAO,KAAI,MAAO,QAAM;AACrC,cAAI,CAACC,GAAS;AACZ,gBAAIC,KAAQ,OAAO,CAACC;AAAO;AACvBD,iBAAQ,MAAKC,IAAQ,OAChBA,KAASD,KAAQ,QAAKC,IAAQ;UAAA;AAEzCF,cAAU,CAACA,KAAWC,KAAQ;QAAA;MAEjC;AAVQZ,QAAAS,IAAA,YAAA;AAcT,UAAIV,GAAMe;AACV,eAASC,EAAIC,GAAIC,GAAOC,GAAM;AAC5B,eAAAnB,IAAOiB,GAAIF,KAAUI,GACdD;MACR;AAHQjB,QAAAe,GAAA,KAAA;AAIT,eAASI,EAAUT,GAAQU,GAAO;AAChC,YAAIC,IAAKX,EAAO,KAAA;AAChB,YAAIW,KAAM,OAAOA,KAAM;AACrB,iBAAAD,EAAM,WAAWE,GAAYD,CAAE,GACxBD,EAAM,SAASV,GAAQU,CAAK;AAC9B,YAAIC,KAAM,OAAOX,EAAO,MAAM,gCAAgC;AACnE,iBAAOK,EAAI,UAAU,QAAQ;AACxB,YAAIM,KAAM,OAAOX,EAAO,MAAM,IAAI;AACvC,iBAAOK,EAAI,UAAU,MAAM;AACtB,YAAI,qBAAqB,KAAKM,CAAE;AACrC,iBAAON,EAAIM,CAAE;AACR,YAAIA,KAAM,OAAOX,EAAO,IAAI,GAAG;AACpC,iBAAOK,EAAI,MAAM,UAAU;AACtB,YAAIM,KAAM,OAAOX,EAAO,MAAM,uCAAuC;AAC1E,iBAAOK,EAAI,UAAU,QAAQ;AACxB,YAAI,KAAK,KAAKM,CAAE;AACrB,iBAAAX,EAAO,MAAM,kDAAkD,GACxDK,EAAI,UAAU,QAAQ;AACxB,YAAIM,KAAM;AACf,iBAAIX,EAAO,IAAI,GAAG,KAChBU,EAAM,WAAWG,IACVA,GAAab,GAAQU,CAAK,KACxBV,EAAO,IAAI,GAAG,KACvBA,EAAO,UAAS,GACTK,EAAI,WAAW,SAAS,KACtBS,GAAkBd,GAAQU,GAAO,CAAC,KAC3CX,GAAWC,CAAM,GACjBA,EAAO,MAAM,mCAAmC,GACzCK,EAAI,UAAU,UAAU,MAE/BL,EAAO,IAAI,GAAG,GACPK,EAAI,YAAY,YAAYL,EAAO,QAAS,CAAA;AAEhD,YAAIW,KAAM;AACf,iBAAAD,EAAM,WAAWK,GACVA,EAAWf,GAAQU,CAAK;AAC1B,YAAIC,KAAM,OAAOX,EAAO,KAAI,KAAM;AACvC,iBAAAA,EAAO,UAAS,GACTK,EAAI,QAAQ,MAAM;AACpB,YAAIM,KAAM,OAAOX,EAAO,SAASd,EAAM;AAC5C,iBAAOmB,EAAI,YAAY,UAAU;AAC5B,YAAIM,KAAM,OAAOX,EAAO,MAAM,KAAK,KAC9BW,KAAM,OAAOX,EAAO,MAAM,IAAI,KAAK,CAAC,KAAK,KAAKA,EAAO,OAAO,MAAM,GAAGA,EAAO,KAAK,CAAC;AAC5F,iBAAAA,EAAO,UAAW,GACXK,EAAI,WAAW,SAAS;AAC1B,YAAIR,GAAe,KAAKc,CAAE;AAS/B,kBARIA,KAAM,OAAO,CAACD,EAAM,WAAWA,EAAM,QAAQ,QAAQ,SACnDV,EAAO,IAAI,GAAG,KACZW,KAAM,OAAOA,KAAM,QAAKX,EAAO,IAAI,GAAG,IACjC,cAAc,KAAKW,CAAE,MAC9BX,EAAO,IAAIW,CAAE,GACTA,KAAM,OAAKX,EAAO,IAAIW,CAAE,KAG5BA,KAAM,OAAOX,EAAO,IAAI,GAAG,IAAUK,EAAI,GAAG,IACzCA,EAAI,YAAY,YAAYL,EAAO,QAAS,CAAA;AAC9C,YAAId,GAAO,KAAKyB,CAAE,GAAG;AAC1BX,YAAO,SAASd,EAAM;AACtB,cAAI8B,IAAOhB,EAAO,QAAS;AAC3B,cAAIU,EAAM,YAAY,KAAK;AACzB,gBAAIvB,GAAS,qBAAqB6B,CAAI,GAAG;AACvC,kBAAI5B,IAAKD,GAAS6B,CAAI;AACtB,qBAAOX,EAAIjB,EAAG,MAAMA,EAAG,OAAO4B,CAAI;YAAA;AAEpC,gBAAIA,KAAQ,WAAWhB,EAAO,MAAM,4CAA4C,KAAK;AACnF,qBAAOK,EAAI,SAAS,WAAWW,CAAI;UAAA;AAEvC,iBAAOX,EAAI,YAAY,YAAYW,CAAI;QAAA;MAE1C;AArEQ1B,QAAAmB,GAAA,WAAA;AAuET,eAASG,GAAYK,GAAO;AAC1B,eAAO,SAASjB,GAAQU,GAAO;AAC7B,cAAIT,IAAU,OAAOC;AACrB,cAAIpB,MAAckB,EAAO,KAAM,KAAI,OAAOA,EAAO,MAAMF,EAAe;AACpE,mBAAAY,EAAM,WAAWD,GACVJ,EAAI,kBAAkB,MAAM;AAErC,kBAAQH,IAAOF,EAAO,KAAI,MAAO,QAC3B,EAAAE,KAAQe,KAAS,CAAChB;AACtBA,gBAAU,CAACA,KAAWC,KAAQ;AAEhC,iBAAKD,MAASS,EAAM,WAAWD,IACxBJ,EAAI,UAAU,QAAQ;QACnC;MACG;AAdQf,QAAAsB,IAAA,aAAA;AAgBT,eAASC,GAAab,GAAQU,GAAO;AAEnC,iBADIQ,IAAW,OAAOP,GACfA,IAAKX,EAAO,KAAA,KAAQ;AACzB,cAAIW,KAAM,OAAOO,GAAU;AACzBR,cAAM,WAAWD;AACjB;UAAA;AAEFS,cAAYP,KAAM;QAAA;AAEpB,eAAON,EAAI,WAAW,SAAS;MAChC;AAVQf,QAAAuB,IAAA,cAAA;AAYT,eAASE,EAAWf,GAAQU,GAAO;AAEjC,iBADIT,IAAU,OAAOC,IACbA,IAAOF,EAAO,KAAI,MAAO,QAAM;AACrC,cAAI,CAACC,MAAYC,KAAQ,OAAOA,KAAQ,OAAOF,EAAO,IAAI,GAAG,IAAI;AAC/DU,cAAM,WAAWD;AACjB;UAAA;AAEFR,cAAU,CAACA,KAAWC,KAAQ;QAAA;AAEhC,eAAOG,EAAI,SAAS,YAAYL,EAAO,QAAS,CAAA;MACjD;AAVQV,QAAAyB,GAAA,YAAA;AAYT,UAAII,KAAW;AAQf,eAASC,GAAapB,GAAQU,GAAO;AAC/BA,UAAM,eAAYA,EAAM,aAAa;AACzC,YAAIW,IAAQrB,EAAO,OAAO,QAAQ,MAAMA,EAAO,KAAK;AACpD,YAAI,EAAAqB,IAAQ,IAEZ;AAAA,cAAIpC,GAAM;AACR,gBAAIqC,IAAI,6CAA6C,KAAKtB,EAAO,OAAO,MAAMA,EAAO,OAAOqB,CAAK,CAAC;AAC9FC,kBAAGD,IAAQC,EAAE;UAAA;AAInB,mBADIC,IAAQ,GAAGC,IAAe,OACrBC,IAAMJ,IAAQ,GAAGI,KAAO,GAAG,EAAEA,GAAK;AACzC,gBAAId,IAAKX,EAAO,OAAO,OAAOyB,CAAG,GAC7BC,IAAUP,GAAS,QAAQR,CAAE;AACjC,gBAAIe,KAAW,KAAKA,IAAU,GAAG;AAC/B,kBAAI,CAACH,GAAO;AAAE,kBAAEE;AAAK;cAAA;AACrB,kBAAI,EAAEF,KAAS,GAAG;AAAMZ,qBAAM,QAAKa,IAAe;AAAM;cAAA;YAAA,WAC/CE,KAAW,KAAKA,IAAU;AACnC,gBAAEH;qBACOrC,GAAO,KAAKyB,CAAE;AACvBa,kBAAe;qBACN,UAAU,KAAKb,CAAE;AAC1B,uBAAQ,EAAEc,GAAK;AACb,oBAAIA,KAAO;AAAG;AACd,oBAAIvB,KAAOF,EAAO,OAAO,OAAOyB,IAAM,CAAC;AACvC,oBAAIvB,MAAQS,KAAMX,EAAO,OAAO,OAAOyB,IAAM,CAAC,KAAK,MAAM;AAAEA;AAAO;gBAAA;cAAA;qBAE3DD,KAAgB,CAACD,GAAO;AACjC,gBAAEE;AACF;YAAA;UAAA;AAGAD,eAAgB,CAACD,MAAOb,EAAM,aAAae;QAAA;MAChD;AAjCQnC,QAAA8B,IAAA,cAAA;AAqCT,UAAIO,KAAc;QAAC,MAAQ;QAAM,QAAU;QAAM,UAAY;QAAM,QAAU;QAC1D,QAAU;QAAM,MAAQ;QAAM,QAAU;QAAM,kBAAkB;MAAI;AAEvF,eAASC,GAAUC,GAAUC,GAAQzC,GAAM0C,GAAOC,GAAMC,GAAM;AAC5D,aAAK,WAAWJ,GAChB,KAAK,SAASC,GACd,KAAK,OAAOzC,GACZ,KAAK,OAAO2C,GACZ,KAAK,OAAOC,GACRF,KAAS,SAAM,KAAK,QAAQA;MACjC;AAPQzC,QAAAsC,IAAA,WAAA;AAST,eAASM,GAAQxB,GAAOyB,GAAS;AAC/B,YAAI,CAACnD;AAAY,iBAAO;AACxB,iBAASoD,IAAI1B,EAAM,WAAW0B,GAAGA,IAAIA,EAAE;AACrC,cAAIA,EAAE,QAAQD;AAAS,mBAAO;AAChC,iBAASE,IAAK3B,EAAM,SAAS2B,GAAIA,IAAKA,EAAG;AACvC,mBAASD,IAAIC,EAAG,MAAMD,GAAGA,IAAIA,EAAE;AAC7B,gBAAIA,EAAE,QAAQD;AAAS,qBAAO;MAEnC;AARQ7C,QAAA4C,IAAA,SAAA;AAUT,eAASI,GAAQ5B,GAAOH,GAAOlB,GAAMe,GAASJ,GAAQ;AACpD,YAAIuC,IAAK7B,EAAM;AAQf,aALA2B,EAAG,QAAQ3B,GAAO2B,EAAG,SAASrC,GAAQqC,EAAG,SAAS,MAAMA,EAAG,KAAKE,GAAIF,EAAG,QAAQ9B,GAE1EG,EAAM,QAAQ,eAAe,OAAO,MACvCA,EAAM,QAAQ,QAAQ,WAEZ;AACV,cAAI8B,IAAaD,EAAG,SAASA,EAAG,IAAA,IAAQxD,IAAW0D,IAAaC;AAChE,cAAIF,EAAWnD,GAAMe,CAAO,GAAG;AAC7B,mBAAMmC,EAAG,UAAUA,EAAGA,EAAG,SAAS,CAAC,EAAE;AACnCA,gBAAG,IAAG,EAAA;AACR,mBAAIF,EAAG,SAAeA,EAAG,SACrBhD,KAAQ,cAAc6C,GAAQxB,GAAON,CAAO,IAAU,eACnDG;UAAA;QAAA;MAGZ;AAnBQjB,QAAAgD,IAAA,SAAA;AAuBT,UAAID,IAAK,EAAC,OAAO,MAAM,QAAQ,MAAM,QAAQ,MAAM,IAAI,KAAI;AAC3D,eAASM,IAAO;AACd,iBAASC,IAAI,UAAU,SAAS,GAAGA,KAAK,GAAGA;AAAKP,YAAG,GAAG,KAAK,UAAUO,CAAC,CAAC;MACxE;AAFQtD,QAAAqD,GAAA,MAAA;AAGT,eAASnC,IAAO;AACd,eAAAmC,EAAK,MAAM,MAAM,SAAS,GACnB;MACR;AAHQrD,QAAAkB,GAAA,MAAA;AAIT,eAASqC,GAAOC,GAAMC,GAAM;AAC1B,iBAASX,IAAIW,GAAMX,GAAGA,IAAIA,EAAE;AAAM,cAAIA,EAAE,QAAQU;AAAM,mBAAO;AAC7D,eAAO;MACR;AAHQxD,QAAAuD,IAAA,QAAA;AAIT,eAASG,EAASb,GAAS;AACzB,YAAIzB,IAAQ2B,EAAG;AAEf,YADAA,EAAG,SAAS,OACR,CAAA,CAACrD,IACL;AAAA,cAAI0B,EAAM,SAAA;AACR,gBAAIA,EAAM,QAAQ,QAAQ,SAASA,EAAM,WAAWA,EAAM,QAAQ,OAAO;AAEvE,kBAAIuC,IAAaC,GAAkBf,GAASzB,EAAM,OAAO;AACzD,kBAAIuC,KAAc,MAAM;AACtBvC,kBAAM,UAAUuC;AAChB;cAAA;YAAA,WAEO,CAACJ,GAAOV,GAASzB,EAAM,SAAS,GAAG;AAC5CA,gBAAM,YAAY,IAAIyC,EAAIhB,GAASzB,EAAM,SAAS;AAClD;YAAA;UAAA;AAIA/B,YAAa,cAAc,CAACkE,GAAOV,GAASzB,EAAM,UAAU,MAC9DA,EAAM,aAAa,IAAIyC,EAAIhB,GAASzB,EAAM,UAAU;QAAA;MACvD;AApBQpB,QAAA0D,GAAA,UAAA;AAqBT,eAASE,GAAkBf,GAASiB,GAAS;AAC3C,YAAKA;AAEE,cAAIA,EAAQ,OAAO;AACxB,gBAAIC,IAAQH,GAAkBf,GAASiB,EAAQ,IAAI;AACnD,mBAAKC,IACDA,KAASD,EAAQ,OAAaA,IAC3B,IAAIE,EAAQD,GAAOD,EAAQ,MAAM,IAAI,IAFzB;UAAA;AAGd,mBAAIP,GAAOV,GAASiB,EAAQ,IAAI,IAC9BA,IAEA,IAAIE,EAAQF,EAAQ,MAAM,IAAID,EAAIhB,GAASiB,EAAQ,IAAI,GAAG,KAAK;;AATtE,iBAAO;MAWV;AAbQ9D,QAAA4D,IAAA,mBAAA;AAeT,eAASK,GAAWT,GAAM;AACxB,eAAOA,KAAQ,YAAYA,KAAQ,aAAaA,KAAQ,eAAeA,KAAQ,cAAcA,KAAQ;MACtG;AAFQxD,QAAAiE,IAAA,YAAA;AAMT,eAASD,EAAQtB,GAAMwB,GAAMC,GAAO;AAAE,aAAK,OAAOzB,GAAM,KAAK,OAAOwB,GAAM,KAAK,QAAQC;MAAO;AAArFnE,QAAAgE,GAAA,SAAA;AACT,eAASH,EAAIL,GAAM5C,GAAM;AAAE,aAAK,OAAO4C,GAAM,KAAK,OAAO5C;MAAM;AAAtDZ,QAAA6D,GAAA,KAAA;AAET,UAAIO,KAAc,IAAIP,EAAI,QAAQ,IAAIA,EAAI,aAAa,IAAI,CAAC;AAC5D,eAASQ,IAAc;AACrBtB,UAAG,MAAM,UAAU,IAAIiB,EAAQjB,EAAG,MAAM,SAASA,EAAG,MAAM,WAAW,KAAK,GAC1EA,EAAG,MAAM,YAAYqB;MACtB;AAHQpE,QAAAqE,GAAA,aAAA;AAIT,eAASC,KAAmB;AAC1BvB,UAAG,MAAM,UAAU,IAAIiB,EAAQjB,EAAG,MAAM,SAASA,EAAG,MAAM,WAAW,IAAI,GACzEA,EAAG,MAAM,YAAY;MACtB;AAHQ/C,QAAAsE,IAAA,kBAAA,GAITD,EAAY,MAAMC,GAAiB,MAAM;AACzC,eAASC,IAAa;AACpBxB,UAAG,MAAM,YAAYA,EAAG,MAAM,QAAQ,MACtCA,EAAG,MAAM,UAAUA,EAAG,MAAM,QAAQ;MACrC;AAHQ/C,QAAAuE,GAAA,YAAA,GAITA,EAAW,MAAM;AACjB,eAASC,EAAQzE,GAAM4C,GAAM;AAC3B,YAAI8B,IAASzE,EAAA,WAAW;AACtB,cAAIoB,IAAQ2B,EAAG,OAAO2B,IAAStD,EAAM;AACrC,cAAIA,EAAM,QAAQ,QAAQ;AAAQsD,gBAAStD,EAAM,QAAQ;;AACpD,qBAASuD,IAAQvD,EAAM,SAASuD,KAASA,EAAM,QAAQ,OAAOA,EAAM,OAAOA,IAAQA,EAAM;AAC5FD,kBAASC,EAAM;AACjBvD,YAAM,UAAU,IAAIkB,GAAUoC,GAAQ3B,EAAG,OAAO,OAAQ,GAAEhD,GAAM,MAAMqB,EAAM,SAASuB,CAAI;QAC/F,GANiB,QAAA;AAOb,eAAA8B,EAAO,MAAM,MACNA;MACR;AAVQzE,QAAAwE,GAAA,SAAA;AAWT,eAASI,IAAS;AAChB,YAAIxD,IAAQ2B,EAAG;AACX3B,UAAM,QAAQ,SACZA,EAAM,QAAQ,QAAQ,QACxBA,EAAM,WAAWA,EAAM,QAAQ,WACjCA,EAAM,UAAUA,EAAM,QAAQ;MAEjC;AAPQpB,QAAA4E,GAAA,QAAA,GAQTA,EAAO,MAAM;AAEb,eAASC,EAAOC,GAAQ;AACtB,iBAASC,EAAIhF,GAAM;AACjB,iBAAIA,KAAQ+E,IAAe5D,EAAAA,IAClB4D,KAAU,OAAO/E,KAAQ,OAAOA,KAAQ,OAAOA,KAAQ,MAAYsD,EAAAA,IAChEnC,EAAK6D,CAAG;QAAA;AAHb,eAAA/E,EAAA+E,GAAA,KAAA,GAKFA;MACR;AAPQ/E,QAAA6E,GAAA,QAAA;AAST,eAASzB,EAAUrD,GAAMiF,GAAO;AAC9B,eAAIjF,KAAQ,QAAcmB,EAAKsD,EAAQ,UAAUQ,CAAK,GAAGC,IAAQJ,EAAO,GAAG,GAAGD,CAAM,IAChF7E,KAAQ,cAAoBmB,EAAKsD,EAAQ,MAAM,GAAGU,IAAW9B,GAAWwB,CAAM,IAC9E7E,KAAQ,cAAoBmB,EAAKsD,EAAQ,MAAM,GAAGpB,GAAWwB,CAAM,IACnE7E,KAAQ,cAAoBgD,EAAG,OAAO,MAAM,SAAS,KAAK,IAAI7B,EAAM,IAAGA,EAAKsD,EAAQ,MAAM,GAAGW,GAAiBN,EAAO,GAAG,GAAGD,CAAM,IACjI7E,KAAQ,aAAmBmB,EAAK2D,EAAO,GAAG,CAAC,IAC3C9E,KAAQ,MAAYmB,EAAKsD,EAAQ,GAAG,GAAGF,IAAkBH,IAAOS,GAAQL,CAAU,IAClFxE,KAAQ,MAAYmB,EAAAA,IACpBnB,KAAQ,QACNgD,EAAG,MAAM,QAAQ,QAAQ,UAAUA,EAAG,MAAM,GAAGA,EAAG,MAAM,GAAG,SAAS,CAAC,KAAK6B,KAC5E7B,EAAG,MAAM,GAAG,IAAK,EAAA,GACZ7B,EAAKsD,EAAQ,MAAM,GAAGU,IAAW9B,GAAWwB,GAAQQ,EAAS,KAElErF,KAAQ,aAAmBmB,EAAKmE,CAAW,IAC3CtF,KAAQ,QAAcmB,EAAKsD,EAAQ,MAAM,GAAGF,IAAkBgB,IAASlC,GAAWmB,GAAYK,CAAM,IACpG7E,KAAQ,WAAYJ,KAAQqF,KAAS,eACvCjC,EAAG,SAAS,WACL7B,EAAKsD,EAAQ,QAAQzE,KAAQ,UAAUA,IAAOiF,CAAK,GAAGO,IAAWX,CAAM,KAE5E7E,KAAQ,aACNJ,KAAQqF,KAAS,aACnBjC,EAAG,SAAS,WACL7B,EAAKkC,CAAS,KACZzD,MAASqF,KAAS,YAAYA,KAAS,UAAUA,KAAS,WAAWjC,EAAG,OAAO,MAAM,UAAU,KAAK,KAC7GA,EAAG,SAAS,WACRiC,KAAS,SAAe9D,EAAKsE,EAAO,IAC/BR,KAAS,SAAe9D,EAAKuE,IAAUZ,EAAO,UAAU,GAAGa,GAAUb,EAAO,GAAG,CAAC,IAC7E3D,EAAKsD,EAAQ,MAAM,GAAGmB,GAASd,EAAO,GAAG,GAAGL,EAAQ,GAAG,GAAGL,IAAOS,GAAQA,CAAM,KAClFjF,KAAQqF,KAAS,eAC1BjC,EAAG,SAAS,WACL7B,EAAKsD,EAAQ,MAAM,GAAGrB,GAAYC,GAAWwB,CAAM,KACjDjF,KAAQqF,KAAS,cAC1BjC,EAAG,SAAS,WACL7B,EAAKkC,CAAS,KAEdlC,EAAKsD,EAAQ,MAAM,GAAGoB,EAAU,IAGvC7F,KAAQ,WAAiBmB;UAAKsD,EAAQ,MAAM;UAAGU;UAAWL,EAAO,GAAG;UAAGL,EAAQ,KAAK,QAAQ;UAAGF;UACjEH;UAAOS;UAAQA;UAAQL;QAAU,IAC/DxE,KAAQ,SAAemB,EAAKiC,GAAY0B,EAAO,GAAG,CAAC,IACnD9E,KAAQ,YAAkBmB,EAAK2D,EAAO,GAAG,CAAC,IAC1C9E,KAAQ,UAAgBmB,EAAKsD,EAAQ,MAAM,GAAGH,GAAawB,IAAmBzC,GAAWwB,GAAQL,CAAU,IAC3GxE,KAAQ,WAAiBmB,EAAKsD,EAAQ,MAAM,GAAGsB,IAAalB,CAAM,IAClE7E,KAAQ,WAAiBmB,EAAKsD,EAAQ,MAAM,GAAGuB,IAAanB,CAAM,IAClE7E,KAAQ,UAAgBmB,EAAKkC,CAAS,IACtC4B,KAAS,MAAY9D,EAAKiC,GAAYC,CAAS,IAC5CC,EAAKmB,EAAQ,MAAM,GAAGrB,GAAY0B,EAAO,GAAG,GAAGD,CAAM;MAC7D;AAhDQ5E,QAAAoD,GAAA,WAAA;AAiDT,eAASyC,GAAkB9F,GAAM;AAC/B,YAAIA,KAAQ;AAAK,iBAAOmB,EAAK8E,GAAQnB,EAAO,GAAG,CAAC;MACjD;AAFQ7E,QAAA6F,IAAA,mBAAA;AAGT,eAAS1C,EAAWpD,GAAMiF,GAAO;AAC/B,eAAOiB,GAAgBlG,GAAMiF,GAAO,KAAK;MAC1C;AAFQhF,QAAAmD,GAAA,YAAA;AAGT,eAAS+C,EAAkBnG,GAAMiF,GAAO;AACtC,eAAOiB,GAAgBlG,GAAMiF,GAAO,IAAI;MACzC;AAFQhF,QAAAkG,GAAA,mBAAA;AAGT,eAAShB,GAAUnF,GAAM;AACvB,eAAIA,KAAQ,MAAYsD,EAAM,IACvBnC,EAAKsD,EAAQ,GAAG,GAAGW,GAAiBN,EAAO,GAAG,GAAGD,CAAM;MAC/D;AAHQ5E,QAAAkF,IAAA,WAAA;AAIT,eAASe,GAAgBlG,GAAMiF,GAAOmB,GAAS;AAC7C,YAAIpD,EAAG,MAAM,cAAcA,EAAG,OAAO,OAAO;AAC1C,cAAIqD,IAAOD,IAAUE,KAAmBC;AACxC,cAAIvG,KAAQ;AAAK,mBAAOmB,EAAKmD,GAAaG,EAAQ,GAAG,GAAG+B,EAASP,GAAQ,GAAG,GAAGpB,GAAQC,EAAO,IAAI,GAAGuB,GAAM7B,CAAU;AAChH,cAAIxE,KAAQ;AAAY,mBAAOsD,EAAKgB,GAAasB,GAASd,EAAO,IAAI,GAAGuB,GAAM7B,CAAU;QAAA;AAG/F,YAAIiC,IAAUL,IAAUM,IAAuBC;AAC/C,eAAIrE,GAAY,eAAetC,CAAI,IAAUmB,EAAKsF,CAAO,IACrDzG,KAAQ,aAAmBmB,EAAKmE,GAAamB,CAAO,IACpDzG,KAAQ,WAAYJ,KAAQqF,KAAS,eAAgBjC,EAAG,SAAS,WAAkB7B,EAAKsD,EAAQ,MAAM,GAAGmC,IAAiB/B,CAAM,KAChI7E,KAAQ,eAAeA,KAAQ,UAAgBmB,EAAKiF,IAAUD,IAAoB/C,CAAU,IAC5FpD,KAAQ,MAAYmB,EAAKsD,EAAQ,GAAG,GAAGW,GAAiBN,EAAO,GAAG,GAAGD,GAAQ4B,CAAO,IACpFzG,KAAQ,cAAcA,KAAQ,WAAiBmB,EAAKiF,IAAUD,IAAoB/C,CAAU,IAC5FpD,KAAQ,MAAYmB,EAAKsD,EAAQ,GAAG,GAAGoC,IAAchC,GAAQ4B,CAAO,IACpEzG,KAAQ,MAAY8G,EAAaC,IAAS,KAAK,MAAMN,CAAO,IAC5DzG,KAAQ,UAAgBsD,EAAK0D,IAAOP,CAAO,IAC3CzG,KAAQ,QAAcmB,EAAK8F,GAAYb,CAAO,CAAC,IAC5CjF,EAAI;MACZ;AAnBQlB,QAAAiG,IAAA,iBAAA;AAoBT,eAASd,EAAgBpF,GAAM;AAC7B,eAAIA,EAAK,MAAM,YAAY,IAAUsD,EAAI,IAClCA,EAAKF,CAAU;MACvB;AAHQnD,QAAAmF,GAAA,iBAAA;AAKT,eAASuB,EAAmB3G,GAAMiF,GAAO;AACvC,eAAIjF,KAAQ,MAAYmB,EAAKiE,CAAe,IACrCsB,EAAqB1G,GAAMiF,GAAO,KAAK;MAC/C;AAHQhF,QAAA0G,GAAA,oBAAA;AAIT,eAASD,EAAqB1G,GAAMiF,GAAOmB,GAAS;AAClD,YAAIc,IAAKd,KAAW,QAAQO,IAAqBD,GAC7CS,IAAOf,KAAW,QAAQhD,IAAa+C;AAC3C,YAAInG,KAAQ;AAAM,iBAAOmB,EAAKmD,GAAa8B,IAAUE,KAAmBC,IAAW/B,CAAU;AAC7F,YAAIxE,KAAQ;AACV,iBAAI,UAAU,KAAKiF,CAAK,KAAKrF,KAAQqF,KAAS,MAAY9D,EAAK+F,CAAE,IAC7DtH,KAAQqF,KAAS,OAAOjC,EAAG,OAAO,MAAM,4BAA4B,KAAK,IACpE7B,EAAKsD,EAAQ,GAAG,GAAG+B,EAASb,GAAU,GAAG,GAAGd,GAAQqC,CAAE,IAC3DjC,KAAS,MAAY9D,EAAKiC,GAAY0B,EAAO,GAAG,GAAGqC,CAAI,IACpDhG,EAAKgG,CAAI;AAElB,YAAInH,KAAQ;AAAW,iBAAOsD,EAAK0D,IAAOE,CAAE;AAC5C,YAAIlH,KAAQ,KACZ;AAAA,cAAIA,KAAQ;AAAK,mBAAO8G,EAAaX,GAAmB,KAAK,QAAQe,CAAE;AACvE,cAAIlH,KAAQ;AAAK,mBAAOmB,EAAKiG,IAAUF,CAAE;AACzC,cAAIlH,KAAQ;AAAK,mBAAOmB,EAAKsD,EAAQ,GAAG,GAAGW,GAAiBN,EAAO,GAAG,GAAGD,GAAQqC,CAAE;AACnF,cAAItH,KAAQqF,KAAS;AAAQ,mBAAAjC,EAAG,SAAS,WAAkB7B,EAAKwE,GAAUuB,CAAE;AAC5E,cAAIlH,KAAQ;AACV,mBAAAgD,EAAG,MAAM,WAAWA,EAAG,SAAS,YAChCA,EAAG,OAAO,OAAOA,EAAG,OAAO,MAAMA,EAAG,OAAO,QAAQ,CAAC,GAC7C7B,EAAKgG,CAAI;QAAA;MAEnB;AAtBQlH,QAAAyG,GAAA,sBAAA;AAuBT,eAASM,GAAMhH,GAAMiF,GAAO;AAC1B,eAAIjF,KAAQ,UAAgBsD,EAAAA,IACxB2B,EAAM,MAAMA,EAAM,SAAS,CAAC,KAAK,OAAa9D,EAAK6F,EAAK,IACrD7F,EAAKiE,GAAiBiC,EAAa;MAC3C;AAJQpH,QAAA+G,IAAA,OAAA;AAKT,eAASK,GAAcrH,GAAM;AAC3B,YAAIA,KAAQ;AACV,iBAAAgD,EAAG,SAAS,YACZA,EAAG,MAAM,WAAWtB,GACbP,EAAK6F,EAAK;MAEpB;AANQ/G,QAAAoH,IAAA,eAAA;AAOT,eAASd,GAAUvG,GAAM;AACvB,eAAA+B,GAAaiB,EAAG,QAAQA,EAAG,KAAK,GACzBM,EAAKtD,KAAQ,MAAMqD,IAAYD,CAAU;MACjD;AAHQnD,QAAAsG,IAAA,WAAA;AAIT,eAASD,GAAiBtG,GAAM;AAC9B,eAAA+B,GAAaiB,EAAG,QAAQA,EAAG,KAAK,GACzBM,EAAKtD,KAAQ,MAAMqD,IAAY8C,CAAiB;MACxD;AAHQlG,QAAAqG,IAAA,kBAAA;AAIT,eAASW,GAAYb,GAAS;AAC5B,eAAO,SAASpG,GAAM;AACpB,iBAAIA,KAAQ,MAAYmB,EAAKiF,IAAUkB,KAAgBC,EAAM,IACpDvH,KAAQ,cAAcJ,IAAauB,EAAKqG,IAAepB,IAAUM,IAAuBC,CAAkB,IACvGrD,EAAK8C,IAAUD,IAAoB/C,CAAU;QAC/D;MACG;AANQnD,QAAAgH,IAAA,aAAA;AAOT,eAASM,GAAOE,GAAGxC,GAAO;AACxB,YAAIA,KAAS;AAAY,iBAAAjC,EAAG,SAAS,WAAkB7B,EAAKwF,CAAkB;MAC/E;AAFQ1G,QAAAsH,IAAA,QAAA;AAGT,eAASD,GAAcG,GAAGxC,GAAO;AAC/B,YAAIA,KAAS;AAAY,iBAAAjC,EAAG,SAAS,WAAkB7B,EAAKuF,CAAoB;MACjF;AAFQzG,QAAAqH,IAAA,eAAA;AAGT,eAASzB,GAAW7F,GAAM;AACxB,eAAIA,KAAQ,MAAYmB,EAAK0D,GAAQxB,CAAS,IACvCC,EAAKqD,GAAoB7B,EAAO,GAAG,GAAGD,CAAM;MACpD;AAHQ5E,QAAA4F,IAAA,YAAA;AAIT,eAASuB,GAASpH,GAAM;AACtB,YAAIA,KAAQ;AAAa,iBAAAgD,EAAG,SAAS,YAAmB7B,EAAI;MAC7D;AAFQlB,QAAAmH,IAAA,UAAA;AAGT,eAASL,GAAQ/G,GAAMiF,GAAO;AAC5B,YAAIjF,KAAQ;AACV,iBAAAgD,EAAG,SAAS,YACL7B,EAAK4F,EAAO;AACd,YAAI/G,KAAQ,cAAcgD,EAAG,SAAS,WAAW;AAEtD,cADAA,EAAG,SAAS,YACRiC,KAAS,SAASA,KAAS;AAAO,mBAAO9D,EAAKuG,EAAY;AAC9D,cAAIzF;AACJ,iBAAIrC,KAAQoD,EAAG,MAAM,cAAcA,EAAG,OAAO,UAAUf,IAAIe,EAAG,OAAO,MAAM,YAAY,KAAK,OAC1FA,EAAG,MAAM,aAAaA,EAAG,OAAO,MAAMf,EAAE,CAAC,EAAE,SACtCd,EAAKwG,CAAS;QAAA,OAChB;AAAA,cAAI3H,KAAQ,YAAYA,KAAQ;AACrC,mBAAAgD,EAAG,SAASvD,KAAa,aAAcuD,EAAG,QAAQ,aAC3C7B,EAAKwG,CAAS;AAChB,cAAI3H,KAAQ;AACjB,mBAAOmB,EAAKwG,CAAS;AAChB,cAAI/H,KAAQsE,GAAWe,CAAK;AACjC,mBAAAjC,EAAG,SAAS,WACL7B,EAAK4F,EAAO;AACd,cAAI/G,KAAQ;AACjB,mBAAOmB,EAAKiC,GAAYwE,GAAW9C,EAAO,GAAG,GAAG6C,CAAS;AACpD,cAAI3H,KAAQ;AACjB,mBAAOmB,EAAKgF,GAAmBwB,CAAS;AACnC,cAAI1C,KAAS;AAClB,mBAAAjC,EAAG,SAAS,WACL7B,EAAK4F,EAAO;AACd,cAAI/G,KAAQ;AACjB,mBAAOsD,EAAKqE,CAAS;QAAA;MAExB;AA7BQ1H,QAAA8G,IAAA,SAAA;AA8BT,eAASW,GAAa1H,GAAM;AAC1B,eAAIA,KAAQ,aAAmBsD,EAAKqE,CAAS,KAC7C3E,EAAG,SAAS,YACL7B,EAAKmE,CAAW;MACxB;AAJQrF,QAAAyH,IAAA,cAAA;AAKT,eAASC,EAAU3H,GAAM;AACvB,YAAIA,KAAQ;AAAK,iBAAOmB,EAAKgF,CAAiB;AAC9C,YAAInG,KAAQ;AAAK,iBAAOsD,EAAKgC,CAAW;MACzC;AAHQrF,QAAA0H,GAAA,WAAA;AAIT,eAASnB,EAASqB,GAAMC,GAAKC,GAAK;AAChC,iBAASC,EAAQhI,GAAMiF,GAAO;AAC5B,cAAI8C,IAAMA,EAAI,QAAQ/H,CAAI,IAAI,KAAKA,KAAQ,KAAK;AAC9C,gBAAIiI,IAAMjF,EAAG,MAAM;AACnB,mBAAIiF,EAAI,QAAQ,WAAQA,EAAI,OAAOA,EAAI,OAAO,KAAK,IAC5C9G,EAAK,SAASnB,GAAMiF,GAAO;AAChC,qBAAIjF,KAAQ8H,KAAO7C,KAAS6C,IAAYxE,EAAM,IACvCA,EAAKuE,CAAI;YACjB,GAAEG,CAAO;UAAA;AAEZ,iBAAIhI,KAAQ8H,KAAO7C,KAAS6C,IAAY3G,EAAI,IACxC4G,KAAOA,EAAI,QAAQ,GAAG,IAAI,KAAWzE,EAAKuE,CAAI,IAC3C1G,EAAK2D,EAAOgD,CAAG,CAAC;QACxB;AAZQ,eAAA7H,EAAA+H,GAAA,SAAA,GAaF,SAAShI,GAAMiF,GAAO;AAC3B,iBAAIjF,KAAQ8H,KAAO7C,KAAS6C,IAAY3G,EAAI,IACrCmC,EAAKuE,GAAMG,CAAO;QAC/B;MACG;AAlBQ/H,QAAAuG,GAAA,UAAA;AAmBT,eAASM,EAAae,GAAMC,GAAKlF,GAAM;AACrC,iBAASW,IAAI,GAAGA,IAAI,UAAU,QAAQA;AACpCP,YAAG,GAAG,KAAK,UAAUO,CAAC,CAAC;AACzB,eAAOpC,EAAKsD,EAAQqD,GAAKlF,CAAI,GAAG4D,EAASqB,GAAMC,CAAG,GAAGjD,CAAM;MAC5D;AAJQ5E,QAAA6G,GAAA,cAAA;AAKT,eAAS1C,GAAMpE,GAAM;AACnB,eAAIA,KAAQ,MAAYmB,EAAAA,IACjBmC,EAAKD,GAAWe,EAAK;MAC7B;AAHQnE,QAAAmE,IAAA,OAAA;AAIT,eAASwD,EAAU5H,GAAMiF,GAAO;AAC9B,YAAIrF,GAAM;AACR,cAAII,KAAQ;AAAK,mBAAOmB,EAAKwE,CAAQ;AACrC,cAAIV,KAAS;AAAK,mBAAO9D,EAAKyG,CAAS;QAAA;MAE1C;AALQ3H,QAAA2H,GAAA,WAAA;AAMT,eAASM,GAAclI,GAAMiF,GAAO;AAClC,YAAIrF,MAASI,KAAQ,OAAOiF,KAAS;AAAO,iBAAO9D,EAAKwE,CAAQ;MACjE;AAFQ1F,QAAAiI,IAAA,eAAA;AAGT,eAASC,GAAanI,GAAM;AAC1B,YAAIJ,KAAQI,KAAQ;AAClB,iBAAIgD,EAAG,OAAO,MAAM,kBAAkB,KAAK,IAAU7B,EAAKiC,GAAYgF,IAAMzC,CAAQ,IACxExE,EAAKwE,CAAQ;MAE5B;AALQ1F,QAAAkI,IAAA,cAAA;AAMT,eAASC,GAAKX,GAAGxC,GAAO;AACtB,YAAIA,KAAS;AACX,iBAAAjC,EAAG,SAAS,WACL7B,EAAM;MAEhB;AALQlB,QAAAmI,IAAA,MAAA;AAMT,eAASzC,EAAS3F,GAAMiF,GAAO;AAC7B,YAAIA,KAAS,WAAWA,KAAS,YAAYA,KAAS,WAAWA,KAAS;AACxE,iBAAAjC,EAAG,SAAS,WACL7B,EAAK8D,KAAS,WAAWkB,IAAoBR,CAAQ;AAE9D,YAAI3F,KAAQ,cAAciF,KAAS;AACjC,iBAAAjC,EAAG,SAAS,QACL7B,EAAKkH,CAAS;AAEvB,YAAIpD,KAAS,OAAOA,KAAS;AAAK,iBAAO9D,EAAKwE,CAAQ;AACtD,YAAI3F,KAAQ,YAAYA,KAAQ,YAAYA,KAAQ;AAAQ,iBAAOmB,EAAKkH,CAAS;AACjF,YAAIrI,KAAQ;AAAK,iBAAOmB,EAAKsD,EAAQ,GAAG,GAAG+B,EAASb,GAAU,KAAK,GAAG,GAAGd,GAAQwD,CAAS;AAC1F,YAAIrI,KAAQ;AAAK,iBAAOmB,EAAKsD,EAAQ,GAAG,GAAG6D,IAAWzD,GAAQwD,CAAS;AACvE,YAAIrI,KAAQ;AAAK,iBAAOmB,EAAKqF,EAAS+B,IAAS,GAAG,GAAGC,IAAiBH,CAAS;AAC/E,YAAIrI,KAAQ;AAAK,iBAAOmB,EAAKqF,EAASb,GAAU,GAAG,GAAGA,CAAQ;AAC9D,YAAI3F,KAAQ;AAAW,iBAAOsD,EAAKmF,IAAWJ,CAAS;MACxD;AAhBQpI,QAAA0F,GAAA,UAAA;AAiBT,eAAS6C,GAAgBxI,GAAM;AAC7B,YAAIA,KAAQ;AAAM,iBAAOmB,EAAKwE,CAAQ;MACvC;AAFQ1F,QAAAuI,IAAA,iBAAA;AAGT,eAASF,GAAUtI,GAAM;AACvB,eAAIA,EAAK,MAAM,UAAU,IAAUmB,EAAM,IACrCnB,KAAQ,OAAOA,KAAQ,MAAYmB,EAAKmH,EAAS,IAC9ChF,EAAKoF,GAAUJ,EAAS;MAChC;AAJQrI,QAAAqI,IAAA,WAAA;AAKT,eAASI,EAAS1I,GAAMiF,GAAO;AAC7B,YAAIjF,KAAQ,cAAcgD,EAAG,SAAS;AACpC,iBAAAA,EAAG,SAAS,YACL7B,EAAKuH,CAAQ;AACf,YAAIzD,KAAS,OAAOjF,KAAQ,YAAYA,KAAQ;AACrD,iBAAOmB,EAAKuH,CAAQ;AACf,YAAI1I,KAAQ;AACjB,iBAAOmB,EAAKwE,CAAQ;AACf,YAAI3F,KAAQ;AACjB,iBAAOmB,EAAK2D,EAAO,UAAU,GAAGoD,IAAepD,EAAO,GAAG,GAAG4D,CAAQ;AAC/D,YAAI1I,KAAQ;AACjB,iBAAOsD,EAAKqF,GAAcD,CAAQ;AAC7B,YAAI,CAAC1I,EAAK,MAAM,YAAY;AACjC,iBAAOmB,EAAM;MAEhB;AAfQlB,QAAAyI,GAAA,UAAA;AAgBT,eAASD,GAAUzI,GAAMiF,GAAO;AAC9B,eAAIjF,KAAQ,UAAgBsD,EAAAA,IACxB2B,EAAM,MAAMA,EAAM,SAAS,CAAC,KAAK,OAAa9D,EAAKsH,EAAS,IACzDtH,EAAKwE,GAAUiD,EAAiB;MACxC;AAJQ3I,QAAAwI,IAAA,WAAA;AAKT,eAASG,GAAkB5I,GAAM;AAC/B,YAAIA,KAAQ;AACV,iBAAAgD,EAAG,SAAS,YACZA,EAAG,MAAM,WAAWtB,GACbP,EAAKsH,EAAS;MAExB;AANQxI,QAAA2I,IAAA,mBAAA;AAOT,eAASL,GAAQvI,GAAMiF,GAAO;AAC5B,eAAIjF,KAAQ,cAAcgD,EAAG,OAAO,MAAM,YAAY,KAAK,KAAKiC,KAAS,MAAY9D,EAAKoH,EAAO,IAC7FvI,KAAQ,MAAYmB,EAAKwE,CAAQ,IACjC3F,KAAQ,WAAiBmB,EAAKoH,EAAO,IAClCjF,EAAKqC,CAAQ;MACrB;AALQ1F,QAAAsI,IAAA,SAAA;AAMT,eAASF,EAAUrI,GAAMiF,GAAO;AAC9B,YAAIA,KAAS;AAAK,iBAAO9D,EAAKsD,EAAQ,GAAG,GAAG+B,EAASb,GAAU,GAAG,GAAGd,GAAQwD,CAAS;AACtF,YAAIpD,KAAS,OAAOjF,KAAQ,OAAOiF,KAAS;AAAK,iBAAO9D,EAAKwE,CAAQ;AACrE,YAAI3F,KAAQ;AAAK,iBAAOmB,EAAKwE,GAAUb,EAAO,GAAG,GAAGuD,CAAS;AAC7D,YAAIpD,KAAS,aAAaA,KAAS;AAAgB,iBAAAjC,EAAG,SAAS,WAAkB7B,EAAKwE,CAAQ;AAC9F,YAAIV,KAAS;AAAK,iBAAO9D,EAAKwE,GAAUb,EAAO,GAAG,GAAGa,CAAQ;MAC9D;AANQ1F,QAAAoI,GAAA,WAAA;AAOT,eAASb,GAAcC,GAAGxC,GAAO;AAC/B,YAAIA,KAAS;AAAK,iBAAO9D,EAAKsD,EAAQ,GAAG,GAAG+B,EAASb,GAAU,GAAG,GAAGd,GAAQwD,CAAS;MACvF;AAFQpI,QAAAuH,IAAA,eAAA;AAGT,eAASqB,KAAY;AACnB,eAAOvF,EAAKqC,GAAUmD,EAAgB;MACvC;AAFQ7I,QAAA4I,IAAA,WAAA;AAGT,eAASC,GAAiBrB,GAAGxC,GAAO;AAClC,YAAIA,KAAS;AAAK,iBAAO9D,EAAKwE,CAAQ;MACvC;AAFQ1F,QAAA6I,IAAA,kBAAA;AAGT,eAAS5D,GAAOuC,GAAGxC,GAAO;AACxB,eAAIA,KAAS,UAASjC,EAAG,SAAS,WAAkB7B,EAAKsE,EAAO,KACzDnC,EAAKsC,GAASgC,GAAWmB,GAAaC,EAAU;MACxD;AAHQ/I,QAAAiF,IAAA,QAAA;AAIT,eAASU,EAAQ5F,GAAMiF,GAAO;AAC5B,YAAIrF,KAAQsE,GAAWe,CAAK;AAAK,iBAAAjC,EAAG,SAAS,WAAkB7B,EAAKyE,CAAO;AAC3E,YAAI5F,KAAQ;AAAc,iBAAA2D,EAASsB,CAAK,GAAU9D,EAAI;AACtD,YAAInB,KAAQ;AAAU,iBAAOmB,EAAKyE,CAAO;AACzC,YAAI5F,KAAQ;AAAK,iBAAO8G,EAAamC,IAAY,GAAG;AACpD,YAAIjJ,KAAQ;AAAK,iBAAO8G,EAAaoC,IAAa,GAAG;MACtD;AANQjJ,QAAA2F,GAAA,SAAA;AAOT,eAASsD,GAAYlJ,GAAMiF,GAAO;AAChC,eAAIjF,KAAQ,cAAc,CAACgD,EAAG,OAAO,MAAM,SAAS,KAAK,KACvDW,EAASsB,CAAK,GACP9D,EAAK4H,CAAW,MAErB/I,KAAQ,eAAYgD,EAAG,SAAS,aAChChD,KAAQ,WAAiBmB,EAAKyE,CAAO,IACrC5F,KAAQ,MAAYsD,EAAAA,IACpBtD,KAAQ,MAAYmB,EAAKiC,GAAY0B,EAAO,GAAG,GAAGA,EAAO,GAAG,GAAGoE,EAAW,IACvE/H,EAAK2D,EAAO,GAAG,GAAGc,GAASmD,CAAW;MAC9C;AAVQ9I,QAAAiJ,IAAA,aAAA;AAWT,eAASD,KAAa;AACpB,eAAO3F,EAAKsC,GAASmD,CAAW;MACjC;AAFQ9I,QAAAgJ,IAAA,YAAA;AAGT,eAASF,EAAYI,GAAOlE,GAAO;AACjC,YAAIA,KAAS;AAAK,iBAAO9D,EAAKgF,CAAiB;MAChD;AAFQlG,QAAA8I,GAAA,aAAA;AAGT,eAASC,GAAWhJ,GAAM;AACxB,YAAIA,KAAQ;AAAK,iBAAOmB,EAAK+D,EAAM;MACpC;AAFQjF,QAAA+I,IAAA,YAAA;AAGT,eAAS3D,GAAUrF,GAAMiF,GAAO;AAC9B,YAAIjF,KAAQ,eAAeiF,KAAS;AAAQ,iBAAO9D,EAAKsD,EAAQ,QAAQ,MAAM,GAAGpB,GAAWwB,CAAM;MACnG;AAFQ5E,QAAAoF,IAAA,WAAA;AAGT,eAASE,GAAQvF,GAAMiF,GAAO;AAC5B,YAAIA,KAAS;AAAS,iBAAO9D,EAAKoE,EAAO;AACzC,YAAIvF,KAAQ;AAAK,iBAAOmB,EAAKsD,EAAQ,GAAG,GAAG2E,IAAUvE,CAAM;MAC5D;AAHQ5E,QAAAsF,IAAA,SAAA;AAIT,eAAS6D,GAASpJ,GAAM;AACtB,eAAIA,KAAQ,QAAcmB,EAAK+D,IAAQmE,CAAQ,IAC3CrJ,KAAQ,aAAmBmB,EAAKkI,CAAQ,IACrC/F,EAAK+F,CAAQ;MACrB;AAJQpJ,QAAAmJ,IAAA,UAAA;AAKT,eAASC,EAASrJ,GAAMiF,GAAO;AAC7B,eAAIjF,KAAQ,MAAYmB,EAAM,IAC1BnB,KAAQ,MAAYmB,EAAKkI,CAAQ,IACjCpE,KAAS,QAAQA,KAAS,QAAQjC,EAAG,SAAS,WAAkB7B,EAAKiC,GAAYiG,CAAQ,KACtF/F,EAAKF,GAAYiG,CAAQ;MACjC;AALQpJ,QAAAoJ,GAAA,UAAA;AAMT,eAAS/D,EAAYtF,GAAMiF,GAAO;AAChC,YAAIA,KAAS;AAAM,iBAAAjC,EAAG,SAAS,WAAkB7B,EAAKmE,CAAW;AACjE,YAAItF,KAAQ;AAAa,iBAAA2D,EAASsB,CAAK,GAAU9D,EAAKmE,CAAW;AACjE,YAAItF,KAAQ;AAAK,iBAAOmB,EAAKmD,GAAaG,EAAQ,GAAG,GAAG+B,EAASP,GAAQ,GAAG,GAAGpB,GAAQsD,IAAc9E,GAAWmB,CAAU;AAC1H,YAAI5E,KAAQqF,KAAS;AAAK,iBAAO9D,EAAKsD,EAAQ,GAAG,GAAG+B,EAASqC,IAAW,GAAG,GAAGhE,GAAQS,CAAW;MAClG;AALQrF,QAAAqF,GAAA,aAAA;AAMT,eAASqD,EAAa3I,GAAMiF,GAAO;AACjC,YAAIA,KAAS;AAAM,iBAAAjC,EAAG,SAAS,WAAkB7B,EAAKwH,CAAY;AAClE,YAAI3I,KAAQ;AAAa,iBAAA2D,EAASsB,CAAK,GAAU9D,EAAKwH,CAAY;AAClE,YAAI3I,KAAQ;AAAK,iBAAOmB,EAAKmD,GAAaG,EAAQ,GAAG,GAAG+B,EAASP,GAAQ,GAAG,GAAGpB,GAAQsD,IAAc3D,CAAU;AAC/G,YAAI5E,KAAQqF,KAAS;AAAK,iBAAO9D,EAAKsD,EAAQ,GAAG,GAAG+B,EAASqC,IAAW,GAAG,GAAGhE,GAAQ8D,CAAY;MACnG;AALQ1I,QAAA0I,GAAA,cAAA;AAMT,eAASjD,GAAS1F,GAAMiF,GAAO;AAC7B,YAAIjF,KAAQ,aAAaA,KAAQ;AAC/B,iBAAAgD,EAAG,SAAS,QACL7B,EAAKuE,EAAQ;AACf,YAAIT,KAAS;AAClB,iBAAO9D,EAAKsD,EAAQ,GAAG,GAAG+B,EAASqC,IAAW,GAAG,GAAGhE,CAAM;MAE7D;AAPQ5E,QAAAyF,IAAA,UAAA;AAQT,eAASO,EAAOjG,GAAMiF,GAAO;AAE3B,eADIA,KAAS,OAAK9D,EAAKiC,GAAY6C,CAAM,GACrCjG,KAAQ,WAAiBmB,EAAK8E,CAAM,IACpCrG,KAAQsE,GAAWe,CAAK,KAAKjC,EAAG,SAAS,WAAkB7B,EAAK8E,CAAM,KACtErG,KAAQI,KAAQ,SAAemB,EAAKyG,GAAWmB,CAAW,IACvDzF,EAAKsC,GAASgC,GAAWmB,CAAW;MAC5C;AANQ9I,QAAAgG,GAAA,QAAA;AAOT,eAASW,GAAgB5G,GAAMiF,GAAO;AAEpC,eAAIjF,KAAQ,aAAmBwF,GAAUxF,GAAMiF,CAAK,IAC7CqE,GAAetJ,GAAMiF,CAAK;MAClC;AAJQhF,QAAA2G,IAAA,iBAAA;AAKT,eAASpB,GAAUxF,GAAMiF,GAAO;AAC9B,YAAIjF,KAAQ;AAAa,iBAAA2D,EAASsB,CAAK,GAAU9D,EAAKmI,EAAc;MACrE;AAFQrJ,QAAAuF,IAAA,WAAA;AAGT,eAAS8D,GAAetJ,GAAMiF,GAAO;AACnC,YAAIA,KAAS;AAAK,iBAAO9D,EAAKsD,EAAQ,GAAG,GAAG+B,EAASqC,IAAW,GAAG,GAAGhE,GAAQyE,EAAc;AAC5F,YAAIrE,KAAS,aAAaA,KAAS,gBAAiBrF,KAAQI,KAAQ;AAClE,iBAAIiF,KAAS,iBAAcjC,EAAG,SAAS,YAChC7B,EAAKvB,IAAO+F,IAAWvC,GAAYkG,EAAc;AAE1D,YAAItJ,KAAQ;AAAK,iBAAOmB,EAAKsD,EAAQ,GAAG,GAAG8E,GAAW1E,CAAM;MAC7D;AAPQ5E,QAAAqJ,IAAA,gBAAA;AAQT,eAASC,EAAUvJ,GAAMiF,GAAO;AAC9B,YAAIjF,KAAQ,WACPA,KAAQ,eACPiF,KAAS,YAAYA,KAAS,SAASA,KAAS,SAAUrF,KAAQsE,GAAWe,CAAK,MACnFjC,EAAG,OAAO,MAAM,wBAAwB,KAAK;AAChD,iBAAAA,EAAG,SAAS,WACL7B,EAAKoI,CAAS;AAEvB,YAAIvJ,KAAQ,cAAcgD,EAAG,SAAS;AACpC,iBAAAA,EAAG,SAAS,YACL7B,EAAKqI,GAAYD,CAAS;AAEnC,YAAIvJ,KAAQ,YAAYA,KAAQ;AAAU,iBAAOmB,EAAKqI,GAAYD,CAAS;AAC3E,YAAIvJ,KAAQ;AACV,iBAAOmB,EAAKiC,GAAYwE,GAAW9C,EAAO,GAAG,GAAG0E,GAAYD,CAAS;AACvE,YAAItE,KAAS;AACX,iBAAAjC,EAAG,SAAS,WACL7B,EAAKoI,CAAS;AAEvB,YAAI3J,KAAQI,KAAQ;AAAK,iBAAOsD,EAAKqF,GAAcY,CAAS;AAC5D,YAAIvJ,KAAQ,OAAOA,KAAQ;AAAK,iBAAOmB,EAAKoI,CAAS;AACrD,YAAIvJ,KAAQ;AAAK,iBAAOmB,EAAAA;AACxB,YAAI8D,KAAS;AAAK,iBAAO9D,EAAKiC,GAAYmG,CAAS;MACpD;AAvBQtJ,QAAAsJ,GAAA,WAAA;AAwBT,eAASC,EAAWxJ,GAAMiF,GAAO;AAE/B,YADIA,KAAS,OACTA,KAAS;AAAK,iBAAO9D,EAAKqI,CAAU;AACxC,YAAIxJ,KAAQ;AAAK,iBAAOmB,EAAKwE,GAAUoD,CAAW;AAClD,YAAI9D,KAAS;AAAK,iBAAO9D,EAAKgF,CAAiB;AAC/C,YAAIpC,IAAUf,EAAG,MAAM,QAAQ,MAAMyG,IAAc1F,KAAWA,EAAQ,QAAQ;AAC9E,eAAOT,EAAKmG,IAAcd,IAAerD,CAAW;MACrD;AAPQrF,QAAAuJ,GAAA,YAAA;AAQT,eAASzD,GAAY/F,GAAMiF,GAAO;AAChC,eAAIA,KAAS,OAAOjC,EAAG,SAAS,WAAkB7B,EAAKuI,IAAW5E,EAAO,GAAG,CAAC,KACzEG,KAAS,aAAajC,EAAG,SAAS,WAAkB7B,EAAKiC,GAAY0B,EAAO,GAAG,CAAC,KAChF9E,KAAQ,MAAYmB,EAAKqF,EAASmD,IAAa,GAAG,GAAGD,IAAW5E,EAAO,GAAG,CAAC,IACxExB,EAAKD,CAAS;MACtB;AALQpD,QAAA8F,IAAA,aAAA;AAMT,eAAS4D,GAAY3J,GAAMiF,GAAO;AAChC,YAAIA,KAAS;AAAQ,iBAAAjC,EAAG,SAAS,WAAkB7B,EAAK2D,EAAO,UAAU,CAAC;AAC1E,YAAI9E,KAAQ;AAAY,iBAAOsD,EAAK6C,GAAmBwD,EAAW;MACnE;AAHQ1J,QAAA0J,IAAA,aAAA;AAIT,eAAS3D,GAAYhG,GAAM;AACzB,eAAIA,KAAQ,WAAiBmB,EAAAA,IACzBnB,KAAQ,MAAYsD,EAAKF,CAAU,IACnCpD,KAAQ,MAAYsD,EAAKqD,CAAkB,IACxCrD,EAAKsG,IAAYC,IAAkBH,EAAS;MACpD;AALQzJ,QAAA+F,IAAA,aAAA;AAMT,eAAS4D,GAAW5J,GAAMiF,GAAO;AAC/B,eAAIjF,KAAQ,MAAY8G,EAAa8C,IAAY,GAAG,KAChD5J,KAAQ,cAAY2D,EAASsB,CAAK,GAClCA,KAAS,QAAKjC,EAAG,SAAS,YACvB7B,EAAK2I,EAAO;MACpB;AALQ7J,QAAA2J,IAAA,YAAA;AAMT,eAASC,GAAiB7J,GAAM;AAC9B,YAAIA,KAAQ;AAAK,iBAAOmB,EAAKyI,IAAYC,EAAgB;MAC1D;AAFQ5J,QAAA4J,IAAA,kBAAA;AAGT,eAASC,GAAQX,GAAOlE,GAAO;AAC7B,YAAIA,KAAS;AAAQ,iBAAAjC,EAAG,SAAS,WAAkB7B,EAAKyI,EAAU;MACnE;AAFQ3J,QAAA6J,IAAA,SAAA;AAGT,eAASJ,GAAUP,GAAOlE,GAAO;AAC/B,YAAIA,KAAS;AAAU,iBAAAjC,EAAG,SAAS,WAAkB7B,EAAKiC,CAAU;MACrE;AAFQnD,QAAAyJ,IAAA,WAAA;AAGT,eAAS7C,GAAa7G,GAAM;AAC1B,eAAIA,KAAQ,MAAYmB,EAAAA,IACjBmC,EAAKkD,EAASL,GAAmB,GAAG,CAAC;MAC7C;AAHQlG,QAAA4G,IAAA,cAAA;AAIT,eAASpB,KAAU;AACjB,eAAOnC,EAAKmB,EAAQ,MAAM,GAAGmB,GAASd,EAAO,GAAG,GAAGL,EAAQ,GAAG,GAAG+B,EAASuD,IAAY,GAAG,GAAGlF,GAAQA,CAAM;MAC3G;AAFQ5E,QAAAwF,IAAA,SAAA;AAGT,eAASsE,KAAa;AACpB,eAAOzG,EAAKsC,GAASmD,CAAW;MACjC;AAFQ9I,QAAA8J,IAAA,YAAA;AAIT,eAASC,GAAqB3I,GAAO4I,GAAW;AAC9C,eAAO5I,EAAM,YAAY,cAAcA,EAAM,YAAY,OACvDb,GAAe,KAAKyJ,EAAU,OAAO,CAAC,CAAC,KACvC,OAAO,KAAKA,EAAU,OAAO,CAAC,CAAC;MAClC;AAJQhK,QAAA+J,IAAA,sBAAA;AAMT,eAASvI,GAAkBd,GAAQU,GAAO6I,GAAQ;AAChD,eAAO7I,EAAM,YAAYD,KACvB,iFAAiF,KAAKC,EAAM,QAAQ,KACnGA,EAAM,YAAY,WAAW,SAAS,KAAKV,EAAO,OAAO,MAAM,GAAGA,EAAO,OAAOuJ,KAAU,EAAE,CAAC;MACjG;AAJQ,aAAAjK,EAAAwB,IAAA,mBAAA,GAQF;QACL,YAAY,SAAS0I,GAAY;AAC/B,cAAI9I,IAAQ;YACV,UAAUD;YACV,UAAU;YACV,IAAI,CAAE;YACN,SAAS,IAAImB,IAAW4H,KAAc,KAAK5K,GAAY,GAAG,SAAS,KAAK;YACxE,WAAWD,EAAa;YACxB,SAASA,EAAa,aAAa,IAAI2E,EAAQ,MAAM,MAAM,KAAK;YAChE,UAAUkG,KAAc;UAChC;AACM,iBAAI7K,EAAa,cAAc,OAAOA,EAAa,cAAc,aAC/D+B,EAAM,aAAa/B,EAAa,aAC3B+B;QACR;QAED,OAAO,SAASV,GAAQU,GAAO;AAO7B,cANIV,EAAO,IAAA,MACJU,EAAM,QAAQ,eAAe,OAAO,MACvCA,EAAM,QAAQ,QAAQ,QACxBA,EAAM,WAAWV,EAAO,YAAA,GACxBoB,GAAapB,GAAQU,CAAK,IAExBA,EAAM,YAAYG,MAAgBb,EAAO,SAAQ;AAAI,mBAAO;AAChE,cAAIO,IAAQG,EAAM,SAASV,GAAQU,CAAK;AACxC,iBAAIrB,KAAQ,YAAkBkB,KAC9BG,EAAM,WAAWrB,KAAQ,eAAee,MAAW,QAAQA,MAAW,QAAQ,WAAWf,GAClFiD,GAAQ5B,GAAOH,GAAOlB,GAAMe,IAASJ,CAAM;QACnD;QAED,QAAQ,SAASU,GAAO4I,GAAW;AACjC,cAAI5I,EAAM,YAAYG,MAAgBH,EAAM,YAAYK;AAAY,mBAAOtC,EAAW;AACtF,cAAIiC,EAAM,YAAYD;AAAW,mBAAO;AACxC,cAAIgJ,IAAYH,KAAaA,EAAU,OAAO,CAAC,GAAGI,IAAUhJ,EAAM,SAASiJ;AAE3E,cAAI,CAAC,aAAa,KAAKL,CAAS;AAAG,qBAAS1G,IAAIlC,EAAM,GAAG,SAAS,GAAGkC,KAAK,GAAG,EAAEA,GAAG;AAChF,kBAAIgH,IAAIlJ,EAAM,GAAGkC,CAAC;AAClB,kBAAIgH,KAAK1F;AAAQwF,oBAAUA,EAAQ;uBAC1BE,KAAKlF,MAAakF,KAAK/F;AAAY;YAAA;AAE9C,kBAAQ6F,EAAQ,QAAQ,UAAUA,EAAQ,QAAQ,YAC1CD,KAAa,QAASE,IAAMjJ,EAAM,GAAGA,EAAM,GAAG,SAAS,CAAC,OAClCiJ,KAAO3D,KAAsB2D,KAAO5D,MACrC,CAAC,mBAAmB,KAAKuD,CAAS;AAC7DI,gBAAUA,EAAQ;AAChB7K,gBAAmB6K,EAAQ,QAAQ,OAAOA,EAAQ,KAAK,QAAQ,WACjEA,IAAUA,EAAQ;AACpB,cAAIrK,IAAOqK,EAAQ,MAAMG,IAAUJ,KAAapK;AAEhD,iBAAIA,KAAQ,WAAiBqK,EAAQ,YAAYhJ,EAAM,YAAY,cAAcA,EAAM,YAAY,MAAMgJ,EAAQ,KAAK,SAAS,IAAI,KAC1HrK,KAAQ,UAAUoK,KAAa,MAAYC,EAAQ,WACnDrK,KAAQ,SAAeqK,EAAQ,WAAW9K,IAC1CS,KAAQ,SACRqK,EAAQ,YAAYL,GAAqB3I,GAAO4I,CAAS,IAAIzK,MAAmBD,IAAa,KAC7F8K,EAAQ,QAAQ,YAAY,CAACG,KAAWlL,EAAa,sBAAsB,QAC3E+K,EAAQ,YAAY,sBAAsB,KAAKJ,CAAS,IAAI1K,IAAa,IAAIA,KAC7E8K,EAAQ,QAAcA,EAAQ,UAAUG,IAAU,IAAI,KACnDH,EAAQ,YAAYG,IAAU,IAAIjL;QAC/C;QAED,eAAe;QACf,mBAAmBG,IAAW,OAAO;QACrC,iBAAiBA,IAAW,OAAO;QACnC,sBAAsBA,IAAW,OAAO;QACxC,aAAaA,IAAW,OAAO;QAC/B,MAAM;QACN,eAAe;QAEf,YAAYA,IAAW,SAAS;QAChC,YAAYD;QACZ,UAAUC;QAEV,mBAAmB+B;QAEnB,gBAAgB,SAASJ,GAAO;AAC9B4B,aAAQ5B,GAAO,QAAQ,QAAQ,QAAQ,IAAIjC,EAAW,aAAa,IAAI,GAAG,IAAI,CAAC;QAChF;MACL;IACA,CAAC,GAEDA,EAAW,eAAe,aAAa,cAAc,OAAO,GAE5DA,EAAW,WAAW,mBAAmB,YAAY,GACrDA,EAAW,WAAW,mBAAmB,YAAY,GACrDA,EAAW,WAAW,0BAA0B,YAAY,GAC5DA,EAAW,WAAW,4BAA4B,YAAY,GAC9DA,EAAW,WAAW,0BAA0B,YAAY,GAC5DA,EAAW,WAAW,oBAAoB,EAAE,MAAM,cAAc,MAAM,KAAI,CAAE,GAC5EA,EAAW,WAAW,sBAAsB,EAAE,MAAM,cAAc,MAAM,KAAI,CAAE,GAC9EA,EAAW,WAAW,6BAA6B,EAAE,MAAM,cAAc,MAAM,KAAA,CAAM,GACrFA,EAAW,WAAW,uBAAuB,EAAE,MAAM,cAAc,QAAQ,KAAI,CAAE,GACjFA,EAAW,WAAW,mBAAmB,EAAE,MAAM,cAAc,YAAY,KAAI,CAAE,GACjFA,EAAW,WAAW,0BAA0B,EAAE,MAAM,cAAc,YAAY,KAAI,CAAE;EAExF,CAAC;;;;;;;;", "names": ["mod", "require$$0", "CodeMirror", "config", "parserConfig", "indentUnit", "statementIndent", "jsonldMode", "jsonMode", "trackScope", "isTS", "wordRE", "keywords", "kw", "type", "__name", "A", "B", "C", "D", "operator", "atom", "isOperatorChar", "isJsonldKeyword", "readRegexp", "stream", "escaped", "next", "inSet", "content", "ret", "tp", "style", "cont", "tokenBase", "state", "ch", "tokenString", "tokenComment", "expressionAllowed", "tokenQuasi", "word", "quote", "maybeEnd", "brackets", "findFatArrow", "arrow", "m", "depth", "sawSomething", "pos", "bracket", "atomicTypes", "JSLexical", "indented", "column", "align", "prev", "info", "inScope", "varname", "v", "cx", "parseJS", "cc", "combinator", "expression", "statement", "pass", "i", "inList", "name", "list", "register", "newContext", "registerVarScoped", "Var", "context", "inner", "Context", "isModifier", "vars", "block", "defaultVars", "pushcontext", "pushblockcontext", "popcontext", "pushlex", "result", "indent", "outer", "poplex", "expect", "wanted", "exp", "value", "var<PERSON><PERSON>", "parenExpr", "maybeexpression", "<PERSON><PERSON><PERSON>", "functiondef", "forspec", "className", "enum<PERSON>f", "typename", "typeexpr", "pattern", "maybelabel", "maybeCatchBinding", "afterExport", "afterImport", "funarg", "expressionInner", "expressionNoComma", "noComma", "body", "arrowBodyNoComma", "arrowBody", "commasep", "maybeop", "maybeoperatorNoComma", "maybeoperatorComma", "classExpression", "arrayLiteral", "contCommasep", "objprop", "quasi", "<PERSON><PERSON><PERSON><PERSON>", "me", "expr", "property", "continueQuasi", "targetNoComma", "target", "maybeTypeArgs", "_", "getterSetter", "afterprop", "maybetype", "what", "end", "sep", "proceed", "lex", "maybetypeOrIn", "mayberettype", "isKW", "afterType", "typeprops", "typearg", "maybeReturnType", "quasiType", "typeprop", "functiondecl", "continueQuasiType", "typeparam", "maybeTypeDefault", "maybeAssign", "vardefCont", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "proppa<PERSON>n", "_type", "forspec1", "forspec2", "classNameAfter", "classBody", "classfield", "isInterface", "maybeFrom", "exportField", "importSpec", "maybeMoreImports", "maybeAs", "enummember", "isContinuedStatement", "textAfter", "backUp", "basecolumn", "firstChar", "lexical", "top", "c", "closing"]}