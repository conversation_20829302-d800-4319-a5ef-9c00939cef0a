{"version": 3, "sources": ["../../../../../@graphiql/codemirror-graphql/esm/utils/jump-addon.js", "../../../../../@graphiql/codemirror-graphql/esm/jump.js"], "sourcesContent": ["import CodeMirror from 'codemirror';\nCodeMirror.defineOption('jump', false, (cm, options, old) => {\n    if (old && old !== CodeMirror.Init) {\n        const oldOnMouseOver = cm.state.jump.onMouseOver;\n        CodeMirror.off(cm.getWrapperElement(), 'mouseover', oldOnMouseOver);\n        const oldOnMouseOut = cm.state.jump.onMouseOut;\n        CodeMirror.off(cm.getWrapperElement(), 'mouseout', oldOnMouseOut);\n        CodeMirror.off(document, 'keydown', cm.state.jump.onKeyDown);\n        delete cm.state.jump;\n    }\n    if (options) {\n        const state = (cm.state.jump = {\n            options,\n            onMouseOver: onMouseOver.bind(null, cm),\n            onMouseOut: onMouseOut.bind(null, cm),\n            onKeyDown: onKeyDown.bind(null, cm),\n        });\n        CodeMirror.on(cm.getWrapperElement(), 'mouseover', state.onMouseOver);\n        CodeMirror.on(cm.getWrapperElement(), 'mouseout', state.onMouseOut);\n        CodeMirror.on(document, 'keydown', state.onKeyDown);\n    }\n});\nfunction onMouseOver(cm, event) {\n    const target = event.target || event.srcElement;\n    if (!(target instanceof HTMLElement)) {\n        return;\n    }\n    if ((target === null || target === void 0 ? void 0 : target.nodeName) !== 'SPAN') {\n        return;\n    }\n    const box = target.getBoundingClientRect();\n    const cursor = {\n        left: (box.left + box.right) / 2,\n        top: (box.top + box.bottom) / 2,\n    };\n    cm.state.jump.cursor = cursor;\n    if (cm.state.jump.isHoldingModifier) {\n        enableJumpMode(cm);\n    }\n}\nfunction onMouseOut(cm) {\n    if (!cm.state.jump.isHoldingModifier && cm.state.jump.cursor) {\n        cm.state.jump.cursor = null;\n        return;\n    }\n    if (cm.state.jump.isHoldingModifier && cm.state.jump.marker) {\n        disableJumpMode(cm);\n    }\n}\nfunction onKeyDown(cm, event) {\n    if (cm.state.jump.isHoldingModifier || !isJumpModifier(event.key)) {\n        return;\n    }\n    cm.state.jump.isHoldingModifier = true;\n    if (cm.state.jump.cursor) {\n        enableJumpMode(cm);\n    }\n    const onKeyUp = (upEvent) => {\n        if (upEvent.code !== event.code) {\n            return;\n        }\n        cm.state.jump.isHoldingModifier = false;\n        if (cm.state.jump.marker) {\n            disableJumpMode(cm);\n        }\n        CodeMirror.off(document, 'keyup', onKeyUp);\n        CodeMirror.off(document, 'click', onClick);\n        cm.off('mousedown', onMouseDown);\n    };\n    const onClick = (clickEvent) => {\n        const { destination, options } = cm.state.jump;\n        if (destination) {\n            options.onClick(destination, clickEvent);\n        }\n    };\n    const onMouseDown = (_, downEvent) => {\n        if (cm.state.jump.destination) {\n            downEvent.codemirrorIgnore = true;\n        }\n    };\n    CodeMirror.on(document, 'keyup', onKeyUp);\n    CodeMirror.on(document, 'click', onClick);\n    cm.on('mousedown', onMouseDown);\n}\nconst isMac = typeof navigator !== 'undefined' &&\n    navigator &&\n    navigator.appVersion.includes('Mac');\nfunction isJumpModifier(key) {\n    return key === (isMac ? 'Meta' : 'Control');\n}\nfunction enableJumpMode(cm) {\n    if (cm.state.jump.marker) {\n        return;\n    }\n    const { cursor, options } = cm.state.jump;\n    const pos = cm.coordsChar(cursor);\n    const token = cm.getTokenAt(pos, true);\n    const getDestination = options.getDestination || cm.getHelper(pos, 'jump');\n    if (getDestination) {\n        const destination = getDestination(token, options, cm);\n        if (destination) {\n            const marker = cm.markText({ line: pos.line, ch: token.start }, { line: pos.line, ch: token.end }, { className: 'CodeMirror-jump-token' });\n            cm.state.jump.marker = marker;\n            cm.state.jump.destination = destination;\n        }\n    }\n}\nfunction disableJumpMode(cm) {\n    const { marker } = cm.state.jump;\n    cm.state.jump.marker = null;\n    cm.state.jump.destination = null;\n    marker.clear();\n}\n//# sourceMappingURL=jump-addon.js.map", "import CodeMirror from 'codemirror';\nimport getTypeInfo from './utils/getTypeInfo';\nimport { getArgumentReference, getDirectiveReference, getEnumValueReference, getFieldReference, getTypeReference, } from './utils/SchemaReference';\nimport './utils/jump-addon';\nCodeMirror.registerHelper('jump', 'graphql', (token, options) => {\n    if (!options.schema || !options.onClick || !token.state) {\n        return;\n    }\n    const { state } = token;\n    const { kind, step } = state;\n    const typeInfo = getTypeInfo(options.schema, state);\n    if ((kind === 'Field' && step === 0 && typeInfo.fieldDef) ||\n        (kind === 'AliasedField' && step === 2 && typeInfo.fieldDef)) {\n        return getFieldReference(typeInfo);\n    }\n    if (kind === 'Directive' && step === 1 && typeInfo.directiveDef) {\n        return getDirectiveReference(typeInfo);\n    }\n    if (kind === 'Argument' && step === 0 && typeInfo.argDef) {\n        return getArgumentReference(typeInfo);\n    }\n    if (kind === 'EnumValue' && typeInfo.enumValue) {\n        return getEnumValueReference(typeInfo);\n    }\n    if (kind === 'NamedType' && typeInfo.type) {\n        return getTypeReference(typeInfo);\n    }\n});\n//# sourceMappingURL=jump.js.map"], "mappings": ";;;;;;;;;;;;;;;;;;;AACAA,EAAW,aAAa,QAAQ,OAAO,CAACC,GAAIC,GAASC,MAAQ;AACzD,MAAIA,KAAOA,MAAQH,EAAW,MAAM;AAChC,UAAMI,IAAiBH,EAAG,MAAM,KAAK;AACrCD,MAAW,IAAIC,EAAG,kBAAiB,GAAI,aAAaG,CAAc;AAClE,UAAMC,IAAgBJ,EAAG,MAAM,KAAK;AACpCD,MAAW,IAAIC,EAAG,kBAAiB,GAAI,YAAYI,CAAa,GAChEL,EAAW,IAAI,UAAU,WAAWC,EAAG,MAAM,KAAK,SAAS,GAC3D,OAAOA,EAAG,MAAM;EAAA;AAEpB,MAAIC,GAAS;AACT,UAAMI,IAASL,EAAG,MAAM,OAAO;MAC3B,SAAAC;MACA,aAAaK,EAAY,KAAK,MAAMN,CAAE;MACtC,YAAYO,EAAW,KAAK,MAAMP,CAAE;MACpC,WAAWQ,EAAU,KAAK,MAAMR,CAAE;IAC9C;AACQD,MAAW,GAAGC,EAAG,kBAAmB,GAAE,aAAaK,EAAM,WAAW,GACpEN,EAAW,GAAGC,EAAG,kBAAmB,GAAE,YAAYK,EAAM,UAAU,GAClEN,EAAW,GAAG,UAAU,WAAWM,EAAM,SAAS;EAAA;AAE1D,CAAC;AACD,SAASC,EAAYN,GAAIS,GAAO;AAC5B,QAAMC,IAASD,EAAM,UAAUA,EAAM;AAIrC,MAHI,EAAEC,aAAkB,iBAGnBA,KAAW,OAA4B,SAASA,EAAO,cAAc;AACtE;AAEJ,QAAMC,IAAMD,EAAO,sBAAA,GACbE,IAAS;IACX,OAAOD,EAAI,OAAOA,EAAI,SAAS;IAC/B,MAAMA,EAAI,MAAMA,EAAI,UAAU;EACtC;AACIX,IAAG,MAAM,KAAK,SAASY,GACnBZ,EAAG,MAAM,KAAK,qBACda,EAAeb,CAAE;AAEzB;AAjBSc,EAAAR,GAAA,aAAA;AAkBT,SAASC,EAAWP,GAAI;AACpB,MAAI,CAACA,EAAG,MAAM,KAAK,qBAAqBA,EAAG,MAAM,KAAK,QAAQ;AAC1DA,MAAG,MAAM,KAAK,SAAS;AACvB;EAAA;AAEAA,IAAG,MAAM,KAAK,qBAAqBA,EAAG,MAAM,KAAK,UACjDe,GAAgBf,CAAE;AAE1B;AARSc,EAAAP,GAAA,YAAA;AAST,SAASC,EAAUR,GAAIS,GAAO;AAC1B,MAAIT,EAAG,MAAM,KAAK,qBAAqB,CAACgB,EAAeP,EAAM,GAAG;AAC5D;AAEJT,IAAG,MAAM,KAAK,oBAAoB,MAC9BA,EAAG,MAAM,KAAK,UACda,EAAeb,CAAE;AAErB,QAAMiB,IAAUH,EAAA,CAACI,MAAY;AACrBA,MAAQ,SAAST,EAAM,SAG3BT,EAAG,MAAM,KAAK,oBAAoB,OAC9BA,EAAG,MAAM,KAAK,UACde,GAAgBf,CAAE,GAEtBD,EAAW,IAAI,UAAU,SAASkB,CAAO,GACzClB,EAAW,IAAI,UAAU,SAASoB,CAAO,GACzCnB,EAAG,IAAI,aAAaoB,CAAW;EACvC,GAXoB,SAAA,GAYVD,IAAUL,EAAA,CAACO,MAAe;AAC5B,UAAM,EAAE,aAAAC,GAAa,SAAArB,EAAO,IAAKD,EAAG,MAAM;AACtCsB,SACArB,EAAQ,QAAQqB,GAAaD,CAAU;EAEnD,GALoB,SAAA,GAMVD,IAAcN,EAAA,CAACS,GAAGC,MAAc;AAC9BxB,MAAG,MAAM,KAAK,gBACdwB,EAAU,mBAAmB;EAEzC,GAJwB,aAAA;AAKpBzB,IAAW,GAAG,UAAU,SAASkB,CAAO,GACxClB,EAAW,GAAG,UAAU,SAASoB,CAAO,GACxCnB,EAAG,GAAG,aAAaoB,CAAW;AAClC;AAlCSN,EAAAN,GAAA,WAAA;AAmCT,IAAMiB,IAAQ,OAAO,YAAc,OAC/B,aACA,UAAU,WAAW,SAAS,KAAK;AACvC,SAAST,EAAeU,GAAK;AACzB,SAAOA,OAASD,IAAQ,SAAS;AACrC;AAFSX,EAAAE,GAAA,gBAAA;AAGT,SAASH,EAAeb,GAAI;AACxB,MAAIA,EAAG,MAAM,KAAK;AACd;AAEJ,QAAM,EAAE,QAAAY,GAAQ,SAAAX,EAAO,IAAKD,EAAG,MAAM,MAC/B2B,IAAM3B,EAAG,WAAWY,CAAM,GAC1BgB,IAAQ5B,EAAG,WAAW2B,GAAK,IAAI,GAC/BE,IAAiB5B,EAAQ,kBAAkBD,EAAG,UAAU2B,GAAK,MAAM;AACzE,MAAIE,GAAgB;AAChB,UAAMP,IAAcO,EAAeD,GAAO3B,GAASD,CAAE;AACrD,QAAIsB,GAAa;AACb,YAAMQ,IAAS9B,EAAG,SAAS,EAAE,MAAM2B,EAAI,MAAM,IAAIC,EAAM,MAAK,GAAI,EAAE,MAAMD,EAAI,MAAM,IAAIC,EAAM,IAAG,GAAI,EAAE,WAAW,wBAAuB,CAAE;AACzI5B,QAAG,MAAM,KAAK,SAAS8B,GACvB9B,EAAG,MAAM,KAAK,cAAcsB;IAAA;EAAA;AAGxC;AAhBSR,EAAAD,GAAA,gBAAA;AAiBT,SAASE,GAAgBf,GAAI;AACzB,QAAM,EAAE,QAAA8B,EAAQ,IAAG9B,EAAG,MAAM;AAC5BA,IAAG,MAAM,KAAK,SAAS,MACvBA,EAAG,MAAM,KAAK,cAAc,MAC5B8B,EAAO,MAAK;AAChB;AALShB,EAAAC,IAAA,iBAAA;ACvGThB,EAAW,eAAe,QAAQ,WAAW,CAAC6B,GAAO3B,MAAY;AAC7D,MAAI,CAACA,EAAQ,UAAU,CAACA,EAAQ,WAAW,CAAC2B,EAAM;AAC9C;AAEJ,QAAM,EAAE,OAAAvB,EAAO,IAAGuB,GACZ,EAAE,MAAAG,GAAM,MAAAC,EAAM,IAAG3B,GACjB4B,IAAWC,GAAYjC,EAAQ,QAAQI,CAAK;AAClD,MAAK0B,MAAS,WAAWC,MAAS,KAAKC,EAAS,YAC3CF,MAAS,kBAAkBC,MAAS,KAAKC,EAAS;AACnD,WAAOE,GAAkBF,CAAQ;AAErC,MAAIF,MAAS,eAAeC,MAAS,KAAKC,EAAS;AAC/C,WAAOG,GAAsBH,CAAQ;AAEzC,MAAIF,MAAS,cAAcC,MAAS,KAAKC,EAAS;AAC9C,WAAOI,GAAqBJ,CAAQ;AAExC,MAAIF,MAAS,eAAeE,EAAS;AACjC,WAAOK,GAAsBL,CAAQ;AAEzC,MAAIF,MAAS,eAAeE,EAAS;AACjC,WAAOM,GAAiBN,CAAQ;AAExC,CAAC;", "names": ["CodeMirror", "cm", "options", "old", "oldOnMouseOver", "oldOnMouseOut", "state", "onMouseOver", "onMouseOut", "onKeyDown", "event", "target", "box", "cursor", "enableJumpMode", "__name", "disableJumpMode", "isJumpModifier", "onKeyUp", "upEvent", "onClick", "onMouseDown", "clickEvent", "destination", "_", "downEvent", "isMac", "key", "pos", "token", "getDestination", "marker", "kind", "step", "typeInfo", "getTypeInfo", "getFieldReference", "getDirectiveReference", "getArgumentReference", "getEnumValueReference", "getTypeReference"]}