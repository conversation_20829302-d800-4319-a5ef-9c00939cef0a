import {
  cu,
  hu
} from "./chunk-MEKKV4OY.js";
import "./chunk-AUZ3RYOM.js";

// node_modules/@graphiql/react/dist/javascript.es.js
var he = Object.defineProperty;
var f = (F, W) => he(F, "name", { value: W, configurable: true });
function ye(F, W) {
  for (var w = 0; w < W.length; w++) {
    const M = W[w];
    if (typeof M != "string" && !Array.isArray(M)) {
      for (const h in M)
        if (h !== "default" && !(h in F)) {
          const A = Object.getOwnPropertyDescriptor(M, h);
          A && Object.defineProperty(F, h, A.get ? A : {
            enumerable: true,
            get: () => M[h]
          });
        }
    }
  }
  return Object.freeze(Object.defineProperty(F, Symbol.toStringTag, { value: "Module" }));
}
f(ye, "_mergeNamespaces");
var je = { exports: {} };
(function(F, W) {
  (function(w) {
    w(cu());
  })(function(w) {
    w.defineMode("javascript", function(M, h) {
      var A = M.indentUnit, vr = h.statementIndent, rr = h.jsonld, O = h.json || rr, gr = h.trackScope !== false, k = h.typescript, er = h.wordCharacters || /[\w$\xa1-\uffff]/, yr = function() {
        function r(y) {
          return { type: y, style: "keyword" };
        }
        f(r, "kw");
        var e = r("keyword a"), t = r("keyword b"), a = r("keyword c"), o = r("keyword d"), d = r("operator"), p = { type: "atom", style: "atom" };
        return {
          if: r("if"),
          while: e,
          with: e,
          else: t,
          do: t,
          try: t,
          finally: t,
          return: o,
          break: o,
          continue: o,
          new: r("new"),
          delete: a,
          void: a,
          throw: a,
          debugger: r("debugger"),
          var: r("var"),
          const: r("var"),
          let: r("var"),
          function: r("function"),
          catch: r("catch"),
          for: r("for"),
          switch: r("switch"),
          case: r("case"),
          default: r("default"),
          in: d,
          typeof: d,
          instanceof: d,
          true: p,
          false: p,
          null: p,
          undefined: p,
          NaN: p,
          Infinity: p,
          this: r("this"),
          class: r("class"),
          super: r("atom"),
          yield: a,
          export: r("export"),
          import: r("import"),
          extends: a,
          await: a
        };
      }(), jr = /[+\-*&%=<>!?|~^@]/, Jr = /^@(context|id|value|language|type|container|list|set|reverse|index|base|vocab|graph)"/;
      function Lr(r) {
        for (var e = false, t, a = false; (t = r.next()) != null; ) {
          if (!e) {
            if (t == "/" && !a)
              return;
            t == "[" ? a = true : a && t == "]" && (a = false);
          }
          e = !e && t == "\\";
        }
      }
      f(Lr, "readRegexp");
      var K, nr;
      function x(r, e, t) {
        return K = r, nr = t, e;
      }
      f(x, "ret");
      function $(r, e) {
        var t = r.next();
        if (t == '"' || t == "'")
          return e.tokenize = Qr(t), e.tokenize(r, e);
        if (t == "." && r.match(/^\d[\d_]*(?:[eE][+\-]?[\d_]+)?/))
          return x("number", "number");
        if (t == "." && r.match(".."))
          return x("spread", "meta");
        if (/[\[\]{}\(\),;\:\.]/.test(t))
          return x(t);
        if (t == "=" && r.eat(">"))
          return x("=>", "operator");
        if (t == "0" && r.match(/^(?:x[\dA-Fa-f_]+|o[0-7_]+|b[01_]+)n?/))
          return x("number", "number");
        if (/\d/.test(t))
          return r.match(/^[\d_]*(?:n|(?:\.[\d_]*)?(?:[eE][+\-]?[\d_]+)?)?/), x("number", "number");
        if (t == "/")
          return r.eat("*") ? (e.tokenize = tr, tr(r, e)) : r.eat("/") ? (r.skipToEnd(), x("comment", "comment")) : Fr(r, e, 1) ? (Lr(r), r.match(/^\b(([gimyus])(?![gimyus]*\2))+\b/), x("regexp", "string-2")) : (r.eat("="), x("operator", "operator", r.current()));
        if (t == "`")
          return e.tokenize = H, H(r, e);
        if (t == "#" && r.peek() == "!")
          return r.skipToEnd(), x("meta", "meta");
        if (t == "#" && r.eatWhile(er))
          return x("variable", "property");
        if (t == "<" && r.match("!--") || t == "-" && r.match("->") && !/\S/.test(r.string.slice(0, r.start)))
          return r.skipToEnd(), x("comment", "comment");
        if (jr.test(t))
          return (t != ">" || !e.lexical || e.lexical.type != ">") && (r.eat("=") ? (t == "!" || t == "=") && r.eat("=") : /[<>*+\-|&?]/.test(t) && (r.eat(t), t == ">" && r.eat(t))), t == "?" && r.eat(".") ? x(".") : x("operator", "operator", r.current());
        if (er.test(t)) {
          r.eatWhile(er);
          var a = r.current();
          if (e.lastType != ".") {
            if (yr.propertyIsEnumerable(a)) {
              var o = yr[a];
              return x(o.type, o.style, a);
            }
            if (a == "async" && r.match(/^(\s|\/\*([^*]|\*(?!\/))*?\*\/)*[\[\(\w]/, false))
              return x("async", "keyword", a);
          }
          return x("variable", "variable", a);
        }
      }
      f($, "tokenBase");
      function Qr(r) {
        return function(e, t) {
          var a = false, o;
          if (rr && e.peek() == "@" && e.match(Jr))
            return t.tokenize = $, x("jsonld-keyword", "meta");
          for (; (o = e.next()) != null && !(o == r && !a); )
            a = !a && o == "\\";
          return a || (t.tokenize = $), x("string", "string");
        };
      }
      f(Qr, "tokenString");
      function tr(r, e) {
        for (var t = false, a; a = r.next(); ) {
          if (a == "/" && t) {
            e.tokenize = $;
            break;
          }
          t = a == "*";
        }
        return x("comment", "comment");
      }
      f(tr, "tokenComment");
      function H(r, e) {
        for (var t = false, a; (a = r.next()) != null; ) {
          if (!t && (a == "`" || a == "$" && r.eat("{"))) {
            e.tokenize = $;
            break;
          }
          t = !t && a == "\\";
        }
        return x("quasi", "string-2", r.current());
      }
      f(H, "tokenQuasi");
      var Rr = "([{}])";
      function dr(r, e) {
        e.fatArrowAt && (e.fatArrowAt = null);
        var t = r.string.indexOf("=>", r.start);
        if (!(t < 0)) {
          if (k) {
            var a = /:\s*(?:\w+(?:<[^>]*>|\[\])?|\{[^}]*\})\s*$/.exec(r.string.slice(r.start, t));
            a && (t = a.index);
          }
          for (var o = 0, d = false, p = t - 1; p >= 0; --p) {
            var y = r.string.charAt(p), V = Rr.indexOf(y);
            if (V >= 0 && V < 3) {
              if (!o) {
                ++p;
                break;
              }
              if (--o == 0) {
                y == "(" && (d = true);
                break;
              }
            } else if (V >= 3 && V < 6)
              ++o;
            else if (er.test(y))
              d = true;
            else if (/["'\/`]/.test(y))
              for (; ; --p) {
                if (p == 0)
                  return;
                var xe = r.string.charAt(p - 1);
                if (xe == y && r.string.charAt(p - 2) != "\\") {
                  p--;
                  break;
                }
              }
            else if (d && !o) {
              ++p;
              break;
            }
          }
          d && !o && (e.fatArrowAt = p);
        }
      }
      f(dr, "findFatArrow");
      var Ur = {
        atom: true,
        number: true,
        variable: true,
        string: true,
        regexp: true,
        this: true,
        import: true,
        "jsonld-keyword": true
      };
      function Er(r, e, t, a, o, d) {
        this.indented = r, this.column = e, this.type = t, this.prev = o, this.info = d, a != null && (this.align = a);
      }
      f(Er, "JSLexical");
      function Wr(r, e) {
        if (!gr)
          return false;
        for (var t = r.localVars; t; t = t.next)
          if (t.name == e)
            return true;
        for (var a = r.context; a; a = a.prev)
          for (var t = a.vars; t; t = t.next)
            if (t.name == e)
              return true;
      }
      f(Wr, "inScope");
      function Tr(r, e, t, a, o) {
        var d = r.cc;
        for (i.state = r, i.stream = o, i.marked = null, i.cc = d, i.style = e, r.lexical.hasOwnProperty("align") || (r.lexical.align = true); ; ) {
          var p = d.length ? d.pop() : O ? b : v;
          if (p(t, a)) {
            for (; d.length && d[d.length - 1].lex; )
              d.pop()();
            return i.marked ? i.marked : t == "variable" && Wr(r, a) ? "variable-2" : e;
          }
        }
      }
      f(Tr, "parseJS");
      var i = { state: null, column: null, marked: null, cc: null };
      function s() {
        for (var r = arguments.length - 1; r >= 0; r--)
          i.cc.push(arguments[r]);
      }
      f(s, "pass");
      function n() {
        return s.apply(null, arguments), true;
      }
      f(n, "cont");
      function mr(r, e) {
        for (var t = e; t; t = t.next)
          if (t.name == r)
            return true;
        return false;
      }
      f(mr, "inList");
      function D(r) {
        var e = i.state;
        if (i.marked = "def", !!gr) {
          if (e.context) {
            if (e.lexical.info == "var" && e.context && e.context.block) {
              var t = Ar(r, e.context);
              if (t != null) {
                e.context = t;
                return;
              }
            } else if (!mr(r, e.localVars)) {
              e.localVars = new X(r, e.localVars);
              return;
            }
          }
          h.globalVars && !mr(r, e.globalVars) && (e.globalVars = new X(r, e.globalVars));
        }
      }
      f(D, "register");
      function Ar(r, e) {
        if (e)
          if (e.block) {
            var t = Ar(r, e.prev);
            return t ? t == e.prev ? e : new G(t, e.vars, true) : null;
          } else
            return mr(r, e.vars) ? e : new G(e.prev, new X(r, e.vars), false);
        else
          return null;
      }
      f(Ar, "registerVarScoped");
      function ir(r) {
        return r == "public" || r == "private" || r == "protected" || r == "abstract" || r == "readonly";
      }
      f(ir, "isModifier");
      function G(r, e, t) {
        this.prev = r, this.vars = e, this.block = t;
      }
      f(G, "Context");
      function X(r, e) {
        this.name = r, this.next = e;
      }
      f(X, "Var");
      var Kr = new X("this", new X("arguments", null));
      function q() {
        i.state.context = new G(i.state.context, i.state.localVars, false), i.state.localVars = Kr;
      }
      f(q, "pushcontext");
      function fr() {
        i.state.context = new G(i.state.context, i.state.localVars, true), i.state.localVars = null;
      }
      f(fr, "pushblockcontext"), q.lex = fr.lex = true;
      function E() {
        i.state.localVars = i.state.context.vars, i.state.context = i.state.context.prev;
      }
      f(E, "popcontext"), E.lex = true;
      function c(r, e) {
        var t = f(function() {
          var a = i.state, o = a.indented;
          if (a.lexical.type == "stat")
            o = a.lexical.indented;
          else
            for (var d = a.lexical; d && d.type == ")" && d.align; d = d.prev)
              o = d.indented;
          a.lexical = new Er(o, i.stream.column(), r, null, a.lexical, e);
        }, "result");
        return t.lex = true, t;
      }
      f(c, "pushlex");
      function u() {
        var r = i.state;
        r.lexical.prev && (r.lexical.type == ")" && (r.indented = r.lexical.indented), r.lexical = r.lexical.prev);
      }
      f(u, "poplex"), u.lex = true;
      function l(r) {
        function e(t) {
          return t == r ? n() : r == ";" || t == "}" || t == ")" || t == "]" ? s() : n(e);
        }
        return f(e, "exp"), e;
      }
      f(l, "expect");
      function v(r, e) {
        return r == "var" ? n(c("vardef", e), xr, l(";"), u) : r == "keyword a" ? n(c("form"), pr, v, u) : r == "keyword b" ? n(c("form"), v, u) : r == "keyword d" ? i.stream.match(/^\s*$/, false) ? n() : n(c("stat"), J, l(";"), u) : r == "debugger" ? n(l(";")) : r == "{" ? n(c("}"), fr, or, u, E) : r == ";" ? n() : r == "if" ? (i.state.lexical.info == "else" && i.state.cc[i.state.cc.length - 1] == u && i.state.cc.pop()(), n(c("form"), pr, v, u, Mr)) : r == "function" ? n(z) : r == "for" ? n(c("form"), fr, Or, v, E, u) : r == "class" || k && e == "interface" ? (i.marked = "keyword", n(c("form", r == "class" ? r : e), qr, u)) : r == "variable" ? k && e == "declare" ? (i.marked = "keyword", n(v)) : k && (e == "module" || e == "enum" || e == "type") && i.stream.match(/^\s*\w/, false) ? (i.marked = "keyword", e == "enum" ? n(Pr) : e == "type" ? n($r, l("operator"), m, l(";")) : n(c("form"), T, l("{"), c("}"), or, u, u)) : k && e == "namespace" ? (i.marked = "keyword", n(c("form"), b, v, u)) : k && e == "abstract" ? (i.marked = "keyword", n(v)) : n(c("stat"), Cr) : r == "switch" ? n(
          c("form"),
          pr,
          l("{"),
          c("}", "switch"),
          fr,
          or,
          u,
          u,
          E
        ) : r == "case" ? n(b, l(":")) : r == "default" ? n(l(":")) : r == "catch" ? n(c("form"), q, Hr, v, u, E) : r == "export" ? n(c("stat"), de, u) : r == "import" ? n(c("stat"), me, u) : r == "async" ? n(v) : e == "@" ? n(b, v) : s(c("stat"), b, l(";"), u);
      }
      f(v, "statement");
      function Hr(r) {
        if (r == "(")
          return n(P, l(")"));
      }
      f(Hr, "maybeCatchBinding");
      function b(r, e) {
        return Vr(r, e, false);
      }
      f(b, "expression");
      function j(r, e) {
        return Vr(r, e, true);
      }
      f(j, "expressionNoComma");
      function pr(r) {
        return r != "(" ? s() : n(c(")"), J, l(")"), u);
      }
      f(pr, "parenExpr");
      function Vr(r, e, t) {
        if (i.state.fatArrowAt == i.stream.start) {
          var a = t ? Sr : Ir;
          if (r == "(")
            return n(q, c(")"), g(P, ")"), u, l("=>"), a, E);
          if (r == "variable")
            return s(q, T, l("=>"), a, E);
        }
        var o = t ? L : N;
        return Ur.hasOwnProperty(r) ? n(o) : r == "function" ? n(z, o) : r == "class" || k && e == "interface" ? (i.marked = "keyword", n(c("form"), le, u)) : r == "keyword c" || r == "async" ? n(t ? j : b) : r == "(" ? n(c(")"), J, l(")"), u, o) : r == "operator" || r == "spread" ? n(t ? j : b) : r == "[" ? n(c("]"), ke, u, o) : r == "{" ? Y(ur, "}", null, o) : r == "quasi" ? s(ar, o) : r == "new" ? n(Xr(t)) : n();
      }
      f(Vr, "expressionInner");
      function J(r) {
        return r.match(/[;\}\)\],]/) ? s() : s(b);
      }
      f(J, "maybeexpression");
      function N(r, e) {
        return r == "," ? n(J) : L(r, e, false);
      }
      f(N, "maybeoperatorComma");
      function L(r, e, t) {
        var a = t == false ? N : L, o = t == false ? b : j;
        if (r == "=>")
          return n(q, t ? Sr : Ir, E);
        if (r == "operator")
          return /\+\+|--/.test(e) || k && e == "!" ? n(a) : k && e == "<" && i.stream.match(/^([^<>]|<[^<>]*>)*>\s*\(/, false) ? n(c(">"), g(m, ">"), u, a) : e == "?" ? n(b, l(":"), o) : n(o);
        if (r == "quasi")
          return s(ar, a);
        if (r != ";") {
          if (r == "(")
            return Y(j, ")", "call", a);
          if (r == ".")
            return n(re, a);
          if (r == "[")
            return n(c("]"), J, l("]"), u, a);
          if (k && e == "as")
            return i.marked = "keyword", n(m, a);
          if (r == "regexp")
            return i.state.lastType = i.marked = "operator", i.stream.backUp(i.stream.pos - i.stream.start - 1), n(o);
        }
      }
      f(L, "maybeoperatorNoComma");
      function ar(r, e) {
        return r != "quasi" ? s() : e.slice(e.length - 2) != "${" ? n(ar) : n(J, Gr);
      }
      f(ar, "quasi");
      function Gr(r) {
        if (r == "}")
          return i.marked = "string-2", i.state.tokenize = H, n(ar);
      }
      f(Gr, "continueQuasi");
      function Ir(r) {
        return dr(i.stream, i.state), s(r == "{" ? v : b);
      }
      f(Ir, "arrowBody");
      function Sr(r) {
        return dr(i.stream, i.state), s(r == "{" ? v : j);
      }
      f(Sr, "arrowBodyNoComma");
      function Xr(r) {
        return function(e) {
          return e == "." ? n(r ? Zr : Yr) : e == "variable" && k ? n(ae, r ? L : N) : s(r ? j : b);
        };
      }
      f(Xr, "maybeTarget");
      function Yr(r, e) {
        if (e == "target")
          return i.marked = "keyword", n(N);
      }
      f(Yr, "target");
      function Zr(r, e) {
        if (e == "target")
          return i.marked = "keyword", n(L);
      }
      f(Zr, "targetNoComma");
      function Cr(r) {
        return r == ":" ? n(u, v) : s(N, l(";"), u);
      }
      f(Cr, "maybelabel");
      function re(r) {
        if (r == "variable")
          return i.marked = "property", n();
      }
      f(re, "property");
      function ur(r, e) {
        if (r == "async")
          return i.marked = "property", n(ur);
        if (r == "variable" || i.style == "keyword") {
          if (i.marked = "property", e == "get" || e == "set")
            return n(ee);
          var t;
          return k && i.state.fatArrowAt == i.stream.start && (t = i.stream.match(/^\s*:\s*/, false)) && (i.state.fatArrowAt = i.stream.pos + t[0].length), n(B);
        } else {
          if (r == "number" || r == "string")
            return i.marked = rr ? "property" : i.style + " property", n(B);
          if (r == "jsonld-keyword")
            return n(B);
          if (k && ir(e))
            return i.marked = "keyword", n(ur);
          if (r == "[")
            return n(b, Q, l("]"), B);
          if (r == "spread")
            return n(j, B);
          if (e == "*")
            return i.marked = "keyword", n(ur);
          if (r == ":")
            return s(B);
        }
      }
      f(ur, "objprop");
      function ee(r) {
        return r != "variable" ? s(B) : (i.marked = "property", n(z));
      }
      f(ee, "getterSetter");
      function B(r) {
        if (r == ":")
          return n(j);
        if (r == "(")
          return s(z);
      }
      f(B, "afterprop");
      function g(r, e, t) {
        function a(o, d) {
          if (t ? t.indexOf(o) > -1 : o == ",") {
            var p = i.state.lexical;
            return p.info == "call" && (p.pos = (p.pos || 0) + 1), n(function(y, V) {
              return y == e || V == e ? s() : s(r);
            }, a);
          }
          return o == e || d == e ? n() : t && t.indexOf(";") > -1 ? s(r) : n(l(e));
        }
        return f(a, "proceed"), function(o, d) {
          return o == e || d == e ? n() : s(r, a);
        };
      }
      f(g, "commasep");
      function Y(r, e, t) {
        for (var a = 3; a < arguments.length; a++)
          i.cc.push(arguments[a]);
        return n(c(e, t), g(r, e), u);
      }
      f(Y, "contCommasep");
      function or(r) {
        return r == "}" ? n() : s(v, or);
      }
      f(or, "block");
      function Q(r, e) {
        if (k) {
          if (r == ":")
            return n(m);
          if (e == "?")
            return n(Q);
        }
      }
      f(Q, "maybetype");
      function ne(r, e) {
        if (k && (r == ":" || e == "in"))
          return n(m);
      }
      f(ne, "maybetypeOrIn");
      function _r(r) {
        if (k && r == ":")
          return i.stream.match(/^\s*\w+\s+is\b/, false) ? n(b, te, m) : n(m);
      }
      f(_r, "mayberettype");
      function te(r, e) {
        if (e == "is")
          return i.marked = "keyword", n();
      }
      f(te, "isKW");
      function m(r, e) {
        if (e == "keyof" || e == "typeof" || e == "infer" || e == "readonly")
          return i.marked = "keyword", n(e == "typeof" ? j : m);
        if (r == "variable" || e == "void")
          return i.marked = "type", n(I);
        if (e == "|" || e == "&")
          return n(m);
        if (r == "string" || r == "number" || r == "atom")
          return n(I);
        if (r == "[")
          return n(c("]"), g(m, "]", ","), u, I);
        if (r == "{")
          return n(c("}"), kr, u, I);
        if (r == "(")
          return n(g(wr, ")"), ie, I);
        if (r == "<")
          return n(g(m, ">"), m);
        if (r == "quasi")
          return s(br, I);
      }
      f(m, "typeexpr");
      function ie(r) {
        if (r == "=>")
          return n(m);
      }
      f(ie, "maybeReturnType");
      function kr(r) {
        return r.match(/[\}\)\]]/) ? n() : r == "," || r == ";" ? n(kr) : s(Z, kr);
      }
      f(kr, "typeprops");
      function Z(r, e) {
        if (r == "variable" || i.style == "keyword")
          return i.marked = "property", n(Z);
        if (e == "?" || r == "number" || r == "string")
          return n(Z);
        if (r == ":")
          return n(m);
        if (r == "[")
          return n(l("variable"), ne, l("]"), Z);
        if (r == "(")
          return s(U, Z);
        if (!r.match(/[;\}\)\],]/))
          return n();
      }
      f(Z, "typeprop");
      function br(r, e) {
        return r != "quasi" ? s() : e.slice(e.length - 2) != "${" ? n(br) : n(m, fe);
      }
      f(br, "quasiType");
      function fe(r) {
        if (r == "}")
          return i.marked = "string-2", i.state.tokenize = H, n(br);
      }
      f(fe, "continueQuasiType");
      function wr(r, e) {
        return r == "variable" && i.stream.match(/^\s*[?:]/, false) || e == "?" ? n(wr) : r == ":" ? n(m) : r == "spread" ? n(wr) : s(m);
      }
      f(wr, "typearg");
      function I(r, e) {
        if (e == "<")
          return n(c(">"), g(m, ">"), u, I);
        if (e == "|" || r == "." || e == "&")
          return n(m);
        if (r == "[")
          return n(m, l("]"), I);
        if (e == "extends" || e == "implements")
          return i.marked = "keyword", n(m);
        if (e == "?")
          return n(m, l(":"), m);
      }
      f(I, "afterType");
      function ae(r, e) {
        if (e == "<")
          return n(c(">"), g(m, ">"), u, I);
      }
      f(ae, "maybeTypeArgs");
      function sr() {
        return s(m, ue);
      }
      f(sr, "typeparam");
      function ue(r, e) {
        if (e == "=")
          return n(m);
      }
      f(ue, "maybeTypeDefault");
      function xr(r, e) {
        return e == "enum" ? (i.marked = "keyword", n(Pr)) : s(T, Q, _, se);
      }
      f(xr, "vardef");
      function T(r, e) {
        if (k && ir(e))
          return i.marked = "keyword", n(T);
        if (r == "variable")
          return D(e), n();
        if (r == "spread")
          return n(T);
        if (r == "[")
          return Y(oe, "]");
        if (r == "{")
          return Y(zr, "}");
      }
      f(T, "pattern");
      function zr(r, e) {
        return r == "variable" && !i.stream.match(/^\s*:/, false) ? (D(e), n(_)) : (r == "variable" && (i.marked = "property"), r == "spread" ? n(T) : r == "}" ? s() : r == "[" ? n(b, l("]"), l(":"), zr) : n(l(":"), T, _));
      }
      f(zr, "proppattern");
      function oe() {
        return s(T, _);
      }
      f(oe, "eltpattern");
      function _(r, e) {
        if (e == "=")
          return n(j);
      }
      f(_, "maybeAssign");
      function se(r) {
        if (r == ",")
          return n(xr);
      }
      f(se, "vardefCont");
      function Mr(r, e) {
        if (r == "keyword b" && e == "else")
          return n(c("form", "else"), v, u);
      }
      f(Mr, "maybeelse");
      function Or(r, e) {
        if (e == "await")
          return n(Or);
        if (r == "(")
          return n(c(")"), ce, u);
      }
      f(Or, "forspec");
      function ce(r) {
        return r == "var" ? n(xr, R) : r == "variable" ? n(R) : s(R);
      }
      f(ce, "forspec1");
      function R(r, e) {
        return r == ")" ? n() : r == ";" ? n(R) : e == "in" || e == "of" ? (i.marked = "keyword", n(b, R)) : s(b, R);
      }
      f(R, "forspec2");
      function z(r, e) {
        if (e == "*")
          return i.marked = "keyword", n(z);
        if (r == "variable")
          return D(e), n(z);
        if (r == "(")
          return n(q, c(")"), g(P, ")"), u, _r, v, E);
        if (k && e == "<")
          return n(c(">"), g(sr, ">"), u, z);
      }
      f(z, "functiondef");
      function U(r, e) {
        if (e == "*")
          return i.marked = "keyword", n(U);
        if (r == "variable")
          return D(e), n(U);
        if (r == "(")
          return n(q, c(")"), g(P, ")"), u, _r, E);
        if (k && e == "<")
          return n(c(">"), g(sr, ">"), u, U);
      }
      f(U, "functiondecl");
      function $r(r, e) {
        if (r == "keyword" || r == "variable")
          return i.marked = "type", n($r);
        if (e == "<")
          return n(c(">"), g(sr, ">"), u);
      }
      f($r, "typename");
      function P(r, e) {
        return e == "@" && n(b, P), r == "spread" ? n(P) : k && ir(e) ? (i.marked = "keyword", n(P)) : k && r == "this" ? n(Q, _) : s(T, Q, _);
      }
      f(P, "funarg");
      function le(r, e) {
        return r == "variable" ? qr(r, e) : cr(r, e);
      }
      f(le, "classExpression");
      function qr(r, e) {
        if (r == "variable")
          return D(e), n(cr);
      }
      f(qr, "className");
      function cr(r, e) {
        if (e == "<")
          return n(c(">"), g(sr, ">"), u, cr);
        if (e == "extends" || e == "implements" || k && r == ",")
          return e == "implements" && (i.marked = "keyword"), n(k ? m : b, cr);
        if (r == "{")
          return n(c("}"), S, u);
      }
      f(cr, "classNameAfter");
      function S(r, e) {
        if (r == "async" || r == "variable" && (e == "static" || e == "get" || e == "set" || k && ir(e)) && i.stream.match(/^\s+[\w$\xa1-\uffff]/, false))
          return i.marked = "keyword", n(S);
        if (r == "variable" || i.style == "keyword")
          return i.marked = "property", n(C, S);
        if (r == "number" || r == "string")
          return n(C, S);
        if (r == "[")
          return n(b, Q, l("]"), C, S);
        if (e == "*")
          return i.marked = "keyword", n(S);
        if (k && r == "(")
          return s(U, S);
        if (r == ";" || r == ",")
          return n(S);
        if (r == "}")
          return n();
        if (e == "@")
          return n(b, S);
      }
      f(S, "classBody");
      function C(r, e) {
        if (e == "!" || e == "?")
          return n(C);
        if (r == ":")
          return n(m, _);
        if (e == "=")
          return n(j);
        var t = i.state.lexical.prev, a = t && t.info == "interface";
        return s(a ? U : z);
      }
      f(C, "classfield");
      function de(r, e) {
        return e == "*" ? (i.marked = "keyword", n(hr, l(";"))) : e == "default" ? (i.marked = "keyword", n(b, l(";"))) : r == "{" ? n(g(Nr, "}"), hr, l(";")) : s(v);
      }
      f(de, "afterExport");
      function Nr(r, e) {
        if (e == "as")
          return i.marked = "keyword", n(l("variable"));
        if (r == "variable")
          return s(j, Nr);
      }
      f(Nr, "exportField");
      function me(r) {
        return r == "string" ? n() : r == "(" ? s(b) : r == "." ? s(N) : s(lr, Br, hr);
      }
      f(me, "afterImport");
      function lr(r, e) {
        return r == "{" ? Y(lr, "}") : (r == "variable" && D(e), e == "*" && (i.marked = "keyword"), n(pe));
      }
      f(lr, "importSpec");
      function Br(r) {
        if (r == ",")
          return n(lr, Br);
      }
      f(Br, "maybeMoreImports");
      function pe(r, e) {
        if (e == "as")
          return i.marked = "keyword", n(lr);
      }
      f(pe, "maybeAs");
      function hr(r, e) {
        if (e == "from")
          return i.marked = "keyword", n(b);
      }
      f(hr, "maybeFrom");
      function ke(r) {
        return r == "]" ? n() : s(g(j, "]"));
      }
      f(ke, "arrayLiteral");
      function Pr() {
        return s(c("form"), T, l("{"), c("}"), g(be, "}"), u, u);
      }
      f(Pr, "enumdef");
      function be() {
        return s(T, _);
      }
      f(be, "enummember");
      function we(r, e) {
        return r.lastType == "operator" || r.lastType == "," || jr.test(e.charAt(0)) || /[,.]/.test(e.charAt(0));
      }
      f(we, "isContinuedStatement");
      function Fr(r, e, t) {
        return e.tokenize == $ && /^(?:operator|sof|keyword [bcd]|case|new|export|default|spread|[\[{}\(,;:]|=>)$/.test(e.lastType) || e.lastType == "quasi" && /\{\s*$/.test(r.string.slice(0, r.pos - (t || 0)));
      }
      return f(Fr, "expressionAllowed"), {
        startState: function(r) {
          var e = {
            tokenize: $,
            lastType: "sof",
            cc: [],
            lexical: new Er((r || 0) - A, 0, "block", false),
            localVars: h.localVars,
            context: h.localVars && new G(null, null, false),
            indented: r || 0
          };
          return h.globalVars && typeof h.globalVars == "object" && (e.globalVars = h.globalVars), e;
        },
        token: function(r, e) {
          if (r.sol() && (e.lexical.hasOwnProperty("align") || (e.lexical.align = false), e.indented = r.indentation(), dr(r, e)), e.tokenize != tr && r.eatSpace())
            return null;
          var t = e.tokenize(r, e);
          return K == "comment" ? t : (e.lastType = K == "operator" && (nr == "++" || nr == "--") ? "incdec" : K, Tr(e, t, K, nr, r));
        },
        indent: function(r, e) {
          if (r.tokenize == tr || r.tokenize == H)
            return w.Pass;
          if (r.tokenize != $)
            return 0;
          var t = e && e.charAt(0), a = r.lexical, o;
          if (!/^\s*else\b/.test(e))
            for (var d = r.cc.length - 1; d >= 0; --d) {
              var p = r.cc[d];
              if (p == u)
                a = a.prev;
              else if (p != Mr && p != E)
                break;
            }
          for (; (a.type == "stat" || a.type == "form") && (t == "}" || (o = r.cc[r.cc.length - 1]) && (o == N || o == L) && !/^[,\.=+\-*:?[\(]/.test(e)); )
            a = a.prev;
          vr && a.type == ")" && a.prev.type == "stat" && (a = a.prev);
          var y = a.type, V = t == y;
          return y == "vardef" ? a.indented + (r.lastType == "operator" || r.lastType == "," ? a.info.length + 1 : 0) : y == "form" && t == "{" ? a.indented : y == "form" ? a.indented + A : y == "stat" ? a.indented + (we(r, e) ? vr || A : 0) : a.info == "switch" && !V && h.doubleIndentSwitch != false ? a.indented + (/^(?:case|default)\b/.test(e) ? A : 2 * A) : a.align ? a.column + (V ? 0 : 1) : a.indented + (V ? 0 : A);
        },
        electricInput: /^\s*(?:case .*?:|default:|\{|\})$/,
        blockCommentStart: O ? null : "/*",
        blockCommentEnd: O ? null : "*/",
        blockCommentContinue: O ? null : " * ",
        lineComment: O ? null : "//",
        fold: "brace",
        closeBrackets: "()[]{}''\"\"``",
        helperType: O ? "json" : "javascript",
        jsonldMode: rr,
        jsonMode: O,
        expressionAllowed: Fr,
        skipExpression: function(r) {
          Tr(r, "atom", "atom", "true", new w.StringStream("", 2, null));
        }
      };
    }), w.registerHelper("wordChars", "javascript", /[\w$]/), w.defineMIME("text/javascript", "javascript"), w.defineMIME("text/ecmascript", "javascript"), w.defineMIME("application/javascript", "javascript"), w.defineMIME("application/x-javascript", "javascript"), w.defineMIME("application/ecmascript", "javascript"), w.defineMIME("application/json", { name: "javascript", json: true }), w.defineMIME("application/x-json", { name: "javascript", json: true }), w.defineMIME("application/manifest+json", { name: "javascript", json: true }), w.defineMIME("application/ld+json", { name: "javascript", jsonld: true }), w.defineMIME("text/typescript", { name: "javascript", typescript: true }), w.defineMIME("application/typescript", { name: "javascript", typescript: true });
  });
})();
var Dr = je.exports;
var Ee = hu(Dr);
var Ve = ye({
  __proto__: null,
  default: Ee
}, [Dr]);
export {
  Ve as j
};
//# sourceMappingURL=javascript.es-EHSZPHW6.js.map
