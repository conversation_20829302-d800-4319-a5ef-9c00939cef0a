import {
  r
} from "./chunk-A4B2V6D5.js";
import {
  d
} from "./chunk-WIXYZC3K.js";
import "./chunk-MEKKV4OY.js";
import {
  list,
  onlineParser,
  opt,
  p,
  t
} from "./chunk-4LQY6QSN.js";
import "./chunk-HD22INE4.js";
import "./chunk-AUZ3RYOM.js";

// node_modules/@graphiql/react/dist/mode.es2.js
var o = Object.defineProperty;
var i = (r2, t2) => o(r2, "name", { value: t2, configurable: true });
d.defineMode("graphql-variables", (r2) => {
  const t2 = onlineParser({
    eatWhitespace: (u) => u.eatSpace(),
    lexRules: V,
    parseRules: m,
    editorConfig: { tabSize: r2.tabSize }
  });
  return {
    config: r2,
    startState: t2.startState,
    token: t2.token,
    indent: r,
    electricInput: /^\s*[}\]]/,
    fold: "brace",
    closeBrackets: {
      pairs: '[]{}""',
      explode: "[]{}"
    }
  };
});
var V = {
  Punctuation: /^\[|]|\{|\}|:|,/,
  Number: /^-?(?:0|(?:[1-9][0-9]*))(?:\.[0-9]*)?(?:[eE][+-]?[0-9]+)?/,
  String: /^"(?:[^"\\]|\\(?:"|\/|\\|b|f|n|r|t|u[0-9a-fA-F]{4}))*"?/,
  Keyword: /^true|false|null/
};
var m = {
  Document: [p("{"), list("Variable", opt(p(","))), p("}")],
  Variable: [s("variable"), p(":"), "Value"],
  Value(r2) {
    switch (r2.kind) {
      case "Number":
        return "NumberValue";
      case "String":
        return "StringValue";
      case "Punctuation":
        switch (r2.value) {
          case "[":
            return "ListValue";
          case "{":
            return "ObjectValue";
        }
        return null;
      case "Keyword":
        switch (r2.value) {
          case "true":
          case "false":
            return "BooleanValue";
          case "null":
            return "NullValue";
        }
        return null;
    }
  },
  NumberValue: [t("Number", "number")],
  StringValue: [t("String", "string")],
  BooleanValue: [t("Keyword", "builtin")],
  NullValue: [t("Keyword", "keyword")],
  ListValue: [p("["), list("Value", opt(p(","))), p("]")],
  ObjectValue: [p("{"), list("ObjectField", opt(p(","))), p("}")],
  ObjectField: [s("attribute"), p(":"), "Value"]
};
function s(r2) {
  return {
    style: r2,
    match: (t2) => t2.kind === "String",
    update(t2, u) {
      t2.name = u.value.slice(1, -1);
    }
  };
}
i(s, "namedKey");
//# sourceMappingURL=mode.es2-I4CZ5GXT.js.map
