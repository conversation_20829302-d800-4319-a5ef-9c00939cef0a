{"version": 3, "sources": ["../../../../../@graphiql/codemirror-graphql/esm/hint.js"], "sourcesContent": ["import CodeMirror from 'codemirror';\nimport 'codemirror/addon/hint/show-hint';\nimport { getAutocompleteSuggestions, Position } from 'graphql-language-service';\nCodeMirror.registerHelper('hint', 'graphql', (editor, options) => {\n    const { schema, externalFragments } = options;\n    if (!schema) {\n        return;\n    }\n    const cur = editor.getCursor();\n    const token = editor.getTokenAt(cur);\n    const tokenStart = token.type !== null && /\"|\\w/.test(token.string[0])\n        ? token.start\n        : token.end;\n    const position = new Position(cur.line, tokenStart);\n    const rawResults = getAutocompleteSuggestions(schema, editor.getValue(), position, token, externalFragments);\n    const results = {\n        list: rawResults.map(item => ({\n            text: item.label,\n            type: item.type,\n            description: item.documentation,\n            isDeprecated: item.isDeprecated,\n            deprecationReason: item.deprecationReason,\n        })),\n        from: { line: cur.line, ch: tokenStart },\n        to: { line: cur.line, ch: token.end },\n    };\n    if ((results === null || results === void 0 ? void 0 : results.list) && results.list.length > 0) {\n        results.from = CodeMirror.Pos(results.from.line, results.from.ch);\n        results.to = CodeMirror.Pos(results.to.line, results.to.ch);\n        CodeMirror.signal(editor, 'hasCompletion', editor, results, token);\n    }\n    return results;\n});\n//# sourceMappingURL=hint.js.map"], "mappings": ";;;;;;;;;;;;;AAGAA,EAAW,eAAe,QAAQ,WAAW,CAACC,GAAQC,MAAY;AAC9D,QAAM,EAAE,QAAAC,GAAQ,mBAAAC,EAAmB,IAAGF;AACtC,MAAI,CAACC;AACD;AAEJ,QAAME,IAAMJ,EAAO,UAAA,GACbK,IAAQL,EAAO,WAAWI,CAAG,GAC7BE,IAAaD,EAAM,SAAS,QAAQ,OAAO,KAAKA,EAAM,OAAO,CAAC,CAAC,IAC/DA,EAAM,QACNA,EAAM,KACNE,IAAW,IAAIC,SAASJ,EAAI,MAAME,CAAU,GAE5CG,IAAU;IACZ,MAFeC,2BAA2BR,GAAQF,EAAO,SAAQ,GAAIO,GAAUF,GAAOF,CAAiB,EAEtF,IAAI,CAAAQ,OAAS;MAC1B,MAAMA,EAAK;MACX,MAAMA,EAAK;MACX,aAAaA,EAAK;MAClB,cAAcA,EAAK;MACnB,mBAAmBA,EAAK;IACpC,EAAU;IACF,MAAM,EAAE,MAAMP,EAAI,MAAM,IAAIE,EAAY;IACxC,IAAI,EAAE,MAAMF,EAAI,MAAM,IAAIC,EAAM,IAAK;EAC7C;AACI,SAAKI,KAAY,QAAsCA,EAAQ,QAASA,EAAQ,KAAK,SAAS,MAC1FA,EAAQ,OAAOV,EAAW,IAAIU,EAAQ,KAAK,MAAMA,EAAQ,KAAK,EAAE,GAChEA,EAAQ,KAAKV,EAAW,IAAIU,EAAQ,GAAG,MAAMA,EAAQ,GAAG,EAAE,GAC1DV,EAAW,OAAOC,GAAQ,iBAAiBA,GAAQS,GAASJ,CAAK,IAE9DI;AACX,CAAC;", "names": ["CodeMirror", "editor", "options", "schema", "externalFragments", "cur", "token", "tokenStart", "position", "Position", "results", "getAutocompleteSuggestions", "item"]}