{"version": 3, "sources": ["../../../../../@graphiql/codemirror-graphql/esm/utils/jsonParse.js", "../../../../../@graphiql/codemirror-graphql/esm/variables/lint.js"], "sourcesContent": ["export default function jsonParse(str) {\n    string = str;\n    strLen = str.length;\n    start = end = lastEnd = -1;\n    ch();\n    lex();\n    const ast = parseObj();\n    expect('EOF');\n    return ast;\n}\nlet string;\nlet strLen;\nlet start;\nlet end;\nlet lastEnd;\nlet code;\nlet kind;\nfunction parseObj() {\n    const nodeStart = start;\n    const members = [];\n    expect('{');\n    if (!skip('}')) {\n        do {\n            members.push(parseMember());\n        } while (skip(','));\n        expect('}');\n    }\n    return {\n        kind: 'Object',\n        start: nodeStart,\n        end: lastEnd,\n        members,\n    };\n}\nfunction parseMember() {\n    const nodeStart = start;\n    const key = kind === 'String' ? curToken() : null;\n    expect('String');\n    expect(':');\n    const value = parseVal();\n    return {\n        kind: 'Member',\n        start: nodeStart,\n        end: lastEnd,\n        key,\n        value,\n    };\n}\nfunction parseArr() {\n    const nodeStart = start;\n    const values = [];\n    expect('[');\n    if (!skip(']')) {\n        do {\n            values.push(parseVal());\n        } while (skip(','));\n        expect(']');\n    }\n    return {\n        kind: 'Array',\n        start: nodeStart,\n        end: lastEnd,\n        values,\n    };\n}\nfunction parseVal() {\n    switch (kind) {\n        case '[':\n            return parseArr();\n        case '{':\n            return parseObj();\n        case 'String':\n        case 'Number':\n        case 'Boolean':\n        case 'Null':\n            const token = curToken();\n            lex();\n            return token;\n    }\n    expect('Value');\n}\nfunction curToken() {\n    return { kind, start, end, value: JSON.parse(string.slice(start, end)) };\n}\nfunction expect(str) {\n    if (kind === str) {\n        lex();\n        return;\n    }\n    let found;\n    if (kind === 'EOF') {\n        found = '[end of file]';\n    }\n    else if (end - start > 1) {\n        found = '`' + string.slice(start, end) + '`';\n    }\n    else {\n        const match = string.slice(start).match(/^.+?\\b/);\n        found = '`' + (match ? match[0] : string[start]) + '`';\n    }\n    throw syntaxError(`Expected ${str} but found ${found}.`);\n}\nexport class JSONSyntaxError extends Error {\n    constructor(message, position) {\n        super(message);\n        this.position = position;\n    }\n}\nfunction syntaxError(message) {\n    return new JSONSyntaxError(message, { start, end });\n}\nfunction skip(k) {\n    if (kind === k) {\n        lex();\n        return true;\n    }\n}\nfunction ch() {\n    if (end < strLen) {\n        end++;\n        code = end === strLen ? 0 : string.charCodeAt(end);\n    }\n    return code;\n}\nfunction lex() {\n    lastEnd = end;\n    while (code === 9 || code === 10 || code === 13 || code === 32) {\n        ch();\n    }\n    if (code === 0) {\n        kind = 'EOF';\n        return;\n    }\n    start = end;\n    switch (code) {\n        case 34:\n            kind = 'String';\n            return readString();\n        case 45:\n        case 48:\n        case 49:\n        case 50:\n        case 51:\n        case 52:\n        case 53:\n        case 54:\n        case 55:\n        case 56:\n        case 57:\n            kind = 'Number';\n            return readNumber();\n        case 102:\n            if (string.slice(start, start + 5) !== 'false') {\n                break;\n            }\n            end += 4;\n            ch();\n            kind = 'Boolean';\n            return;\n        case 110:\n            if (string.slice(start, start + 4) !== 'null') {\n                break;\n            }\n            end += 3;\n            ch();\n            kind = 'Null';\n            return;\n        case 116:\n            if (string.slice(start, start + 4) !== 'true') {\n                break;\n            }\n            end += 3;\n            ch();\n            kind = 'Boolean';\n            return;\n    }\n    kind = string[start];\n    ch();\n}\nfunction readString() {\n    ch();\n    while (code !== 34 && code > 31) {\n        if (code === 92) {\n            code = ch();\n            switch (code) {\n                case 34:\n                case 47:\n                case 92:\n                case 98:\n                case 102:\n                case 110:\n                case 114:\n                case 116:\n                    ch();\n                    break;\n                case 117:\n                    ch();\n                    readHex();\n                    readHex();\n                    readHex();\n                    readHex();\n                    break;\n                default:\n                    throw syntaxError('Bad character escape sequence.');\n            }\n        }\n        else if (end === strLen) {\n            throw syntaxError('Unterminated string.');\n        }\n        else {\n            ch();\n        }\n    }\n    if (code === 34) {\n        ch();\n        return;\n    }\n    throw syntaxError('Unterminated string.');\n}\nfunction readHex() {\n    if ((code >= 48 && code <= 57) ||\n        (code >= 65 && code <= 70) ||\n        (code >= 97 && code <= 102)) {\n        return ch();\n    }\n    throw syntaxError('Expected hexadecimal digit.');\n}\nfunction readNumber() {\n    if (code === 45) {\n        ch();\n    }\n    if (code === 48) {\n        ch();\n    }\n    else {\n        readDigits();\n    }\n    if (code === 46) {\n        ch();\n        readDigits();\n    }\n    if (code === 69 || code === 101) {\n        code = ch();\n        if (code === 43 || code === 45) {\n            ch();\n        }\n        readDigits();\n    }\n}\nfunction readDigits() {\n    if (code < 48 || code > 57) {\n        throw syntaxError('Expected decimal digit.');\n    }\n    do {\n        ch();\n    } while (code >= 48 && code <= 57);\n}\n//# sourceMappingURL=jsonParse.js.map", "import CodeMirror from 'codemirror';\nimport { GraphQLEnumType, GraphQLInputObjectType, GraphQLList, GraphQLNonNull, GraphQLScalarType, } from 'graphql';\nimport jsonParse, { JSONSyntaxError, } from '../utils/jsonParse';\nCodeMirror.registerHelper('lint', 'graphql-variables', (text, options, editor) => {\n    if (!text) {\n        return [];\n    }\n    let ast;\n    try {\n        ast = jsonParse(text);\n    }\n    catch (error) {\n        if (error instanceof JSONSyntaxError) {\n            return [lintError(editor, error.position, error.message)];\n        }\n        throw error;\n    }\n    const { variableToType } = options;\n    if (!variableToType) {\n        return [];\n    }\n    return validateVariables(editor, variableToType, ast);\n});\nfunction validateVariables(editor, variableToType, variablesAST) {\n    var _a;\n    const errors = [];\n    for (const member of variablesAST.members) {\n        if (member) {\n            const variableName = (_a = member.key) === null || _a === void 0 ? void 0 : _a.value;\n            const type = variableToType[variableName];\n            if (type) {\n                for (const [node, message] of validateValue(type, member.value)) {\n                    errors.push(lintError(editor, node, message));\n                }\n            }\n            else {\n                errors.push(lintError(editor, member.key, `Variable \"$${variableName}\" does not appear in any GraphQL query.`));\n            }\n        }\n    }\n    return errors;\n}\nfunction validateValue(type, valueAST) {\n    if (!type || !valueAST) {\n        return [];\n    }\n    if (type instanceof GraphQLNonNull) {\n        if (valueAST.kind === 'Null') {\n            return [[valueAST, `Type \"${type}\" is non-nullable and cannot be null.`]];\n        }\n        return validateValue(type.ofType, valueAST);\n    }\n    if (valueAST.kind === 'Null') {\n        return [];\n    }\n    if (type instanceof GraphQLList) {\n        const itemType = type.ofType;\n        if (valueAST.kind === 'Array') {\n            const values = valueAST.values || [];\n            return mapCat(values, item => validateValue(itemType, item));\n        }\n        return validateValue(itemType, valueAST);\n    }\n    if (type instanceof GraphQLInputObjectType) {\n        if (valueAST.kind !== 'Object') {\n            return [[valueAST, `Type \"${type}\" must be an Object.`]];\n        }\n        const providedFields = Object.create(null);\n        const fieldErrors = mapCat(valueAST.members, member => {\n            var _a;\n            const fieldName = (_a = member === null || member === void 0 ? void 0 : member.key) === null || _a === void 0 ? void 0 : _a.value;\n            providedFields[fieldName] = true;\n            const inputField = type.getFields()[fieldName];\n            if (!inputField) {\n                return [\n                    [\n                        member.key,\n                        `Type \"${type}\" does not have a field \"${fieldName}\".`,\n                    ],\n                ];\n            }\n            const fieldType = inputField ? inputField.type : undefined;\n            return validateValue(fieldType, member.value);\n        });\n        for (const fieldName of Object.keys(type.getFields())) {\n            const field = type.getFields()[fieldName];\n            if (!providedFields[fieldName] &&\n                field.type instanceof GraphQLNonNull &&\n                !field.defaultValue) {\n                fieldErrors.push([\n                    valueAST,\n                    `Object of type \"${type}\" is missing required field \"${fieldName}\".`,\n                ]);\n            }\n        }\n        return fieldErrors;\n    }\n    if ((type.name === 'Boolean' && valueAST.kind !== 'Boolean') ||\n        (type.name === 'String' && valueAST.kind !== 'String') ||\n        (type.name === 'ID' &&\n            valueAST.kind !== 'Number' &&\n            valueAST.kind !== 'String') ||\n        (type.name === 'Float' && valueAST.kind !== 'Number') ||\n        (type.name === 'Int' &&\n            (valueAST.kind !== 'Number' || (valueAST.value | 0) !== valueAST.value))) {\n        return [[valueAST, `Expected value of type \"${type}\".`]];\n    }\n    if ((type instanceof GraphQLEnumType || type instanceof GraphQLScalarType) &&\n        ((valueAST.kind !== 'String' &&\n            valueAST.kind !== 'Number' &&\n            valueAST.kind !== 'Boolean' &&\n            valueAST.kind !== 'Null') ||\n            isNullish(type.parseValue(valueAST.value)))) {\n        return [[valueAST, `Expected value of type \"${type}\".`]];\n    }\n    return [];\n}\nfunction lintError(editor, node, message) {\n    return {\n        message,\n        severity: 'error',\n        type: 'validation',\n        from: editor.posFromIndex(node.start),\n        to: editor.posFromIndex(node.end),\n    };\n}\nfunction isNullish(value) {\n    return value === null || value === undefined || value !== value;\n}\nfunction mapCat(array, mapper) {\n    return Array.prototype.concat.apply([], array.map(mapper));\n}\n//# sourceMappingURL=lint.js.map"], "mappings": ";;;;;;;;;;;;;;;;AAAe,SAASA,EAAUC,GAAK;AACnCC,EAAAA,KAASD,GACTE,IAASF,EAAI,QACbG,IAAQC,IAAMC,IAAU,IACxBC,EAAAA,GACAC,EAAAA;AACA,QAAMC,IAAMC,EAAAA;AACZ,SAAAC,EAAO,KAAK,GACLF;AACX;AATwBG,EAAAZ,GAAA,WAAA;AAUxB,IAAIE;AAAJ,IACIC;AADJ,IAEIC;AAFJ,IAGIC;AAHJ,IAIIC;AAJJ,IAKIO;AALJ,IAMIC;AACJ,SAASJ,IAAW;AAChB,QAAMK,IAAYX,GACZY,IAAU,CAAA;AAEhB,MADAL,EAAO,GAAG,GACN,CAACM,EAAK,GAAG,GAAG;AACZ;AACID,QAAQ,KAAKE,EAAW,CAAE;WACrBD,EAAK,GAAG;AACjBN,MAAO,GAAG;EAAA;AAEd,SAAO;IACH,MAAM;IACN,OAAOI;IACP,KAAKT;IACL,SAAAU;EACR;AACA;AAhBSJ,EAAAF,GAAA,UAAA;AAiBT,SAASQ,IAAc;AACnB,QAAMH,IAAYX,GACZe,IAAML,MAAS,WAAWM,EAAQ,IAAK;AAC7CT,IAAO,QAAQ,GACfA,EAAO,GAAG;AACV,QAAMU,IAAQC,EAAAA;AACd,SAAO;IACH,MAAM;IACN,OAAOP;IACP,KAAKT;IACL,KAAAa;IACA,OAAAE;EACR;AACA;AAbST,EAAAM,GAAA,aAAA;AAcT,SAASK,IAAW;AAChB,QAAMR,IAAYX,GACZoB,IAAS,CAAA;AAEf,MADAb,EAAO,GAAG,GACN,CAACM,EAAK,GAAG,GAAG;AACZ;AACIO,QAAO,KAAKF,EAAQ,CAAE;WACjBL,EAAK,GAAG;AACjBN,MAAO,GAAG;EAAA;AAEd,SAAO;IACH,MAAM;IACN,OAAOI;IACP,KAAKT;IACL,QAAAkB;EACR;AACA;AAhBSZ,EAAAW,GAAA,UAAA;AAiBT,SAASD,IAAW;AAChB,UAAQR,GAAI;IACR,KAAK;AACD,aAAOS,EAAQ;IACnB,KAAK;AACD,aAAOb,EAAQ;IACnB,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;AACD,YAAMe,IAAQL,EAAAA;AACd,aAAAZ,EAAAA,GACOiB;EACd;AACDd,IAAO,OAAO;AAClB;AAfSC,EAAAU,GAAA,UAAA;AAgBT,SAASF,IAAW;AAChB,SAAO,EAAE,MAAAN,GAAM,OAAAV,GAAO,KAAAC,GAAK,OAAO,KAAK,MAAMH,GAAO,MAAME,GAAOC,CAAG,CAAC,EAAC;AAC1E;AAFSO,EAAAQ,GAAA,UAAA;AAGT,SAAST,EAAOV,GAAK;AACjB,MAAIa,MAASb,GAAK;AACdO,MAAAA;AACA;EAAA;AAEJ,MAAIkB;AACJ,MAAIZ,MAAS;AACTY,QAAQ;WAEHrB,IAAMD,IAAQ;AACnBsB,QAAQ,MAAMxB,GAAO,MAAME,GAAOC,CAAG,IAAI;OAExC;AACD,UAAMsB,IAAQzB,GAAO,MAAME,CAAK,EAAE,MAAM,QAAQ;AAChDsB,QAAQ,OAAOC,IAAQA,EAAM,CAAC,IAAIzB,GAAOE,CAAK,KAAK;EAAA;AAEvD,QAAMwB,EAAY,YAAY3B,CAAAA,cAAiByB,CAAAA,GAAQ;AAC3D;AAjBSd,EAAAD,GAAA,QAAA;AAkBF,IAAMkB,IAAN,cAA8B,MAAM;EACvC,YAAYC,GAASC,GAAU;AAC3B,UAAMD,CAAO,GACb,KAAK,WAAWC;EACnB;AACL;AALanB,EAAAiB,GAAA,iBAAA;AAMb,SAASD,EAAYE,GAAS;AAC1B,SAAO,IAAID,EAAgBC,GAAS,EAAE,OAAA1B,GAAO,KAAAC,EAAK,CAAA;AACtD;AAFSO,EAAAgB,GAAA,aAAA;AAGT,SAASX,EAAKe,GAAG;AACb,MAAIlB,MAASkB;AACT,WAAAxB,EAAAA,GACO;AAEf;AALSI,EAAAK,GAAA,MAAA;AAMT,SAASV,IAAK;AACV,SAAIF,IAAMF,MACNE,KACAQ,IAAOR,MAAQF,IAAS,IAAID,GAAO,WAAWG,CAAG,IAE9CQ;AACX;AANSD,EAAAL,GAAA,IAAA;AAOT,SAASC,IAAM;AAEX,OADAF,IAAUD,GACHQ,MAAS,KAAKA,MAAS,MAAMA,MAAS,MAAMA,MAAS;AACxDN,MAAAA;AAEJ,MAAIM,MAAS,GAAG;AACZC,QAAO;AACP;EAAA;AAGJ,UADAV,IAAQC,GACAQ,GAAI;IACR,KAAK;AACD,aAAAC,IAAO,UACAmB,EAAU;IACrB,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;AACD,aAAAnB,IAAO,UACAoB,EAAU;IACrB,KAAK;AACD,UAAIhC,GAAO,MAAME,GAAOA,IAAQ,CAAC,MAAM;AACnC;AAEJC,WAAO,GACPE,EAAAA,GACAO,IAAO;AACP;IACJ,KAAK;AACD,UAAIZ,GAAO,MAAME,GAAOA,IAAQ,CAAC,MAAM;AACnC;AAEJC,WAAO,GACPE,EAAAA,GACAO,IAAO;AACP;IACJ,KAAK;AACD,UAAIZ,GAAO,MAAME,GAAOA,IAAQ,CAAC,MAAM;AACnC;AAEJC,WAAO,GACPE,EAAAA,GACAO,IAAO;AACP;EACP;AACDA,MAAOZ,GAAOE,CAAK,GACnBG,EAAAA;AACJ;AAtDSK,EAAAJ,GAAA,KAAA;AAuDT,SAASyB,IAAa;AAElB,OADA1B,EAAAA,GACOM,MAAS,MAAMA,IAAO;AACzB,QAAIA,MAAS;AAET,cADAA,IAAON,EAAE,GACDM,GAAI;QACR,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;AACDN,YAAAA;AACA;QACJ,KAAK;AACDA,YAAAA,GACA4B,EAAAA,GACAA,EAAAA,GACAA,EAAAA,GACAA,EAAAA;AACA;QACJ;AACI,gBAAMP,EAAY,gCAAgC;MACzD;SAEA;AAAA,UAAIvB,MAAQF;AACb,cAAMyB,EAAY,sBAAsB;AAGxCrB,QAAAA;;AAGR,MAAIM,MAAS,IAAI;AACbN,MAAAA;AACA;EAAA;AAEJ,QAAMqB,EAAY,sBAAsB;AAC5C;AAvCShB,EAAAqB,GAAA,YAAA;AAwCT,SAASE,IAAU;AACf,MAAKtB,KAAQ,MAAMA,KAAQ,MACtBA,KAAQ,MAAMA,KAAQ,MACtBA,KAAQ,MAAMA,KAAQ;AACvB,WAAON,EAAE;AAEb,QAAMqB,EAAY,6BAA6B;AACnD;AAPShB,EAAAuB,GAAA,SAAA;AAQT,SAASD,IAAa;AACdrB,QAAS,MACTN,EAAAA,GAEAM,MAAS,KACTN,EAAAA,IAGA6B,EAAAA,GAEAvB,MAAS,OACTN,EAAAA,GACA6B,EAAAA,KAEAvB,MAAS,MAAMA,MAAS,SACxBA,IAAON,EAAE,IACLM,MAAS,MAAMA,MAAS,OACxBN,EAAAA,GAEJ6B,EAAAA;AAER;AArBSxB,EAAAsB,GAAA,YAAA;AAsBT,SAASE,IAAa;AAClB,MAAIvB,IAAO,MAAMA,IAAO;AACpB,UAAMe,EAAY,yBAAyB;AAE/C;AACIrB,MAAAA;SACKM,KAAQ,MAAMA,KAAQ;AACnC;AAPSD,EAAAwB,GAAA,YAAA;ACtPTC,EAAW,eAAe,QAAQ,qBAAqB,CAACC,GAAMC,GAASC,MAAW;AAC9E,MAAI,CAACF;AACD,WAAO,CAAA;AAEX,MAAI7B;AACJ,MAAI;AACAA,QAAMT,EAAUsC,CAAI;EACvB,SACMG,GAAP;AACI,QAAIA,aAAiBZ;AACjB,aAAO,CAACa,EAAUF,GAAQC,EAAM,UAAUA,EAAM,OAAO,CAAC;AAE5D,UAAMA;EACT;AACD,QAAM,EAAE,gBAAAE,EAAgB,IAAGJ;AAC3B,SAAKI,IAGEC,EAAkBJ,GAAQG,GAAgBlC,CAAG,IAFzC,CAAA;AAGf,CAAC;AACD,SAASmC,EAAkBJ,GAAQG,GAAgBE,GAAc;AAC7D,MAAIC;AACJ,QAAMC,IAAS,CAAA;AACf,aAAWC,KAAUH,EAAa;AAC9B,QAAIG,GAAQ;AACR,YAAMC,KAAgBH,IAAKE,EAAO,SAAS,QAAQF,MAAO,SAAS,SAASA,EAAG,OACzEI,IAAOP,EAAeM,CAAY;AACxC,UAAIC;AACA,mBAAW,CAACC,GAAMrB,CAAO,KAAKsB,EAAcF,GAAMF,EAAO,KAAK;AAC1DD,YAAO,KAAKL,EAAUF,GAAQW,GAAMrB,CAAO,CAAC;;AAIhDiB,UAAO,KAAKL,EAAUF,GAAQQ,EAAO,KAAK,cAAcC,CAAAA,yCAAqD,CAAC;IAAA;AAI1H,SAAOF;AACX;AAlBSnC,EAAAgC,GAAA,mBAAA;AAmBT,SAASQ,EAAcF,GAAMG,GAAU;AACnC,MAAI,CAACH,KAAQ,CAACG;AACV,WAAO,CAAA;AAEX,MAAIH,aAAgBI;AAChB,WAAID,EAAS,SAAS,SACX,CAAC,CAACA,GAAU,SAASH,CAAAA,uCAA2C,CAAC,IAErEE,EAAcF,EAAK,QAAQG,CAAQ;AAE9C,MAAIA,EAAS,SAAS;AAClB,WAAO,CAAA;AAEX,MAAIH,aAAgBK,aAAa;AAC7B,UAAMC,IAAWN,EAAK;AACtB,QAAIG,EAAS,SAAS,SAAS;AAC3B,YAAM7B,IAAS6B,EAAS,UAAU,CAAA;AAClC,aAAOI,EAAOjC,GAAQ,CAAAkC,MAAQN,EAAcI,GAAUE,CAAI,CAAC;IAAA;AAE/D,WAAON,EAAcI,GAAUH,CAAQ;EAAA;AAE3C,MAAIH,aAAgBS,wBAAwB;AACxC,QAAIN,EAAS,SAAS;AAClB,aAAO,CAAC,CAACA,GAAU,SAASH,CAAAA,sBAA0B,CAAC;AAE3D,UAAMU,IAAiB,uBAAO,OAAO,IAAI,GACnCC,IAAcJ,EAAOJ,EAAS,SAAS,CAAAL,MAAU;AACnD,UAAIF;AACJ,YAAMgB,KAAahB,IAAKE,KAAW,OAA4B,SAASA,EAAO,SAAS,QAAQF,MAAO,SAAS,SAASA,EAAG;AAC5Hc,QAAeE,CAAS,IAAI;AAC5B,YAAMC,IAAab,EAAK,UAAW,EAACY,CAAS;AAC7C,UAAI,CAACC;AACD,eAAO;UACH;YACIf,EAAO;YACP,SAASE,CAAAA,4BAAgCY,CAAAA;UAC5C;QACrB;AAEY,YAAME,IAAYD,IAAaA,EAAW,OAAO;AACjD,aAAOX,EAAcY,GAAWhB,EAAO,KAAK;IACxD,CAAS;AACD,eAAWc,KAAa,OAAO,KAAKZ,EAAK,UAAS,CAAE,GAAG;AACnD,YAAMe,IAAQf,EAAK,UAAW,EAACY,CAAS;AACpC,OAACF,EAAeE,CAAS,KACzBG,EAAM,gBAAgBX,kBACtB,CAACW,EAAM,gBACPJ,EAAY,KAAK;QACbR;QACA,mBAAmBH,CAAAA,gCAAoCY,CAAAA;MAC3E,CAAiB;IAAA;AAGT,WAAOD;EAAA;AAEX,SAAKX,EAAK,SAAS,aAAaG,EAAS,SAAS,aAC7CH,EAAK,SAAS,YAAYG,EAAS,SAAS,YAC5CH,EAAK,SAAS,QACXG,EAAS,SAAS,YAClBA,EAAS,SAAS,YACrBH,EAAK,SAAS,WAAWG,EAAS,SAAS,YAC3CH,EAAK,SAAS,UACVG,EAAS,SAAS,aAAaA,EAAS,QAAQ,OAAOA,EAAS,SAC9D,CAAC,CAACA,GAAU,2BAA2BH,CAAAA,IAAQ,CAAC,KAEtDA,aAAgBgB,mBAAmBhB,aAAgBiB,uBAClDd,EAAS,SAAS,YAChBA,EAAS,SAAS,YAClBA,EAAS,SAAS,aAClBA,EAAS,SAAS,UAClBe,EAAUlB,EAAK,WAAWG,EAAS,KAAK,CAAC,KACtC,CAAC,CAACA,GAAU,2BAA2BH,CAAAA,IAAQ,CAAC,IAEpD,CAAA;AACX;AA1EStC,EAAAwC,GAAA,eAAA;AA2ET,SAASV,EAAUF,GAAQW,GAAMrB,GAAS;AACtC,SAAO;IACH,SAAAA;IACA,UAAU;IACV,MAAM;IACN,MAAMU,EAAO,aAAaW,EAAK,KAAK;IACpC,IAAIX,EAAO,aAAaW,EAAK,GAAG;EACxC;AACA;AARSvC,EAAA8B,GAAA,WAAA;AAST,SAAS0B,EAAU/C,GAAO;AACtB,SAAOA,KAAU,QAA+BA,MAAUA;AAC9D;AAFST,EAAAwD,GAAA,WAAA;AAGT,SAASX,EAAOY,GAAOC,GAAQ;AAC3B,SAAO,MAAM,UAAU,OAAO,MAAM,CAAE,GAAED,EAAM,IAAIC,CAAM,CAAC;AAC7D;AAFS1D,EAAA6C,GAAA,QAAA;", "names": ["jsonParse", "str", "string", "strLen", "start", "end", "lastEnd", "ch", "lex", "ast", "parseObj", "expect", "__name", "code", "kind", "nodeStart", "members", "skip", "parseMember", "key", "curToken", "value", "parseVal", "parseArr", "values", "token", "found", "match", "syntaxError", "JSONSyntaxError", "message", "position", "k", "readString", "readNumber", "readHex", "readDigits", "CodeMirror", "text", "options", "editor", "error", "lintError", "variableToType", "validateVariables", "variablesAST", "_a", "errors", "member", "variableName", "type", "node", "validate<PERSON><PERSON>ue", "valueAST", "GraphQLNonNull", "GraphQLList", "itemType", "mapCat", "item", "GraphQLInputObjectType", "providedFields", "fieldErrors", "fieldName", "inputField", "fieldType", "field", "GraphQLEnumType", "GraphQLScalarType", "<PERSON><PERSON><PERSON><PERSON>", "array", "mapper"]}