{"version": 3, "sources": ["browser-external:node:fs", "browser-external:node:path", "browser-external:node:os", "browser-external:node:crypto", "../../tinacms/dist/node-cache-5e8db9f0.mjs"], "sourcesContent": ["module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"node:fs\" has been externalized for browser compatibility. Cannot access \"node:fs.${key}\" in client code. See http://vitejs.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"node:path\" has been externalized for browser compatibility. Cannot access \"node:path.${key}\" in client code. See http://vitejs.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"node:os\" has been externalized for browser compatibility. Cannot access \"node:os.${key}\" in client code. See http://vitejs.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"node:crypto\" has been externalized for browser compatibility. Cannot access \"node:crypto.${key}\" in client code. See http://vitejs.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "const makeCacheDir = async (dir, fs, path, os) => {\n  const pathParts = dir.split(path.sep).filter(Boolean);\n  const cacheHash = pathParts[pathParts.length - 1];\n  const rootUser = pathParts[0];\n  let cacheDir = dir;\n  if (!fs.existsSync(path.join(path.sep, rootUser))) {\n    cacheDir = path.join(os.tmpdir(), cacheHash);\n  }\n  try {\n    fs.mkdirSync(cacheDir, { recursive: true });\n  } catch (error) {\n    throw new Error(`Failed to create cache directory: ${error.message}`);\n  }\n  return cacheDir;\n};\nconst NodeCache = async (dir) => {\n  const fs = require(\"node:fs\");\n  const path = require(\"node:path\");\n  const os = require(\"node:os\");\n  const { createHash } = require(\"node:crypto\");\n  const cacheDir = await makeCacheDir(dir, fs, path, os);\n  return {\n    makeKey: (key) => {\n      const input = key && key instanceof Object ? JSON.stringify(key) : key || \"\";\n      return createHash(\"sha256\").update(input).digest(\"hex\");\n    },\n    get: async (key) => {\n      let readValue;\n      const cacheFilename = `${cacheDir}/${key}`;\n      try {\n        const data = await fs.promises.readFile(cacheFilename, \"utf-8\");\n        readValue = JSON.parse(data);\n      } catch (e) {\n        if (e.code !== \"ENOENT\") {\n          console.error(\n            `Failed to read cache file to ${cacheFilename}: ${e.message}`\n          );\n        }\n      }\n      return readValue;\n    },\n    set: async (key, value) => {\n      const cacheFilename = `${cacheDir}/${key}`;\n      try {\n        await fs.promises.writeFile(cacheFilename, JSON.stringify(value), {\n          encoding: \"utf-8\",\n          flag: \"wx\"\n          // Don't overwrite existing caches\n        });\n      } catch (e) {\n        if (e.code !== \"EEXIST\") {\n          console.error(\n            `Failed to write cache file to ${cacheFilename}: ${e.message}`\n          );\n        }\n      }\n    }\n  };\n};\nexport {\n  NodeCache,\n  makeCacheDir\n};\n"], "mappings": ";;;;;AAAA;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,4FAA4F,GAAG,oIAAoI;AAAA,QAClP;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,gGAAgG,GAAG,oIAAoI;AAAA,QACtP;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,4FAA4F,GAAG,oIAAoI;AAAA,QAClP;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,oGAAoG,GAAG,oIAAoI;AAAA,QAC1P;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF,IAAM,eAAe,OAAO,KAAK,IAAI,MAAM,OAAO;AAChD,QAAM,YAAY,IAAI,MAAM,KAAK,GAAG,EAAE,OAAO,OAAO;AACpD,QAAM,YAAY,UAAU,UAAU,SAAS,CAAC;AAChD,QAAM,WAAW,UAAU,CAAC;AAC5B,MAAI,WAAW;AACf,MAAI,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,KAAK,QAAQ,CAAC,GAAG;AACjD,eAAW,KAAK,KAAK,GAAG,OAAO,GAAG,SAAS;AAAA,EAC7C;AACA,MAAI;AACF,OAAG,UAAU,UAAU,EAAE,WAAW,KAAK,CAAC;AAAA,EAC5C,SAAS,OAAO;AACd,UAAM,IAAI,MAAM,qCAAqC,MAAM,OAAO,EAAE;AAAA,EACtE;AACA,SAAO;AACT;AACA,IAAM,YAAY,OAAO,QAAQ;AAC/B,QAAM,KAAK;AACX,QAAM,OAAO;AACb,QAAM,KAAK;AACX,QAAM,EAAE,WAAW,IAAI;AACvB,QAAM,WAAW,MAAM,aAAa,KAAK,IAAI,MAAM,EAAE;AACrD,SAAO;AAAA,IACL,SAAS,CAAC,QAAQ;AAChB,YAAM,QAAQ,OAAO,eAAe,SAAS,KAAK,UAAU,GAAG,IAAI,OAAO;AAC1E,aAAO,WAAW,QAAQ,EAAE,OAAO,KAAK,EAAE,OAAO,KAAK;AAAA,IACxD;AAAA,IACA,KAAK,OAAO,QAAQ;AAClB,UAAI;AACJ,YAAM,gBAAgB,GAAG,QAAQ,IAAI,GAAG;AACxC,UAAI;AACF,cAAM,OAAO,MAAM,GAAG,SAAS,SAAS,eAAe,OAAO;AAC9D,oBAAY,KAAK,MAAM,IAAI;AAAA,MAC7B,SAAS,GAAG;AACV,YAAI,EAAE,SAAS,UAAU;AACvB,kBAAQ;AAAA,YACN,gCAAgC,aAAa,KAAK,EAAE,OAAO;AAAA,UAC7D;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA,IACA,KAAK,OAAO,KAAK,UAAU;AACzB,YAAM,gBAAgB,GAAG,QAAQ,IAAI,GAAG;AACxC,UAAI;AACF,cAAM,GAAG,SAAS,UAAU,eAAe,KAAK,UAAU,KAAK,GAAG;AAAA,UAChE,UAAU;AAAA,UACV,MAAM;AAAA;AAAA,QAER,CAAC;AAAA,MACH,SAAS,GAAG;AACV,YAAI,EAAE,SAAS,UAAU;AACvB,kBAAQ;AAAA,YACN,iCAAiC,aAAa,KAAK,EAAE,OAAO;AAAA,UAC9D;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;", "names": []}