import {
  cu,
  hu
} from "./chunk-MEKKV4OY.js";

// node_modules/@graphiql/react/dist/show-hint.es.js
var rt = Object.defineProperty;
var p = (H, A) => rt(H, "name", { value: A, configurable: true });
function ht(H, A) {
  for (var r = 0; r < A.length; r++) {
    const w = A[r];
    if (typeof w != "string" && !Array.isArray(w)) {
      for (const v in w)
        if (v !== "default" && !(v in H)) {
          const b = Object.getOwnPropertyDescriptor(w, v);
          b && Object.defineProperty(H, v, b.get ? b : {
            enumerable: true,
            get: () => w[v]
          });
        }
    }
  }
  return Object.freeze(Object.defineProperty(H, Symbol.toStringTag, { value: "Module" }));
}
p(ht, "_mergeNamespaces");
var at = { exports: {} };
(function(H, A) {
  (function(r) {
    r(cu());
  })(function(r) {
    var w = "CodeMirror-hint", v = "CodeMirror-hint-active";
    r.showHint = function(t, e, i) {
      if (!e)
        return t.showHint(i);
      i && i.async && (e.async = true);
      var n = { hint: e };
      if (i)
        for (var s in i)
          n[s] = i[s];
      return t.showHint(n);
    }, r.defineExtension("showHint", function(t) {
      t = Z(this, this.getCursor("start"), t);
      var e = this.listSelections();
      if (!(e.length > 1)) {
        if (this.somethingSelected()) {
          if (!t.hint.supportsSelection)
            return;
          for (var i = 0; i < e.length; i++)
            if (e[i].head.line != e[i].anchor.line)
              return;
        }
        this.state.completionActive && this.state.completionActive.close();
        var n = this.state.completionActive = new b(this, t);
        n.options.hint && (r.signal(this, "startCompletion", this), n.update(true));
      }
    }), r.defineExtension("closeHint", function() {
      this.state.completionActive && this.state.completionActive.close();
    });
    function b(t, e) {
      if (this.cm = t, this.options = e, this.widget = null, this.debounce = 0, this.tick = 0, this.startPos = this.cm.getCursor("start"), this.startLen = this.cm.getLine(this.startPos.line).length - this.cm.getSelection().length, this.options.updateOnCursorActivity) {
        var i = this;
        t.on("cursorActivity", this.activityFunc = function() {
          i.cursorActivity();
        });
      }
    }
    p(b, "Completion");
    var J = window.requestAnimationFrame || function(t) {
      return setTimeout(t, 1e3 / 60);
    }, Q = window.cancelAnimationFrame || clearTimeout;
    b.prototype = {
      close: function() {
        this.active() && (this.cm.state.completionActive = null, this.tick = null, this.options.updateOnCursorActivity && this.cm.off("cursorActivity", this.activityFunc), this.widget && this.data && r.signal(this.data, "close"), this.widget && this.widget.close(), r.signal(this.cm, "endCompletion", this.cm));
      },
      active: function() {
        return this.cm.state.completionActive == this;
      },
      pick: function(t, e) {
        var i = t.list[e], n = this;
        this.cm.operation(function() {
          i.hint ? i.hint(n.cm, t, i) : n.cm.replaceRange(
            _(i),
            i.from || t.from,
            i.to || t.to,
            "complete"
          ), r.signal(t, "pick", i), n.cm.scrollIntoView();
        }), this.options.closeOnPick && this.close();
      },
      cursorActivity: function() {
        this.debounce && (Q(this.debounce), this.debounce = 0);
        var t = this.startPos;
        this.data && (t = this.data.from);
        var e = this.cm.getCursor(), i = this.cm.getLine(e.line);
        if (e.line != this.startPos.line || i.length - e.ch != this.startLen - this.startPos.ch || e.ch < t.ch || this.cm.somethingSelected() || !e.ch || this.options.closeCharacters.test(i.charAt(e.ch - 1)))
          this.close();
        else {
          var n = this;
          this.debounce = J(function() {
            n.update();
          }), this.widget && this.widget.disable();
        }
      },
      update: function(t) {
        if (this.tick != null) {
          var e = this, i = ++this.tick;
          U(this.options.hint, this.cm, this.options, function(n) {
            e.tick == i && e.finishUpdate(n, t);
          });
        }
      },
      finishUpdate: function(t, e) {
        this.data && r.signal(this.data, "update");
        var i = this.widget && this.widget.picked || e && this.options.completeSingle;
        this.widget && this.widget.close(), this.data = t, t && t.list.length && (i && t.list.length == 1 ? this.pick(t, 0) : (this.widget = new K(this, t), r.signal(t, "shown")));
      }
    };
    function Z(t, e, i) {
      var n = t.options.hintOptions, s = {};
      for (var c in D)
        s[c] = D[c];
      if (n)
        for (var c in n)
          n[c] !== void 0 && (s[c] = n[c]);
      if (i)
        for (var c in i)
          i[c] !== void 0 && (s[c] = i[c]);
      return s.hint.resolve && (s.hint = s.hint.resolve(t, e)), s;
    }
    p(Z, "parseOptions");
    function _(t) {
      return typeof t == "string" ? t : t.text;
    }
    p(_, "getText");
    function tt(t, e) {
      var i = {
        Up: function() {
          e.moveFocus(-1);
        },
        Down: function() {
          e.moveFocus(1);
        },
        PageUp: function() {
          e.moveFocus(-e.menuSize() + 1, true);
        },
        PageDown: function() {
          e.moveFocus(e.menuSize() - 1, true);
        },
        Home: function() {
          e.setFocus(0);
        },
        End: function() {
          e.setFocus(e.length - 1);
        },
        Enter: e.pick,
        Tab: e.pick,
        Esc: e.close
      }, n = /Mac/.test(navigator.platform);
      n && (i["Ctrl-P"] = function() {
        e.moveFocus(-1);
      }, i["Ctrl-N"] = function() {
        e.moveFocus(1);
      });
      var s = t.options.customKeys, c = s ? {} : i;
      function o(u, l) {
        var a;
        typeof l != "string" ? a = p(function(S) {
          return l(S, e);
        }, "bound") : i.hasOwnProperty(l) ? a = i[l] : a = l, c[u] = a;
      }
      if (p(o, "addBinding"), s)
        for (var f in s)
          s.hasOwnProperty(f) && o(f, s[f]);
      var h = t.options.extraKeys;
      if (h)
        for (var f in h)
          h.hasOwnProperty(f) && o(f, h[f]);
      return c;
    }
    p(tt, "buildKeyMap");
    function B(t, e) {
      for (; e && e != t; ) {
        if (e.nodeName.toUpperCase() === "LI" && e.parentNode == t)
          return e;
        e = e.parentNode;
      }
    }
    p(B, "getHintElement");
    function K(t, e) {
      this.id = "cm-complete-" + Math.floor(Math.random(1e6)), this.completion = t, this.data = e, this.picked = false;
      var i = this, n = t.cm, s = n.getInputField().ownerDocument, c = s.defaultView || s.parentWindow, o = this.hints = s.createElement("ul");
      o.setAttribute("role", "listbox"), o.setAttribute("aria-expanded", "true"), o.id = this.id;
      var f = t.cm.options.theme;
      o.className = "CodeMirror-hints " + f, this.selectedHint = e.selectedHint || 0;
      for (var h = e.list, u = 0; u < h.length; ++u) {
        var l = o.appendChild(s.createElement("li")), a = h[u], S = w + (u != this.selectedHint ? "" : " " + v);
        a.className != null && (S = a.className + " " + S), l.className = S, u == this.selectedHint && l.setAttribute("aria-selected", "true"), l.id = this.id + "-" + u, l.setAttribute("role", "option"), a.render ? a.render(l, e, a) : l.appendChild(s.createTextNode(a.displayText || _(a))), l.hintId = u;
      }
      var x = t.options.container || s.body, y = n.cursorCoords(t.options.alignWithWord ? e.from : null), k = y.left, O = y.bottom, j = true, F = 0, E = 0;
      if (x !== s.body) {
        var nt = ["absolute", "relative", "fixed"].indexOf(c.getComputedStyle(x).position) !== -1, W = nt ? x : x.offsetParent, M = W.getBoundingClientRect(), z = s.body.getBoundingClientRect();
        F = M.left - z.left - W.scrollLeft, E = M.top - z.top - W.scrollTop;
      }
      o.style.left = k - F + "px", o.style.top = O - E + "px";
      var N = c.innerWidth || Math.max(s.body.offsetWidth, s.documentElement.offsetWidth), L = c.innerHeight || Math.max(s.body.offsetHeight, s.documentElement.offsetHeight);
      x.appendChild(o), n.getInputField().setAttribute("aria-autocomplete", "list"), n.getInputField().setAttribute("aria-owns", this.id), n.getInputField().setAttribute("aria-activedescendant", this.id + "-" + this.selectedHint);
      var m = t.options.moveOnOverlap ? o.getBoundingClientRect() : new DOMRect(), q = t.options.paddingForScrollbar ? o.scrollHeight > o.clientHeight + 1 : false, T;
      setTimeout(function() {
        T = n.getScrollInfo();
      });
      var st = m.bottom - L;
      if (st > 0) {
        var P = m.bottom - m.top, ot = y.top - (y.bottom - m.top);
        if (ot - P > 0)
          o.style.top = (O = y.top - P - E) + "px", j = false;
        else if (P > L) {
          o.style.height = L - 5 + "px", o.style.top = (O = y.bottom - m.top - E) + "px";
          var V = n.getCursor();
          e.from.ch != V.ch && (y = n.cursorCoords(V), o.style.left = (k = y.left - F) + "px", m = o.getBoundingClientRect());
        }
      }
      var C = m.right - N;
      if (q && (C += n.display.nativeBarWidth), C > 0 && (m.right - m.left > N && (o.style.width = N - 5 + "px", C -= m.right - m.left - N), o.style.left = (k = y.left - C - F) + "px"), q)
        for (var I = o.firstChild; I; I = I.nextSibling)
          I.style.paddingRight = n.display.nativeBarWidth + "px";
      if (n.addKeyMap(this.keyMap = tt(t, {
        moveFocus: function(d, g) {
          i.changeActive(i.selectedHint + d, g);
        },
        setFocus: function(d) {
          i.changeActive(d);
        },
        menuSize: function() {
          return i.screenAmount();
        },
        length: h.length,
        close: function() {
          t.close();
        },
        pick: function() {
          i.pick();
        },
        data: e
      })), t.options.closeOnUnfocus) {
        var Y;
        n.on("blur", this.onBlur = function() {
          Y = setTimeout(function() {
            t.close();
          }, 100);
        }), n.on("focus", this.onFocus = function() {
          clearTimeout(Y);
        });
      }
      n.on("scroll", this.onScroll = function() {
        var d = n.getScrollInfo(), g = n.getWrapperElement().getBoundingClientRect();
        T || (T = n.getScrollInfo());
        var X = O + T.top - d.top, R = X - (c.pageYOffset || (s.documentElement || s.body).scrollTop);
        if (j || (R += o.offsetHeight), R <= g.top || R >= g.bottom)
          return t.close();
        o.style.top = X + "px", o.style.left = k + T.left - d.left + "px";
      }), r.on(o, "dblclick", function(d) {
        var g = B(o, d.target || d.srcElement);
        g && g.hintId != null && (i.changeActive(g.hintId), i.pick());
      }), r.on(o, "click", function(d) {
        var g = B(o, d.target || d.srcElement);
        g && g.hintId != null && (i.changeActive(g.hintId), t.options.completeOnSingleClick && i.pick());
      }), r.on(o, "mousedown", function() {
        setTimeout(function() {
          n.focus();
        }, 20);
      });
      var $ = this.getSelectedHintRange();
      return ($.from !== 0 || $.to !== 0) && this.scrollToActive(), r.signal(e, "select", h[this.selectedHint], o.childNodes[this.selectedHint]), true;
    }
    p(K, "Widget"), K.prototype = {
      close: function() {
        if (this.completion.widget == this) {
          this.completion.widget = null, this.hints.parentNode && this.hints.parentNode.removeChild(this.hints), this.completion.cm.removeKeyMap(this.keyMap);
          var t = this.completion.cm.getInputField();
          t.removeAttribute("aria-activedescendant"), t.removeAttribute("aria-owns");
          var e = this.completion.cm;
          this.completion.options.closeOnUnfocus && (e.off("blur", this.onBlur), e.off("focus", this.onFocus)), e.off("scroll", this.onScroll);
        }
      },
      disable: function() {
        this.completion.cm.removeKeyMap(this.keyMap);
        var t = this;
        this.keyMap = { Enter: function() {
          t.picked = true;
        } }, this.completion.cm.addKeyMap(this.keyMap);
      },
      pick: function() {
        this.completion.pick(this.data, this.selectedHint);
      },
      changeActive: function(t, e) {
        if (t >= this.data.list.length ? t = e ? this.data.list.length - 1 : 0 : t < 0 && (t = e ? 0 : this.data.list.length - 1), this.selectedHint != t) {
          var i = this.hints.childNodes[this.selectedHint];
          i && (i.className = i.className.replace(" " + v, ""), i.removeAttribute("aria-selected")), i = this.hints.childNodes[this.selectedHint = t], i.className += " " + v, i.setAttribute("aria-selected", "true"), this.completion.cm.getInputField().setAttribute("aria-activedescendant", i.id), this.scrollToActive(), r.signal(this.data, "select", this.data.list[this.selectedHint], i);
        }
      },
      scrollToActive: function() {
        var t = this.getSelectedHintRange(), e = this.hints.childNodes[t.from], i = this.hints.childNodes[t.to], n = this.hints.firstChild;
        e.offsetTop < this.hints.scrollTop ? this.hints.scrollTop = e.offsetTop - n.offsetTop : i.offsetTop + i.offsetHeight > this.hints.scrollTop + this.hints.clientHeight && (this.hints.scrollTop = i.offsetTop + i.offsetHeight - this.hints.clientHeight + n.offsetTop);
      },
      screenAmount: function() {
        return Math.floor(this.hints.clientHeight / this.hints.firstChild.offsetHeight) || 1;
      },
      getSelectedHintRange: function() {
        var t = this.completion.options.scrollMargin || 0;
        return {
          from: Math.max(0, this.selectedHint - t),
          to: Math.min(this.data.list.length - 1, this.selectedHint + t)
        };
      }
    };
    function et(t, e) {
      if (!t.somethingSelected())
        return e;
      for (var i = [], n = 0; n < e.length; n++)
        e[n].supportsSelection && i.push(e[n]);
      return i;
    }
    p(et, "applicableHelpers");
    function U(t, e, i, n) {
      if (t.async)
        t(e, n, i);
      else {
        var s = t(e, i);
        s && s.then ? s.then(n) : n(s);
      }
    }
    p(U, "fetchHints");
    function it(t, e) {
      var i = t.getHelpers(e, "hint"), n;
      if (i.length) {
        var s = p(function(c, o, f) {
          var h = et(c, i);
          function u(l) {
            if (l == h.length)
              return o(null);
            U(h[l], c, f, function(a) {
              a && a.list.length > 0 ? o(a) : u(l + 1);
            });
          }
          p(u, "run"), u(0);
        }, "resolved");
        return s.async = true, s.supportsSelection = true, s;
      } else
        return (n = t.getHelper(t.getCursor(), "hintWords")) ? function(c) {
          return r.hint.fromList(c, { words: n });
        } : r.hint.anyword ? function(c, o) {
          return r.hint.anyword(c, o);
        } : function() {
        };
    }
    p(it, "resolveAutoHints"), r.registerHelper("hint", "auto", {
      resolve: it
    }), r.registerHelper("hint", "fromList", function(t, e) {
      var i = t.getCursor(), n = t.getTokenAt(i), s, c = r.Pos(i.line, n.start), o = i;
      n.start < i.ch && /\w/.test(n.string.charAt(i.ch - n.start - 1)) ? s = n.string.substr(0, i.ch - n.start) : (s = "", c = i);
      for (var f = [], h = 0; h < e.words.length; h++) {
        var u = e.words[h];
        u.slice(0, s.length) == s && f.push(u);
      }
      if (f.length)
        return { list: f, from: c, to: o };
    }), r.commands.autocomplete = r.showHint;
    var D = {
      hint: r.hint.auto,
      completeSingle: true,
      alignWithWord: true,
      closeCharacters: /[\s()\[\]{};:>,]/,
      closeOnPick: true,
      closeOnUnfocus: true,
      updateOnCursorActivity: true,
      completeOnSingleClick: true,
      container: null,
      customKeys: null,
      extraKeys: null,
      paddingForScrollbar: true,
      moveOnOverlap: true
    };
    r.defineOption("hintOptions", null);
  });
})();
var G = at.exports;
var ft = hu(G);
var dt = ht({
  __proto__: null,
  default: ft
}, [G]);

export {
  dt
};
//# sourceMappingURL=chunk-KC5P2RGA.js.map
