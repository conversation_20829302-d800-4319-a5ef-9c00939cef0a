import {
  cu,
  hu
} from "./chunk-MEKKV4OY.js";
import "./chunk-AUZ3RYOM.js";

// node_modules/@graphiql/react/dist/brace-fold.es.js
var m = Object.defineProperty;
var y = (T, d) => m(T, "name", { value: d, configurable: true });
function I(T, d) {
  for (var e = 0; e < d.length; e++) {
    const c = d[e];
    if (typeof c != "string" && !Array.isArray(c)) {
      for (const t in c)
        if (t !== "default" && !(t in T)) {
          const a = Object.getOwnPropertyDescriptor(c, t);
          a && Object.defineProperty(T, t, a.get ? a : {
            enumerable: true,
            get: () => c[t]
          });
        }
    }
  }
  return Object.freeze(Object.defineProperty(T, Symbol.toStringTag, { value: "Module" }));
}
y(I, "_mergeNamespaces");
var w = { exports: {} };
(function(T, d) {
  (function(e) {
    e(cu());
  })(function(e) {
    function c(t) {
      return function(a, f) {
        var r = f.line, s = a.getLine(r);
        function v(l) {
          for (var u, g = f.ch, h = 0; ; ) {
            var p = g <= 0 ? -1 : s.lastIndexOf(l[0], g - 1);
            if (p == -1) {
              if (h == 1)
                break;
              h = 1, g = s.length;
              continue;
            }
            if (h == 1 && p < f.ch)
              break;
            if (u = a.getTokenTypeAt(e.Pos(r, p + 1)), !/^(comment|string)/.test(u))
              return { ch: p + 1, tokenType: u, pair: l };
            g = p - 1;
          }
        }
        y(v, "findOpening");
        function k(l) {
          var u = 1, g = a.lastLine(), h, p = l.ch, j;
          e:
            for (var L = r; L <= g; ++L)
              for (var A = a.getLine(L), b = L == r ? p : 0; ; ) {
                var O = A.indexOf(l.pair[0], b), F = A.indexOf(l.pair[1], b);
                if (O < 0 && (O = A.length), F < 0 && (F = A.length), b = Math.min(O, F), b == A.length)
                  break;
                if (a.getTokenTypeAt(e.Pos(L, b + 1)) == l.tokenType) {
                  if (b == O)
                    ++u;
                  else if (!--u) {
                    h = L, j = b;
                    break e;
                  }
                }
                ++b;
              }
          return h == null || r == h ? null : {
            from: e.Pos(r, p),
            to: e.Pos(h, j)
          };
        }
        y(k, "findRange");
        for (var i = [], n = 0; n < t.length; n++) {
          var o = v(t[n]);
          o && i.push(o);
        }
        i.sort(function(l, u) {
          return l.ch - u.ch;
        });
        for (var n = 0; n < i.length; n++) {
          var P = k(i[n]);
          if (P)
            return P;
        }
        return null;
      };
    }
    y(c, "bracketFolding"), e.registerHelper("fold", "brace", c([["{", "}"], ["[", "]"]])), e.registerHelper("fold", "brace-paren", c([["{", "}"], ["[", "]"], ["(", ")"]])), e.registerHelper("fold", "import", function(t, a) {
      function f(n) {
        if (n < t.firstLine() || n > t.lastLine())
          return null;
        var o = t.getTokenAt(e.Pos(n, 1));
        if (/\S/.test(o.string) || (o = t.getTokenAt(e.Pos(n, o.end + 1))), o.type != "keyword" || o.string != "import")
          return null;
        for (var P = n, l = Math.min(t.lastLine(), n + 10); P <= l; ++P) {
          var u = t.getLine(P), g = u.indexOf(";");
          if (g != -1)
            return { startCh: o.end, end: e.Pos(P, g) };
        }
      }
      y(f, "hasImport");
      var r = a.line, s = f(r), v;
      if (!s || f(r - 1) || (v = f(r - 2)) && v.end.line == r - 1)
        return null;
      for (var k = s.end; ; ) {
        var i = f(k.line + 1);
        if (i == null)
          break;
        k = i.end;
      }
      return { from: t.clipPos(e.Pos(r, s.startCh + 1)), to: k };
    }), e.registerHelper("fold", "include", function(t, a) {
      function f(i) {
        if (i < t.firstLine() || i > t.lastLine())
          return null;
        var n = t.getTokenAt(e.Pos(i, 1));
        if (/\S/.test(n.string) || (n = t.getTokenAt(e.Pos(i, n.end + 1))), n.type == "meta" && n.string.slice(0, 8) == "#include")
          return n.start + 8;
      }
      y(f, "hasInclude");
      var r = a.line, s = f(r);
      if (s == null || f(r - 1) != null)
        return null;
      for (var v = r; ; ) {
        var k = f(v + 1);
        if (k == null)
          break;
        ++v;
      }
      return {
        from: e.Pos(r, s + 1),
        to: t.clipPos(e.Pos(v))
      };
    });
  });
})();
var _ = w.exports;
var D = hu(_);
var q = I({
  __proto__: null,
  default: D
}, [_]);
export {
  q as b
};
//# sourceMappingURL=brace-fold.es-VP5LGWRX.js.map
