// node_modules/@graphiql/react/dist/mode-indent.es.js
var d = Object.defineProperty;
var o = (e, n) => d(e, "name", { value: n, configurable: true });
function r(e, n) {
  var t, i;
  const { levels: l, indentLevel: v } = e;
  return ((!l || l.length === 0 ? v : l.at(-1) - (!((t = this.electricInput) === null || t === void 0) && t.test(n) ? 1 : 0)) || 0) * (((i = this.config) === null || i === void 0 ? void 0 : i.indentUnit) || 0);
}
o(r, "indent");

export {
  r
};
//# sourceMappingURL=chunk-A4B2V6D5.js.map
