import "./chunk-IZ46667E.js";
import {
  Kn,
  Wn,
  Xn,
  Zn,
  et,
  nt
} from "./chunk-ROL6L7B7.js";
import "./chunk-PEYRUYRT.js";
import {
  d
} from "./chunk-WIXYZC3K.js";
import "./chunk-MEKKV4OY.js";
import {
  GraphQLList,
  GraphQLNonNull
} from "./chunk-HD22INE4.js";
import "./chunk-AUZ3RYOM.js";

// node_modules/@graphiql/react/dist/info.es.js
var D = Object.defineProperty;
var m = (a, e) => D(a, "name", { value: e, configurable: true });
d.registerHelper("info", "graphql", (a, e) => {
  if (!e.schema || !a.state)
    return;
  const { kind: d2, step: n } = a.state, r = Xn(e.schema, a.state);
  if (d2 === "Field" && n === 0 && r.fieldDef || d2 === "AliasedField" && n === 2 && r.fieldDef) {
    const i = document.createElement("div");
    i.className = "CodeMirror-info-header", M(i, r, e);
    const c = document.createElement("div");
    return c.append(i), o(c, e, r.fieldDef), c;
  }
  if (d2 === "Directive" && n === 1 && r.directiveDef) {
    const i = document.createElement("div");
    i.className = "CodeMirror-info-header", V(i, r, e);
    const c = document.createElement("div");
    return c.append(i), o(c, e, r.directiveDef), c;
  }
  if (d2 === "Argument" && n === 0 && r.argDef) {
    const i = document.createElement("div");
    i.className = "CodeMirror-info-header", x(i, r, e);
    const c = document.createElement("div");
    return c.append(i), o(c, e, r.argDef), c;
  }
  if (d2 === "EnumValue" && r.enumValue && r.enumValue.description) {
    const i = document.createElement("div");
    i.className = "CodeMirror-info-header", L(i, r, e);
    const c = document.createElement("div");
    return c.append(i), o(c, e, r.enumValue), c;
  }
  if (d2 === "NamedType" && r.type && r.type.description) {
    const i = document.createElement("div");
    i.className = "CodeMirror-info-header", l(i, r, e, r.type);
    const c = document.createElement("div");
    return c.append(i), o(c, e, r.type), c;
  }
});
function M(a, e, d2) {
  y(a, e, d2), p(a, e, d2, e.type);
}
m(M, "renderField");
function y(a, e, d2) {
  var n;
  const r = ((n = e.fieldDef) === null || n === void 0 ? void 0 : n.name) || "";
  t(a, r, "field-name", d2, Wn(e));
}
m(y, "renderQualifiedField");
function V(a, e, d2) {
  var n;
  const r = "@" + (((n = e.directiveDef) === null || n === void 0 ? void 0 : n.name) || "");
  t(a, r, "directive-name", d2, Zn(e));
}
m(V, "renderDirective");
function x(a, e, d2) {
  var n;
  const r = ((n = e.argDef) === null || n === void 0 ? void 0 : n.name) || "";
  t(a, r, "arg-name", d2, Kn(e)), p(a, e, d2, e.inputType);
}
m(x, "renderArg");
function L(a, e, d2) {
  var n;
  const r = ((n = e.enumValue) === null || n === void 0 ? void 0 : n.name) || "";
  l(a, e, d2, e.inputType), t(a, "."), t(a, r, "enum-value", d2, et(e));
}
m(L, "renderEnumValue");
function p(a, e, d2, n) {
  const r = document.createElement("span");
  r.className = "type-name-pill", n instanceof GraphQLNonNull ? (l(r, e, d2, n.ofType), t(r, "!")) : n instanceof GraphQLList ? (t(r, "["), l(r, e, d2, n.ofType), t(r, "]")) : t(r, (n == null ? void 0 : n.name) || "", "type-name", d2, nt(e, n)), a.append(r);
}
m(p, "renderTypeAnnotation");
function l(a, e, d2, n) {
  n instanceof GraphQLNonNull ? (l(a, e, d2, n.ofType), t(a, "!")) : n instanceof GraphQLList ? (t(a, "["), l(a, e, d2, n.ofType), t(a, "]")) : t(a, (n == null ? void 0 : n.name) || "", "type-name", d2, nt(e, n));
}
m(l, "renderType");
function o(a, e, d2) {
  const { description: n } = d2;
  if (n) {
    const r = document.createElement("div");
    r.className = "info-description", e.renderDescription ? r.innerHTML = e.renderDescription(n) : r.append(document.createTextNode(n)), a.append(r);
  }
  R(a, e, d2);
}
m(o, "renderDescription");
function R(a, e, d2) {
  const n = d2.deprecationReason;
  if (n) {
    const r = document.createElement("div");
    r.className = "info-deprecation", a.append(r);
    const i = document.createElement("span");
    i.className = "info-deprecation-label", i.append(document.createTextNode("Deprecated")), r.append(i);
    const c = document.createElement("div");
    c.className = "info-deprecation-reason", e.renderDescription ? c.innerHTML = e.renderDescription(n) : c.append(document.createTextNode(n)), r.append(c);
  }
}
m(R, "renderDeprecation");
function t(a, e, d2 = "", n = { onClick: null }, r = null) {
  if (d2) {
    const { onClick: i } = n;
    let c;
    i ? (c = document.createElement("a"), c.href = "javascript:void 0", c.addEventListener("click", (v) => {
      i(r, v);
    })) : c = document.createElement("span"), c.className = d2, c.append(document.createTextNode(e)), a.append(c);
  } else
    a.append(document.createTextNode(e));
}
m(t, "text");
//# sourceMappingURL=info.es-UZNTDYRV.js.map
