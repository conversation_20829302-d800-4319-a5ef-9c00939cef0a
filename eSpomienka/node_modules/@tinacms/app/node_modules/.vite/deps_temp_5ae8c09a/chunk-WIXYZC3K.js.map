{"version": 3, "sources": ["../../../../../@graphiql/react/dist/codemirror.es.js"], "sourcesContent": ["var c = Object.defineProperty;\nvar n = (r, o) => c(r, \"name\", { value: o, configurable: !0 });\nimport { r as f, g as p } from \"./codemirror.es2.js\";\nfunction u(r, o) {\n  for (var a = 0; a < o.length; a++) {\n    const e = o[a];\n    if (typeof e != \"string\" && !Array.isArray(e)) {\n      for (const t in e)\n        if (t !== \"default\" && !(t in r)) {\n          const i = Object.getOwnPropertyDescriptor(e, t);\n          i && Object.defineProperty(r, t, i.get ? i : {\n            enumerable: !0,\n            get: () => e[t]\n          });\n        }\n    }\n  }\n  return Object.freeze(Object.defineProperty(r, Symbol.toStringTag, { value: \"Module\" }));\n}\nn(u, \"_mergeNamespaces\");\nvar s = f();\nconst d = /* @__PURE__ */ p(s), m = /* @__PURE__ */ u({\n  __proto__: null,\n  default: d\n}, [s]);\nexport {\n  d as C,\n  m as c\n};\n//# sourceMappingURL=codemirror.es.js.map\n"], "mappings": ";;;;;;AAAA,IAAI,IAAI,OAAO;AACf,IAAI,IAAI,CAAC,GAAG,MAAM,EAAE,GAAG,QAAQ,EAAE,OAAO,GAAG,cAAc,KAAG,CAAC;AAE7D,SAAS,EAAE,GAAG,GAAG;AACf,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,UAAM,IAAI,EAAE,CAAC;AACb,QAAI,OAAO,KAAK,YAAY,CAAC,MAAM,QAAQ,CAAC,GAAG;AAC7C,iBAAW,KAAK;AACd,YAAI,MAAM,aAAa,EAAE,KAAK,IAAI;AAChC,gBAAM,IAAI,OAAO,yBAAyB,GAAG,CAAC;AAC9C,eAAK,OAAO,eAAe,GAAG,GAAG,EAAE,MAAM,IAAI;AAAA,YAC3C,YAAY;AAAA,YACZ,KAAK,MAAM,EAAE,CAAC;AAAA,UAChB,CAAC;AAAA,QACH;AAAA,IACJ;AAAA,EACF;AACA,SAAO,OAAO,OAAO,OAAO,eAAe,GAAG,OAAO,aAAa,EAAE,OAAO,SAAS,CAAC,CAAC;AACxF;AACA,EAAE,GAAG,kBAAkB;AACvB,IAAI,IAAI,GAAE;AACV,IAAM,IAAoB,GAAE,CAAC;AAA7B,IAAgC,IAAoB,EAAE;AAAA,EACpD,WAAW;AAAA,EACX,SAAS;AACX,GAAG,CAAC,CAAC,CAAC;", "names": []}