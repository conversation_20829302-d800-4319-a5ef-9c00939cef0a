import {
  __commonJS
} from "./chunk-AUZ3RYOM.js";

// browser-external:node:fs
var require_node_fs = __commonJS({
  "browser-external:node:fs"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "node:fs" has been externalized for browser compatibility. Cannot access "node:fs.${key}" in client code. See http://vitejs.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});

// browser-external:node:path
var require_node_path = __commonJS({
  "browser-external:node:path"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "node:path" has been externalized for browser compatibility. Cannot access "node:path.${key}" in client code. See http://vitejs.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});

// browser-external:node:os
var require_node_os = __commonJS({
  "browser-external:node:os"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "node:os" has been externalized for browser compatibility. Cannot access "node:os.${key}" in client code. See http://vitejs.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});

// browser-external:node:crypto
var require_node_crypto = __commonJS({
  "browser-external:node:crypto"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "node:crypto" has been externalized for browser compatibility. Cannot access "node:crypto.${key}" in client code. See http://vitejs.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});

// node_modules/@tinacms/app/node_modules/tinacms/dist/node-cache-5e8db9f0.mjs
var makeCacheDir = async (dir, fs, path, os) => {
  const pathParts = dir.split(path.sep).filter(Boolean);
  const cacheHash = pathParts[pathParts.length - 1];
  const rootUser = pathParts[0];
  let cacheDir = dir;
  if (!fs.existsSync(path.join(path.sep, rootUser))) {
    cacheDir = path.join(os.tmpdir(), cacheHash);
  }
  try {
    fs.mkdirSync(cacheDir, { recursive: true });
  } catch (error) {
    throw new Error(`Failed to create cache directory: ${error.message}`);
  }
  return cacheDir;
};
var NodeCache = async (dir) => {
  const fs = require_node_fs();
  const path = require_node_path();
  const os = require_node_os();
  const { createHash } = require_node_crypto();
  const cacheDir = await makeCacheDir(dir, fs, path, os);
  return {
    makeKey: (key) => {
      const input = key && key instanceof Object ? JSON.stringify(key) : key || "";
      return createHash("sha256").update(input).digest("hex");
    },
    get: async (key) => {
      let readValue;
      const cacheFilename = `${cacheDir}/${key}`;
      try {
        const data = await fs.promises.readFile(cacheFilename, "utf-8");
        readValue = JSON.parse(data);
      } catch (e) {
        if (e.code !== "ENOENT") {
          console.error(
            `Failed to read cache file to ${cacheFilename}: ${e.message}`
          );
        }
      }
      return readValue;
    },
    set: async (key, value) => {
      const cacheFilename = `${cacheDir}/${key}`;
      try {
        await fs.promises.writeFile(cacheFilename, JSON.stringify(value), {
          encoding: "utf-8",
          flag: "wx"
          // Don't overwrite existing caches
        });
      } catch (e) {
        if (e.code !== "EEXIST") {
          console.error(
            `Failed to write cache file to ${cacheFilename}: ${e.message}`
          );
        }
      }
    }
  };
};
export {
  NodeCache,
  makeCacheDir
};
//# sourceMappingURL=node-cache-5e8db9f0-L56A52NC.js.map
