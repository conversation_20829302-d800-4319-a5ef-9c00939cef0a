{"version": 3, "sources": ["../../../../../nullthrows/nullthrows.js", "../../../../../graphql-language-service/src/parser/RuleHelpers.ts", "../../../../../graphql-language-service/src/parser/Rules.ts", "../../../../../graphql-language-service/src/parser/onlineParser.ts", "../../../../../graphql-language-service/src/parser/CharacterStream.ts", "../../../../../graphql-language-service/src/parser/api.ts", "../../../../../graphql-language-service/src/parser/getTypeInfo.ts", "../../../../../graphql-language-service/src/parser/types.ts", "../../../../../vscode-languageserver-types/lib/esm/main.js", "../../../../../graphql-language-service/src/types.ts", "../../../../../graphql-language-service/src/interface/autocompleteUtils.ts", "../../../../../graphql-language-service/src/interface/getAutocompleteSuggestions.ts", "../../../../../graphql-language-service/src/utils/Range.ts", "../../../../../graphql-language-service/src/utils/fragmentDependencies.ts", "../../../../../graphql-language-service/src/utils/validateWithCustomRules.ts", "../../../../../graphql-language-service/src/utils/collectVariables.ts", "../../../../../graphql-language-service/src/utils/getOperationFacts.ts", "../../../../../graphql-language-service/src/interface/getDiagnostics.ts"], "sourcesContent": ["'use strict';\n\nfunction nullthrows(x, message) {\n  if (x != null) {\n    return x;\n  }\n  var error = new Error(message !== undefined ? message : 'Got unexpected ' + x);\n  error.framesToPop = 1; // Skip nullthrows's own stack frame.\n  throw error;\n}\n\nmodule.exports = nullthrows;\nmodule.exports.default = nullthrows;\n\nObject.defineProperty(module.exports, '__esModule', {value: true});\n", "/**\n *  Copyright (c) 2021 GraphQL Contributors\n *  All rights reserved.\n *\n *  This source code is licensed under the license found in the\n *  LICENSE file in the root directory of this source tree.\n *\n */\n\n// These functions help build matching rules for ParseRules.\n\nimport { Rule, Token } from './types';\n\n// An optional rule.\nexport function opt(ofRule: Rule | string): Rule {\n  return { ofRule };\n}\n\n// A list of another rule.\nexport function list(ofRule: Rule | string, separator?: string | Rule): Rule {\n  return { ofRule, isList: true, separator };\n}\n\n// A constraint described as `but not` in the GraphQL spec.\nexport function butNot(rule: Rule, exclusions: Array<Rule>) {\n  const ruleMatch = rule.match;\n  rule.match = token => {\n    let check = false;\n    if (ruleMatch) {\n      check = ruleMatch(token);\n    }\n    return (\n      check &&\n      // eslint-disable-next-line unicorn/prefer-regexp-test -- false positive exclusion is not string\n      exclusions.every(exclusion => exclusion.match && !exclusion.match(token))\n    );\n  };\n  return rule;\n}\n\n// Token of a kind\nexport function t(kind: string, style: string) {\n  return { style, match: (token: Token) => token.kind === kind };\n}\n\n// Punctuator\nexport function p(value: string, style?: string): Rule {\n  return {\n    style: style || 'punctuation',\n    match: (token: Token) =>\n      token.kind === 'Punctuation' && token.value === value,\n  };\n}\n", "/**\n *  Copyright (c) 2021 GraphQL Contributors\n *  All rights reserved.\n *\n *  This source code is licensed under the license found in the\n *  LICENSE file in the root directory of this source tree.\n *\n */\n\nimport { State, Token, Rule, RuleKind, ParseRule } from './types';\nimport CharacterStream from './CharacterStream';\nimport { opt, list, butNot, t, p } from './RuleHelpers';\nimport { Kind } from 'graphql';\n\n/**\n * Whitespace tokens defined in GraphQL spec.\n */\nexport const isIgnored = (ch: string) =>\n  ch === ' ' ||\n  ch === '\\t' ||\n  ch === ',' ||\n  ch === '\\n' ||\n  ch === '\\r' ||\n  ch === '\\uFEFF' ||\n  ch === '\\u00A0';\n\n/**\n * The lexer rules. These are exactly as described by the spec.\n */\nexport const LexRules = {\n  // The Name token.\n  Name: /^[_A-Za-z][_0-9A-Za-z]*/,\n\n  // All Punctuation used in GraphQL\n  Punctuation: /^(?:!|\\$|\\(|\\)|\\.\\.\\.|:|=|&|@|\\[|]|\\{|\\||\\})/,\n\n  // Combines the IntValue and FloatValue tokens.\n  Number: /^-?(?:0|(?:[1-9][0-9]*))(?:\\.[0-9]*)?(?:[eE][+-]?[0-9]+)?/,\n\n  // Note the closing quote is made optional as an IDE experience improvement.\n  String:\n    /^(?:\"\"\"(?:\\\\\"\"\"|[^\"]|\"[^\"]|\"\"[^\"])*(?:\"\"\")?|\"(?:[^\"\\\\]|\\\\(?:\"|\\/|\\\\|b|f|n|r|t|u[0-9a-fA-F]{4}))*\"?)/,\n\n  // Comments consume entire lines.\n  Comment: /^#.*/,\n};\n\n/**\n * The parser rules. These are very close to, but not exactly the same as the\n * spec. Minor deviations allow for a simpler implementation. The resulting\n * parser can parse everything the spec declares possible.\n */\nexport const ParseRules: { [name: string]: ParseRule } = {\n  Document: [list('Definition')],\n  Definition(token: Token): RuleKind | void {\n    switch (token.value) {\n      case '{':\n        return 'ShortQuery';\n      case 'query':\n        return 'Query';\n      case 'mutation':\n        return 'Mutation';\n      case 'subscription':\n        return 'Subscription';\n      case 'fragment':\n        return Kind.FRAGMENT_DEFINITION;\n      case 'schema':\n        return 'SchemaDef';\n      case 'scalar':\n        return 'ScalarDef';\n      case 'type':\n        return 'ObjectTypeDef';\n      case 'interface':\n        return 'InterfaceDef';\n      case 'union':\n        return 'UnionDef';\n      case 'enum':\n        return 'EnumDef';\n      case 'input':\n        return 'InputDef';\n      case 'extend':\n        return 'ExtendDef';\n      case 'directive':\n        return 'DirectiveDef';\n    }\n  },\n  // Note: instead of \"Operation\", these rules have been separated out.\n  ShortQuery: ['SelectionSet'],\n  Query: [\n    word('query'),\n    opt(name('def')),\n    opt('VariableDefinitions'),\n    list('Directive'),\n    'SelectionSet',\n  ],\n\n  Mutation: [\n    word('mutation'),\n    opt(name('def')),\n    opt('VariableDefinitions'),\n    list('Directive'),\n    'SelectionSet',\n  ],\n\n  Subscription: [\n    word('subscription'),\n    opt(name('def')),\n    opt('VariableDefinitions'),\n    list('Directive'),\n    'SelectionSet',\n  ],\n\n  VariableDefinitions: [p('('), list('VariableDefinition'), p(')')],\n  VariableDefinition: ['Variable', p(':'), 'Type', opt('DefaultValue')],\n  Variable: [p('$', 'variable'), name('variable')],\n  DefaultValue: [p('='), 'Value'],\n  SelectionSet: [p('{'), list('Selection'), p('}')],\n  Selection(token: Token, stream: CharacterStream) {\n    return token.value === '...'\n      ? stream.match(/[\\s\\u00a0,]*(on\\b|@|{)/, false)\n        ? 'InlineFragment'\n        : 'FragmentSpread'\n      : stream.match(/[\\s\\u00a0,]*:/, false)\n        ? 'AliasedField'\n        : 'Field';\n  },\n  // Note: this minor deviation of \"AliasedField\" simplifies the lookahead.\n  AliasedField: [\n    name('property'),\n    p(':'),\n    name('qualifier'),\n    opt('Arguments'),\n    list('Directive'),\n    opt('SelectionSet'),\n  ],\n\n  Field: [\n    name('property'),\n    opt('Arguments'),\n    list('Directive'),\n    opt('SelectionSet'),\n  ],\n\n  Arguments: [p('('), list('Argument'), p(')')],\n  Argument: [name('attribute'), p(':'), 'Value'],\n  FragmentSpread: [p('...'), name('def'), list('Directive')],\n  InlineFragment: [\n    p('...'),\n    opt('TypeCondition'),\n    list('Directive'),\n    'SelectionSet',\n  ],\n\n  FragmentDefinition: [\n    word('fragment'),\n    opt(butNot(name('def'), [word('on')])),\n    'TypeCondition',\n    list('Directive'),\n    'SelectionSet',\n  ],\n\n  TypeCondition: [word('on'), 'NamedType'],\n  // Variables could be parsed in cases where only Const is expected by spec.\n  Value(token: Token) {\n    switch (token.kind) {\n      case 'Number':\n        return 'NumberValue';\n      case 'String':\n        return 'StringValue';\n      case 'Punctuation':\n        switch (token.value) {\n          case '[':\n            return 'ListValue';\n          case '{':\n            return 'ObjectValue';\n          case '$':\n            return 'Variable';\n          case '&':\n            return 'NamedType';\n        }\n\n        return null;\n      case 'Name':\n        switch (token.value) {\n          case 'true':\n          case 'false':\n            return 'BooleanValue';\n        }\n\n        if (token.value === 'null') {\n          return 'NullValue';\n        }\n        return 'EnumValue';\n    }\n  },\n  NumberValue: [t('Number', 'number')],\n  StringValue: [\n    {\n      style: 'string',\n      match: (token: Token) => token.kind === 'String',\n      update(state: State, token: Token) {\n        if (token.value.startsWith('\"\"\"')) {\n          state.inBlockstring = !token.value.slice(3).endsWith('\"\"\"');\n        }\n      },\n    },\n  ],\n  BooleanValue: [t('Name', 'builtin')],\n  NullValue: [t('Name', 'keyword')],\n  EnumValue: [name('string-2')],\n  ListValue: [p('['), list('Value'), p(']')],\n  ObjectValue: [p('{'), list('ObjectField'), p('}')],\n  ObjectField: [name('attribute'), p(':'), 'Value'],\n  Type(token: Token) {\n    return token.value === '[' ? 'ListType' : 'NonNullType';\n  },\n  // NonNullType has been merged into ListType to simplify.\n  ListType: [p('['), 'Type', p(']'), opt(p('!'))],\n  NonNullType: ['NamedType', opt(p('!'))],\n  NamedType: [type('atom')],\n  Directive: [p('@', 'meta'), name('meta'), opt('Arguments')],\n  DirectiveDef: [\n    word('directive'),\n    p('@', 'meta'),\n    name('meta'),\n    opt('ArgumentsDef'),\n    word('on'),\n    list('DirectiveLocation', p('|')),\n  ],\n  InterfaceDef: [\n    word('interface'),\n    name('atom'),\n    opt('Implements'),\n    list('Directive'),\n    p('{'),\n    list('FieldDef'),\n    p('}'),\n  ],\n  Implements: [word('implements'), list('NamedType', p('&'))],\n  DirectiveLocation: [name('string-2')],\n  // GraphQL schema language\n  SchemaDef: [\n    word('schema'),\n    list('Directive'),\n    p('{'),\n    list('OperationTypeDef'),\n    p('}'),\n  ],\n\n  OperationTypeDef: [name('keyword'), p(':'), name('atom')],\n  ScalarDef: [word('scalar'), name('atom'), list('Directive')],\n  ObjectTypeDef: [\n    word('type'),\n    name('atom'),\n    opt('Implements'),\n    list('Directive'),\n    p('{'),\n    list('FieldDef'),\n    p('}'),\n  ],\n\n  FieldDef: [\n    name('property'),\n    opt('ArgumentsDef'),\n    p(':'),\n    'Type',\n    list('Directive'),\n  ],\n\n  ArgumentsDef: [p('('), list('InputValueDef'), p(')')],\n  InputValueDef: [\n    name('attribute'),\n    p(':'),\n    'Type',\n    opt('DefaultValue'),\n    list('Directive'),\n  ],\n\n  UnionDef: [\n    word('union'),\n    name('atom'),\n    list('Directive'),\n    p('='),\n    list('UnionMember', p('|')),\n  ],\n\n  UnionMember: ['NamedType'],\n  EnumDef: [\n    word('enum'),\n    name('atom'),\n    list('Directive'),\n    p('{'),\n    list('EnumValueDef'),\n    p('}'),\n  ],\n\n  EnumValueDef: [name('string-2'), list('Directive')],\n  InputDef: [\n    word('input'),\n    name('atom'),\n    list('Directive'),\n    p('{'),\n    list('InputValueDef'),\n    p('}'),\n  ],\n  ExtendDef: [word('extend'), 'ExtensionDefinition'],\n  ExtensionDefinition(token: Token): RuleKind | void {\n    switch (token.value) {\n      case 'schema':\n        return Kind.SCHEMA_EXTENSION;\n      case 'scalar':\n        return Kind.SCALAR_TYPE_EXTENSION;\n      case 'type':\n        return Kind.OBJECT_TYPE_EXTENSION;\n      case 'interface':\n        return Kind.INTERFACE_TYPE_EXTENSION;\n      case 'union':\n        return Kind.UNION_TYPE_EXTENSION;\n      case 'enum':\n        return Kind.ENUM_TYPE_EXTENSION;\n      case 'input':\n        return Kind.INPUT_OBJECT_TYPE_EXTENSION;\n    }\n  },\n  [Kind.SCHEMA_EXTENSION]: ['SchemaDef'],\n  [Kind.SCALAR_TYPE_EXTENSION]: ['ScalarDef'],\n  [Kind.OBJECT_TYPE_EXTENSION]: ['ObjectTypeDef'],\n  [Kind.INTERFACE_TYPE_EXTENSION]: ['InterfaceDef'],\n  [Kind.UNION_TYPE_EXTENSION]: ['UnionDef'],\n  [Kind.ENUM_TYPE_EXTENSION]: ['EnumDef'],\n  [Kind.INPUT_OBJECT_TYPE_EXTENSION]: ['InputDef'],\n};\n\n// A keyword Token.\nfunction word(value: string) {\n  return {\n    style: 'keyword',\n    match: (token: Token) => token.kind === 'Name' && token.value === value,\n  };\n}\n\n// A Name Token which will decorate the state with a `name`.\nfunction name(style: string): Rule {\n  return {\n    style,\n    match: (token: Token) => token.kind === 'Name',\n    update(state: State, token: Token) {\n      state.name = token.value;\n    },\n  };\n}\n\n// A Name Token which will decorate the previous state with a `type`.\nfunction type(style: string) {\n  return {\n    style,\n    match: (token: Token) => token.kind === 'Name',\n    update(state: State, token: Token) {\n      if (state.prevState?.prevState) {\n        state.name = token.value;\n        state.prevState.prevState.type = token.value;\n      }\n    },\n  };\n}\n", "/**\n *  Copyright (c) 2021 GraphQL Contributors\n *  All rights reserved.\n *\n *  This source code is licensed under the license found in the\n *  LICENSE file in the root directory of this source tree.\n *\n */\n\n/**\n * Builds an online immutable parser, designed to be used as part of a syntax\n * highlighting and code intelligence tools.\n *\n * Options:\n *\n *     eatWhitespace: (\n *       stream: Stream | CodeMirror.StringStream | CharacterStream\n *     ) => boolean\n *       Use CodeMirror API.\n *\n *     LexRules: { [name: string]: RegExp }, Includes `Punctuation`, `Comment`.\n *\n *     ParseRules: { [name: string]: Array<Rule> }, Includes `Document`.\n *\n *     editorConfig: { [name: string]: any }, Provides an editor-specific\n *       configurations set.\n *\n */\n\nimport CharacterStream from './CharacterStream';\nimport { State, Token, Rule, RuleKind } from './types';\n\nimport { LexRules, ParseRules, isIgnored } from './Rules';\nimport { Kind } from 'graphql';\n\nexport type ParserOptions = {\n  eatWhitespace: (stream: CharacterStream) => boolean;\n  lexRules: Partial<typeof LexRules>;\n  parseRules: typeof ParseRules;\n  editorConfig: { [name: string]: any };\n};\n\nexport default function onlineParser(\n  options: ParserOptions = {\n    eatWhitespace: stream => stream.eatWhile(isIgnored),\n    lexRules: LexRules,\n    parseRules: ParseRules,\n    editorConfig: {},\n  },\n): {\n  startState: () => State;\n  token: (stream: CharacterStream, state: State) => string;\n} {\n  return {\n    startState() {\n      const initialState = {\n        level: 0,\n        step: 0,\n        name: null,\n        kind: null,\n        type: null,\n        rule: null,\n        needsSeparator: false,\n        prevState: null,\n      };\n\n      pushRule(options.parseRules, initialState, Kind.DOCUMENT);\n      return initialState;\n    },\n    token(stream: CharacterStream, state: State) {\n      return getToken(stream, state, options);\n    },\n  };\n}\n\nfunction getToken(\n  stream: CharacterStream,\n  state: State,\n  options: ParserOptions,\n): string {\n  if (state.inBlockstring) {\n    // eslint-disable-next-line unicorn/prefer-regexp-test -- false positive stream is not string\n    if (stream.match(/.*\"\"\"/)) {\n      state.inBlockstring = false;\n      return 'string';\n    }\n    stream.skipToEnd();\n    return 'string';\n  }\n\n  const { lexRules, parseRules, eatWhitespace, editorConfig } = options;\n  // Restore state after an empty-rule.\n  if (state.rule && state.rule.length === 0) {\n    popRule(state);\n  } else if (state.needsAdvance) {\n    state.needsAdvance = false;\n    advanceRule(state, true);\n  }\n\n  // Remember initial indentation\n  if (stream.sol()) {\n    const tabSize = editorConfig?.tabSize || 2;\n    state.indentLevel = Math.floor(stream.indentation() / tabSize);\n  }\n\n  // Consume spaces and ignored characters\n  if (eatWhitespace(stream)) {\n    return 'ws';\n  }\n\n  // Get a matched token from the stream, using lex\n  const token = lex(lexRules, stream);\n\n  // If there's no matching token, skip ahead.\n  if (!token) {\n    const matchedSomething = stream.match(/\\S+/);\n    if (!matchedSomething) {\n      // We need to eat at least one character, and we couldn't match any\n      // non-whitespace, so it must be exotic whitespace.\n      stream.match(/\\s/);\n    }\n    pushRule(SpecialParseRules, state, 'Invalid');\n    return 'invalidchar';\n  }\n\n  // If the next token is a Comment, insert a Comment parsing rule.\n  if (token.kind === 'Comment') {\n    pushRule(SpecialParseRules, state, 'Comment');\n    return 'comment';\n  }\n\n  // Save state before continuing.\n  const backupState = assign({}, state);\n\n  // Handle changes in expected indentation level\n  if (token.kind === 'Punctuation') {\n    if (/^[{([]/.test(token.value)) {\n      if (state.indentLevel !== undefined) {\n        // Push on the stack of levels one level deeper than the current level.\n        state.levels = (state.levels || []).concat(state.indentLevel + 1);\n      }\n    } else if (/^[})\\]]/.test(token.value)) {\n      // Pop from the stack of levels.\n      // If the top of the stack is lower than the current level, lower the\n      // current level to match.\n      const levels = (state.levels = (state.levels || []).slice(0, -1));\n      // FIXME\n      // what if state.indentLevel === 0?\n      if (\n        state.indentLevel &&\n        levels.length > 0 &&\n        levels.at(-1)! < state.indentLevel\n      ) {\n        state.indentLevel = levels.at(-1);\n      }\n    }\n  }\n\n  while (state.rule) {\n    // If this is a forking rule, determine what rule to use based on\n    // the current token, otherwise expect based on the current step.\n    let expected: any =\n      typeof state.rule === 'function'\n        ? state.step === 0\n          ? state.rule(token, stream)\n          : null\n        : state.rule[state.step];\n\n    // Separator between list elements if necessary.\n    if (state.needsSeparator) {\n      expected = expected?.separator;\n    }\n\n    if (expected) {\n      // Un-wrap optional/list parseRules.\n      if (expected.ofRule) {\n        expected = expected.ofRule;\n      }\n\n      // A string represents a Rule\n      if (typeof expected === 'string') {\n        pushRule(parseRules, state, expected as RuleKind);\n        continue;\n      }\n\n      // Otherwise, match a Terminal.\n      if (expected.match?.(token)) {\n        if (expected.update) {\n          expected.update(state, token);\n        }\n\n        // If this token was a punctuator, advance the parse rule, otherwise\n        // mark the state to be advanced before the next token. This ensures\n        // that tokens which can be appended to keep the appropriate state.\n        if (token.kind === 'Punctuation') {\n          advanceRule(state, true);\n        } else {\n          state.needsAdvance = true;\n        }\n\n        return expected.style;\n      }\n    }\n    unsuccessful(state);\n  }\n\n  // The parser does not know how to interpret this token, do not affect state.\n  assign(state, backupState);\n  pushRule(SpecialParseRules, state, 'Invalid');\n  return 'invalidchar';\n}\n\n// Utility function to assign from object to another object.\nfunction assign(to: object, from: object): object {\n  const keys = Object.keys(from);\n  for (let i = 0; i < keys.length; i++) {\n    // @ts-ignore\n    // TODO: ParseRules as numerical index\n    to[keys[i]] = from[keys[i]];\n  }\n  return to;\n}\n\n// A special rule set for parsing comment tokens.\nconst SpecialParseRules = {\n  Invalid: [],\n  Comment: [],\n};\n\n// Push a new rule onto the state.\nfunction pushRule(\n  rules: typeof ParseRules,\n  state: State,\n  ruleKind: RuleKind,\n): void {\n  if (!rules[ruleKind]) {\n    throw new TypeError('Unknown rule: ' + ruleKind);\n  }\n  state.prevState = { ...state };\n  state.kind = ruleKind;\n  state.name = null;\n  state.type = null;\n  state.rule = rules[ruleKind];\n  state.step = 0;\n  state.needsSeparator = false;\n}\n\n// Pop the current rule from the state.\nfunction popRule(state: State): undefined {\n  // Check if there's anything to pop\n  if (!state.prevState) {\n    return;\n  }\n  state.kind = state.prevState.kind;\n  state.name = state.prevState.name;\n  state.type = state.prevState.type;\n  state.rule = state.prevState.rule;\n  state.step = state.prevState.step;\n  state.needsSeparator = state.prevState.needsSeparator;\n  state.prevState = state.prevState.prevState;\n}\n\n// Advance the step of the current rule.\nfunction advanceRule(state: State, successful: boolean): undefined {\n  // If this is advancing successfully and the current state is a list, give\n  // it an opportunity to repeat itself.\n  if (isList(state) && state.rule) {\n    // @ts-ignore\n    // TODO: ParseRules as numerical index\n    const step = state.rule[state.step];\n    if (step.separator) {\n      const { separator } = step;\n      state.needsSeparator = !state.needsSeparator;\n      // If the separator was optional, then give it an opportunity to repeat.\n      if (!state.needsSeparator && separator.ofRule) {\n        return;\n      }\n    }\n    // If this was a successful list parse, then allow it to repeat itself.\n    if (successful) {\n      return;\n    }\n  }\n\n  // Advance the step in the rule. If the rule is completed, pop\n  // the rule and advance the parent rule as well (recursively).\n  state.needsSeparator = false;\n  state.step++;\n\n  // While the current rule is completed.\n  while (\n    state.rule &&\n    !(Array.isArray(state.rule) && state.step < state.rule.length)\n  ) {\n    popRule(state);\n\n    if (state.rule) {\n      // Do not advance a List step so it has the opportunity to repeat itself.\n      if (isList(state)) {\n        // @ts-ignore\n        // TODO: ParseRules as numerical index\n        if (state.rule?.[state.step].separator) {\n          state.needsSeparator = !state.needsSeparator;\n        }\n      } else {\n        state.needsSeparator = false;\n        state.step++;\n      }\n    }\n  }\n}\n\nfunction isList(state: State): boolean | null | undefined {\n  const step =\n    Array.isArray(state.rule) &&\n    typeof state.rule[state.step] !== 'string' &&\n    (state.rule[state.step] as Rule);\n  return step && step.isList;\n}\n\n// Unwind the state after an unsuccessful match.\nfunction unsuccessful(state: State): void {\n  // Fall back to the parent rule until you get to an optional or list rule or\n  // until the entire stack of rules is empty.\n  while (\n    state.rule &&\n    // TODO: not sure how to fix this in a performant way\n    // @ts-ignore\n    !(Array.isArray(state.rule) && state.rule[state.step].ofRule)\n  ) {\n    popRule(state);\n  }\n\n  // If there is still a rule, it must be an optional or list rule.\n  // Consider this rule a success so that we may move past it.\n  if (state.rule) {\n    advanceRule(state, false);\n  }\n}\n\n// Given a stream, returns a { kind, value } pair, or null.\nfunction lex(\n  lexRules: Partial<typeof LexRules>,\n  stream: CharacterStream,\n): Token | null | undefined {\n  const kinds = Object.keys(lexRules);\n  for (let i = 0; i < kinds.length; i++) {\n    // @ts-ignore\n    // TODO: ParseRules as numerical index\n    const match = stream.match(lexRules[kinds[i]]);\n    if (match && match instanceof Array) {\n      return { kind: kinds[i], value: match[0] };\n    }\n  }\n}\n", "/**\n *  Copyright (c) 2021 GraphQL Contributors\n *  All rights reserved.\n *\n *  This source code is licensed under the license found in the\n *  LICENSE file in the root directory of this source tree.\n *\n */\n\n/**\n * CharacterStream implements a stream of character tokens given a source text.\n * The API design follows that of CodeMirror.StringStream.\n *\n * Required:\n *\n *      sourceText: (string), A raw GraphQL source text. Works best if a line\n *        is supplied.\n *\n */\nimport { TokenPattern, CharacterStreamInterface } from './types';\n\nexport default class CharacterStream implements CharacterStreamInterface {\n  private _start = 0;\n  private _pos = 0;\n  private _sourceText: string;\n\n  constructor(sourceText: string) {\n    this._sourceText = sourceText;\n  }\n\n  public getStartOfToken = (): number => this._start;\n\n  public getCurrentPosition = (): number => this._pos;\n\n  private _testNextCharacter(pattern: TokenPattern): boolean {\n    const character = this._sourceText.charAt(this._pos);\n    let isMatched = false;\n    if (typeof pattern === 'string') {\n      isMatched = character === pattern;\n    } else {\n      isMatched =\n        pattern instanceof RegExp\n          ? pattern.test(character)\n          : pattern(character);\n    }\n    return isMatched;\n  }\n\n  public eol = (): boolean => this._sourceText.length === this._pos;\n\n  public sol = (): boolean => this._pos === 0;\n\n  public peek = (): string | null => {\n    return this._sourceText.charAt(this._pos) || null;\n  };\n\n  public next = (): string => {\n    const char = this._sourceText.charAt(this._pos);\n    this._pos++;\n    return char;\n  };\n\n  public eat = (pattern: TokenPattern): string | undefined => {\n    const isMatched = this._testNextCharacter(pattern);\n    if (isMatched) {\n      this._start = this._pos;\n      this._pos++;\n      return this._sourceText.charAt(this._pos - 1);\n    }\n    return undefined;\n  };\n\n  public eatWhile = (match: TokenPattern): boolean => {\n    let isMatched = this._testNextCharacter(match);\n    let didEat = false;\n\n    // If a match, treat the total upcoming matches as one token\n    if (isMatched) {\n      didEat = isMatched;\n      this._start = this._pos;\n    }\n\n    while (isMatched) {\n      this._pos++;\n      isMatched = this._testNextCharacter(match);\n      didEat = true;\n    }\n\n    return didEat;\n  };\n\n  public eatSpace = (): boolean => this.eatWhile(/[\\s\\u00a0]/);\n\n  public skipToEnd = (): void => {\n    this._pos = this._sourceText.length;\n  };\n\n  public skipTo = (position: number): void => {\n    this._pos = position;\n  };\n\n  public match = (\n    pattern: TokenPattern,\n    consume: boolean | null | undefined = true,\n    caseFold: boolean | null | undefined = false,\n  ): Array<string> | boolean => {\n    let token = null;\n    let match = null;\n\n    if (typeof pattern === 'string') {\n      const regex = new RegExp(pattern, caseFold ? 'i' : 'g');\n      match = regex.test(\n        this._sourceText.slice(this._pos, this._pos + pattern.length),\n      );\n      token = pattern;\n    } else if (pattern instanceof RegExp) {\n      match = this._sourceText.slice(this._pos).match(pattern);\n      token = match?.[0];\n    }\n\n    if (\n      match != null &&\n      (typeof pattern === 'string' ||\n        (match instanceof Array &&\n          // String.match returns 'index' property, which flow fails to detect\n          // for some reason. The below is a workaround, but an easier solution\n          // is just checking if `match.index === 0`\n          this._sourceText.startsWith(match[0], this._pos)))\n    ) {\n      if (consume) {\n        this._start = this._pos;\n        // eslint-disable-next-line @typescript-eslint/prefer-optional-chain -- otherwise has type issue\n        if (token && token.length) {\n          this._pos += token.length;\n        }\n      }\n      return match;\n    }\n\n    // No match available.\n    return false;\n  };\n\n  public backUp = (num: number): void => {\n    this._pos -= num;\n  };\n\n  public column = (): number => this._pos;\n\n  public indentation = (): number => {\n    const match = this._sourceText.match(/\\s*/);\n    let indent = 0;\n    if (match && match.length !== 0) {\n      const whiteSpaces = match[0];\n      let pos = 0;\n      while (whiteSpaces.length > pos) {\n        if (whiteSpaces.charCodeAt(pos) === 9) {\n          indent += 2;\n        } else {\n          indent++;\n        }\n        pos++;\n      }\n    }\n\n    return indent;\n  };\n\n  public current = (): string => this._sourceText.slice(this._start, this._pos);\n}\n", "/**\n *  Copyright (c) 2021 GraphQL Contributors\n *  All rights reserved.\n *\n *  This source code is licensed under the license found in the\n *  LICENSE file in the root directory of this source tree.\n *\n */\n\nimport { IPosition } from '..';\nimport {\n  CharacterStream,\n  onlineParser,\n  ContextToken,\n  State,\n  getTypeInfo,\n} from '.';\nimport { BREAK, GraphQLSchema, Kind, parse, visit } from 'graphql';\n\nexport type ParserCallbackFn = (\n  stream: CharacterStream,\n  state: State,\n  style: string,\n  index: number,\n) => void | 'BREAK';\n\n/**\n * Provides an utility function to parse a given query text and construct a\n * `token` context object.\n * A token context provides useful information about the token/style that\n * CharacterStream currently possesses, as well as the end state and style\n * of the token.\n */\nexport function runOnlineParser(\n  queryText: string,\n  callback: ParserCallbackFn,\n): ContextToken {\n  const lines = queryText.split('\\n');\n  const parser = onlineParser();\n  let state = parser.startState();\n  let style = '';\n\n  let stream: CharacterStream = new CharacterStream('');\n\n  for (let i = 0; i < lines.length; i++) {\n    stream = new CharacterStream(lines[i]);\n    while (!stream.eol()) {\n      style = parser.token(stream, state);\n      const code = callback(stream, state, style, i);\n      if (code === 'BREAK') {\n        break;\n      }\n    }\n\n    // Above while loop won't run if there is an empty line.\n    // Run the callback one more time to catch this.\n    callback(stream, state, style, i);\n\n    if (!state.kind) {\n      state = parser.startState();\n    }\n  }\n\n  return {\n    start: stream.getStartOfToken(),\n    end: stream.getCurrentPosition(),\n    string: stream.current(),\n    state,\n    style,\n  };\n}\n\nexport enum GraphQLDocumentMode {\n  TYPE_SYSTEM = 'TYPE_SYSTEM',\n  EXECUTABLE = 'EXECUTABLE',\n  UNKNOWN = 'UNKNOWN',\n}\n\nexport const TYPE_SYSTEM_KINDS: Kind[] = [\n  // TypeSystemDefinition\n  Kind.SCHEMA_DEFINITION,\n  Kind.OPERATION_TYPE_DEFINITION,\n  Kind.SCALAR_TYPE_DEFINITION,\n  Kind.OBJECT_TYPE_DEFINITION,\n  Kind.INTERFACE_TYPE_DEFINITION,\n  Kind.UNION_TYPE_DEFINITION,\n  Kind.ENUM_TYPE_DEFINITION,\n  Kind.INPUT_OBJECT_TYPE_DEFINITION,\n  Kind.DIRECTIVE_DEFINITION,\n  // TypeSystemExtension\n  Kind.SCHEMA_EXTENSION,\n  Kind.SCALAR_TYPE_EXTENSION,\n  Kind.OBJECT_TYPE_EXTENSION,\n  Kind.INTERFACE_TYPE_EXTENSION,\n  Kind.UNION_TYPE_EXTENSION,\n  Kind.ENUM_TYPE_EXTENSION,\n  Kind.INPUT_OBJECT_TYPE_EXTENSION,\n];\n\nconst getParsedMode = (sdl: string | undefined): GraphQLDocumentMode => {\n  let mode = GraphQLDocumentMode.UNKNOWN;\n  if (sdl) {\n    try {\n      visit(parse(sdl), {\n        enter(node) {\n          if (node.kind === 'Document') {\n            mode = GraphQLDocumentMode.EXECUTABLE;\n            return;\n          }\n          if (TYPE_SYSTEM_KINDS.includes(node.kind)) {\n            mode = GraphQLDocumentMode.TYPE_SYSTEM;\n            return BREAK;\n          }\n          return false;\n        },\n      });\n    } catch {\n      return mode;\n    }\n  }\n  return mode;\n};\n\nexport function getDocumentMode(\n  documentText: string,\n  uri?: string,\n): GraphQLDocumentMode {\n  if (uri?.endsWith('.graphqls')) {\n    return GraphQLDocumentMode.TYPE_SYSTEM;\n  }\n  return getParsedMode(documentText);\n}\n\n/**\n * Given a query text and a cursor position, return the context token\n */\nexport function getTokenAtPosition(\n  queryText: string,\n  cursor: IPosition,\n  offset = 0,\n): ContextToken {\n  let styleAtCursor = null;\n  let stateAtCursor = null;\n  let stringAtCursor = null;\n  const token = runOnlineParser(queryText, (stream, state, style, index) => {\n    if (\n      index !== cursor.line ||\n      stream.getCurrentPosition() + offset < cursor.character + 1\n    ) {\n      return;\n    }\n    styleAtCursor = style;\n    stateAtCursor = { ...state };\n    stringAtCursor = stream.current();\n    return 'BREAK';\n  });\n\n  // Return the state/style of parsed token in case those at cursor aren't\n  // available.\n  return {\n    start: token.start,\n    end: token.end,\n    string: stringAtCursor || token.string,\n    state: stateAtCursor || token.state,\n    style: styleAtCursor || token.style,\n  };\n}\n\n/**\n * Returns the token, state, typeInfo and mode at the cursor position\n * Used by getAutocompleteSuggestions\n */\nexport function getContextAtPosition(\n  queryText: string,\n  cursor: IPosition,\n  schema: GraphQLSchema,\n  contextToken?: ContextToken,\n  options?: { mode?: GraphQLDocumentMode; uri?: string },\n): {\n  token: ContextToken;\n  state: State;\n  typeInfo: ReturnType<typeof getTypeInfo>;\n  mode: GraphQLDocumentMode;\n} | null {\n  const token: ContextToken =\n    contextToken || getTokenAtPosition(queryText, cursor, 1);\n  if (!token) {\n    return null;\n  }\n\n  const state =\n    token.state.kind === 'Invalid' ? token.state.prevState : token.state;\n  if (!state) {\n    return null;\n  }\n\n  // relieve flow errors by checking if `state` exists\n\n  const typeInfo = getTypeInfo(schema, token.state);\n  const mode = options?.mode || getDocumentMode(queryText, options?.uri);\n  return {\n    token,\n    state,\n    typeInfo,\n    mode,\n  };\n}\n", "/**\n *  Copyright (c) 2021 GraphQL Contributors\n *  All rights reserved.\n *\n *  This source code is licensed under the license found in the\n *  LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {\n  GraphQLSchema,\n  GraphQLEnumValue,\n  GraphQLField,\n  GraphQLInterfaceType,\n  GraphQLObjectType,\n  GraphQLArgument,\n  GraphQLEnumType,\n  GraphQLInputObjectType,\n  GraphQLList,\n  getNamedType,\n  getNullableType,\n  SchemaMetaFieldDef,\n  GraphQLType,\n  TypeMetaFieldDef,\n  TypeNameMetaFieldDef,\n  isCompositeType,\n} from 'graphql';\n\nimport { AllTypeInfo } from '../types';\n\nimport { State, RuleKinds } from '.';\n\n// Gets the field definition given a type and field name\nexport function getFieldDef(\n  schema: GraphQLSchema,\n  type: GraphQLType,\n  fieldName: string,\n): GraphQLField<any, any> | null | undefined {\n  if (fieldName === SchemaMetaFieldDef.name && schema.getQueryType() === type) {\n    return SchemaMetaFieldDef;\n  }\n  if (fieldName === TypeMetaFieldDef.name && schema.getQueryType() === type) {\n    return TypeMetaFieldDef;\n  }\n  if (fieldName === TypeNameMetaFieldDef.name && isCompositeType(type)) {\n    return TypeNameMetaFieldDef;\n  }\n  if ('getFields' in type) {\n    return type.getFields()[fieldName] as any;\n  }\n\n  return null;\n}\n\n// Utility for iterating through a CodeMirror parse state stack bottom-up.\nexport function forEachState(\n  stack: State,\n  fn: (state: State) => AllTypeInfo | null | void,\n): void {\n  const reverseStateStack = [];\n  let state: State | null | undefined = stack;\n  while (state?.kind) {\n    reverseStateStack.push(state);\n    state = state.prevState;\n  }\n  for (let i = reverseStateStack.length - 1; i >= 0; i--) {\n    fn(reverseStateStack[i]);\n  }\n}\n\n// Utility for returning the state representing the Definition this token state\n// is within, if any.\nexport function getDefinitionState(\n  tokenState: State,\n): State | null | undefined {\n  let definitionState;\n\n  // TODO - couldn't figure this one out\n  forEachState(tokenState, (state: State): void => {\n    switch (state.kind) {\n      case 'Query':\n      case 'ShortQuery':\n      case 'Mutation':\n      case 'Subscription':\n      case 'FragmentDefinition':\n        definitionState = state;\n        break;\n    }\n  });\n\n  return definitionState;\n}\n\n// Utility for collecting rich type information given any token's state\n// from the graphql-mode parser.\nexport function getTypeInfo(\n  schema: GraphQLSchema,\n  tokenState: State,\n): AllTypeInfo {\n  let argDef: AllTypeInfo['argDef'];\n  let argDefs: AllTypeInfo['argDefs'];\n  let directiveDef: AllTypeInfo['directiveDef'];\n  let enumValue: AllTypeInfo['enumValue'];\n  let fieldDef: AllTypeInfo['fieldDef'];\n  let inputType: AllTypeInfo['inputType'];\n  let objectTypeDef: AllTypeInfo['objectTypeDef'];\n  let objectFieldDefs: AllTypeInfo['objectFieldDefs'];\n  let parentType: AllTypeInfo['parentType'];\n  let type: AllTypeInfo['type'];\n  let interfaceDef: AllTypeInfo['interfaceDef'];\n  forEachState(tokenState, state => {\n    switch (state.kind) {\n      case RuleKinds.QUERY:\n      case 'ShortQuery':\n        type = schema.getQueryType();\n        break;\n      case RuleKinds.MUTATION:\n        type = schema.getMutationType();\n        break;\n      case RuleKinds.SUBSCRIPTION:\n        type = schema.getSubscriptionType();\n        break;\n      case RuleKinds.INLINE_FRAGMENT:\n      case RuleKinds.FRAGMENT_DEFINITION:\n        if (state.type) {\n          type = schema.getType(state.type);\n        }\n        break;\n      case RuleKinds.FIELD:\n      case RuleKinds.ALIASED_FIELD: {\n        if (!type || !state.name) {\n          fieldDef = null;\n        } else {\n          fieldDef = parentType\n            ? getFieldDef(schema, parentType, state.name)\n            : null;\n          type = fieldDef ? fieldDef.type : null;\n        }\n        break;\n      }\n      case RuleKinds.SELECTION_SET:\n        parentType = getNamedType(type!);\n        break;\n      case RuleKinds.DIRECTIVE:\n        directiveDef = state.name ? schema.getDirective(state.name) : null;\n        break;\n\n      case RuleKinds.INTERFACE_DEF:\n        if (state.name) {\n          objectTypeDef = null;\n          interfaceDef = new GraphQLInterfaceType({\n            name: state.name,\n            interfaces: [],\n            fields: {},\n          });\n        }\n\n        break;\n\n      case RuleKinds.OBJECT_TYPE_DEF:\n        if (state.name) {\n          interfaceDef = null;\n          objectTypeDef = new GraphQLObjectType({\n            name: state.name,\n            interfaces: [],\n            fields: {},\n          });\n        }\n\n        break;\n      case RuleKinds.ARGUMENTS: {\n        if (state.prevState) {\n          switch (state.prevState.kind) {\n            case RuleKinds.FIELD:\n              argDefs = fieldDef && (fieldDef.args as GraphQLArgument[]);\n              break;\n            case RuleKinds.DIRECTIVE:\n              argDefs =\n                directiveDef && (directiveDef.args as GraphQLArgument[]);\n              break;\n            // TODO: needs more tests\n            case RuleKinds.ALIASED_FIELD: {\n              const name = state.prevState?.name;\n              if (!name) {\n                argDefs = null;\n                break;\n              }\n              const field = parentType\n                ? getFieldDef(schema, parentType, name)\n                : null;\n              if (!field) {\n                argDefs = null;\n                break;\n              }\n              argDefs = field.args as GraphQLArgument[];\n              break;\n            }\n            default:\n              argDefs = null;\n              break;\n          }\n        } else {\n          argDefs = null;\n        }\n        break;\n      }\n      case RuleKinds.ARGUMENT:\n        if (argDefs) {\n          for (let i = 0; i < argDefs.length; i++) {\n            if (argDefs[i].name === state.name) {\n              argDef = argDefs[i];\n              break;\n            }\n          }\n        }\n        inputType = argDef?.type;\n        break;\n      case RuleKinds.VARIABLE_DEFINITION:\n      case RuleKinds.VARIABLE:\n        type = inputType;\n        break;\n      // TODO: needs tests\n      case RuleKinds.ENUM_VALUE:\n        const enumType = getNamedType(inputType!);\n        enumValue =\n          enumType instanceof GraphQLEnumType\n            ? enumType\n                .getValues()\n                .find((val: GraphQLEnumValue) => val.value === state.name)\n            : null;\n        break;\n      // TODO: needs tests\n      case RuleKinds.LIST_VALUE:\n        const nullableType = getNullableType(inputType!);\n        inputType =\n          nullableType instanceof GraphQLList ? nullableType.ofType : null;\n        break;\n      case RuleKinds.OBJECT_VALUE:\n        const objectType = getNamedType(inputType!);\n        objectFieldDefs =\n          objectType instanceof GraphQLInputObjectType\n            ? objectType.getFields()\n            : null;\n        break;\n      // TODO: needs tests\n      case RuleKinds.OBJECT_FIELD:\n        const objectField =\n          state.name && objectFieldDefs ? objectFieldDefs[state.name] : null;\n        inputType = objectField?.type;\n        // @ts-expect-error\n        fieldDef = objectField as GraphQLField<null, null>;\n        type = fieldDef ? fieldDef.type : null;\n        break;\n      case RuleKinds.NAMED_TYPE:\n        if (state.name) {\n          type = schema.getType(state.name);\n        }\n        // TODO: collect already extended interfaces of the type/interface we're extending\n        //  here to eliminate them from the completion list\n        // because \"type A extends B & C &\" should not show completion options for B & C still.\n\n        break;\n    }\n  });\n\n  return {\n    argDef,\n    argDefs,\n    directiveDef,\n    enumValue,\n    fieldDef,\n    inputType,\n    objectFieldDefs,\n    parentType,\n    type,\n    interfaceDef,\n    objectTypeDef,\n  };\n}\n", "import { Kind } from 'graphql';\nimport { Maybe } from '../types';\nimport CharacterStream from './CharacterStream';\n\nexport type ContextToken = {\n  start: number;\n  end: number;\n  string: string;\n  state: State;\n  style?: string;\n};\n\nexport type ContextTokenForCodeMirror = {\n  start: number;\n  end: number;\n  string: string;\n  type: string | null;\n  state: State;\n};\n\nexport type ContextTokenUnion = ContextToken | ContextTokenForCodeMirror;\n\nexport type RuleOrString = Rule | string;\n\nexport type ParseRule =\n  | RuleOrString[]\n  | ((token: Token, stream: CharacterStream) => string | null | void);\n\nexport type Token = {\n  kind: string;\n  value: string;\n};\n\nexport type Rule = {\n  style?: string;\n  match?: (token: Token) => boolean;\n  update?: (state: State, token: Token) => void;\n  separator?: string | Rule;\n  isList?: boolean;\n  ofRule?: Rule | string;\n};\n\nexport type State = {\n  level: number;\n  levels?: number[];\n  prevState: Maybe<State>;\n  rule: Maybe<ParseRule>;\n  kind: Maybe<RuleKind>;\n  name: Maybe<string>;\n  type: Maybe<string>;\n  step: number;\n  needsSeparator: boolean;\n  needsAdvance?: boolean;\n  indentLevel?: number;\n  inBlockstring?: boolean;\n};\n\nexport const AdditionalRuleKinds: _AdditionalRuleKinds = {\n  ALIASED_FIELD: 'AliasedField',\n  ARGUMENTS: 'Arguments',\n  SHORT_QUERY: 'ShortQuery',\n  QUERY: 'Query',\n  MUTATION: 'Mutation',\n  SUBSCRIPTION: 'Subscription',\n  TYPE_CONDITION: 'TypeCondition',\n  INVALID: 'Invalid',\n  COMMENT: 'Comment',\n  SCHEMA_DEF: 'SchemaDef',\n  SCALAR_DEF: 'ScalarDef',\n  OBJECT_TYPE_DEF: 'ObjectTypeDef',\n  OBJECT_VALUE: 'ObjectValue',\n  LIST_VALUE: 'ListValue',\n  INTERFACE_DEF: 'InterfaceDef',\n  UNION_DEF: 'UnionDef',\n  ENUM_DEF: 'EnumDef',\n  ENUM_VALUE: 'EnumValue',\n  FIELD_DEF: 'FieldDef',\n  INPUT_DEF: 'InputDef',\n  INPUT_VALUE_DEF: 'InputValueDef',\n  ARGUMENTS_DEF: 'ArgumentsDef',\n  EXTEND_DEF: 'ExtendDef',\n  EXTENSION_DEFINITION: 'ExtensionDefinition',\n  DIRECTIVE_DEF: 'DirectiveDef',\n  IMPLEMENTS: 'Implements',\n  VARIABLE_DEFINITIONS: 'VariableDefinitions',\n  TYPE: 'Type',\n  VARIABLE: 'Variable',\n};\n\nexport type _AdditionalRuleKinds = {\n  ALIASED_FIELD: 'AliasedField';\n  ARGUMENTS: 'Arguments';\n  SHORT_QUERY: 'ShortQuery';\n  QUERY: 'Query';\n  MUTATION: 'Mutation';\n  SUBSCRIPTION: 'Subscription';\n  TYPE_CONDITION: 'TypeCondition';\n  INVALID: 'Invalid';\n  COMMENT: 'Comment';\n  SCHEMA_DEF: 'SchemaDef';\n  SCALAR_DEF: 'ScalarDef';\n  OBJECT_TYPE_DEF: 'ObjectTypeDef';\n  OBJECT_VALUE: 'ObjectValue';\n  LIST_VALUE: 'ListValue';\n  INTERFACE_DEF: 'InterfaceDef';\n  UNION_DEF: 'UnionDef';\n  ENUM_DEF: 'EnumDef';\n  ENUM_VALUE: 'EnumValue';\n  FIELD_DEF: 'FieldDef';\n  INPUT_DEF: 'InputDef';\n  INPUT_VALUE_DEF: 'InputValueDef';\n  ARGUMENTS_DEF: 'ArgumentsDef';\n  EXTEND_DEF: 'ExtendDef';\n  EXTENSION_DEFINITION: 'ExtensionDefinition';\n  DIRECTIVE_DEF: 'DirectiveDef';\n  IMPLEMENTS: 'Implements';\n  VARIABLE_DEFINITIONS: 'VariableDefinitions';\n  TYPE: 'Type';\n  VARIABLE: 'Variable';\n};\n\nexport const RuleKinds = {\n  ...Kind,\n  ...AdditionalRuleKinds,\n};\n\nexport type _RuleKinds = Omit<typeof Kind, 'VARIABLE'> &\n  typeof AdditionalRuleKinds;\n\nexport type RuleKind = _RuleKinds[keyof _RuleKinds];\nexport type RuleKindEnum = RuleKind;\nexport type TokenPattern = string | ((char: string) => boolean) | RegExp;\n\nexport interface CharacterStreamInterface {\n  getStartOfToken: () => number;\n  getCurrentPosition: () => number;\n  eol: () => boolean;\n  sol: () => boolean;\n  peek: () => string | null;\n  next: () => string;\n  eat: (pattern: TokenPattern) => string | undefined;\n  eatWhile: (match: TokenPattern) => boolean;\n  eatSpace: () => boolean;\n  skipToEnd: () => void;\n  skipTo: (position: number) => void;\n  match: (\n    pattern: TokenPattern,\n    consume?: Maybe<boolean>,\n    caseFold?: Maybe<boolean>,\n  ) => string[] | boolean;\n  backUp: (num: number) => void;\n  column: () => number;\n  indentation: () => number;\n  current: () => string;\n}\n", "/* --------------------------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n * ------------------------------------------------------------------------------------------ */\n'use strict';\nexport var DocumentUri;\n(function (DocumentUri) {\n    function is(value) {\n        return typeof value === 'string';\n    }\n    DocumentUri.is = is;\n})(DocumentUri || (DocumentUri = {}));\nexport var URI;\n(function (URI) {\n    function is(value) {\n        return typeof value === 'string';\n    }\n    URI.is = is;\n})(URI || (URI = {}));\nexport var integer;\n(function (integer) {\n    integer.MIN_VALUE = -**********;\n    integer.MAX_VALUE = **********;\n    function is(value) {\n        return typeof value === 'number' && integer.MIN_VALUE <= value && value <= integer.MAX_VALUE;\n    }\n    integer.is = is;\n})(integer || (integer = {}));\nexport var uinteger;\n(function (uinteger) {\n    uinteger.MIN_VALUE = 0;\n    uinteger.MAX_VALUE = **********;\n    function is(value) {\n        return typeof value === 'number' && uinteger.MIN_VALUE <= value && value <= uinteger.MAX_VALUE;\n    }\n    uinteger.is = is;\n})(uinteger || (uinteger = {}));\n/**\n * The Position namespace provides helper functions to work with\n * {@link Position} literals.\n */\nexport var Position;\n(function (Position) {\n    /**\n     * Creates a new Position literal from the given line and character.\n     * @param line The position's line.\n     * @param character The position's character.\n     */\n    function create(line, character) {\n        if (line === Number.MAX_VALUE) {\n            line = uinteger.MAX_VALUE;\n        }\n        if (character === Number.MAX_VALUE) {\n            character = uinteger.MAX_VALUE;\n        }\n        return { line, character };\n    }\n    Position.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link Position} interface.\n     */\n    function is(value) {\n        let candidate = value;\n        return Is.objectLiteral(candidate) && Is.uinteger(candidate.line) && Is.uinteger(candidate.character);\n    }\n    Position.is = is;\n})(Position || (Position = {}));\n/**\n * The Range namespace provides helper functions to work with\n * {@link Range} literals.\n */\nexport var Range;\n(function (Range) {\n    function create(one, two, three, four) {\n        if (Is.uinteger(one) && Is.uinteger(two) && Is.uinteger(three) && Is.uinteger(four)) {\n            return { start: Position.create(one, two), end: Position.create(three, four) };\n        }\n        else if (Position.is(one) && Position.is(two)) {\n            return { start: one, end: two };\n        }\n        else {\n            throw new Error(`Range#create called with invalid arguments[${one}, ${two}, ${three}, ${four}]`);\n        }\n    }\n    Range.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link Range} interface.\n     */\n    function is(value) {\n        let candidate = value;\n        return Is.objectLiteral(candidate) && Position.is(candidate.start) && Position.is(candidate.end);\n    }\n    Range.is = is;\n})(Range || (Range = {}));\n/**\n * The Location namespace provides helper functions to work with\n * {@link Location} literals.\n */\nexport var Location;\n(function (Location) {\n    /**\n     * Creates a Location literal.\n     * @param uri The location's uri.\n     * @param range The location's range.\n     */\n    function create(uri, range) {\n        return { uri, range };\n    }\n    Location.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link Location} interface.\n     */\n    function is(value) {\n        let candidate = value;\n        return Is.objectLiteral(candidate) && Range.is(candidate.range) && (Is.string(candidate.uri) || Is.undefined(candidate.uri));\n    }\n    Location.is = is;\n})(Location || (Location = {}));\n/**\n * The LocationLink namespace provides helper functions to work with\n * {@link LocationLink} literals.\n */\nexport var LocationLink;\n(function (LocationLink) {\n    /**\n     * Creates a LocationLink literal.\n     * @param targetUri The definition's uri.\n     * @param targetRange The full range of the definition.\n     * @param targetSelectionRange The span of the symbol definition at the target.\n     * @param originSelectionRange The span of the symbol being defined in the originating source file.\n     */\n    function create(targetUri, targetRange, targetSelectionRange, originSelectionRange) {\n        return { targetUri, targetRange, targetSelectionRange, originSelectionRange };\n    }\n    LocationLink.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link LocationLink} interface.\n     */\n    function is(value) {\n        let candidate = value;\n        return Is.objectLiteral(candidate) && Range.is(candidate.targetRange) && Is.string(candidate.targetUri)\n            && Range.is(candidate.targetSelectionRange)\n            && (Range.is(candidate.originSelectionRange) || Is.undefined(candidate.originSelectionRange));\n    }\n    LocationLink.is = is;\n})(LocationLink || (LocationLink = {}));\n/**\n * The Color namespace provides helper functions to work with\n * {@link Color} literals.\n */\nexport var Color;\n(function (Color) {\n    /**\n     * Creates a new Color literal.\n     */\n    function create(red, green, blue, alpha) {\n        return {\n            red,\n            green,\n            blue,\n            alpha,\n        };\n    }\n    Color.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link Color} interface.\n     */\n    function is(value) {\n        const candidate = value;\n        return Is.objectLiteral(candidate) && Is.numberRange(candidate.red, 0, 1)\n            && Is.numberRange(candidate.green, 0, 1)\n            && Is.numberRange(candidate.blue, 0, 1)\n            && Is.numberRange(candidate.alpha, 0, 1);\n    }\n    Color.is = is;\n})(Color || (Color = {}));\n/**\n * The ColorInformation namespace provides helper functions to work with\n * {@link ColorInformation} literals.\n */\nexport var ColorInformation;\n(function (ColorInformation) {\n    /**\n     * Creates a new ColorInformation literal.\n     */\n    function create(range, color) {\n        return {\n            range,\n            color,\n        };\n    }\n    ColorInformation.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link ColorInformation} interface.\n     */\n    function is(value) {\n        const candidate = value;\n        return Is.objectLiteral(candidate) && Range.is(candidate.range) && Color.is(candidate.color);\n    }\n    ColorInformation.is = is;\n})(ColorInformation || (ColorInformation = {}));\n/**\n * The Color namespace provides helper functions to work with\n * {@link ColorPresentation} literals.\n */\nexport var ColorPresentation;\n(function (ColorPresentation) {\n    /**\n     * Creates a new ColorInformation literal.\n     */\n    function create(label, textEdit, additionalTextEdits) {\n        return {\n            label,\n            textEdit,\n            additionalTextEdits,\n        };\n    }\n    ColorPresentation.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link ColorInformation} interface.\n     */\n    function is(value) {\n        const candidate = value;\n        return Is.objectLiteral(candidate) && Is.string(candidate.label)\n            && (Is.undefined(candidate.textEdit) || TextEdit.is(candidate))\n            && (Is.undefined(candidate.additionalTextEdits) || Is.typedArray(candidate.additionalTextEdits, TextEdit.is));\n    }\n    ColorPresentation.is = is;\n})(ColorPresentation || (ColorPresentation = {}));\n/**\n * A set of predefined range kinds.\n */\nexport var FoldingRangeKind;\n(function (FoldingRangeKind) {\n    /**\n     * Folding range for a comment\n     */\n    FoldingRangeKind.Comment = 'comment';\n    /**\n     * Folding range for an import or include\n     */\n    FoldingRangeKind.Imports = 'imports';\n    /**\n     * Folding range for a region (e.g. `#region`)\n     */\n    FoldingRangeKind.Region = 'region';\n})(FoldingRangeKind || (FoldingRangeKind = {}));\n/**\n * The folding range namespace provides helper functions to work with\n * {@link FoldingRange} literals.\n */\nexport var FoldingRange;\n(function (FoldingRange) {\n    /**\n     * Creates a new FoldingRange literal.\n     */\n    function create(startLine, endLine, startCharacter, endCharacter, kind, collapsedText) {\n        const result = {\n            startLine,\n            endLine\n        };\n        if (Is.defined(startCharacter)) {\n            result.startCharacter = startCharacter;\n        }\n        if (Is.defined(endCharacter)) {\n            result.endCharacter = endCharacter;\n        }\n        if (Is.defined(kind)) {\n            result.kind = kind;\n        }\n        if (Is.defined(collapsedText)) {\n            result.collapsedText = collapsedText;\n        }\n        return result;\n    }\n    FoldingRange.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link FoldingRange} interface.\n     */\n    function is(value) {\n        const candidate = value;\n        return Is.objectLiteral(candidate) && Is.uinteger(candidate.startLine) && Is.uinteger(candidate.startLine)\n            && (Is.undefined(candidate.startCharacter) || Is.uinteger(candidate.startCharacter))\n            && (Is.undefined(candidate.endCharacter) || Is.uinteger(candidate.endCharacter))\n            && (Is.undefined(candidate.kind) || Is.string(candidate.kind));\n    }\n    FoldingRange.is = is;\n})(FoldingRange || (FoldingRange = {}));\n/**\n * The DiagnosticRelatedInformation namespace provides helper functions to work with\n * {@link DiagnosticRelatedInformation} literals.\n */\nexport var DiagnosticRelatedInformation;\n(function (DiagnosticRelatedInformation) {\n    /**\n     * Creates a new DiagnosticRelatedInformation literal.\n     */\n    function create(location, message) {\n        return {\n            location,\n            message\n        };\n    }\n    DiagnosticRelatedInformation.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link DiagnosticRelatedInformation} interface.\n     */\n    function is(value) {\n        let candidate = value;\n        return Is.defined(candidate) && Location.is(candidate.location) && Is.string(candidate.message);\n    }\n    DiagnosticRelatedInformation.is = is;\n})(DiagnosticRelatedInformation || (DiagnosticRelatedInformation = {}));\n/**\n * The diagnostic's severity.\n */\nexport var DiagnosticSeverity;\n(function (DiagnosticSeverity) {\n    /**\n     * Reports an error.\n     */\n    DiagnosticSeverity.Error = 1;\n    /**\n     * Reports a warning.\n     */\n    DiagnosticSeverity.Warning = 2;\n    /**\n     * Reports an information.\n     */\n    DiagnosticSeverity.Information = 3;\n    /**\n     * Reports a hint.\n     */\n    DiagnosticSeverity.Hint = 4;\n})(DiagnosticSeverity || (DiagnosticSeverity = {}));\n/**\n * The diagnostic tags.\n *\n * @since 3.15.0\n */\nexport var DiagnosticTag;\n(function (DiagnosticTag) {\n    /**\n     * Unused or unnecessary code.\n     *\n     * Clients are allowed to render diagnostics with this tag faded out instead of having\n     * an error squiggle.\n     */\n    DiagnosticTag.Unnecessary = 1;\n    /**\n     * Deprecated or obsolete code.\n     *\n     * Clients are allowed to rendered diagnostics with this tag strike through.\n     */\n    DiagnosticTag.Deprecated = 2;\n})(DiagnosticTag || (DiagnosticTag = {}));\n/**\n * The CodeDescription namespace provides functions to deal with descriptions for diagnostic codes.\n *\n * @since 3.16.0\n */\nexport var CodeDescription;\n(function (CodeDescription) {\n    function is(value) {\n        const candidate = value;\n        return Is.objectLiteral(candidate) && Is.string(candidate.href);\n    }\n    CodeDescription.is = is;\n})(CodeDescription || (CodeDescription = {}));\n/**\n * The Diagnostic namespace provides helper functions to work with\n * {@link Diagnostic} literals.\n */\nexport var Diagnostic;\n(function (Diagnostic) {\n    /**\n     * Creates a new Diagnostic literal.\n     */\n    function create(range, message, severity, code, source, relatedInformation) {\n        let result = { range, message };\n        if (Is.defined(severity)) {\n            result.severity = severity;\n        }\n        if (Is.defined(code)) {\n            result.code = code;\n        }\n        if (Is.defined(source)) {\n            result.source = source;\n        }\n        if (Is.defined(relatedInformation)) {\n            result.relatedInformation = relatedInformation;\n        }\n        return result;\n    }\n    Diagnostic.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link Diagnostic} interface.\n     */\n    function is(value) {\n        var _a;\n        let candidate = value;\n        return Is.defined(candidate)\n            && Range.is(candidate.range)\n            && Is.string(candidate.message)\n            && (Is.number(candidate.severity) || Is.undefined(candidate.severity))\n            && (Is.integer(candidate.code) || Is.string(candidate.code) || Is.undefined(candidate.code))\n            && (Is.undefined(candidate.codeDescription) || (Is.string((_a = candidate.codeDescription) === null || _a === void 0 ? void 0 : _a.href)))\n            && (Is.string(candidate.source) || Is.undefined(candidate.source))\n            && (Is.undefined(candidate.relatedInformation) || Is.typedArray(candidate.relatedInformation, DiagnosticRelatedInformation.is));\n    }\n    Diagnostic.is = is;\n})(Diagnostic || (Diagnostic = {}));\n/**\n * The Command namespace provides helper functions to work with\n * {@link Command} literals.\n */\nexport var Command;\n(function (Command) {\n    /**\n     * Creates a new Command literal.\n     */\n    function create(title, command, ...args) {\n        let result = { title, command };\n        if (Is.defined(args) && args.length > 0) {\n            result.arguments = args;\n        }\n        return result;\n    }\n    Command.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link Command} interface.\n     */\n    function is(value) {\n        let candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.title) && Is.string(candidate.command);\n    }\n    Command.is = is;\n})(Command || (Command = {}));\n/**\n * The TextEdit namespace provides helper function to create replace,\n * insert and delete edits more easily.\n */\nexport var TextEdit;\n(function (TextEdit) {\n    /**\n     * Creates a replace text edit.\n     * @param range The range of text to be replaced.\n     * @param newText The new text.\n     */\n    function replace(range, newText) {\n        return { range, newText };\n    }\n    TextEdit.replace = replace;\n    /**\n     * Creates an insert text edit.\n     * @param position The position to insert the text at.\n     * @param newText The text to be inserted.\n     */\n    function insert(position, newText) {\n        return { range: { start: position, end: position }, newText };\n    }\n    TextEdit.insert = insert;\n    /**\n     * Creates a delete text edit.\n     * @param range The range of text to be deleted.\n     */\n    function del(range) {\n        return { range, newText: '' };\n    }\n    TextEdit.del = del;\n    function is(value) {\n        const candidate = value;\n        return Is.objectLiteral(candidate)\n            && Is.string(candidate.newText)\n            && Range.is(candidate.range);\n    }\n    TextEdit.is = is;\n})(TextEdit || (TextEdit = {}));\nexport var ChangeAnnotation;\n(function (ChangeAnnotation) {\n    function create(label, needsConfirmation, description) {\n        const result = { label };\n        if (needsConfirmation !== undefined) {\n            result.needsConfirmation = needsConfirmation;\n        }\n        if (description !== undefined) {\n            result.description = description;\n        }\n        return result;\n    }\n    ChangeAnnotation.create = create;\n    function is(value) {\n        const candidate = value;\n        return Is.objectLiteral(candidate) && Is.string(candidate.label) &&\n            (Is.boolean(candidate.needsConfirmation) || candidate.needsConfirmation === undefined) &&\n            (Is.string(candidate.description) || candidate.description === undefined);\n    }\n    ChangeAnnotation.is = is;\n})(ChangeAnnotation || (ChangeAnnotation = {}));\nexport var ChangeAnnotationIdentifier;\n(function (ChangeAnnotationIdentifier) {\n    function is(value) {\n        const candidate = value;\n        return Is.string(candidate);\n    }\n    ChangeAnnotationIdentifier.is = is;\n})(ChangeAnnotationIdentifier || (ChangeAnnotationIdentifier = {}));\nexport var AnnotatedTextEdit;\n(function (AnnotatedTextEdit) {\n    /**\n     * Creates an annotated replace text edit.\n     *\n     * @param range The range of text to be replaced.\n     * @param newText The new text.\n     * @param annotation The annotation.\n     */\n    function replace(range, newText, annotation) {\n        return { range, newText, annotationId: annotation };\n    }\n    AnnotatedTextEdit.replace = replace;\n    /**\n     * Creates an annotated insert text edit.\n     *\n     * @param position The position to insert the text at.\n     * @param newText The text to be inserted.\n     * @param annotation The annotation.\n     */\n    function insert(position, newText, annotation) {\n        return { range: { start: position, end: position }, newText, annotationId: annotation };\n    }\n    AnnotatedTextEdit.insert = insert;\n    /**\n     * Creates an annotated delete text edit.\n     *\n     * @param range The range of text to be deleted.\n     * @param annotation The annotation.\n     */\n    function del(range, annotation) {\n        return { range, newText: '', annotationId: annotation };\n    }\n    AnnotatedTextEdit.del = del;\n    function is(value) {\n        const candidate = value;\n        return TextEdit.is(candidate) && (ChangeAnnotation.is(candidate.annotationId) || ChangeAnnotationIdentifier.is(candidate.annotationId));\n    }\n    AnnotatedTextEdit.is = is;\n})(AnnotatedTextEdit || (AnnotatedTextEdit = {}));\n/**\n * The TextDocumentEdit namespace provides helper function to create\n * an edit that manipulates a text document.\n */\nexport var TextDocumentEdit;\n(function (TextDocumentEdit) {\n    /**\n     * Creates a new `TextDocumentEdit`\n     */\n    function create(textDocument, edits) {\n        return { textDocument, edits };\n    }\n    TextDocumentEdit.create = create;\n    function is(value) {\n        let candidate = value;\n        return Is.defined(candidate)\n            && OptionalVersionedTextDocumentIdentifier.is(candidate.textDocument)\n            && Array.isArray(candidate.edits);\n    }\n    TextDocumentEdit.is = is;\n})(TextDocumentEdit || (TextDocumentEdit = {}));\nexport var CreateFile;\n(function (CreateFile) {\n    function create(uri, options, annotation) {\n        let result = {\n            kind: 'create',\n            uri\n        };\n        if (options !== undefined && (options.overwrite !== undefined || options.ignoreIfExists !== undefined)) {\n            result.options = options;\n        }\n        if (annotation !== undefined) {\n            result.annotationId = annotation;\n        }\n        return result;\n    }\n    CreateFile.create = create;\n    function is(value) {\n        let candidate = value;\n        return candidate && candidate.kind === 'create' && Is.string(candidate.uri) && (candidate.options === undefined ||\n            ((candidate.options.overwrite === undefined || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === undefined || Is.boolean(candidate.options.ignoreIfExists)))) && (candidate.annotationId === undefined || ChangeAnnotationIdentifier.is(candidate.annotationId));\n    }\n    CreateFile.is = is;\n})(CreateFile || (CreateFile = {}));\nexport var RenameFile;\n(function (RenameFile) {\n    function create(oldUri, newUri, options, annotation) {\n        let result = {\n            kind: 'rename',\n            oldUri,\n            newUri\n        };\n        if (options !== undefined && (options.overwrite !== undefined || options.ignoreIfExists !== undefined)) {\n            result.options = options;\n        }\n        if (annotation !== undefined) {\n            result.annotationId = annotation;\n        }\n        return result;\n    }\n    RenameFile.create = create;\n    function is(value) {\n        let candidate = value;\n        return candidate && candidate.kind === 'rename' && Is.string(candidate.oldUri) && Is.string(candidate.newUri) && (candidate.options === undefined ||\n            ((candidate.options.overwrite === undefined || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === undefined || Is.boolean(candidate.options.ignoreIfExists)))) && (candidate.annotationId === undefined || ChangeAnnotationIdentifier.is(candidate.annotationId));\n    }\n    RenameFile.is = is;\n})(RenameFile || (RenameFile = {}));\nexport var DeleteFile;\n(function (DeleteFile) {\n    function create(uri, options, annotation) {\n        let result = {\n            kind: 'delete',\n            uri\n        };\n        if (options !== undefined && (options.recursive !== undefined || options.ignoreIfNotExists !== undefined)) {\n            result.options = options;\n        }\n        if (annotation !== undefined) {\n            result.annotationId = annotation;\n        }\n        return result;\n    }\n    DeleteFile.create = create;\n    function is(value) {\n        let candidate = value;\n        return candidate && candidate.kind === 'delete' && Is.string(candidate.uri) && (candidate.options === undefined ||\n            ((candidate.options.recursive === undefined || Is.boolean(candidate.options.recursive)) && (candidate.options.ignoreIfNotExists === undefined || Is.boolean(candidate.options.ignoreIfNotExists)))) && (candidate.annotationId === undefined || ChangeAnnotationIdentifier.is(candidate.annotationId));\n    }\n    DeleteFile.is = is;\n})(DeleteFile || (DeleteFile = {}));\nexport var WorkspaceEdit;\n(function (WorkspaceEdit) {\n    function is(value) {\n        let candidate = value;\n        return candidate &&\n            (candidate.changes !== undefined || candidate.documentChanges !== undefined) &&\n            (candidate.documentChanges === undefined || candidate.documentChanges.every((change) => {\n                if (Is.string(change.kind)) {\n                    return CreateFile.is(change) || RenameFile.is(change) || DeleteFile.is(change);\n                }\n                else {\n                    return TextDocumentEdit.is(change);\n                }\n            }));\n    }\n    WorkspaceEdit.is = is;\n})(WorkspaceEdit || (WorkspaceEdit = {}));\nclass TextEditChangeImpl {\n    constructor(edits, changeAnnotations) {\n        this.edits = edits;\n        this.changeAnnotations = changeAnnotations;\n    }\n    insert(position, newText, annotation) {\n        let edit;\n        let id;\n        if (annotation === undefined) {\n            edit = TextEdit.insert(position, newText);\n        }\n        else if (ChangeAnnotationIdentifier.is(annotation)) {\n            id = annotation;\n            edit = AnnotatedTextEdit.insert(position, newText, annotation);\n        }\n        else {\n            this.assertChangeAnnotations(this.changeAnnotations);\n            id = this.changeAnnotations.manage(annotation);\n            edit = AnnotatedTextEdit.insert(position, newText, id);\n        }\n        this.edits.push(edit);\n        if (id !== undefined) {\n            return id;\n        }\n    }\n    replace(range, newText, annotation) {\n        let edit;\n        let id;\n        if (annotation === undefined) {\n            edit = TextEdit.replace(range, newText);\n        }\n        else if (ChangeAnnotationIdentifier.is(annotation)) {\n            id = annotation;\n            edit = AnnotatedTextEdit.replace(range, newText, annotation);\n        }\n        else {\n            this.assertChangeAnnotations(this.changeAnnotations);\n            id = this.changeAnnotations.manage(annotation);\n            edit = AnnotatedTextEdit.replace(range, newText, id);\n        }\n        this.edits.push(edit);\n        if (id !== undefined) {\n            return id;\n        }\n    }\n    delete(range, annotation) {\n        let edit;\n        let id;\n        if (annotation === undefined) {\n            edit = TextEdit.del(range);\n        }\n        else if (ChangeAnnotationIdentifier.is(annotation)) {\n            id = annotation;\n            edit = AnnotatedTextEdit.del(range, annotation);\n        }\n        else {\n            this.assertChangeAnnotations(this.changeAnnotations);\n            id = this.changeAnnotations.manage(annotation);\n            edit = AnnotatedTextEdit.del(range, id);\n        }\n        this.edits.push(edit);\n        if (id !== undefined) {\n            return id;\n        }\n    }\n    add(edit) {\n        this.edits.push(edit);\n    }\n    all() {\n        return this.edits;\n    }\n    clear() {\n        this.edits.splice(0, this.edits.length);\n    }\n    assertChangeAnnotations(value) {\n        if (value === undefined) {\n            throw new Error(`Text edit change is not configured to manage change annotations.`);\n        }\n    }\n}\n/**\n * A helper class\n */\nclass ChangeAnnotations {\n    constructor(annotations) {\n        this._annotations = annotations === undefined ? Object.create(null) : annotations;\n        this._counter = 0;\n        this._size = 0;\n    }\n    all() {\n        return this._annotations;\n    }\n    get size() {\n        return this._size;\n    }\n    manage(idOrAnnotation, annotation) {\n        let id;\n        if (ChangeAnnotationIdentifier.is(idOrAnnotation)) {\n            id = idOrAnnotation;\n        }\n        else {\n            id = this.nextId();\n            annotation = idOrAnnotation;\n        }\n        if (this._annotations[id] !== undefined) {\n            throw new Error(`Id ${id} is already in use.`);\n        }\n        if (annotation === undefined) {\n            throw new Error(`No annotation provided for id ${id}`);\n        }\n        this._annotations[id] = annotation;\n        this._size++;\n        return id;\n    }\n    nextId() {\n        this._counter++;\n        return this._counter.toString();\n    }\n}\n/**\n * A workspace change helps constructing changes to a workspace.\n */\nexport class WorkspaceChange {\n    constructor(workspaceEdit) {\n        this._textEditChanges = Object.create(null);\n        if (workspaceEdit !== undefined) {\n            this._workspaceEdit = workspaceEdit;\n            if (workspaceEdit.documentChanges) {\n                this._changeAnnotations = new ChangeAnnotations(workspaceEdit.changeAnnotations);\n                workspaceEdit.changeAnnotations = this._changeAnnotations.all();\n                workspaceEdit.documentChanges.forEach((change) => {\n                    if (TextDocumentEdit.is(change)) {\n                        const textEditChange = new TextEditChangeImpl(change.edits, this._changeAnnotations);\n                        this._textEditChanges[change.textDocument.uri] = textEditChange;\n                    }\n                });\n            }\n            else if (workspaceEdit.changes) {\n                Object.keys(workspaceEdit.changes).forEach((key) => {\n                    const textEditChange = new TextEditChangeImpl(workspaceEdit.changes[key]);\n                    this._textEditChanges[key] = textEditChange;\n                });\n            }\n        }\n        else {\n            this._workspaceEdit = {};\n        }\n    }\n    /**\n     * Returns the underlying {@link WorkspaceEdit} literal\n     * use to be returned from a workspace edit operation like rename.\n     */\n    get edit() {\n        this.initDocumentChanges();\n        if (this._changeAnnotations !== undefined) {\n            if (this._changeAnnotations.size === 0) {\n                this._workspaceEdit.changeAnnotations = undefined;\n            }\n            else {\n                this._workspaceEdit.changeAnnotations = this._changeAnnotations.all();\n            }\n        }\n        return this._workspaceEdit;\n    }\n    getTextEditChange(key) {\n        if (OptionalVersionedTextDocumentIdentifier.is(key)) {\n            this.initDocumentChanges();\n            if (this._workspaceEdit.documentChanges === undefined) {\n                throw new Error('Workspace edit is not configured for document changes.');\n            }\n            const textDocument = { uri: key.uri, version: key.version };\n            let result = this._textEditChanges[textDocument.uri];\n            if (!result) {\n                const edits = [];\n                const textDocumentEdit = {\n                    textDocument,\n                    edits\n                };\n                this._workspaceEdit.documentChanges.push(textDocumentEdit);\n                result = new TextEditChangeImpl(edits, this._changeAnnotations);\n                this._textEditChanges[textDocument.uri] = result;\n            }\n            return result;\n        }\n        else {\n            this.initChanges();\n            if (this._workspaceEdit.changes === undefined) {\n                throw new Error('Workspace edit is not configured for normal text edit changes.');\n            }\n            let result = this._textEditChanges[key];\n            if (!result) {\n                let edits = [];\n                this._workspaceEdit.changes[key] = edits;\n                result = new TextEditChangeImpl(edits);\n                this._textEditChanges[key] = result;\n            }\n            return result;\n        }\n    }\n    initDocumentChanges() {\n        if (this._workspaceEdit.documentChanges === undefined && this._workspaceEdit.changes === undefined) {\n            this._changeAnnotations = new ChangeAnnotations();\n            this._workspaceEdit.documentChanges = [];\n            this._workspaceEdit.changeAnnotations = this._changeAnnotations.all();\n        }\n    }\n    initChanges() {\n        if (this._workspaceEdit.documentChanges === undefined && this._workspaceEdit.changes === undefined) {\n            this._workspaceEdit.changes = Object.create(null);\n        }\n    }\n    createFile(uri, optionsOrAnnotation, options) {\n        this.initDocumentChanges();\n        if (this._workspaceEdit.documentChanges === undefined) {\n            throw new Error('Workspace edit is not configured for document changes.');\n        }\n        let annotation;\n        if (ChangeAnnotation.is(optionsOrAnnotation) || ChangeAnnotationIdentifier.is(optionsOrAnnotation)) {\n            annotation = optionsOrAnnotation;\n        }\n        else {\n            options = optionsOrAnnotation;\n        }\n        let operation;\n        let id;\n        if (annotation === undefined) {\n            operation = CreateFile.create(uri, options);\n        }\n        else {\n            id = ChangeAnnotationIdentifier.is(annotation) ? annotation : this._changeAnnotations.manage(annotation);\n            operation = CreateFile.create(uri, options, id);\n        }\n        this._workspaceEdit.documentChanges.push(operation);\n        if (id !== undefined) {\n            return id;\n        }\n    }\n    renameFile(oldUri, newUri, optionsOrAnnotation, options) {\n        this.initDocumentChanges();\n        if (this._workspaceEdit.documentChanges === undefined) {\n            throw new Error('Workspace edit is not configured for document changes.');\n        }\n        let annotation;\n        if (ChangeAnnotation.is(optionsOrAnnotation) || ChangeAnnotationIdentifier.is(optionsOrAnnotation)) {\n            annotation = optionsOrAnnotation;\n        }\n        else {\n            options = optionsOrAnnotation;\n        }\n        let operation;\n        let id;\n        if (annotation === undefined) {\n            operation = RenameFile.create(oldUri, newUri, options);\n        }\n        else {\n            id = ChangeAnnotationIdentifier.is(annotation) ? annotation : this._changeAnnotations.manage(annotation);\n            operation = RenameFile.create(oldUri, newUri, options, id);\n        }\n        this._workspaceEdit.documentChanges.push(operation);\n        if (id !== undefined) {\n            return id;\n        }\n    }\n    deleteFile(uri, optionsOrAnnotation, options) {\n        this.initDocumentChanges();\n        if (this._workspaceEdit.documentChanges === undefined) {\n            throw new Error('Workspace edit is not configured for document changes.');\n        }\n        let annotation;\n        if (ChangeAnnotation.is(optionsOrAnnotation) || ChangeAnnotationIdentifier.is(optionsOrAnnotation)) {\n            annotation = optionsOrAnnotation;\n        }\n        else {\n            options = optionsOrAnnotation;\n        }\n        let operation;\n        let id;\n        if (annotation === undefined) {\n            operation = DeleteFile.create(uri, options);\n        }\n        else {\n            id = ChangeAnnotationIdentifier.is(annotation) ? annotation : this._changeAnnotations.manage(annotation);\n            operation = DeleteFile.create(uri, options, id);\n        }\n        this._workspaceEdit.documentChanges.push(operation);\n        if (id !== undefined) {\n            return id;\n        }\n    }\n}\n/**\n * The TextDocumentIdentifier namespace provides helper functions to work with\n * {@link TextDocumentIdentifier} literals.\n */\nexport var TextDocumentIdentifier;\n(function (TextDocumentIdentifier) {\n    /**\n     * Creates a new TextDocumentIdentifier literal.\n     * @param uri The document's uri.\n     */\n    function create(uri) {\n        return { uri };\n    }\n    TextDocumentIdentifier.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link TextDocumentIdentifier} interface.\n     */\n    function is(value) {\n        let candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.uri);\n    }\n    TextDocumentIdentifier.is = is;\n})(TextDocumentIdentifier || (TextDocumentIdentifier = {}));\n/**\n * The VersionedTextDocumentIdentifier namespace provides helper functions to work with\n * {@link VersionedTextDocumentIdentifier} literals.\n */\nexport var VersionedTextDocumentIdentifier;\n(function (VersionedTextDocumentIdentifier) {\n    /**\n     * Creates a new VersionedTextDocumentIdentifier literal.\n     * @param uri The document's uri.\n     * @param version The document's version.\n     */\n    function create(uri, version) {\n        return { uri, version };\n    }\n    VersionedTextDocumentIdentifier.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link VersionedTextDocumentIdentifier} interface.\n     */\n    function is(value) {\n        let candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.uri) && Is.integer(candidate.version);\n    }\n    VersionedTextDocumentIdentifier.is = is;\n})(VersionedTextDocumentIdentifier || (VersionedTextDocumentIdentifier = {}));\n/**\n * The OptionalVersionedTextDocumentIdentifier namespace provides helper functions to work with\n * {@link OptionalVersionedTextDocumentIdentifier} literals.\n */\nexport var OptionalVersionedTextDocumentIdentifier;\n(function (OptionalVersionedTextDocumentIdentifier) {\n    /**\n     * Creates a new OptionalVersionedTextDocumentIdentifier literal.\n     * @param uri The document's uri.\n     * @param version The document's version.\n     */\n    function create(uri, version) {\n        return { uri, version };\n    }\n    OptionalVersionedTextDocumentIdentifier.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link OptionalVersionedTextDocumentIdentifier} interface.\n     */\n    function is(value) {\n        let candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.uri) && (candidate.version === null || Is.integer(candidate.version));\n    }\n    OptionalVersionedTextDocumentIdentifier.is = is;\n})(OptionalVersionedTextDocumentIdentifier || (OptionalVersionedTextDocumentIdentifier = {}));\n/**\n * The TextDocumentItem namespace provides helper functions to work with\n * {@link TextDocumentItem} literals.\n */\nexport var TextDocumentItem;\n(function (TextDocumentItem) {\n    /**\n     * Creates a new TextDocumentItem literal.\n     * @param uri The document's uri.\n     * @param languageId The document's language identifier.\n     * @param version The document's version number.\n     * @param text The document's text.\n     */\n    function create(uri, languageId, version, text) {\n        return { uri, languageId, version, text };\n    }\n    TextDocumentItem.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link TextDocumentItem} interface.\n     */\n    function is(value) {\n        let candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.uri) && Is.string(candidate.languageId) && Is.integer(candidate.version) && Is.string(candidate.text);\n    }\n    TextDocumentItem.is = is;\n})(TextDocumentItem || (TextDocumentItem = {}));\n/**\n * Describes the content type that a client supports in various\n * result literals like `Hover`, `ParameterInfo` or `CompletionItem`.\n *\n * Please note that `MarkupKinds` must not start with a `$`. This kinds\n * are reserved for internal usage.\n */\nexport var MarkupKind;\n(function (MarkupKind) {\n    /**\n     * Plain text is supported as a content format\n     */\n    MarkupKind.PlainText = 'plaintext';\n    /**\n     * Markdown is supported as a content format\n     */\n    MarkupKind.Markdown = 'markdown';\n    /**\n     * Checks whether the given value is a value of the {@link MarkupKind} type.\n     */\n    function is(value) {\n        const candidate = value;\n        return candidate === MarkupKind.PlainText || candidate === MarkupKind.Markdown;\n    }\n    MarkupKind.is = is;\n})(MarkupKind || (MarkupKind = {}));\nexport var MarkupContent;\n(function (MarkupContent) {\n    /**\n     * Checks whether the given value conforms to the {@link MarkupContent} interface.\n     */\n    function is(value) {\n        const candidate = value;\n        return Is.objectLiteral(value) && MarkupKind.is(candidate.kind) && Is.string(candidate.value);\n    }\n    MarkupContent.is = is;\n})(MarkupContent || (MarkupContent = {}));\n/**\n * The kind of a completion entry.\n */\nexport var CompletionItemKind;\n(function (CompletionItemKind) {\n    CompletionItemKind.Text = 1;\n    CompletionItemKind.Method = 2;\n    CompletionItemKind.Function = 3;\n    CompletionItemKind.Constructor = 4;\n    CompletionItemKind.Field = 5;\n    CompletionItemKind.Variable = 6;\n    CompletionItemKind.Class = 7;\n    CompletionItemKind.Interface = 8;\n    CompletionItemKind.Module = 9;\n    CompletionItemKind.Property = 10;\n    CompletionItemKind.Unit = 11;\n    CompletionItemKind.Value = 12;\n    CompletionItemKind.Enum = 13;\n    CompletionItemKind.Keyword = 14;\n    CompletionItemKind.Snippet = 15;\n    CompletionItemKind.Color = 16;\n    CompletionItemKind.File = 17;\n    CompletionItemKind.Reference = 18;\n    CompletionItemKind.Folder = 19;\n    CompletionItemKind.EnumMember = 20;\n    CompletionItemKind.Constant = 21;\n    CompletionItemKind.Struct = 22;\n    CompletionItemKind.Event = 23;\n    CompletionItemKind.Operator = 24;\n    CompletionItemKind.TypeParameter = 25;\n})(CompletionItemKind || (CompletionItemKind = {}));\n/**\n * Defines whether the insert text in a completion item should be interpreted as\n * plain text or a snippet.\n */\nexport var InsertTextFormat;\n(function (InsertTextFormat) {\n    /**\n     * The primary text to be inserted is treated as a plain string.\n     */\n    InsertTextFormat.PlainText = 1;\n    /**\n     * The primary text to be inserted is treated as a snippet.\n     *\n     * A snippet can define tab stops and placeholders with `$1`, `$2`\n     * and `${3:foo}`. `$0` defines the final tab stop, it defaults to\n     * the end of the snippet. Placeholders with equal identifiers are linked,\n     * that is typing in one will update others too.\n     *\n     * See also: https://microsoft.github.io/language-server-protocol/specifications/specification-current/#snippet_syntax\n     */\n    InsertTextFormat.Snippet = 2;\n})(InsertTextFormat || (InsertTextFormat = {}));\n/**\n * Completion item tags are extra annotations that tweak the rendering of a completion\n * item.\n *\n * @since 3.15.0\n */\nexport var CompletionItemTag;\n(function (CompletionItemTag) {\n    /**\n     * Render a completion as obsolete, usually using a strike-out.\n     */\n    CompletionItemTag.Deprecated = 1;\n})(CompletionItemTag || (CompletionItemTag = {}));\n/**\n * The InsertReplaceEdit namespace provides functions to deal with insert / replace edits.\n *\n * @since 3.16.0\n */\nexport var InsertReplaceEdit;\n(function (InsertReplaceEdit) {\n    /**\n     * Creates a new insert / replace edit\n     */\n    function create(newText, insert, replace) {\n        return { newText, insert, replace };\n    }\n    InsertReplaceEdit.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link InsertReplaceEdit} interface.\n     */\n    function is(value) {\n        const candidate = value;\n        return candidate && Is.string(candidate.newText) && Range.is(candidate.insert) && Range.is(candidate.replace);\n    }\n    InsertReplaceEdit.is = is;\n})(InsertReplaceEdit || (InsertReplaceEdit = {}));\n/**\n * How whitespace and indentation is handled during completion\n * item insertion.\n *\n * @since 3.16.0\n */\nexport var InsertTextMode;\n(function (InsertTextMode) {\n    /**\n     * The insertion or replace strings is taken as it is. If the\n     * value is multi line the lines below the cursor will be\n     * inserted using the indentation defined in the string value.\n     * The client will not apply any kind of adjustments to the\n     * string.\n     */\n    InsertTextMode.asIs = 1;\n    /**\n     * The editor adjusts leading whitespace of new lines so that\n     * they match the indentation up to the cursor of the line for\n     * which the item is accepted.\n     *\n     * Consider a line like this: <2tabs><cursor><3tabs>foo. Accepting a\n     * multi line completion item is indented using 2 tabs and all\n     * following lines inserted will be indented using 2 tabs as well.\n     */\n    InsertTextMode.adjustIndentation = 2;\n})(InsertTextMode || (InsertTextMode = {}));\nexport var CompletionItemLabelDetails;\n(function (CompletionItemLabelDetails) {\n    function is(value) {\n        const candidate = value;\n        return candidate && (Is.string(candidate.detail) || candidate.detail === undefined) &&\n            (Is.string(candidate.description) || candidate.description === undefined);\n    }\n    CompletionItemLabelDetails.is = is;\n})(CompletionItemLabelDetails || (CompletionItemLabelDetails = {}));\n/**\n * The CompletionItem namespace provides functions to deal with\n * completion items.\n */\nexport var CompletionItem;\n(function (CompletionItem) {\n    /**\n     * Create a completion item and seed it with a label.\n     * @param label The completion item's label\n     */\n    function create(label) {\n        return { label };\n    }\n    CompletionItem.create = create;\n})(CompletionItem || (CompletionItem = {}));\n/**\n * The CompletionList namespace provides functions to deal with\n * completion lists.\n */\nexport var CompletionList;\n(function (CompletionList) {\n    /**\n     * Creates a new completion list.\n     *\n     * @param items The completion items.\n     * @param isIncomplete The list is not complete.\n     */\n    function create(items, isIncomplete) {\n        return { items: items ? items : [], isIncomplete: !!isIncomplete };\n    }\n    CompletionList.create = create;\n})(CompletionList || (CompletionList = {}));\nexport var MarkedString;\n(function (MarkedString) {\n    /**\n     * Creates a marked string from plain text.\n     *\n     * @param plainText The plain text.\n     */\n    function fromPlainText(plainText) {\n        return plainText.replace(/[\\\\`*_{}[\\]()#+\\-.!]/g, '\\\\$&'); // escape markdown syntax tokens: http://daringfireball.net/projects/markdown/syntax#backslash\n    }\n    MarkedString.fromPlainText = fromPlainText;\n    /**\n     * Checks whether the given value conforms to the {@link MarkedString} type.\n     */\n    function is(value) {\n        const candidate = value;\n        return Is.string(candidate) || (Is.objectLiteral(candidate) && Is.string(candidate.language) && Is.string(candidate.value));\n    }\n    MarkedString.is = is;\n})(MarkedString || (MarkedString = {}));\nexport var Hover;\n(function (Hover) {\n    /**\n     * Checks whether the given value conforms to the {@link Hover} interface.\n     */\n    function is(value) {\n        let candidate = value;\n        return !!candidate && Is.objectLiteral(candidate) && (MarkupContent.is(candidate.contents) ||\n            MarkedString.is(candidate.contents) ||\n            Is.typedArray(candidate.contents, MarkedString.is)) && (value.range === undefined || Range.is(value.range));\n    }\n    Hover.is = is;\n})(Hover || (Hover = {}));\n/**\n * The ParameterInformation namespace provides helper functions to work with\n * {@link ParameterInformation} literals.\n */\nexport var ParameterInformation;\n(function (ParameterInformation) {\n    /**\n     * Creates a new parameter information literal.\n     *\n     * @param label A label string.\n     * @param documentation A doc string.\n     */\n    function create(label, documentation) {\n        return documentation ? { label, documentation } : { label };\n    }\n    ParameterInformation.create = create;\n})(ParameterInformation || (ParameterInformation = {}));\n/**\n * The SignatureInformation namespace provides helper functions to work with\n * {@link SignatureInformation} literals.\n */\nexport var SignatureInformation;\n(function (SignatureInformation) {\n    function create(label, documentation, ...parameters) {\n        let result = { label };\n        if (Is.defined(documentation)) {\n            result.documentation = documentation;\n        }\n        if (Is.defined(parameters)) {\n            result.parameters = parameters;\n        }\n        else {\n            result.parameters = [];\n        }\n        return result;\n    }\n    SignatureInformation.create = create;\n})(SignatureInformation || (SignatureInformation = {}));\n/**\n * A document highlight kind.\n */\nexport var DocumentHighlightKind;\n(function (DocumentHighlightKind) {\n    /**\n     * A textual occurrence.\n     */\n    DocumentHighlightKind.Text = 1;\n    /**\n     * Read-access of a symbol, like reading a variable.\n     */\n    DocumentHighlightKind.Read = 2;\n    /**\n     * Write-access of a symbol, like writing to a variable.\n     */\n    DocumentHighlightKind.Write = 3;\n})(DocumentHighlightKind || (DocumentHighlightKind = {}));\n/**\n * DocumentHighlight namespace to provide helper functions to work with\n * {@link DocumentHighlight} literals.\n */\nexport var DocumentHighlight;\n(function (DocumentHighlight) {\n    /**\n     * Create a DocumentHighlight object.\n     * @param range The range the highlight applies to.\n     * @param kind The highlight kind\n     */\n    function create(range, kind) {\n        let result = { range };\n        if (Is.number(kind)) {\n            result.kind = kind;\n        }\n        return result;\n    }\n    DocumentHighlight.create = create;\n})(DocumentHighlight || (DocumentHighlight = {}));\n/**\n * A symbol kind.\n */\nexport var SymbolKind;\n(function (SymbolKind) {\n    SymbolKind.File = 1;\n    SymbolKind.Module = 2;\n    SymbolKind.Namespace = 3;\n    SymbolKind.Package = 4;\n    SymbolKind.Class = 5;\n    SymbolKind.Method = 6;\n    SymbolKind.Property = 7;\n    SymbolKind.Field = 8;\n    SymbolKind.Constructor = 9;\n    SymbolKind.Enum = 10;\n    SymbolKind.Interface = 11;\n    SymbolKind.Function = 12;\n    SymbolKind.Variable = 13;\n    SymbolKind.Constant = 14;\n    SymbolKind.String = 15;\n    SymbolKind.Number = 16;\n    SymbolKind.Boolean = 17;\n    SymbolKind.Array = 18;\n    SymbolKind.Object = 19;\n    SymbolKind.Key = 20;\n    SymbolKind.Null = 21;\n    SymbolKind.EnumMember = 22;\n    SymbolKind.Struct = 23;\n    SymbolKind.Event = 24;\n    SymbolKind.Operator = 25;\n    SymbolKind.TypeParameter = 26;\n})(SymbolKind || (SymbolKind = {}));\n/**\n * Symbol tags are extra annotations that tweak the rendering of a symbol.\n *\n * @since 3.16\n */\nexport var SymbolTag;\n(function (SymbolTag) {\n    /**\n     * Render a symbol as obsolete, usually using a strike-out.\n     */\n    SymbolTag.Deprecated = 1;\n})(SymbolTag || (SymbolTag = {}));\nexport var SymbolInformation;\n(function (SymbolInformation) {\n    /**\n     * Creates a new symbol information literal.\n     *\n     * @param name The name of the symbol.\n     * @param kind The kind of the symbol.\n     * @param range The range of the location of the symbol.\n     * @param uri The resource of the location of symbol.\n     * @param containerName The name of the symbol containing the symbol.\n     */\n    function create(name, kind, range, uri, containerName) {\n        let result = {\n            name,\n            kind,\n            location: { uri, range }\n        };\n        if (containerName) {\n            result.containerName = containerName;\n        }\n        return result;\n    }\n    SymbolInformation.create = create;\n})(SymbolInformation || (SymbolInformation = {}));\nexport var WorkspaceSymbol;\n(function (WorkspaceSymbol) {\n    /**\n     * Create a new workspace symbol.\n     *\n     * @param name The name of the symbol.\n     * @param kind The kind of the symbol.\n     * @param uri The resource of the location of the symbol.\n     * @param range An options range of the location.\n     * @returns A WorkspaceSymbol.\n     */\n    function create(name, kind, uri, range) {\n        return range !== undefined\n            ? { name, kind, location: { uri, range } }\n            : { name, kind, location: { uri } };\n    }\n    WorkspaceSymbol.create = create;\n})(WorkspaceSymbol || (WorkspaceSymbol = {}));\nexport var DocumentSymbol;\n(function (DocumentSymbol) {\n    /**\n     * Creates a new symbol information literal.\n     *\n     * @param name The name of the symbol.\n     * @param detail The detail of the symbol.\n     * @param kind The kind of the symbol.\n     * @param range The range of the symbol.\n     * @param selectionRange The selectionRange of the symbol.\n     * @param children Children of the symbol.\n     */\n    function create(name, detail, kind, range, selectionRange, children) {\n        let result = {\n            name,\n            detail,\n            kind,\n            range,\n            selectionRange\n        };\n        if (children !== undefined) {\n            result.children = children;\n        }\n        return result;\n    }\n    DocumentSymbol.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link DocumentSymbol} interface.\n     */\n    function is(value) {\n        let candidate = value;\n        return candidate &&\n            Is.string(candidate.name) && Is.number(candidate.kind) &&\n            Range.is(candidate.range) && Range.is(candidate.selectionRange) &&\n            (candidate.detail === undefined || Is.string(candidate.detail)) &&\n            (candidate.deprecated === undefined || Is.boolean(candidate.deprecated)) &&\n            (candidate.children === undefined || Array.isArray(candidate.children)) &&\n            (candidate.tags === undefined || Array.isArray(candidate.tags));\n    }\n    DocumentSymbol.is = is;\n})(DocumentSymbol || (DocumentSymbol = {}));\n/**\n * A set of predefined code action kinds\n */\nexport var CodeActionKind;\n(function (CodeActionKind) {\n    /**\n     * Empty kind.\n     */\n    CodeActionKind.Empty = '';\n    /**\n     * Base kind for quickfix actions: 'quickfix'\n     */\n    CodeActionKind.QuickFix = 'quickfix';\n    /**\n     * Base kind for refactoring actions: 'refactor'\n     */\n    CodeActionKind.Refactor = 'refactor';\n    /**\n     * Base kind for refactoring extraction actions: 'refactor.extract'\n     *\n     * Example extract actions:\n     *\n     * - Extract method\n     * - Extract function\n     * - Extract variable\n     * - Extract interface from class\n     * - ...\n     */\n    CodeActionKind.RefactorExtract = 'refactor.extract';\n    /**\n     * Base kind for refactoring inline actions: 'refactor.inline'\n     *\n     * Example inline actions:\n     *\n     * - Inline function\n     * - Inline variable\n     * - Inline constant\n     * - ...\n     */\n    CodeActionKind.RefactorInline = 'refactor.inline';\n    /**\n     * Base kind for refactoring rewrite actions: 'refactor.rewrite'\n     *\n     * Example rewrite actions:\n     *\n     * - Convert JavaScript function to class\n     * - Add or remove parameter\n     * - Encapsulate field\n     * - Make method static\n     * - Move method to base class\n     * - ...\n     */\n    CodeActionKind.RefactorRewrite = 'refactor.rewrite';\n    /**\n     * Base kind for source actions: `source`\n     *\n     * Source code actions apply to the entire file.\n     */\n    CodeActionKind.Source = 'source';\n    /**\n     * Base kind for an organize imports source action: `source.organizeImports`\n     */\n    CodeActionKind.SourceOrganizeImports = 'source.organizeImports';\n    /**\n     * Base kind for auto-fix source actions: `source.fixAll`.\n     *\n     * Fix all actions automatically fix errors that have a clear fix that do not require user input.\n     * They should not suppress errors or perform unsafe fixes such as generating new types or classes.\n     *\n     * @since 3.15.0\n     */\n    CodeActionKind.SourceFixAll = 'source.fixAll';\n})(CodeActionKind || (CodeActionKind = {}));\n/**\n * The reason why code actions were requested.\n *\n * @since 3.17.0\n */\nexport var CodeActionTriggerKind;\n(function (CodeActionTriggerKind) {\n    /**\n     * Code actions were explicitly requested by the user or by an extension.\n     */\n    CodeActionTriggerKind.Invoked = 1;\n    /**\n     * Code actions were requested automatically.\n     *\n     * This typically happens when current selection in a file changes, but can\n     * also be triggered when file content changes.\n     */\n    CodeActionTriggerKind.Automatic = 2;\n})(CodeActionTriggerKind || (CodeActionTriggerKind = {}));\n/**\n * The CodeActionContext namespace provides helper functions to work with\n * {@link CodeActionContext} literals.\n */\nexport var CodeActionContext;\n(function (CodeActionContext) {\n    /**\n     * Creates a new CodeActionContext literal.\n     */\n    function create(diagnostics, only, triggerKind) {\n        let result = { diagnostics };\n        if (only !== undefined && only !== null) {\n            result.only = only;\n        }\n        if (triggerKind !== undefined && triggerKind !== null) {\n            result.triggerKind = triggerKind;\n        }\n        return result;\n    }\n    CodeActionContext.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link CodeActionContext} interface.\n     */\n    function is(value) {\n        let candidate = value;\n        return Is.defined(candidate) && Is.typedArray(candidate.diagnostics, Diagnostic.is)\n            && (candidate.only === undefined || Is.typedArray(candidate.only, Is.string))\n            && (candidate.triggerKind === undefined || candidate.triggerKind === CodeActionTriggerKind.Invoked || candidate.triggerKind === CodeActionTriggerKind.Automatic);\n    }\n    CodeActionContext.is = is;\n})(CodeActionContext || (CodeActionContext = {}));\nexport var CodeAction;\n(function (CodeAction) {\n    function create(title, kindOrCommandOrEdit, kind) {\n        let result = { title };\n        let checkKind = true;\n        if (typeof kindOrCommandOrEdit === 'string') {\n            checkKind = false;\n            result.kind = kindOrCommandOrEdit;\n        }\n        else if (Command.is(kindOrCommandOrEdit)) {\n            result.command = kindOrCommandOrEdit;\n        }\n        else {\n            result.edit = kindOrCommandOrEdit;\n        }\n        if (checkKind && kind !== undefined) {\n            result.kind = kind;\n        }\n        return result;\n    }\n    CodeAction.create = create;\n    function is(value) {\n        let candidate = value;\n        return candidate && Is.string(candidate.title) &&\n            (candidate.diagnostics === undefined || Is.typedArray(candidate.diagnostics, Diagnostic.is)) &&\n            (candidate.kind === undefined || Is.string(candidate.kind)) &&\n            (candidate.edit !== undefined || candidate.command !== undefined) &&\n            (candidate.command === undefined || Command.is(candidate.command)) &&\n            (candidate.isPreferred === undefined || Is.boolean(candidate.isPreferred)) &&\n            (candidate.edit === undefined || WorkspaceEdit.is(candidate.edit));\n    }\n    CodeAction.is = is;\n})(CodeAction || (CodeAction = {}));\n/**\n * The CodeLens namespace provides helper functions to work with\n * {@link CodeLens} literals.\n */\nexport var CodeLens;\n(function (CodeLens) {\n    /**\n     * Creates a new CodeLens literal.\n     */\n    function create(range, data) {\n        let result = { range };\n        if (Is.defined(data)) {\n            result.data = data;\n        }\n        return result;\n    }\n    CodeLens.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link CodeLens} interface.\n     */\n    function is(value) {\n        let candidate = value;\n        return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.command) || Command.is(candidate.command));\n    }\n    CodeLens.is = is;\n})(CodeLens || (CodeLens = {}));\n/**\n * The FormattingOptions namespace provides helper functions to work with\n * {@link FormattingOptions} literals.\n */\nexport var FormattingOptions;\n(function (FormattingOptions) {\n    /**\n     * Creates a new FormattingOptions literal.\n     */\n    function create(tabSize, insertSpaces) {\n        return { tabSize, insertSpaces };\n    }\n    FormattingOptions.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link FormattingOptions} interface.\n     */\n    function is(value) {\n        let candidate = value;\n        return Is.defined(candidate) && Is.uinteger(candidate.tabSize) && Is.boolean(candidate.insertSpaces);\n    }\n    FormattingOptions.is = is;\n})(FormattingOptions || (FormattingOptions = {}));\n/**\n * The DocumentLink namespace provides helper functions to work with\n * {@link DocumentLink} literals.\n */\nexport var DocumentLink;\n(function (DocumentLink) {\n    /**\n     * Creates a new DocumentLink literal.\n     */\n    function create(range, target, data) {\n        return { range, target, data };\n    }\n    DocumentLink.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link DocumentLink} interface.\n     */\n    function is(value) {\n        let candidate = value;\n        return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.target) || Is.string(candidate.target));\n    }\n    DocumentLink.is = is;\n})(DocumentLink || (DocumentLink = {}));\n/**\n * The SelectionRange namespace provides helper function to work with\n * SelectionRange literals.\n */\nexport var SelectionRange;\n(function (SelectionRange) {\n    /**\n     * Creates a new SelectionRange\n     * @param range the range.\n     * @param parent an optional parent.\n     */\n    function create(range, parent) {\n        return { range, parent };\n    }\n    SelectionRange.create = create;\n    function is(value) {\n        let candidate = value;\n        return Is.objectLiteral(candidate) && Range.is(candidate.range) && (candidate.parent === undefined || SelectionRange.is(candidate.parent));\n    }\n    SelectionRange.is = is;\n})(SelectionRange || (SelectionRange = {}));\n/**\n * A set of predefined token types. This set is not fixed\n * an clients can specify additional token types via the\n * corresponding client capabilities.\n *\n * @since 3.16.0\n */\nexport var SemanticTokenTypes;\n(function (SemanticTokenTypes) {\n    SemanticTokenTypes[\"namespace\"] = \"namespace\";\n    /**\n     * Represents a generic type. Acts as a fallback for types which can't be mapped to\n     * a specific type like class or enum.\n     */\n    SemanticTokenTypes[\"type\"] = \"type\";\n    SemanticTokenTypes[\"class\"] = \"class\";\n    SemanticTokenTypes[\"enum\"] = \"enum\";\n    SemanticTokenTypes[\"interface\"] = \"interface\";\n    SemanticTokenTypes[\"struct\"] = \"struct\";\n    SemanticTokenTypes[\"typeParameter\"] = \"typeParameter\";\n    SemanticTokenTypes[\"parameter\"] = \"parameter\";\n    SemanticTokenTypes[\"variable\"] = \"variable\";\n    SemanticTokenTypes[\"property\"] = \"property\";\n    SemanticTokenTypes[\"enumMember\"] = \"enumMember\";\n    SemanticTokenTypes[\"event\"] = \"event\";\n    SemanticTokenTypes[\"function\"] = \"function\";\n    SemanticTokenTypes[\"method\"] = \"method\";\n    SemanticTokenTypes[\"macro\"] = \"macro\";\n    SemanticTokenTypes[\"keyword\"] = \"keyword\";\n    SemanticTokenTypes[\"modifier\"] = \"modifier\";\n    SemanticTokenTypes[\"comment\"] = \"comment\";\n    SemanticTokenTypes[\"string\"] = \"string\";\n    SemanticTokenTypes[\"number\"] = \"number\";\n    SemanticTokenTypes[\"regexp\"] = \"regexp\";\n    SemanticTokenTypes[\"operator\"] = \"operator\";\n    /**\n     * @since 3.17.0\n     */\n    SemanticTokenTypes[\"decorator\"] = \"decorator\";\n})(SemanticTokenTypes || (SemanticTokenTypes = {}));\n/**\n * A set of predefined token modifiers. This set is not fixed\n * an clients can specify additional token types via the\n * corresponding client capabilities.\n *\n * @since 3.16.0\n */\nexport var SemanticTokenModifiers;\n(function (SemanticTokenModifiers) {\n    SemanticTokenModifiers[\"declaration\"] = \"declaration\";\n    SemanticTokenModifiers[\"definition\"] = \"definition\";\n    SemanticTokenModifiers[\"readonly\"] = \"readonly\";\n    SemanticTokenModifiers[\"static\"] = \"static\";\n    SemanticTokenModifiers[\"deprecated\"] = \"deprecated\";\n    SemanticTokenModifiers[\"abstract\"] = \"abstract\";\n    SemanticTokenModifiers[\"async\"] = \"async\";\n    SemanticTokenModifiers[\"modification\"] = \"modification\";\n    SemanticTokenModifiers[\"documentation\"] = \"documentation\";\n    SemanticTokenModifiers[\"defaultLibrary\"] = \"defaultLibrary\";\n})(SemanticTokenModifiers || (SemanticTokenModifiers = {}));\n/**\n * @since 3.16.0\n */\nexport var SemanticTokens;\n(function (SemanticTokens) {\n    function is(value) {\n        const candidate = value;\n        return Is.objectLiteral(candidate) && (candidate.resultId === undefined || typeof candidate.resultId === 'string') &&\n            Array.isArray(candidate.data) && (candidate.data.length === 0 || typeof candidate.data[0] === 'number');\n    }\n    SemanticTokens.is = is;\n})(SemanticTokens || (SemanticTokens = {}));\n/**\n * The InlineValueText namespace provides functions to deal with InlineValueTexts.\n *\n * @since 3.17.0\n */\nexport var InlineValueText;\n(function (InlineValueText) {\n    /**\n     * Creates a new InlineValueText literal.\n     */\n    function create(range, text) {\n        return { range, text };\n    }\n    InlineValueText.create = create;\n    function is(value) {\n        const candidate = value;\n        return candidate !== undefined && candidate !== null && Range.is(candidate.range) && Is.string(candidate.text);\n    }\n    InlineValueText.is = is;\n})(InlineValueText || (InlineValueText = {}));\n/**\n * The InlineValueVariableLookup namespace provides functions to deal with InlineValueVariableLookups.\n *\n * @since 3.17.0\n */\nexport var InlineValueVariableLookup;\n(function (InlineValueVariableLookup) {\n    /**\n     * Creates a new InlineValueText literal.\n     */\n    function create(range, variableName, caseSensitiveLookup) {\n        return { range, variableName, caseSensitiveLookup };\n    }\n    InlineValueVariableLookup.create = create;\n    function is(value) {\n        const candidate = value;\n        return candidate !== undefined && candidate !== null && Range.is(candidate.range) && Is.boolean(candidate.caseSensitiveLookup)\n            && (Is.string(candidate.variableName) || candidate.variableName === undefined);\n    }\n    InlineValueVariableLookup.is = is;\n})(InlineValueVariableLookup || (InlineValueVariableLookup = {}));\n/**\n * The InlineValueEvaluatableExpression namespace provides functions to deal with InlineValueEvaluatableExpression.\n *\n * @since 3.17.0\n */\nexport var InlineValueEvaluatableExpression;\n(function (InlineValueEvaluatableExpression) {\n    /**\n     * Creates a new InlineValueEvaluatableExpression literal.\n     */\n    function create(range, expression) {\n        return { range, expression };\n    }\n    InlineValueEvaluatableExpression.create = create;\n    function is(value) {\n        const candidate = value;\n        return candidate !== undefined && candidate !== null && Range.is(candidate.range)\n            && (Is.string(candidate.expression) || candidate.expression === undefined);\n    }\n    InlineValueEvaluatableExpression.is = is;\n})(InlineValueEvaluatableExpression || (InlineValueEvaluatableExpression = {}));\n/**\n * The InlineValueContext namespace provides helper functions to work with\n * {@link InlineValueContext} literals.\n *\n * @since 3.17.0\n */\nexport var InlineValueContext;\n(function (InlineValueContext) {\n    /**\n     * Creates a new InlineValueContext literal.\n     */\n    function create(frameId, stoppedLocation) {\n        return { frameId, stoppedLocation };\n    }\n    InlineValueContext.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link InlineValueContext} interface.\n     */\n    function is(value) {\n        const candidate = value;\n        return Is.defined(candidate) && Range.is(value.stoppedLocation);\n    }\n    InlineValueContext.is = is;\n})(InlineValueContext || (InlineValueContext = {}));\n/**\n * Inlay hint kinds.\n *\n * @since 3.17.0\n */\nexport var InlayHintKind;\n(function (InlayHintKind) {\n    /**\n     * An inlay hint that for a type annotation.\n     */\n    InlayHintKind.Type = 1;\n    /**\n     * An inlay hint that is for a parameter.\n     */\n    InlayHintKind.Parameter = 2;\n    function is(value) {\n        return value === 1 || value === 2;\n    }\n    InlayHintKind.is = is;\n})(InlayHintKind || (InlayHintKind = {}));\nexport var InlayHintLabelPart;\n(function (InlayHintLabelPart) {\n    function create(value) {\n        return { value };\n    }\n    InlayHintLabelPart.create = create;\n    function is(value) {\n        const candidate = value;\n        return Is.objectLiteral(candidate)\n            && (candidate.tooltip === undefined || Is.string(candidate.tooltip) || MarkupContent.is(candidate.tooltip))\n            && (candidate.location === undefined || Location.is(candidate.location))\n            && (candidate.command === undefined || Command.is(candidate.command));\n    }\n    InlayHintLabelPart.is = is;\n})(InlayHintLabelPart || (InlayHintLabelPart = {}));\nexport var InlayHint;\n(function (InlayHint) {\n    function create(position, label, kind) {\n        const result = { position, label };\n        if (kind !== undefined) {\n            result.kind = kind;\n        }\n        return result;\n    }\n    InlayHint.create = create;\n    function is(value) {\n        const candidate = value;\n        return Is.objectLiteral(candidate) && Position.is(candidate.position)\n            && (Is.string(candidate.label) || Is.typedArray(candidate.label, InlayHintLabelPart.is))\n            && (candidate.kind === undefined || InlayHintKind.is(candidate.kind))\n            && (candidate.textEdits === undefined) || Is.typedArray(candidate.textEdits, TextEdit.is)\n            && (candidate.tooltip === undefined || Is.string(candidate.tooltip) || MarkupContent.is(candidate.tooltip))\n            && (candidate.paddingLeft === undefined || Is.boolean(candidate.paddingLeft))\n            && (candidate.paddingRight === undefined || Is.boolean(candidate.paddingRight));\n    }\n    InlayHint.is = is;\n})(InlayHint || (InlayHint = {}));\nexport var StringValue;\n(function (StringValue) {\n    function createSnippet(value) {\n        return { kind: 'snippet', value };\n    }\n    StringValue.createSnippet = createSnippet;\n})(StringValue || (StringValue = {}));\nexport var InlineCompletionItem;\n(function (InlineCompletionItem) {\n    function create(insertText, filterText, range, command) {\n        return { insertText, filterText, range, command };\n    }\n    InlineCompletionItem.create = create;\n})(InlineCompletionItem || (InlineCompletionItem = {}));\nexport var InlineCompletionList;\n(function (InlineCompletionList) {\n    function create(items) {\n        return { items };\n    }\n    InlineCompletionList.create = create;\n})(InlineCompletionList || (InlineCompletionList = {}));\n/**\n * Describes how an {@link InlineCompletionItemProvider inline completion provider} was triggered.\n *\n * @since 3.18.0\n * @proposed\n */\nexport var InlineCompletionTriggerKind;\n(function (InlineCompletionTriggerKind) {\n    /**\n     * Completion was triggered explicitly by a user gesture.\n     */\n    InlineCompletionTriggerKind.Invoked = 0;\n    /**\n     * Completion was triggered automatically while editing.\n     */\n    InlineCompletionTriggerKind.Automatic = 1;\n})(InlineCompletionTriggerKind || (InlineCompletionTriggerKind = {}));\nexport var SelectedCompletionInfo;\n(function (SelectedCompletionInfo) {\n    function create(range, text) {\n        return { range, text };\n    }\n    SelectedCompletionInfo.create = create;\n})(SelectedCompletionInfo || (SelectedCompletionInfo = {}));\nexport var InlineCompletionContext;\n(function (InlineCompletionContext) {\n    function create(triggerKind, selectedCompletionInfo) {\n        return { triggerKind, selectedCompletionInfo };\n    }\n    InlineCompletionContext.create = create;\n})(InlineCompletionContext || (InlineCompletionContext = {}));\nexport var WorkspaceFolder;\n(function (WorkspaceFolder) {\n    function is(value) {\n        const candidate = value;\n        return Is.objectLiteral(candidate) && URI.is(candidate.uri) && Is.string(candidate.name);\n    }\n    WorkspaceFolder.is = is;\n})(WorkspaceFolder || (WorkspaceFolder = {}));\nexport const EOL = ['\\n', '\\r\\n', '\\r'];\n/**\n * @deprecated Use the text document from the new vscode-languageserver-textdocument package.\n */\nexport var TextDocument;\n(function (TextDocument) {\n    /**\n     * Creates a new ITextDocument literal from the given uri and content.\n     * @param uri The document's uri.\n     * @param languageId The document's language Id.\n     * @param version The document's version.\n     * @param content The document's content.\n     */\n    function create(uri, languageId, version, content) {\n        return new FullTextDocument(uri, languageId, version, content);\n    }\n    TextDocument.create = create;\n    /**\n     * Checks whether the given literal conforms to the {@link ITextDocument} interface.\n     */\n    function is(value) {\n        let candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.uri) && (Is.undefined(candidate.languageId) || Is.string(candidate.languageId)) && Is.uinteger(candidate.lineCount)\n            && Is.func(candidate.getText) && Is.func(candidate.positionAt) && Is.func(candidate.offsetAt) ? true : false;\n    }\n    TextDocument.is = is;\n    function applyEdits(document, edits) {\n        let text = document.getText();\n        let sortedEdits = mergeSort(edits, (a, b) => {\n            let diff = a.range.start.line - b.range.start.line;\n            if (diff === 0) {\n                return a.range.start.character - b.range.start.character;\n            }\n            return diff;\n        });\n        let lastModifiedOffset = text.length;\n        for (let i = sortedEdits.length - 1; i >= 0; i--) {\n            let e = sortedEdits[i];\n            let startOffset = document.offsetAt(e.range.start);\n            let endOffset = document.offsetAt(e.range.end);\n            if (endOffset <= lastModifiedOffset) {\n                text = text.substring(0, startOffset) + e.newText + text.substring(endOffset, text.length);\n            }\n            else {\n                throw new Error('Overlapping edit');\n            }\n            lastModifiedOffset = startOffset;\n        }\n        return text;\n    }\n    TextDocument.applyEdits = applyEdits;\n    function mergeSort(data, compare) {\n        if (data.length <= 1) {\n            // sorted\n            return data;\n        }\n        const p = (data.length / 2) | 0;\n        const left = data.slice(0, p);\n        const right = data.slice(p);\n        mergeSort(left, compare);\n        mergeSort(right, compare);\n        let leftIdx = 0;\n        let rightIdx = 0;\n        let i = 0;\n        while (leftIdx < left.length && rightIdx < right.length) {\n            let ret = compare(left[leftIdx], right[rightIdx]);\n            if (ret <= 0) {\n                // smaller_equal -> take left to preserve order\n                data[i++] = left[leftIdx++];\n            }\n            else {\n                // greater -> take right\n                data[i++] = right[rightIdx++];\n            }\n        }\n        while (leftIdx < left.length) {\n            data[i++] = left[leftIdx++];\n        }\n        while (rightIdx < right.length) {\n            data[i++] = right[rightIdx++];\n        }\n        return data;\n    }\n})(TextDocument || (TextDocument = {}));\n/**\n * @deprecated Use the text document from the new vscode-languageserver-textdocument package.\n */\nclass FullTextDocument {\n    constructor(uri, languageId, version, content) {\n        this._uri = uri;\n        this._languageId = languageId;\n        this._version = version;\n        this._content = content;\n        this._lineOffsets = undefined;\n    }\n    get uri() {\n        return this._uri;\n    }\n    get languageId() {\n        return this._languageId;\n    }\n    get version() {\n        return this._version;\n    }\n    getText(range) {\n        if (range) {\n            let start = this.offsetAt(range.start);\n            let end = this.offsetAt(range.end);\n            return this._content.substring(start, end);\n        }\n        return this._content;\n    }\n    update(event, version) {\n        this._content = event.text;\n        this._version = version;\n        this._lineOffsets = undefined;\n    }\n    getLineOffsets() {\n        if (this._lineOffsets === undefined) {\n            let lineOffsets = [];\n            let text = this._content;\n            let isLineStart = true;\n            for (let i = 0; i < text.length; i++) {\n                if (isLineStart) {\n                    lineOffsets.push(i);\n                    isLineStart = false;\n                }\n                let ch = text.charAt(i);\n                isLineStart = (ch === '\\r' || ch === '\\n');\n                if (ch === '\\r' && i + 1 < text.length && text.charAt(i + 1) === '\\n') {\n                    i++;\n                }\n            }\n            if (isLineStart && text.length > 0) {\n                lineOffsets.push(text.length);\n            }\n            this._lineOffsets = lineOffsets;\n        }\n        return this._lineOffsets;\n    }\n    positionAt(offset) {\n        offset = Math.max(Math.min(offset, this._content.length), 0);\n        let lineOffsets = this.getLineOffsets();\n        let low = 0, high = lineOffsets.length;\n        if (high === 0) {\n            return Position.create(0, offset);\n        }\n        while (low < high) {\n            let mid = Math.floor((low + high) / 2);\n            if (lineOffsets[mid] > offset) {\n                high = mid;\n            }\n            else {\n                low = mid + 1;\n            }\n        }\n        // low is the least x for which the line offset is larger than the current offset\n        // or array.length if no line offset is larger than the current offset\n        let line = low - 1;\n        return Position.create(line, offset - lineOffsets[line]);\n    }\n    offsetAt(position) {\n        let lineOffsets = this.getLineOffsets();\n        if (position.line >= lineOffsets.length) {\n            return this._content.length;\n        }\n        else if (position.line < 0) {\n            return 0;\n        }\n        let lineOffset = lineOffsets[position.line];\n        let nextLineOffset = (position.line + 1 < lineOffsets.length) ? lineOffsets[position.line + 1] : this._content.length;\n        return Math.max(Math.min(lineOffset + position.character, nextLineOffset), lineOffset);\n    }\n    get lineCount() {\n        return this.getLineOffsets().length;\n    }\n}\nvar Is;\n(function (Is) {\n    const toString = Object.prototype.toString;\n    function defined(value) {\n        return typeof value !== 'undefined';\n    }\n    Is.defined = defined;\n    function undefined(value) {\n        return typeof value === 'undefined';\n    }\n    Is.undefined = undefined;\n    function boolean(value) {\n        return value === true || value === false;\n    }\n    Is.boolean = boolean;\n    function string(value) {\n        return toString.call(value) === '[object String]';\n    }\n    Is.string = string;\n    function number(value) {\n        return toString.call(value) === '[object Number]';\n    }\n    Is.number = number;\n    function numberRange(value, min, max) {\n        return toString.call(value) === '[object Number]' && min <= value && value <= max;\n    }\n    Is.numberRange = numberRange;\n    function integer(value) {\n        return toString.call(value) === '[object Number]' && -********** <= value && value <= **********;\n    }\n    Is.integer = integer;\n    function uinteger(value) {\n        return toString.call(value) === '[object Number]' && 0 <= value && value <= **********;\n    }\n    Is.uinteger = uinteger;\n    function func(value) {\n        return toString.call(value) === '[object Function]';\n    }\n    Is.func = func;\n    function objectLiteral(value) {\n        // Strictly speaking class instances pass this check as well. Since the LSP\n        // doesn't use classes we ignore this for now. If we do we need to add something\n        // like this: `Object.getPrototypeOf(Object.getPrototypeOf(x)) === null`\n        return value !== null && typeof value === 'object';\n    }\n    Is.objectLiteral = objectLiteral;\n    function typedArray(value, check) {\n        return Array.isArray(value) && value.every(check);\n    }\n    Is.typedArray = typedArray;\n})(Is || (Is = {}));\n", "/**\n *  Copyright (c) 2021 GraphQL Contributors\n *  All rights reserved.\n *\n *  This source code is licensed under the license found in the\n *  LICENSE file in the root directory of this source tree.\n *\n */\nimport type {\n  Diagnostic as DiagnosticType,\n  CompletionItem as CompletionItemType,\n} from 'vscode-languageserver-types';\n\nexport { InsertTextFormat } from 'vscode-languageserver-types';\n\nimport type {\n  ASTNode,\n  GraphQLSchema,\n  DocumentNode,\n  FragmentDefinitionNode,\n  NamedTypeNode,\n  TypeDefinitionNode,\n  NameNode,\n  GraphQLArgument,\n  GraphQLEnumValue,\n  GraphQLField,\n  GraphQLInputFieldMap,\n  GraphQLInterfaceType,\n  GraphQLObjectType,\n  GraphQLType,\n  GraphQLDirective,\n} from 'graphql';\n\nexport type Maybe<T> = T | null | undefined;\n\nimport type {\n  GraphQLConfig,\n  GraphQLProjectConfig,\n  GraphQLExtensionDeclaration,\n} from 'graphql-config';\n\nexport { GraphQLDocumentMode } from './parser';\n\nexport type {\n  GraphQLConfig,\n  GraphQLProjectConfig,\n  GraphQLExtensionDeclaration,\n};\n\nexport interface GraphQLCache {\n  getGraphQLConfig: () => GraphQLConfig;\n\n  getProjectForFile: (uri: string) => GraphQLProjectConfig | void;\n\n  getObjectTypeDependenciesForAST: (\n    parsedQuery: ASTNode,\n    fragmentDefinitions: Map<string, ObjectTypeInfo>,\n  ) => Promise<ObjectTypeInfo[]>;\n\n  getObjectTypeDefinitions: (\n    graphQLConfig: GraphQLProjectConfig,\n  ) => Promise<Map<string, ObjectTypeInfo>>;\n\n  updateObjectTypeDefinition: (\n    rootDir: Uri,\n    filePath: Uri,\n    contents: CachedContent[],\n  ) => Promise<void>;\n\n  getFragmentDependencies: (\n    query: string,\n    fragmentDefinitions: Maybe<Map<string, FragmentInfo>>,\n  ) => Promise<FragmentInfo[]>;\n\n  getFragmentDependenciesForAST: (\n    parsedQuery: ASTNode,\n    fragmentDefinitions: Map<string, FragmentInfo>,\n  ) => Promise<FragmentInfo[]>;\n\n  getFragmentDefinitions: (\n    graphQLConfig: GraphQLProjectConfig,\n  ) => Promise<Map<string, FragmentInfo>>;\n\n  updateFragmentDefinition: (\n    rootDir: Uri,\n    filePath: Uri,\n    contents: CachedContent[],\n  ) => Promise<void>;\n  getSchema: (\n    appName: string,\n    queryHasExtensions?: boolean,\n  ) => Promise<GraphQLSchema | null>;\n}\n\n// online-parser related\nexport interface IPosition {\n  line: number;\n  character: number;\n  setLine(line: number): void;\n  setCharacter(character: number): void;\n  lessThanOrEqualTo(position: IPosition): boolean;\n}\n\nexport interface IRange {\n  start: IPosition;\n  end: IPosition;\n  setEnd(line: number, character: number): void;\n  setStart(line: number, character: number): void;\n  containsPosition(position: IPosition): boolean;\n}\nexport type CachedContent = {\n  query: string;\n  range: IRange | null;\n};\n\n// GraphQL Language Service related types\nexport type Uri = string;\n\nexport type GraphQLFileMetadata = {\n  filePath: Uri;\n  size: number;\n  mtime: number;\n};\n\nexport type GraphQLFileInfo = {\n  filePath: Uri;\n  content: string;\n  asts: DocumentNode[];\n  queries: CachedContent[];\n  size: number;\n  mtime: number;\n};\n\nexport type AllTypeInfo = {\n  type: Maybe<GraphQLType>;\n  parentType: Maybe<GraphQLType>;\n  inputType: Maybe<GraphQLType>;\n  directiveDef: Maybe<GraphQLDirective>;\n  fieldDef: Maybe<GraphQLField<any, any>>;\n  enumValue: Maybe<GraphQLEnumValue>;\n  argDef: Maybe<GraphQLArgument>;\n  argDefs: Maybe<GraphQLArgument[]>;\n  objectFieldDefs: Maybe<GraphQLInputFieldMap>;\n  interfaceDef: Maybe<GraphQLInterfaceType>;\n  objectTypeDef: Maybe<GraphQLObjectType>;\n};\n\nexport type FragmentInfo = {\n  filePath?: Uri;\n  content: string;\n  definition: FragmentDefinitionNode;\n};\n\nexport type NamedTypeInfo = {\n  filePath?: Uri;\n  content: string;\n  definition: NamedTypeNode;\n};\n\nexport type ObjectTypeInfo = {\n  filePath?: Uri;\n  content: string;\n  definition: TypeDefinitionNode;\n};\n\nexport type Diagnostic = DiagnosticType;\n\nexport type CompletionItemBase = {\n  label: string;\n  isDeprecated?: boolean;\n};\n\nexport type CompletionItem = CompletionItemType & {\n  isDeprecated?: boolean;\n  documentation?: string | null;\n  deprecationReason?: string | null;\n  type?: GraphQLType;\n  command?: CompletionItemType['command'];\n  // if label differs from what should be inserted\n  rawInsert?: string;\n};\n// Below are basically a copy-paste from Nuclide rpc types for definitions.\n\n// Definitions/hyperlink\nexport type Definition = {\n  path: Uri;\n  position: IPosition;\n  range?: IRange;\n  id?: string;\n  name?: string;\n  language?: string;\n  projectRoot?: Uri;\n  locator?: string;\n};\n\n// Outline view\nexport type TokenKind =\n  | 'keyword'\n  | 'class-name'\n  | 'constructor'\n  | 'method'\n  | 'param'\n  | 'string'\n  | 'whitespace'\n  | 'plain'\n  | 'type';\nexport type TextToken = {\n  kind: TokenKind;\n  value: string | NameNode;\n};\n\nexport type TokenizedText = TextToken[];\nexport type OutlineTree = {\n  // Must be one or the other. If both are present, tokenizedText is preferred.\n  plainText?: string;\n  tokenizedText?: TokenizedText;\n  representativeName?: string;\n  kind: string;\n  startPosition: IPosition;\n  endPosition?: IPosition;\n  children: OutlineTree[];\n};\n\nexport type Outline = {\n  outlineTrees: OutlineTree[];\n};\n\nexport interface FileEvent {\n  uri: string;\n  type: FileChangeType;\n}\n\nexport const FileChangeTypeKind = {\n  Created: 1,\n  Changed: 2,\n  Deleted: 3,\n};\n\nexport type FileChangeTypeKind = {\n  Created: 1;\n  Changed: 2;\n  Deleted: 3;\n};\n\nexport type FileChangeTypeKeys = keyof FileChangeTypeKind;\n\nexport type FileChangeType = FileChangeTypeKind[FileChangeTypeKeys];\n\n// copied from `microsoft/vscode-languageserver-types` to prevent import issues\n\n/**\n * The kind of completion entry.\n */\nexport namespace CompletionItemKind {\n  export const Text = 1;\n  export const Method = 2;\n  export const Function = 3;\n  export const Constructor = 4;\n  export const Field = 5;\n  export const Variable = 6;\n  export const Class = 7;\n  export const Interface = 8;\n  export const Module = 9;\n  export const Property = 10;\n  export const Unit = 11;\n  export const Value = 12;\n  export const Enum = 13;\n  export const Keyword = 14;\n  export const Snippet = 15;\n  export const Color = 16;\n  export const File = 17;\n  export const Reference = 18;\n  export const Folder = 19;\n  export const EnumMember = 20;\n  export const Constant = 21;\n  export const Struct = 22;\n  export const Event = 23;\n  export const Operator = 24;\n  export const TypeParameter = 25;\n}\n\nexport type CompletionItemKind =\n  | 1\n  | 2\n  | 3\n  | 4\n  | 5\n  | 6\n  | 7\n  | 8\n  | 9\n  | 10\n  | 11\n  | 12\n  | 13\n  | 14\n  | 15\n  | 16\n  | 17\n  | 18\n  | 19\n  | 20\n  | 21\n  | 22\n  | 23\n  | 24\n  | 25;\n", "/**\n *  Copyright (c) 2021 GraphQL Contributors\n *  All rights reserved.\n *\n *  This source code is licensed under the license found in the\n *  LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {\n  GraphQLField,\n  GraphQLType,\n  isListType,\n  isObjectType,\n  isInputObjectType,\n  getNamedType,\n  isAbstractType,\n} from 'graphql';\nimport { CompletionItemBase } from '../types';\nimport { ContextTokenUnion } from '../parser';\n\nexport function objectValues<T>(object: Record<string, T>): Array<T> {\n  const keys = Object.keys(object);\n  const len = keys.length;\n  const values = new Array(len);\n  for (let i = 0; i < len; ++i) {\n    values[i] = object[keys[i]];\n  }\n  return values;\n}\n\n// Create the expected hint response given a possible list and a token\nexport function hintList<T extends CompletionItemBase>(\n  token: ContextTokenUnion,\n  list: Array<T>,\n): Array<T> {\n  return filterAndSortList(list, normalizeText(token.string));\n}\n\n// Given a list of hint entries and currently typed text, sort and filter to\n// provide a concise list.\nfunction filterAndSortList<T extends CompletionItemBase>(\n  list: Array<T>,\n  text: string,\n): Array<T> {\n  if (\n    !text ||\n    text.trim() === '' ||\n    text.trim() === ':' ||\n    text.trim() === '{'\n  ) {\n    return filterNonEmpty<T>(list, entry => !entry.isDeprecated);\n  }\n\n  const byProximity = list.map(entry => ({\n    proximity: getProximity(normalizeText(entry.label), text),\n    entry,\n  }));\n\n  return filterNonEmpty(\n    filterNonEmpty(byProximity, pair => pair.proximity <= 2),\n    pair => !pair.entry.isDeprecated,\n  )\n    .sort(\n      (a, b) =>\n        (a.entry.isDeprecated ? 1 : 0) - (b.entry.isDeprecated ? 1 : 0) ||\n        a.proximity - b.proximity ||\n        a.entry.label.length - b.entry.label.length,\n    )\n    .map(pair => pair.entry);\n}\n\n// Filters the array by the predicate, unless it results in an empty array,\n// in which case return the original array.\nfunction filterNonEmpty<T>(\n  array: Array<T>,\n  predicate: (entry: T) => boolean,\n): Array<T> {\n  const filtered = array.filter(predicate);\n  return filtered.length === 0 ? array : filtered;\n}\n\nfunction normalizeText(text: string): string {\n  return text.toLowerCase().replaceAll(/\\W/g, '');\n}\n\n// Determine a numeric proximity for a suggestion based on current text.\nfunction getProximity(suggestion: string, text: string): number {\n  // start with lexical distance\n  let proximity = lexicalDistance(text, suggestion);\n  if (suggestion.length > text.length) {\n    // do not penalize long suggestions.\n    proximity -= suggestion.length - text.length - 1;\n    // penalize suggestions not starting with this phrase\n    proximity += suggestion.indexOf(text) === 0 ? 0 : 0.5;\n  }\n  return proximity;\n}\n\n/**\n * Computes the lexical distance between strings A and B.\n *\n * The \"distance\" between two strings is given by counting the minimum number\n * of edits needed to transform string A into string B. An edit can be an\n * insertion, deletion, or substitution of a single character, or a swap of two\n * adjacent characters.\n *\n * This distance can be useful for detecting typos in input or sorting\n *\n * @param {string} a\n * @param {string} b\n * @return {int} distance in number of edits\n */\nfunction lexicalDistance(a: string, b: string): number {\n  let i;\n  let j;\n  const d = [];\n  const aLength = a.length;\n  const bLength = b.length;\n\n  for (i = 0; i <= aLength; i++) {\n    d[i] = [i];\n  }\n\n  for (j = 1; j <= bLength; j++) {\n    d[0][j] = j;\n  }\n\n  for (i = 1; i <= aLength; i++) {\n    for (j = 1; j <= bLength; j++) {\n      const cost = a[i - 1] === b[j - 1] ? 0 : 1;\n\n      d[i][j] = Math.min(\n        d[i - 1][j] + 1,\n        d[i][j - 1] + 1,\n        d[i - 1][j - 1] + cost,\n      );\n\n      if (i > 1 && j > 1 && a[i - 1] === b[j - 2] && a[i - 2] === b[j - 1]) {\n        d[i][j] = Math.min(d[i][j], d[i - 2][j - 2] + cost);\n      }\n    }\n  }\n\n  return d[aLength][bLength];\n}\n\nconst insertSuffix = (n?: number) => ` {\\n   $${n ?? 1}\\n}`;\n\nexport const getInsertText = (\n  prefix: string,\n  type?: GraphQLType,\n  fallback?: string,\n): string => {\n  if (!type) {\n    return fallback ?? prefix;\n  }\n\n  const namedType = getNamedType(type);\n  if (\n    isObjectType(namedType) ||\n    isInputObjectType(namedType) ||\n    isListType(namedType) ||\n    isAbstractType(namedType)\n  ) {\n    return prefix + insertSuffix();\n  }\n\n  return fallback ?? prefix;\n};\n\nexport const getInputInsertText = (\n  prefix: string,\n  type: GraphQLType,\n  fallback?: string,\n): string => {\n  // if (isScalarType(type) && type.name === GraphQLString.name) {\n  //   return prefix + '\"$1\"';\n  // }\n  if (isListType(type)) {\n    const baseType = getNamedType(type.ofType);\n    return prefix + `[${getInsertText('', baseType, '$1')}]`;\n  }\n  return getInsertText(prefix, type, fallback);\n};\n\n/**\n * generates a TextSnippet for a field with possible required arguments\n * that dynamically adjusts to the number of required arguments\n * @param field\n * @returns\n */\nexport const getFieldInsertText = (field: GraphQLField<null, null>) => {\n  const requiredArgs = field.args.filter(arg =>\n    arg.type.toString().endsWith('!'),\n  );\n  if (!requiredArgs.length) {\n    return;\n  }\n  return (\n    field.name +\n    `(${requiredArgs.map(\n      (arg, i) => `${arg.name}: $${i + 1}`,\n    )}) ${getInsertText('', field.type, '\\n')}`\n  );\n};\n", "/**\n *  Copyright (c) 2021 GraphQL Contributors\n *  All rights reserved.\n *\n *  This source code is licensed under the license found in the\n *  LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {\n  FragmentDefinitionNode,\n  GraphQLDirective,\n  GraphQLSchema,\n  GraphQLType,\n  GraphQLCompositeType,\n  GraphQLEnumValue,\n  GraphQLField,\n  GraphQLFieldMap,\n  GraphQLNamedType,\n  isInterfaceType,\n  GraphQLInterfaceType,\n  GraphQLObjectType,\n  Kind,\n  DirectiveLocation,\n  GraphQLArgument,\n  // isNonNullType,\n  isScalarType,\n  isObjectType,\n  isUnionType,\n  isEnumType,\n  isInputObjectType,\n  isOutputType,\n  GraphQLBoolean,\n  GraphQLEnumType,\n  GraphQLInputObjectType,\n  SchemaMetaFieldDef,\n  TypeMetaFieldDef,\n  TypeNameMetaFieldDef,\n  assertAbstractType,\n  doTypesOverlap,\n  getNamedType,\n  isAbstractType,\n  isCompositeType,\n  isInputType,\n  visit,\n  parse,\n} from 'graphql';\n\nimport {\n  CompletionItem,\n  AllTypeInfo,\n  IPosition,\n  CompletionItemKind,\n  InsertTextFormat,\n} from '../types';\n\nimport type {\n  ContextToken,\n  State,\n  RuleKind,\n  ContextTokenForCodeMirror,\n} from '../parser';\nimport {\n  getTypeInfo,\n  runOnlineParser,\n  RuleKinds,\n  getContextAtPosition,\n  getDefinitionState,\n  GraphQLDocumentMode,\n} from '../parser';\nimport {\n  hintList,\n  objectValues,\n  getInputInsertText,\n  getFieldInsertText,\n  getInsertText,\n} from './autocompleteUtils';\n\nimport { InsertTextMode } from 'vscode-languageserver-types';\n\nexport { runOnlineParser, getTypeInfo };\n\nexport const SuggestionCommand = {\n  command: 'editor.action.triggerSuggest',\n  title: 'Suggestions',\n};\n\nconst collectFragmentDefs = (op: string | undefined) => {\n  const externalFragments: FragmentDefinitionNode[] = [];\n  if (op) {\n    try {\n      visit(parse(op), {\n        FragmentDefinition(def) {\n          externalFragments.push(def);\n        },\n      });\n    } catch {\n      return [];\n    }\n  }\n  return externalFragments;\n};\n\nexport type AutocompleteSuggestionOptions = {\n  /**\n   * EXPERIMENTAL: Automatically fill required leaf nodes recursively\n   * upon triggering code completion events.\n   *\n   *\n   * - [x] fills required nodes\n   * - [x] automatically expands relay-style node/edge fields\n   * - [ ] automatically jumps to first required argument field\n   *      - then, continues to prompt for required argument fields\n   *      - (fixing this will make it non-experimental)\n   *      - when it runs out of arguments, or you choose `{` as a completion option\n   *        that appears when all required arguments are supplied, the argument\n   *        selection closes `)` and the leaf field expands again `{ \\n| }`\n   */\n  fillLeafsOnComplete?: boolean;\n  uri?: string;\n  mode?: GraphQLDocumentMode;\n};\n\ntype InternalAutocompleteOptions = AutocompleteSuggestionOptions & {\n  schema?: GraphQLSchema;\n};\n\n/**\n * Given GraphQLSchema, queryText, and context of the current position within\n * the source text, provide a list of typeahead entries.\n */\nexport function getAutocompleteSuggestions(\n  schema: GraphQLSchema,\n  queryText: string,\n  cursor: IPosition,\n  contextToken?: ContextTokenForCodeMirror,\n  fragmentDefs?: FragmentDefinitionNode[] | string,\n  options?: AutocompleteSuggestionOptions,\n): Array<CompletionItem> {\n  const opts = {\n    ...options,\n    schema,\n  } as InternalAutocompleteOptions;\n\n  const context = getContextAtPosition(\n    queryText,\n    cursor,\n    schema,\n    contextToken,\n    options,\n  );\n  if (!context) {\n    return [];\n  }\n  const { state, typeInfo, mode, token } = context;\n\n  const { kind, step, prevState } = state;\n\n  // Definition kinds\n  if (kind === RuleKinds.DOCUMENT) {\n    if (mode === GraphQLDocumentMode.TYPE_SYSTEM) {\n      return getSuggestionsForTypeSystemDefinitions(token);\n    }\n    if (mode === GraphQLDocumentMode.EXECUTABLE) {\n      return getSuggestionsForExecutableDefinitions(token);\n    }\n    return getSuggestionsForUnknownDocumentMode(token);\n  }\n\n  if (kind === RuleKinds.EXTEND_DEF) {\n    return getSuggestionsForExtensionDefinitions(token);\n  }\n\n  if (\n    prevState?.prevState?.kind === RuleKinds.EXTENSION_DEFINITION &&\n    state.name\n  ) {\n    return hintList(token, []);\n  }\n\n  // extend scalar\n  if (prevState?.kind === Kind.SCALAR_TYPE_EXTENSION) {\n    return hintList(\n      token,\n      Object.values(schema.getTypeMap())\n        .filter(isScalarType)\n        .map(type => ({\n          label: type.name,\n          kind: CompletionItemKind.Function,\n        })),\n    );\n  }\n\n  // extend object type\n  if (prevState?.kind === Kind.OBJECT_TYPE_EXTENSION) {\n    return hintList(\n      token,\n      Object.values(schema.getTypeMap())\n        .filter(type => isObjectType(type) && !type.name.startsWith('__'))\n        .map(type => ({\n          label: type.name,\n          kind: CompletionItemKind.Function,\n        })),\n    );\n  }\n\n  // extend interface type\n  if (prevState?.kind === Kind.INTERFACE_TYPE_EXTENSION) {\n    return hintList(\n      token,\n      Object.values(schema.getTypeMap())\n        .filter(isInterfaceType)\n        .map(type => ({\n          label: type.name,\n          kind: CompletionItemKind.Function,\n        })),\n    );\n  }\n\n  // extend union type\n  if (prevState?.kind === Kind.UNION_TYPE_EXTENSION) {\n    return hintList(\n      token,\n      Object.values(schema.getTypeMap())\n        .filter(isUnionType)\n        .map(type => ({\n          label: type.name,\n          kind: CompletionItemKind.Function,\n        })),\n    );\n  }\n\n  // extend enum type\n  if (prevState?.kind === Kind.ENUM_TYPE_EXTENSION) {\n    return hintList(\n      token,\n      Object.values(schema.getTypeMap())\n        .filter(type => isEnumType(type) && !type.name.startsWith('__'))\n        .map(type => ({\n          label: type.name,\n          kind: CompletionItemKind.Function,\n        })),\n    );\n  }\n\n  // extend input object type\n  if (prevState?.kind === Kind.INPUT_OBJECT_TYPE_EXTENSION) {\n    return hintList(\n      token,\n      Object.values(schema.getTypeMap())\n        .filter(isInputObjectType)\n        .map(type => ({\n          label: type.name,\n          kind: CompletionItemKind.Function,\n        })),\n    );\n  }\n\n  if (\n    kind === RuleKinds.IMPLEMENTS ||\n    (kind === RuleKinds.NAMED_TYPE && prevState?.kind === RuleKinds.IMPLEMENTS)\n  ) {\n    return getSuggestionsForImplements(\n      token,\n      state,\n      schema,\n      queryText,\n      typeInfo,\n    );\n  }\n\n  // Field names\n  if (\n    kind === RuleKinds.SELECTION_SET ||\n    kind === RuleKinds.FIELD ||\n    kind === RuleKinds.ALIASED_FIELD\n  ) {\n    return getSuggestionsForFieldNames(token, typeInfo, opts);\n  }\n\n  // Argument names\n  if (\n    kind === RuleKinds.ARGUMENTS ||\n    (kind === RuleKinds.ARGUMENT && step === 0)\n  ) {\n    const { argDefs } = typeInfo;\n    if (argDefs) {\n      return hintList(\n        token,\n        argDefs.map(\n          (argDef: GraphQLArgument): CompletionItem => ({\n            label: argDef.name,\n            insertText: getInputInsertText(argDef.name + ': ', argDef.type),\n            insertTextMode: InsertTextMode.adjustIndentation,\n            insertTextFormat: InsertTextFormat.Snippet,\n            command: SuggestionCommand,\n            labelDetails: {\n              detail: ' ' + String(argDef.type),\n            },\n            documentation: argDef.description ?? undefined,\n            kind: CompletionItemKind.Variable,\n            type: argDef.type,\n          }),\n        ),\n      );\n    }\n  }\n\n  // Input Object fields\n  if (\n    (kind === RuleKinds.OBJECT_VALUE ||\n      (kind === RuleKinds.OBJECT_FIELD && step === 0)) &&\n    typeInfo.objectFieldDefs\n  ) {\n    const objectFields = objectValues(typeInfo.objectFieldDefs);\n    const completionKind =\n      kind === RuleKinds.OBJECT_VALUE\n        ? CompletionItemKind.Value\n        : CompletionItemKind.Field;\n    return hintList(\n      token,\n      objectFields.map(field => ({\n        label: field.name,\n        detail: String(field.type),\n        documentation: field?.description ?? undefined,\n        kind: completionKind,\n        type: field.type,\n        insertText: getInputInsertText(field.name + ': ', field.type),\n        insertTextMode: InsertTextMode.adjustIndentation,\n        insertTextFormat: InsertTextFormat.Snippet,\n        command: SuggestionCommand,\n      })),\n    );\n  }\n\n  // Input values: Enum and Boolean\n  if (\n    kind === RuleKinds.ENUM_VALUE ||\n    (kind === RuleKinds.LIST_VALUE && step === 1) ||\n    (kind === RuleKinds.OBJECT_FIELD && step === 2) ||\n    (kind === RuleKinds.ARGUMENT && step === 2)\n  ) {\n    return getSuggestionsForInputValues(token, typeInfo, queryText, schema);\n  }\n  // complete for all variables available in the query scoped to this\n  if (kind === RuleKinds.VARIABLE && step === 1) {\n    const namedInputType = getNamedType(typeInfo.inputType!);\n    const variableDefinitions = getVariableCompletions(\n      queryText,\n      schema,\n      token,\n    );\n    return hintList(\n      token,\n      variableDefinitions.filter(v => v.detail === namedInputType?.name),\n    );\n  }\n\n  // Fragment type conditions\n  if (\n    (kind === RuleKinds.TYPE_CONDITION && step === 1) ||\n    (kind === RuleKinds.NAMED_TYPE &&\n      prevState != null &&\n      prevState.kind === RuleKinds.TYPE_CONDITION)\n  ) {\n    return getSuggestionsForFragmentTypeConditions(\n      token,\n      typeInfo,\n      schema,\n      kind,\n    );\n  }\n\n  // Fragment spread names\n  if (kind === RuleKinds.FRAGMENT_SPREAD && step === 1) {\n    return getSuggestionsForFragmentSpread(\n      token,\n      typeInfo,\n      schema,\n      queryText,\n      Array.isArray(fragmentDefs)\n        ? fragmentDefs\n        : collectFragmentDefs(fragmentDefs),\n    );\n  }\n\n  const unwrappedState = unwrapType(state);\n\n  if (unwrappedState.kind === RuleKinds.FIELD_DEF) {\n    return hintList(\n      token,\n      Object.values(schema.getTypeMap())\n        .filter(type => isOutputType(type) && !type.name.startsWith('__'))\n        .map(type => ({\n          label: type.name,\n          kind: CompletionItemKind.Function,\n          insertText: options?.fillLeafsOnComplete\n            ? type.name + '\\n'\n            : type.name,\n          insertTextMode: InsertTextMode.adjustIndentation,\n        })),\n    );\n  }\n  if (unwrappedState.kind === RuleKinds.INPUT_VALUE_DEF && step === 2) {\n    return hintList(\n      token,\n      Object.values(schema.getTypeMap())\n        .filter(type => isInputType(type) && !type.name.startsWith('__'))\n        .map(type => ({\n          label: type.name,\n          kind: CompletionItemKind.Function,\n          insertText: options?.fillLeafsOnComplete\n            ? type.name + '\\n$1'\n            : type.name,\n          insertTextMode: InsertTextMode.adjustIndentation,\n          insertTextFormat: InsertTextFormat.Snippet,\n        })),\n    );\n  }\n\n  // Variable definition types\n  if (\n    (kind === RuleKinds.VARIABLE_DEFINITION && step === 2) ||\n    (kind === RuleKinds.LIST_TYPE && step === 1) ||\n    (kind === RuleKinds.NAMED_TYPE &&\n      prevState &&\n      (prevState.kind === RuleKinds.VARIABLE_DEFINITION ||\n        prevState.kind === RuleKinds.LIST_TYPE ||\n        prevState.kind === RuleKinds.NON_NULL_TYPE))\n  ) {\n    return getSuggestionsForVariableDefinition(token, schema, kind);\n  }\n\n  // Directive names\n  if (kind === RuleKinds.DIRECTIVE) {\n    return getSuggestionsForDirective(token, state, schema, kind);\n  }\n  if (kind === RuleKinds.DIRECTIVE_DEF) {\n    return getSuggestionsForDirectiveArguments(token, state, schema, kind);\n  }\n\n  return [];\n}\n\nconst typeSystemCompletionItems: CompletionItem[] = [\n  { label: 'type', kind: CompletionItemKind.Function },\n  { label: 'interface', kind: CompletionItemKind.Function },\n  { label: 'union', kind: CompletionItemKind.Function },\n  { label: 'input', kind: CompletionItemKind.Function },\n  { label: 'scalar', kind: CompletionItemKind.Function },\n  { label: 'schema', kind: CompletionItemKind.Function },\n];\n\nconst executableCompletionItems: CompletionItem[] = [\n  { label: 'query', kind: CompletionItemKind.Function },\n  { label: 'mutation', kind: CompletionItemKind.Function },\n  { label: 'subscription', kind: CompletionItemKind.Function },\n  { label: 'fragment', kind: CompletionItemKind.Function },\n  { label: '{', kind: CompletionItemKind.Constructor },\n];\n\n// Helper functions to get suggestions for each kinds\nfunction getSuggestionsForTypeSystemDefinitions(\n  token: ContextToken,\n): CompletionItem[] {\n  return hintList(token, [\n    { label: 'extend', kind: CompletionItemKind.Function },\n    ...typeSystemCompletionItems,\n  ]);\n}\n\nfunction getSuggestionsForExecutableDefinitions(\n  token: ContextToken,\n): CompletionItem[] {\n  return hintList(token, executableCompletionItems);\n}\n\nfunction getSuggestionsForUnknownDocumentMode(\n  token: ContextToken,\n): CompletionItem[] {\n  return hintList(token, [\n    { label: 'extend', kind: CompletionItemKind.Function },\n    ...executableCompletionItems,\n    ...typeSystemCompletionItems,\n  ]);\n}\n\nfunction getSuggestionsForExtensionDefinitions(\n  token: ContextToken,\n): CompletionItem[] {\n  return hintList(token, typeSystemCompletionItems);\n}\n\nfunction getSuggestionsForFieldNames(\n  token: ContextToken,\n  typeInfo: AllTypeInfo,\n  options?: InternalAutocompleteOptions,\n): CompletionItem[] {\n  if (typeInfo.parentType) {\n    const { parentType } = typeInfo;\n    // const { parentType, fieldDef, argDefs } = typeInfo;\n    let fields: GraphQLField<null, null>[] = [];\n    if ('getFields' in parentType) {\n      fields = objectValues<GraphQLField<null, null>>(\n        // TODO: getFields returns `GraphQLFieldMap<any, any> | GraphQLInputFieldMap`\n        parentType.getFields() as GraphQLFieldMap<any, any>,\n      );\n    }\n\n    if (isCompositeType(parentType)) {\n      fields.push(TypeNameMetaFieldDef);\n    }\n    if (parentType === options?.schema?.getQueryType()) {\n      fields.push(SchemaMetaFieldDef, TypeMetaFieldDef);\n    }\n\n    return hintList(\n      token,\n      fields.map<CompletionItem>((field, index) => {\n        const suggestion: CompletionItem = {\n          // This will sort the fields in the same order they are listed in the schema\n          sortText: String(index) + field.name,\n          label: field.name,\n          detail: String(field.type),\n\n          documentation: field.description ?? undefined,\n          deprecated: Boolean(field.deprecationReason),\n          isDeprecated: Boolean(field.deprecationReason),\n          deprecationReason: field.deprecationReason,\n          kind: CompletionItemKind.Field,\n          labelDetails: {\n            detail: ' ' + field.type.toString(),\n          },\n\n          type: field.type,\n        };\n        if (options?.fillLeafsOnComplete) {\n          // const hasArgs =\n          //   // token.state.needsAdvance &&\n          //   // @ts-expect-error\n          //   parentType?._fields[field?.name];\n\n          suggestion.insertText = getFieldInsertText(field);\n\n          // eslint-disable-next-line logical-assignment-operators\n          if (!suggestion.insertText) {\n            suggestion.insertText = getInsertText(\n              field.name,\n              field.type,\n              // if we are replacing a field with arguments, we don't want the extra line\n              field.name + (token.state.needsAdvance ? '' : '\\n'),\n            );\n          }\n\n          if (suggestion.insertText) {\n            suggestion.insertTextFormat = InsertTextFormat.Snippet;\n            suggestion.insertTextMode = InsertTextMode.adjustIndentation;\n            suggestion.command = SuggestionCommand;\n          }\n        }\n\n        return suggestion;\n      }),\n    );\n  }\n  return [];\n}\n\nfunction getSuggestionsForInputValues(\n  token: ContextToken,\n  typeInfo: AllTypeInfo,\n  queryText: string,\n  schema: GraphQLSchema,\n): Array<CompletionItem> {\n  const namedInputType = getNamedType(typeInfo.inputType!);\n\n  const queryVariables: CompletionItem[] = getVariableCompletions(\n    queryText,\n    schema,\n    token,\n  ).filter(v => v.detail === namedInputType?.name);\n\n  if (namedInputType instanceof GraphQLEnumType) {\n    const values = namedInputType.getValues();\n    return hintList(\n      token,\n      values\n        .map<CompletionItem>((value: GraphQLEnumValue) => ({\n          label: value.name,\n          detail: String(namedInputType),\n          documentation: value.description ?? undefined,\n          deprecated: Boolean(value.deprecationReason),\n          isDeprecated: Boolean(value.deprecationReason),\n          deprecationReason: value.deprecationReason,\n          kind: CompletionItemKind.EnumMember,\n          type: namedInputType,\n        }))\n        .concat(queryVariables),\n    );\n  }\n  if (namedInputType === GraphQLBoolean) {\n    return hintList(\n      token,\n      queryVariables.concat([\n        {\n          label: 'true',\n          detail: String(GraphQLBoolean),\n          documentation: 'Not false.',\n          kind: CompletionItemKind.Variable,\n          type: GraphQLBoolean,\n        },\n        {\n          label: 'false',\n          detail: String(GraphQLBoolean),\n          documentation: 'Not true.',\n          kind: CompletionItemKind.Variable,\n          type: GraphQLBoolean,\n        },\n      ]),\n    );\n  }\n\n  return queryVariables;\n}\n\nfunction getSuggestionsForImplements(\n  token: ContextToken,\n  tokenState: State,\n  schema: GraphQLSchema,\n  documentText: string,\n  typeInfo: AllTypeInfo,\n): Array<CompletionItem> {\n  // exit empty if we need an &\n  if (tokenState.needsSeparator) {\n    return [];\n  }\n  const typeMap = schema.getTypeMap();\n\n  const schemaInterfaces = objectValues(typeMap).filter(isInterfaceType);\n  const schemaInterfaceNames = schemaInterfaces.map(({ name }) => name);\n  const inlineInterfaces: Set<string> = new Set();\n  runOnlineParser(documentText, (_, state: State) => {\n    if (state.name) {\n      // gather inline interface definitions\n      if (\n        state.kind === RuleKinds.INTERFACE_DEF &&\n        !schemaInterfaceNames.includes(state.name)\n      ) {\n        inlineInterfaces.add(state.name);\n      }\n      // gather the other interfaces the current type/interface definition implements\n      // so we can filter them out below\n      if (\n        state.kind === RuleKinds.NAMED_TYPE &&\n        state.prevState?.kind === RuleKinds.IMPLEMENTS\n      ) {\n        if (typeInfo.interfaceDef) {\n          const existingType = typeInfo.interfaceDef\n            ?.getInterfaces()\n            .find(({ name }) => name === state.name);\n          if (existingType) {\n            return;\n          }\n          const type = schema.getType(state.name);\n          const interfaceConfig = typeInfo.interfaceDef?.toConfig();\n          typeInfo.interfaceDef = new GraphQLInterfaceType({\n            ...interfaceConfig,\n            interfaces: [\n              ...interfaceConfig.interfaces,\n              (type as GraphQLInterfaceType) ||\n                new GraphQLInterfaceType({ name: state.name, fields: {} }),\n            ],\n          });\n        } else if (typeInfo.objectTypeDef) {\n          const existingType = typeInfo.objectTypeDef\n            ?.getInterfaces()\n            .find(({ name }) => name === state.name);\n          if (existingType) {\n            return;\n          }\n          const type = schema.getType(state.name);\n          const objectTypeConfig = typeInfo.objectTypeDef?.toConfig();\n          typeInfo.objectTypeDef = new GraphQLObjectType({\n            ...objectTypeConfig,\n            interfaces: [\n              ...objectTypeConfig.interfaces,\n              (type as GraphQLInterfaceType) ||\n                new GraphQLInterfaceType({ name: state.name, fields: {} }),\n            ],\n          });\n        }\n      }\n    }\n  });\n\n  const currentTypeToExtend = typeInfo.interfaceDef || typeInfo.objectTypeDef;\n\n  const siblingInterfaces = currentTypeToExtend?.getInterfaces() || [];\n  const siblingInterfaceNames = siblingInterfaces.map(({ name }) => name);\n\n  // TODO: we should be using schema.getPossibleTypes() here, but\n  const possibleInterfaces = schemaInterfaces\n    .concat(\n      [...inlineInterfaces].map(name => ({ name }) as GraphQLInterfaceType),\n    )\n    .filter(\n      ({ name }) =>\n        name !== currentTypeToExtend?.name &&\n        !siblingInterfaceNames.includes(name),\n    );\n\n  return hintList(\n    token,\n    possibleInterfaces.map(type => {\n      const result = {\n        label: type.name,\n        kind: CompletionItemKind.Interface,\n        type,\n      } as CompletionItem;\n      if (type?.description) {\n        result.documentation = type.description;\n      }\n      // TODO: should we report what an interface implements in CompletionItem.detail?\n      // result.detail = 'Interface'\n      // const interfaces = type.astNode?.interfaces;\n      // if (interfaces && interfaces.length > 0) {\n      //   result.detail += ` (implements ${interfaces\n      //     .map(i => i.name.value)\n      //     .join(' & ')})`;\n      // }\n\n      return result;\n    }),\n  );\n}\n\nfunction getSuggestionsForFragmentTypeConditions(\n  token: ContextToken,\n  typeInfo: AllTypeInfo,\n  schema: GraphQLSchema,\n  _kind: 'NamedType' | 'TypeCondition',\n): Array<CompletionItem> {\n  let possibleTypes: GraphQLType[];\n  if (typeInfo.parentType) {\n    if (isAbstractType(typeInfo.parentType)) {\n      const abstractType = assertAbstractType(typeInfo.parentType);\n      // Collect both the possible Object types as well as the interfaces\n      // they implement.\n      const possibleObjTypes = schema.getPossibleTypes(abstractType);\n      const possibleIfaceMap = Object.create(null);\n      for (const type of possibleObjTypes) {\n        for (const iface of type.getInterfaces()) {\n          possibleIfaceMap[iface.name] = iface;\n        }\n      }\n      possibleTypes = possibleObjTypes.concat(objectValues(possibleIfaceMap));\n    } else {\n      // The parent type is a non-abstract Object type, so the only possible\n      // type that can be used is that same type.\n      possibleTypes = [typeInfo.parentType];\n    }\n  } else {\n    const typeMap = schema.getTypeMap();\n    possibleTypes = objectValues(typeMap).filter(\n      type => isCompositeType(type) && !type.name.startsWith('__'),\n    );\n  }\n  return hintList(\n    token,\n    possibleTypes.map(type => {\n      const namedType = getNamedType(type);\n      return {\n        label: String(type),\n        documentation: (namedType?.description as string | undefined) || '',\n        kind: CompletionItemKind.Field,\n      };\n    }),\n  );\n}\n\nfunction getSuggestionsForFragmentSpread(\n  token: ContextToken,\n  typeInfo: AllTypeInfo,\n  schema: GraphQLSchema,\n  queryText: string,\n  fragmentDefs?: FragmentDefinitionNode[],\n): Array<CompletionItem> {\n  if (!queryText) {\n    return [];\n  }\n  const typeMap = schema.getTypeMap();\n  const defState = getDefinitionState(token.state);\n  const fragments = getFragmentDefinitions(queryText);\n\n  if (fragmentDefs && fragmentDefs.length > 0) {\n    fragments.push(...fragmentDefs);\n  }\n\n  // Filter down to only the fragments which may exist here.\n  const relevantFrags = fragments.filter(\n    frag =>\n      // Only include fragments with known types.\n      typeMap[frag.typeCondition.name.value] &&\n      // Only include fragments which are not cyclic.\n      !(\n        defState &&\n        defState.kind === RuleKinds.FRAGMENT_DEFINITION &&\n        defState.name === frag.name.value\n      ) &&\n      // Only include fragments which could possibly be spread here.\n      isCompositeType(typeInfo.parentType) &&\n      isCompositeType(typeMap[frag.typeCondition.name.value]) &&\n      doTypesOverlap(\n        schema,\n        typeInfo.parentType,\n        typeMap[frag.typeCondition.name.value] as GraphQLCompositeType,\n      ),\n  );\n\n  return hintList(\n    token,\n    relevantFrags.map(frag => ({\n      label: frag.name.value,\n      detail: String(typeMap[frag.typeCondition.name.value]),\n      documentation: `fragment ${frag.name.value} on ${frag.typeCondition.name.value}`,\n      labelDetails: {\n        detail: `fragment ${frag.name.value} on ${frag.typeCondition.name.value}`,\n      },\n      kind: CompletionItemKind.Field,\n      type: typeMap[frag.typeCondition.name.value],\n    })),\n  );\n}\n\n// TODO: should be using getTypeInfo() for this if we can\nconst getParentDefinition = (state: State, kind: RuleKind) => {\n  if (state.prevState?.kind === kind) {\n    return state.prevState;\n  }\n  if (state.prevState?.prevState?.kind === kind) {\n    return state.prevState.prevState;\n  }\n  if (state.prevState?.prevState?.prevState?.kind === kind) {\n    return state.prevState.prevState.prevState;\n  }\n  if (state.prevState?.prevState?.prevState?.prevState?.kind === kind) {\n    return state.prevState.prevState.prevState.prevState;\n  }\n};\n\nexport function getVariableCompletions(\n  queryText: string,\n  schema: GraphQLSchema,\n  token: ContextToken,\n): CompletionItem[] {\n  let variableName: null | string = null;\n  let variableType: GraphQLInputObjectType | undefined | null;\n  const definitions: Record<string, any> = Object.create({});\n\n  runOnlineParser(queryText, (_, state: State) => {\n    // TODO: gather this as part of `AllTypeInfo`, as I don't think it's optimal to re-run the parser like this\n    if (state?.kind === RuleKinds.VARIABLE && state.name) {\n      variableName = state.name;\n    }\n    if (state?.kind === RuleKinds.NAMED_TYPE && variableName) {\n      const parentDefinition = getParentDefinition(state, RuleKinds.TYPE);\n      if (parentDefinition?.type) {\n        variableType = schema.getType(\n          parentDefinition?.type,\n        ) as GraphQLInputObjectType;\n      }\n    }\n\n    if (variableName && variableType && !definitions[variableName]) {\n      // append `$` if the `token.string` is not already `$`, or describing a variable\n      // this appears to take care of it everywhere\n      const replaceString =\n        token.string === '$' || token?.state?.kind === 'Variable'\n          ? variableName\n          : '$' + variableName;\n      definitions[variableName] = {\n        detail: variableType.toString(),\n        insertText: replaceString,\n        label: '$' + variableName,\n        rawInsert: replaceString,\n        type: variableType,\n        kind: CompletionItemKind.Variable,\n      } as CompletionItem;\n\n      variableName = null;\n      variableType = null;\n    }\n  });\n\n  return objectValues(definitions);\n}\n\nexport function getFragmentDefinitions(\n  queryText: string,\n): Array<FragmentDefinitionNode> {\n  const fragmentDefs: FragmentDefinitionNode[] = [];\n  runOnlineParser(queryText, (_, state: State) => {\n    if (\n      state.kind === RuleKinds.FRAGMENT_DEFINITION &&\n      state.name &&\n      state.type\n    ) {\n      fragmentDefs.push({\n        kind: RuleKinds.FRAGMENT_DEFINITION,\n        name: {\n          kind: Kind.NAME,\n          value: state.name,\n        },\n\n        selectionSet: {\n          kind: RuleKinds.SELECTION_SET,\n          selections: [],\n        },\n\n        typeCondition: {\n          kind: RuleKinds.NAMED_TYPE,\n          name: {\n            kind: Kind.NAME,\n            value: state.type,\n          },\n        },\n      });\n    }\n  });\n\n  return fragmentDefs;\n}\n\nfunction getSuggestionsForVariableDefinition(\n  token: ContextToken,\n  schema: GraphQLSchema,\n  _kind: string,\n): Array<CompletionItem> {\n  const inputTypeMap = schema.getTypeMap();\n  const inputTypes = objectValues(inputTypeMap).filter(isInputType);\n  return hintList(\n    token,\n    // TODO: couldn't get Exclude<> working here\n    inputTypes.map((type: GraphQLNamedType) => ({\n      label: type.name,\n      documentation: type?.description || '',\n      kind: CompletionItemKind.Variable,\n    })),\n  );\n}\n\nfunction getSuggestionsForDirective(\n  token: ContextToken,\n  state: State,\n  schema: GraphQLSchema,\n  _kind: string,\n): Array<CompletionItem> {\n  if (state.prevState?.kind) {\n    const directives = schema\n      .getDirectives()\n      .filter(directive => canUseDirective(state.prevState, directive));\n    return hintList(\n      token,\n      directives.map(directive => ({\n        label: directive.name,\n        documentation: directive?.description || '',\n        kind: CompletionItemKind.Function,\n      })),\n    );\n  }\n  return [];\n}\n\n// I thought this added functionality somewhere, but I couldn't write any tests\n// to execute it. I think it's handled as Arguments\nfunction getSuggestionsForDirectiveArguments(\n  token: ContextToken,\n  state: State,\n  schema: GraphQLSchema,\n  _kind: string,\n): Array<CompletionItem> {\n  const directive = schema.getDirectives().find(d => d.name === state.name);\n  return hintList(\n    token,\n    directive?.args.map(arg => ({\n      label: arg.name,\n      documentation: arg.description || '',\n      kind: CompletionItemKind.Field,\n    })) || [],\n  );\n}\n\nexport function canUseDirective(\n  state: State['prevState'],\n  directive: GraphQLDirective,\n): boolean {\n  if (!state?.kind) {\n    return false;\n  }\n  const { kind, prevState } = state;\n  const { locations } = directive;\n  switch (kind) {\n    case RuleKinds.QUERY:\n      return locations.includes(DirectiveLocation.QUERY);\n    case RuleKinds.MUTATION:\n      return locations.includes(DirectiveLocation.MUTATION);\n    case RuleKinds.SUBSCRIPTION:\n      return locations.includes(DirectiveLocation.SUBSCRIPTION);\n    case RuleKinds.FIELD:\n    case RuleKinds.ALIASED_FIELD:\n      return locations.includes(DirectiveLocation.FIELD);\n    case RuleKinds.FRAGMENT_DEFINITION:\n      return locations.includes(DirectiveLocation.FRAGMENT_DEFINITION);\n    case RuleKinds.FRAGMENT_SPREAD:\n      return locations.includes(DirectiveLocation.FRAGMENT_SPREAD);\n    case RuleKinds.INLINE_FRAGMENT:\n      return locations.includes(DirectiveLocation.INLINE_FRAGMENT);\n\n    // Schema Definitions\n    case RuleKinds.SCHEMA_DEF:\n      return locations.includes(DirectiveLocation.SCHEMA);\n    case RuleKinds.SCALAR_DEF:\n      return locations.includes(DirectiveLocation.SCALAR);\n    case RuleKinds.OBJECT_TYPE_DEF:\n      return locations.includes(DirectiveLocation.OBJECT);\n    case RuleKinds.FIELD_DEF:\n      return locations.includes(DirectiveLocation.FIELD_DEFINITION);\n    case RuleKinds.INTERFACE_DEF:\n      return locations.includes(DirectiveLocation.INTERFACE);\n    case RuleKinds.UNION_DEF:\n      return locations.includes(DirectiveLocation.UNION);\n    case RuleKinds.ENUM_DEF:\n      return locations.includes(DirectiveLocation.ENUM);\n    case RuleKinds.ENUM_VALUE:\n      return locations.includes(DirectiveLocation.ENUM_VALUE);\n    case RuleKinds.INPUT_DEF:\n      return locations.includes(DirectiveLocation.INPUT_OBJECT);\n    case RuleKinds.INPUT_VALUE_DEF:\n      const prevStateKind = prevState?.kind;\n      switch (prevStateKind) {\n        case RuleKinds.ARGUMENTS_DEF:\n          return locations.includes(DirectiveLocation.ARGUMENT_DEFINITION);\n        case RuleKinds.INPUT_DEF:\n          return locations.includes(DirectiveLocation.INPUT_FIELD_DEFINITION);\n      }\n  }\n\n  return false;\n}\n\nfunction unwrapType(state: State): State {\n  if (\n    state.prevState &&\n    state.kind &&\n    (\n      [\n        RuleKinds.NAMED_TYPE,\n        RuleKinds.LIST_TYPE,\n        RuleKinds.TYPE,\n        RuleKinds.NON_NULL_TYPE,\n      ] as RuleKind[]\n    ).includes(state.kind)\n  ) {\n    return unwrapType(state.prevState);\n  }\n  return state;\n}\n", "/**\n *  Copyright (c) 2021 GraphQL Contributors\n *  All rights reserved.\n *\n *  This source code is licensed under the license found in the\n *  LICENSE file in the root directory of this source tree.\n *\n */\n\nimport { Location } from 'graphql';\nimport { IRange, IPosition } from '../types';\n\nexport class Range implements IRange {\n  start: IPosition;\n  end: IPosition;\n  constructor(start: IPosition, end: IPosition) {\n    this.start = start;\n    this.end = end;\n  }\n\n  setStart(line: number, character: number) {\n    this.start = new Position(line, character);\n  }\n\n  setEnd(line: number, character: number) {\n    this.end = new Position(line, character);\n  }\n\n  containsPosition = (position: IPosition): boolean => {\n    if (this.start.line === position.line) {\n      return this.start.character <= position.character;\n    }\n    if (this.end.line === position.line) {\n      return this.end.character >= position.character;\n    }\n    return this.start.line <= position.line && this.end.line >= position.line;\n  };\n}\n\nexport class Position implements IPosition {\n  line: number;\n  character: number;\n  constructor(line: number, character: number) {\n    this.line = line;\n    this.character = character;\n  }\n\n  setLine(line: number) {\n    this.line = line;\n  }\n\n  setCharacter(character: number) {\n    this.character = character;\n  }\n\n  lessThanOrEqualTo = (position: IPosition): boolean =>\n    this.line < position.line ||\n    (this.line === position.line && this.character <= position.character);\n}\n\nexport function offsetToPosition(text: string, loc: number): Position {\n  const EOL = '\\n';\n  const buf = text.slice(0, loc);\n  const lines = buf.split(EOL).length - 1;\n  const lastLineIndex = buf.lastIndexOf(EOL);\n  return new Position(lines, loc - lastLineIndex - 1);\n}\n\nexport function locToRange(text: string, loc: Location): Range {\n  const start = offsetToPosition(text, loc.start);\n  const end = offsetToPosition(text, loc.end);\n  return new Range(start, end);\n}\n", "/**\n *  Copyright (c) 2021 GraphQL Contributors.\n *\n *  This source code is licensed under the MIT license found in the\n *  LICENSE file in the root directory of this source tree.\n */\n\nimport { DocumentNode, FragmentDefinitionNode, parse, visit } from 'graphql';\nimport nullthrows from 'nullthrows';\n\nexport const getFragmentDependencies = (\n  operationString: string,\n  fragmentDefinitions?: Map<string, FragmentDefinitionNode> | null,\n): FragmentDefinitionNode[] => {\n  // If there isn't context for fragment references,\n  // return an empty array.\n  if (!fragmentDefinitions) {\n    return [];\n  }\n  // If the operation cannot be parsed, validations cannot happen yet.\n  // Return an empty array.\n  let parsedOperation;\n  try {\n    parsedOperation = parse(operationString);\n  } catch {\n    return [];\n  }\n  return getFragmentDependenciesForAST(parsedOperation, fragmentDefinitions);\n};\n\nexport const getFragmentDependenciesForAST = (\n  parsedOperation: DocumentNode,\n  fragmentDefinitions: Map<string, FragmentDefinitionNode>,\n): FragmentDefinitionNode[] => {\n  if (!fragmentDefinitions) {\n    return [];\n  }\n\n  const existingFrags = new Map();\n  const referencedFragNames = new Set<string>();\n\n  visit(parsedOperation, {\n    FragmentDefinition(node) {\n      existingFrags.set(node.name.value, true);\n    },\n    FragmentSpread(node) {\n      if (!referencedFragNames.has(node.name.value)) {\n        referencedFragNames.add(node.name.value);\n      }\n    },\n  });\n\n  const asts = new Set<FragmentDefinitionNode>();\n  for (const name of referencedFragNames) {\n    if (!existingFrags.has(name) && fragmentDefinitions.has(name)) {\n      asts.add(nullthrows(fragmentDefinitions.get(name)));\n    }\n  }\n\n  const referencedFragments: FragmentDefinitionNode[] = [];\n\n  for (const ast of asts) {\n    visit(ast, {\n      FragmentSpread(node) {\n        if (\n          !referencedFragNames.has(node.name.value) &&\n          fragmentDefinitions.get(node.name.value)\n        ) {\n          asts.add(nullthrows(fragmentDefinitions.get(node.name.value)));\n          referencedFragNames.add(node.name.value);\n        }\n      },\n    });\n    if (!existingFrags.has(ast.name.value)) {\n      referencedFragments.push(ast);\n    }\n  }\n\n  return referencedFragments;\n};\n", "/**\n *  Copyright (c) 2021 GraphQL Contributors\n *  All rights reserved.\n *\n *  This source code is licensed under the license found in the\n *  LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {\n  ValidationRule,\n  DocumentNode,\n  specifiedRules,\n  validate,\n  GraphQLError,\n  GraphQLSchema,\n  NoUnusedFragmentsRule,\n  KnownFragmentNamesRule,\n  Kind,\n  ExecutableDefinitionsRule,\n  // specifiedSDLRules:\n  LoneSchemaDefinitionRule,\n  UniqueOperationTypesRule,\n  UniqueTypeNamesRule,\n  UniqueEnumValueNamesRule,\n  UniqueFieldDefinitionNamesRule,\n  UniqueDirectiveNamesRule,\n  KnownTypeNamesRule,\n  KnownDirectivesRule,\n  UniqueDirectivesPerLocationRule,\n  PossibleTypeExtensionsRule,\n  // KnownArgumentNamesOnDirectivesRule,\n  UniqueArgumentNamesRule,\n  UniqueInputFieldNamesRule,\n  UniqueVariableNamesRule,\n  FragmentsOnCompositeTypesRule,\n  ProvidedRequiredArgumentsRule,\n} from 'graphql';\n\nconst specifiedSDLRules = [\n  LoneSchemaDefinitionRule,\n  UniqueOperationTypesRule,\n  UniqueTypeNamesRule,\n  UniqueEnumValueNamesRule,\n  UniqueFieldDefinitionNamesRule,\n  UniqueDirectiveNamesRule,\n  KnownTypeNamesRule,\n  KnownDirectivesRule,\n  UniqueDirectivesPerLocationRule,\n  PossibleTypeExtensionsRule,\n  // KnownArgumentNamesOnDirectivesRule,\n  UniqueArgumentNamesRule,\n  UniqueInputFieldNamesRule,\n  UniqueVariableNamesRule,\n  FragmentsOnCompositeTypesRule,\n  ProvidedRequiredArgumentsRule,\n];\n\n/**\n * Validate a GraphQL Document optionally with custom validation rules.\n */\nexport function validateWithCustomRules(\n  schema: GraphQLSchema,\n  ast: DocumentNode,\n  customRules?: Array<ValidationRule> | null,\n  isRelayCompatMode?: boolean,\n  isSchemaDocument?: boolean,\n): Array<GraphQLError> {\n  const rules = specifiedRules.filter(rule => {\n    // Because every fragment is considered for determining model subsets that may\n    // be used anywhere in the codebase they're all technically \"used\" by clients\n    // of graphql-data. So we remove this rule from the validators.\n    if (rule === NoUnusedFragmentsRule || rule === ExecutableDefinitionsRule) {\n      return false;\n    }\n    if (isRelayCompatMode && rule === KnownFragmentNamesRule) {\n      return false;\n    }\n    return true;\n  });\n\n  if (customRules) {\n    Array.prototype.push.apply(rules, customRules);\n  }\n  if (isSchemaDocument) {\n    Array.prototype.push.apply(rules, specifiedSDLRules);\n  }\n  const errors = validate(schema, ast, rules);\n  return errors.filter(error => {\n    if (error.message.includes('Unknown directive') && error.nodes) {\n      const node = error.nodes[0];\n      if (node && node.kind === Kind.DIRECTIVE) {\n        const name = node.name.value;\n        if (name === 'arguments' || name === 'argumentDefinitions') {\n          return false;\n        }\n      }\n    }\n    return true;\n  });\n}\n", "import {\n  typeFromAST,\n  GraphQLSchema,\n  DocumentNode,\n  NamedTypeNode,\n  GraphQLInputType,\n  GraphQLFloat,\n  Kind,\n} from 'graphql';\n\nexport type VariableToType = {\n  [variable: string]: GraphQLInputType;\n};\n\n/**\n * Generates a map of GraphQLInputTypes for\n * all the variables in an AST document of operations\n *\n * @param schema\n * @param documentAST\n * @returns {VariableToType}\n */\nexport function collectVariables(\n  schema: GraphQLSchema,\n  documentAST: DocumentNode,\n): VariableToType {\n  const variableToType: VariableToType = Object.create(null);\n  // it would be more ideal to use visitWithTypeInfo here but it's very simple\n  for (const definition of documentAST.definitions) {\n    if (definition.kind === 'OperationDefinition') {\n      const { variableDefinitions } = definition;\n      if (variableDefinitions) {\n        for (const { variable, type } of variableDefinitions) {\n          const inputType = typeFromAST(\n            schema,\n            type as NamedTypeNode,\n          ) as GraphQLInputType;\n          if (inputType) {\n            variableToType[variable.name.value] = inputType;\n          } else if (\n            type.kind === Kind.NAMED_TYPE &&\n            // in the experimental stream defer branch we are using, it seems typeFromAST() doesn't recognize Floats?\n            type.name.value === 'Float'\n          ) {\n            variableToType[variable.name.value] = GraphQLFloat;\n          }\n        }\n      }\n    }\n  }\n  return variableToType;\n}\n", "/**\n *  Copyright (c) 2021 GraphQL Contributors.\n *\n *  This source code is licensed under the MIT license found in the\n *  LICENSE file in the root directory of this source tree.\n */\nimport { parse, visit } from 'graphql';\nimport { collectVariables } from './collectVariables';\n\nimport type { VariableToType } from './collectVariables';\nimport type {\n  GraphQLSchema,\n  DocumentNode,\n  OperationDefinitionNode,\n} from 'graphql';\n\nexport type OperationASTFacts = {\n  variableToType?: VariableToType;\n  operations: OperationDefinitionNode[];\n};\n\n/**\n * extract all operation nodes, and if schema is present, variable definitions, in a map\n *\n * @param documentAST {DocumentNode} a graphql-js compatible AST node\n * @param schema {GraphQLSchema} optional schema\n * @returns {OperationASTFacts}\n * @example\n *\n * ```ts\n *  const { variablesToType, operations } = getOperationASTFacts(\n *    parse('documentString'),\n *  );\n *  operations.forEach(op => {\n *    console.log(op.name, op.operation, op.loc);\n *  });\n *   Object.entries(variablesToType).forEach(([variableName, type]) => {\n *    console.log(variableName, type);\n *  });\n * ```\n */\n\nexport function getOperationASTFacts(\n  documentAST: DocumentNode,\n  schema?: GraphQLSchema | null,\n): OperationASTFacts {\n  const variableToType = schema\n    ? collectVariables(schema, documentAST)\n    : undefined;\n\n  // Collect operations by their names.\n  const operations: OperationDefinitionNode[] = [];\n\n  visit(documentAST, {\n    OperationDefinition(node) {\n      operations.push(node);\n    },\n  });\n\n  return { variableToType, operations };\n}\n\nexport type OperationFacts = {\n  documentAST: DocumentNode;\n} & OperationASTFacts;\n\nexport type QueryFacts = OperationFacts;\n\n/**\n * Provided previous \"queryFacts\", a GraphQL schema, and a query document\n * string, return a set of facts about that query useful for GraphiQL features.\n *\n * If the query cannot be parsed, returns undefined.\n * @param schema {GraphQLSchema} (optional)\n * @param documentString {string} the document you want to parse for operations (optional)\n *\n * @returns {OperationFacts | undefined}\n */\nexport default function getOperationFacts(\n  schema?: GraphQLSchema | null,\n  documentString?: string | null,\n): OperationFacts | undefined {\n  if (!documentString) {\n    return;\n  }\n\n  try {\n    const documentAST = parse(documentString);\n    return {\n      ...getOperationASTFacts(documentAST, schema),\n      documentAST,\n    };\n  } catch {\n    return;\n  }\n}\n\n/**\n * for backwards compatibility\n */\nexport const getQueryFacts = getOperationFacts;\n", "/**\n *  Copyright (c) 2021 GraphQL Contributors\n *  All rights reserved.\n *\n *  This source code is licensed under the license found in the\n *  LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {\n  ASTNode,\n  DocumentNode,\n  FragmentDefinitionNode,\n  GraphQLError,\n  GraphQLSchema,\n  Location,\n  SourceLocation,\n  ValidationRule,\n  print,\n  validate,\n  NoDeprecatedCustomRule,\n  parse,\n} from 'graphql';\n\nimport { CharacterStream, onlineParser } from '../parser';\n\nimport { Range, validateWithCustomRules, Position } from '../utils';\n\nimport { DiagnosticSeverity, Diagnostic } from 'vscode-languageserver-types';\n\nimport { IRange } from '../types';\n\n// this doesn't work without the 'as', kinda goofy\n\nexport const SEVERITY = {\n  Error: 'Error',\n  Warning: 'Warning',\n  Information: 'Information',\n  Hint: 'Hint',\n} as const;\n\nexport type Severity = typeof SEVERITY;\n\nexport type SeverityEnum = keyof Severity;\n\nexport const DIAGNOSTIC_SEVERITY = {\n  [SEVERITY.Error]: 1 as DiagnosticSeverity,\n  [SEVERITY.Warning]: 2 as DiagnosticSeverity,\n  [SEVERITY.Information]: 3 as DiagnosticSeverity,\n  [SEVERITY.Hint]: 4 as DiagnosticSeverity,\n};\n\nconst invariant = (condition: any, message: string) => {\n  if (!condition) {\n    throw new Error(message);\n  }\n};\n\nexport function getDiagnostics(\n  query: string,\n  schema: GraphQLSchema | null | undefined = null,\n  customRules?: Array<ValidationRule>,\n  isRelayCompatMode?: boolean,\n  externalFragments?: FragmentDefinitionNode[] | string,\n): Array<Diagnostic> {\n  let ast = null;\n  let fragments = '';\n  if (externalFragments) {\n    fragments =\n      typeof externalFragments === 'string'\n        ? externalFragments\n        : externalFragments.reduce(\n            (acc, node) => acc + print(node) + '\\n\\n',\n            '',\n          );\n  }\n  const enhancedQuery = fragments ? `${query}\\n\\n${fragments}` : query;\n  try {\n    ast = parse(enhancedQuery);\n  } catch (error) {\n    if (error instanceof GraphQLError) {\n      const range = getRange(\n        error.locations?.[0] ?? { line: 0, column: 0 },\n        enhancedQuery,\n      );\n\n      return [\n        {\n          severity: DIAGNOSTIC_SEVERITY.Error,\n          message: error.message,\n          source: 'GraphQL: Syntax',\n          range,\n        },\n      ];\n    }\n    throw error;\n  }\n\n  return validateQuery(ast, schema, customRules, isRelayCompatMode);\n}\n\nexport function validateQuery(\n  ast: DocumentNode,\n  schema: GraphQLSchema | null | undefined = null,\n  customRules?: Array<ValidationRule> | null,\n  isRelayCompatMode?: boolean,\n): Array<Diagnostic> {\n  // We cannot validate the query unless a schema is provided.\n  if (!schema) {\n    return [];\n  }\n\n  const validationErrorAnnotations = validateWithCustomRules(\n    schema,\n    ast,\n    customRules,\n    isRelayCompatMode,\n  ).flatMap(error =>\n    annotations(error, DIAGNOSTIC_SEVERITY.Error, 'Validation'),\n  );\n\n  // TODO: detect if > graphql@15.2.0, and use the new rule for this.\n  const deprecationWarningAnnotations = validate(schema, ast, [\n    NoDeprecatedCustomRule,\n  ]).flatMap(error =>\n    annotations(error, DIAGNOSTIC_SEVERITY.Warning, 'Deprecation'),\n  );\n  return validationErrorAnnotations.concat(deprecationWarningAnnotations);\n}\n\nfunction annotations(\n  error: GraphQLError,\n  severity: DiagnosticSeverity,\n  type: string,\n): Diagnostic[] {\n  if (!error.nodes) {\n    return [];\n  }\n  const highlightedNodes: Diagnostic[] = [];\n  for (const [i, node] of error.nodes.entries()) {\n    const highlightNode =\n      node.kind !== 'Variable' && 'name' in node && node.name !== undefined\n        ? node.name\n        : 'variable' in node && node.variable !== undefined\n          ? node.variable\n          : node;\n    if (highlightNode) {\n      invariant(\n        error.locations,\n        'GraphQL validation error requires locations.',\n      );\n\n      // @ts-ignore\n      // https://github.com/microsoft/TypeScript/pull/32695\n      const loc = error.locations[i];\n      const highlightLoc = getLocation(highlightNode);\n      const end = loc.column + (highlightLoc.end - highlightLoc.start);\n      highlightedNodes.push({\n        source: `GraphQL: ${type}`,\n        message: error.message,\n        severity,\n        range: new Range(\n          new Position(loc.line - 1, loc.column - 1),\n          new Position(loc.line - 1, end),\n        ),\n      });\n    }\n  }\n  return highlightedNodes;\n}\n\nexport function getRange(location: SourceLocation, queryText: string): IRange {\n  const parser = onlineParser();\n  const state = parser.startState();\n  const lines = queryText.split('\\n');\n\n  invariant(\n    lines.length >= location.line,\n    'Query text must have more lines than where the error happened',\n  );\n\n  let stream = null;\n\n  for (let i = 0; i < location.line; i++) {\n    stream = new CharacterStream(lines[i]);\n    while (!stream.eol()) {\n      const style = parser.token(stream, state);\n      if (style === 'invalidchar') {\n        break;\n      }\n    }\n  }\n\n  invariant(stream, 'Expected Parser stream to be available.');\n  const line = location.line - 1;\n  // @ts-expect-error -- https://github.com/microsoft/TypeScript/pull/32695\n  const start = stream.getStartOfToken();\n  // @ts-expect-error -- https://github.com/microsoft/TypeScript/pull/32695\n  const end = stream.getCurrentPosition();\n  return new Range(new Position(line, start), new Position(line, end));\n}\n\n/**\n * Get location info from a node in a type-safe way.\n *\n * The only way a node could not have a location is if we initialized the parser\n * (and therefore the lexer) with the `noLocation` option, but we always\n * call `parse` without options above.\n */\nfunction getLocation(node: any): Location {\n  const typeCastedNode = node as ASTNode;\n  const location = typeCastedNode.loc;\n  invariant(location, 'Expected ASTNode to have a location.');\n  // @ts-ignore\n  // https://github.com/microsoft/TypeScript/pull/32695\n  return location;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAEA,aAASA,YAAW,GAAG,SAAS;AAC9B,UAAI,KAAK,MAAM;AACb,eAAO;AAAA,MACT;AACA,UAAI,QAAQ,IAAI,MAAM,YAAY,SAAY,UAAU,oBAAoB,CAAC;AAC7E,YAAM,cAAc;AACpB,YAAM;AAAA,IACR;AAEA,WAAO,UAAUA;AACjB,WAAO,QAAQ,UAAUA;AAEzB,WAAO,eAAe,OAAO,SAAS,cAAc,EAAC,OAAO,KAAI,CAAC;AAAA;AAAA;;;ACA3D,SAAU,IAAI,QAAqB;AACvC,SAAO,EAAE,OAAM;AACjB;AAGM,SAAU,KAAK,QAAuB,WAAyB;AACnE,SAAO,EAAE,QAAQ,QAAQ,MAAM,UAAS;AAC1C;AAGM,SAAU,OAAO,MAAY,YAAuB;AACxD,QAAM,YAAY,KAAK;AACvB,OAAK,QAAQ,WAAQ;AACnB,QAAI,QAAQ;AACZ,QAAI,WAAW;AACb,cAAQ,UAAU,KAAK;;AAEzB,WACE,SAEA,WAAW,MAAM,eAAa,UAAU,SAAS,CAAC,UAAU,MAAM,KAAK,CAAC;EAE5E;AACA,SAAO;AACT;AAGM,SAAU,EAAE,MAAc,OAAa;AAC3C,SAAO,EAAE,OAAO,OAAO,CAAC,UAAiB,MAAM,SAAS,KAAI;AAC9D;AAGM,SAAU,EAAE,OAAe,OAAc;AAC7C,SAAO;IACL,OAAO,SAAS;IAChB,OAAO,CAAC,UACN,MAAM,SAAS,iBAAiB,MAAM,UAAU;;AAEtD;;;ACnCO,IAAM,YAAY,CAAC,OACxB,OAAO,OACP,OAAO,OACP,OAAO,OACP,OAAO,QACP,OAAO,QACP,OAAO,YACP,OAAO;AAKF,IAAM,WAAW;EAEtB,MAAM;EAGN,aAAa;EAGb,QAAQ;EAGR,QACE;EAGF,SAAS;;AAQJ,IAAM,aAA4C;EACvD,UAAU,CAAC,KAAK,YAAY,CAAC;EAC7B,WAAW,OAAY;AACrB,YAAQ,MAAM,OAAO;MACnB,KAAK;AACH,eAAO;MACT,KAAK;AACH,eAAO;MACT,KAAK;AACH,eAAO;MACT,KAAK;AACH,eAAO;MACT,KAAK;AACH,eAAO,KAAK;MACd,KAAK;AACH,eAAO;MACT,KAAK;AACH,eAAO;MACT,KAAK;AACH,eAAO;MACT,KAAK;AACH,eAAO;MACT,KAAK;AACH,eAAO;MACT,KAAK;AACH,eAAO;MACT,KAAK;AACH,eAAO;MACT,KAAK;AACH,eAAO;MACT,KAAK;AACH,eAAO;;EAEb;EAEA,YAAY,CAAC,cAAc;EAC3B,OAAO;IACL,KAAK,OAAO;IACZ,IAAI,KAAK,KAAK,CAAC;IACf,IAAI,qBAAqB;IACzB,KAAK,WAAW;IAChB;;EAGF,UAAU;IACR,KAAK,UAAU;IACf,IAAI,KAAK,KAAK,CAAC;IACf,IAAI,qBAAqB;IACzB,KAAK,WAAW;IAChB;;EAGF,cAAc;IACZ,KAAK,cAAc;IACnB,IAAI,KAAK,KAAK,CAAC;IACf,IAAI,qBAAqB;IACzB,KAAK,WAAW;IAChB;;EAGF,qBAAqB,CAAC,EAAE,GAAG,GAAG,KAAK,oBAAoB,GAAG,EAAE,GAAG,CAAC;EAChE,oBAAoB,CAAC,YAAY,EAAE,GAAG,GAAG,QAAQ,IAAI,cAAc,CAAC;EACpE,UAAU,CAAC,EAAE,KAAK,UAAU,GAAG,KAAK,UAAU,CAAC;EAC/C,cAAc,CAAC,EAAE,GAAG,GAAG,OAAO;EAC9B,cAAc,CAAC,EAAE,GAAG,GAAG,KAAK,WAAW,GAAG,EAAE,GAAG,CAAC;EAChD,UAAU,OAAc,QAAuB;AAC7C,WAAO,MAAM,UAAU,QACnB,OAAO,MAAM,0BAA0B,KAAK,IAC1C,mBACA,mBACF,OAAO,MAAM,iBAAiB,KAAK,IACjC,iBACA;EACR;EAEA,cAAc;IACZ,KAAK,UAAU;IACf,EAAE,GAAG;IACL,KAAK,WAAW;IAChB,IAAI,WAAW;IACf,KAAK,WAAW;IAChB,IAAI,cAAc;;EAGpB,OAAO;IACL,KAAK,UAAU;IACf,IAAI,WAAW;IACf,KAAK,WAAW;IAChB,IAAI,cAAc;;EAGpB,WAAW,CAAC,EAAE,GAAG,GAAG,KAAK,UAAU,GAAG,EAAE,GAAG,CAAC;EAC5C,UAAU,CAAC,KAAK,WAAW,GAAG,EAAE,GAAG,GAAG,OAAO;EAC7C,gBAAgB,CAAC,EAAE,KAAK,GAAG,KAAK,KAAK,GAAG,KAAK,WAAW,CAAC;EACzD,gBAAgB;IACd,EAAE,KAAK;IACP,IAAI,eAAe;IACnB,KAAK,WAAW;IAChB;;EAGF,oBAAoB;IAClB,KAAK,UAAU;IACf,IAAI,OAAO,KAAK,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC;IACrC;IACA,KAAK,WAAW;IAChB;;EAGF,eAAe,CAAC,KAAK,IAAI,GAAG,WAAW;EAEvC,MAAM,OAAY;AAChB,YAAQ,MAAM,MAAM;MAClB,KAAK;AACH,eAAO;MACT,KAAK;AACH,eAAO;MACT,KAAK;AACH,gBAAQ,MAAM,OAAO;UACnB,KAAK;AACH,mBAAO;UACT,KAAK;AACH,mBAAO;UACT,KAAK;AACH,mBAAO;UACT,KAAK;AACH,mBAAO;;AAGX,eAAO;MACT,KAAK;AACH,gBAAQ,MAAM,OAAO;UACnB,KAAK;UACL,KAAK;AACH,mBAAO;;AAGX,YAAI,MAAM,UAAU,QAAQ;AAC1B,iBAAO;;AAET,eAAO;;EAEb;EACA,aAAa,CAAC,EAAE,UAAU,QAAQ,CAAC;EACnC,aAAa;IACX;MACE,OAAO;MACP,OAAO,CAAC,UAAiB,MAAM,SAAS;MACxC,OAAO,OAAc,OAAY;AAC/B,YAAI,MAAM,MAAM,WAAW,KAAK,GAAG;AACjC,gBAAM,gBAAgB,CAAC,MAAM,MAAM,MAAM,CAAC,EAAE,SAAS,KAAK;;MAE9D;;;EAGJ,cAAc,CAAC,EAAE,QAAQ,SAAS,CAAC;EACnC,WAAW,CAAC,EAAE,QAAQ,SAAS,CAAC;EAChC,WAAW,CAAC,KAAK,UAAU,CAAC;EAC5B,WAAW,CAAC,EAAE,GAAG,GAAG,KAAK,OAAO,GAAG,EAAE,GAAG,CAAC;EACzC,aAAa,CAAC,EAAE,GAAG,GAAG,KAAK,aAAa,GAAG,EAAE,GAAG,CAAC;EACjD,aAAa,CAAC,KAAK,WAAW,GAAG,EAAE,GAAG,GAAG,OAAO;EAChD,KAAK,OAAY;AACf,WAAO,MAAM,UAAU,MAAM,aAAa;EAC5C;EAEA,UAAU,CAAC,EAAE,GAAG,GAAG,QAAQ,EAAE,GAAG,GAAG,IAAI,EAAE,GAAG,CAAC,CAAC;EAC9C,aAAa,CAAC,aAAa,IAAI,EAAE,GAAG,CAAC,CAAC;EACtC,WAAW,CAAC,KAAK,MAAM,CAAC;EACxB,WAAW,CAAC,EAAE,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG,IAAI,WAAW,CAAC;EAC1D,cAAc;IACZ,KAAK,WAAW;IAChB,EAAE,KAAK,MAAM;IACb,KAAK,MAAM;IACX,IAAI,cAAc;IAClB,KAAK,IAAI;IACT,KAAK,qBAAqB,EAAE,GAAG,CAAC;;EAElC,cAAc;IACZ,KAAK,WAAW;IAChB,KAAK,MAAM;IACX,IAAI,YAAY;IAChB,KAAK,WAAW;IAChB,EAAE,GAAG;IACL,KAAK,UAAU;IACf,EAAE,GAAG;;EAEP,YAAY,CAAC,KAAK,YAAY,GAAG,KAAK,aAAa,EAAE,GAAG,CAAC,CAAC;EAC1D,mBAAmB,CAAC,KAAK,UAAU,CAAC;EAEpC,WAAW;IACT,KAAK,QAAQ;IACb,KAAK,WAAW;IAChB,EAAE,GAAG;IACL,KAAK,kBAAkB;IACvB,EAAE,GAAG;;EAGP,kBAAkB,CAAC,KAAK,SAAS,GAAG,EAAE,GAAG,GAAG,KAAK,MAAM,CAAC;EACxD,WAAW,CAAC,KAAK,QAAQ,GAAG,KAAK,MAAM,GAAG,KAAK,WAAW,CAAC;EAC3D,eAAe;IACb,KAAK,MAAM;IACX,KAAK,MAAM;IACX,IAAI,YAAY;IAChB,KAAK,WAAW;IAChB,EAAE,GAAG;IACL,KAAK,UAAU;IACf,EAAE,GAAG;;EAGP,UAAU;IACR,KAAK,UAAU;IACf,IAAI,cAAc;IAClB,EAAE,GAAG;IACL;IACA,KAAK,WAAW;;EAGlB,cAAc,CAAC,EAAE,GAAG,GAAG,KAAK,eAAe,GAAG,EAAE,GAAG,CAAC;EACpD,eAAe;IACb,KAAK,WAAW;IAChB,EAAE,GAAG;IACL;IACA,IAAI,cAAc;IAClB,KAAK,WAAW;;EAGlB,UAAU;IACR,KAAK,OAAO;IACZ,KAAK,MAAM;IACX,KAAK,WAAW;IAChB,EAAE,GAAG;IACL,KAAK,eAAe,EAAE,GAAG,CAAC;;EAG5B,aAAa,CAAC,WAAW;EACzB,SAAS;IACP,KAAK,MAAM;IACX,KAAK,MAAM;IACX,KAAK,WAAW;IAChB,EAAE,GAAG;IACL,KAAK,cAAc;IACnB,EAAE,GAAG;;EAGP,cAAc,CAAC,KAAK,UAAU,GAAG,KAAK,WAAW,CAAC;EAClD,UAAU;IACR,KAAK,OAAO;IACZ,KAAK,MAAM;IACX,KAAK,WAAW;IAChB,EAAE,GAAG;IACL,KAAK,eAAe;IACpB,EAAE,GAAG;;EAEP,WAAW,CAAC,KAAK,QAAQ,GAAG,qBAAqB;EACjD,oBAAoB,OAAY;AAC9B,YAAQ,MAAM,OAAO;MACnB,KAAK;AACH,eAAO,KAAK;MACd,KAAK;AACH,eAAO,KAAK;MACd,KAAK;AACH,eAAO,KAAK;MACd,KAAK;AACH,eAAO,KAAK;MACd,KAAK;AACH,eAAO,KAAK;MACd,KAAK;AACH,eAAO,KAAK;MACd,KAAK;AACH,eAAO,KAAK;;EAElB;EACA,CAAC,KAAK,gBAAgB,GAAG,CAAC,WAAW;EACrC,CAAC,KAAK,qBAAqB,GAAG,CAAC,WAAW;EAC1C,CAAC,KAAK,qBAAqB,GAAG,CAAC,eAAe;EAC9C,CAAC,KAAK,wBAAwB,GAAG,CAAC,cAAc;EAChD,CAAC,KAAK,oBAAoB,GAAG,CAAC,UAAU;EACxC,CAAC,KAAK,mBAAmB,GAAG,CAAC,SAAS;EACtC,CAAC,KAAK,2BAA2B,GAAG,CAAC,UAAU;;AAIjD,SAAS,KAAK,OAAa;AACzB,SAAO;IACL,OAAO;IACP,OAAO,CAAC,UAAiB,MAAM,SAAS,UAAU,MAAM,UAAU;;AAEtE;AAGA,SAAS,KAAK,OAAa;AACzB,SAAO;IACL;IACA,OAAO,CAAC,UAAiB,MAAM,SAAS;IACxC,OAAO,OAAc,OAAY;AAC/B,YAAM,OAAO,MAAM;IACrB;;AAEJ;AAGA,SAAS,KAAK,OAAa;AACzB,SAAO;IACL;IACA,OAAO,CAAC,UAAiB,MAAM,SAAS;IACxC,OAAO,OAAc,OAAY;;AAC/B,WAAI,KAAA,MAAM,eAAS,QAAA,OAAA,SAAA,SAAA,GAAE,WAAW;AAC9B,cAAM,OAAO,MAAM;AACnB,cAAM,UAAU,UAAU,OAAO,MAAM;;IAE3C;;AAEJ;;;AClUc,SAAP,aACL,UAAyB;EACvB,eAAe,YAAU,OAAO,SAAS,SAAS;EAClD,UAAU;EACV,YAAY;EACZ,cAAc,CAAA;GACf;AAKD,SAAO;IACL,aAAU;AACR,YAAM,eAAe;QACnB,OAAO;QACP,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,gBAAgB;QAChB,WAAW;;AAGb,eAAS,QAAQ,YAAY,cAAc,KAAK,QAAQ;AACxD,aAAO;IACT;IACA,MAAM,QAAyB,OAAY;AACzC,aAAO,SAAS,QAAQ,OAAO,OAAO;IACxC;;AAEJ;AAEA,SAAS,SACP,QACA,OACA,SAAsB;;AAEtB,MAAI,MAAM,eAAe;AAEvB,QAAI,OAAO,MAAM,OAAO,GAAG;AACzB,YAAM,gBAAgB;AACtB,aAAO;;AAET,WAAO,UAAS;AAChB,WAAO;;AAGT,QAAM,EAAE,UAAU,YAAY,eAAe,aAAY,IAAK;AAE9D,MAAI,MAAM,QAAQ,MAAM,KAAK,WAAW,GAAG;AACzC,YAAQ,KAAK;aACJ,MAAM,cAAc;AAC7B,UAAM,eAAe;AACrB,gBAAY,OAAO,IAAI;;AAIzB,MAAI,OAAO,IAAG,GAAI;AAChB,UAAM,WAAU,iBAAY,QAAZ,iBAAY,SAAA,SAAZ,aAAc,YAAW;AACzC,UAAM,cAAc,KAAK,MAAM,OAAO,YAAW,IAAK,OAAO;;AAI/D,MAAI,cAAc,MAAM,GAAG;AACzB,WAAO;;AAIT,QAAM,QAAQ,IAAI,UAAU,MAAM;AAGlC,MAAI,CAAC,OAAO;AACV,UAAM,mBAAmB,OAAO,MAAM,KAAK;AAC3C,QAAI,CAAC,kBAAkB;AAGrB,aAAO,MAAM,IAAI;;AAEnB,aAAS,mBAAmB,OAAO,SAAS;AAC5C,WAAO;;AAIT,MAAI,MAAM,SAAS,WAAW;AAC5B,aAAS,mBAAmB,OAAO,SAAS;AAC5C,WAAO;;AAIT,QAAM,cAAc,OAAO,CAAA,GAAI,KAAK;AAGpC,MAAI,MAAM,SAAS,eAAe;AAChC,QAAI,SAAS,KAAK,MAAM,KAAK,GAAG;AAC9B,UAAI,MAAM,gBAAgB,QAAW;AAEnC,cAAM,UAAU,MAAM,UAAU,CAAA,GAAI,OAAO,MAAM,cAAc,CAAC;;eAEzD,UAAU,KAAK,MAAM,KAAK,GAAG;AAItC,YAAM,SAAU,MAAM,UAAU,MAAM,UAAU,CAAA,GAAI,MAAM,GAAG,EAAE;AAG/D,UACE,MAAM,eACN,OAAO,SAAS,KAChB,OAAO,GAAG,EAAE,IAAK,MAAM,aACvB;AACA,cAAM,cAAc,OAAO,GAAG,EAAE;;;;AAKtC,SAAO,MAAM,MAAM;AAGjB,QAAI,WACF,OAAO,MAAM,SAAS,aAClB,MAAM,SAAS,IACb,MAAM,KAAK,OAAO,MAAM,IACxB,OACF,MAAM,KAAK,MAAM,IAAI;AAG3B,QAAI,MAAM,gBAAgB;AACxB,iBAAW,aAAQ,QAAR,aAAQ,SAAA,SAAR,SAAU;;AAGvB,QAAI,UAAU;AAEZ,UAAI,SAAS,QAAQ;AACnB,mBAAW,SAAS;;AAItB,UAAI,OAAO,aAAa,UAAU;AAChC,iBAAS,YAAY,OAAO,QAAoB;AAChD;;AAIF,WAAI,KAAA,SAAS,WAAK,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,UAAG,KAAK,GAAG;AAC3B,YAAI,SAAS,QAAQ;AACnB,mBAAS,OAAO,OAAO,KAAK;;AAM9B,YAAI,MAAM,SAAS,eAAe;AAChC,sBAAY,OAAO,IAAI;eAClB;AACL,gBAAM,eAAe;;AAGvB,eAAO,SAAS;;;AAGpB,iBAAa,KAAK;;AAIpB,SAAO,OAAO,WAAW;AACzB,WAAS,mBAAmB,OAAO,SAAS;AAC5C,SAAO;AACT;AAGA,SAAS,OAAO,IAAY,MAAY;AACtC,QAAM,OAAO,OAAO,KAAK,IAAI;AAC7B,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAGpC,OAAG,KAAK,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC;;AAE5B,SAAO;AACT;AAGA,IAAM,oBAAoB;EACxB,SAAS,CAAA;EACT,SAAS,CAAA;;AAIX,SAAS,SACP,OACA,OACA,UAAkB;AAElB,MAAI,CAAC,MAAM,QAAQ,GAAG;AACpB,UAAM,IAAI,UAAU,mBAAmB,QAAQ;;AAEjD,QAAM,YAAS,OAAA,OAAA,CAAA,GAAQ,KAAK;AAC5B,QAAM,OAAO;AACb,QAAM,OAAO;AACb,QAAM,OAAO;AACb,QAAM,OAAO,MAAM,QAAQ;AAC3B,QAAM,OAAO;AACb,QAAM,iBAAiB;AACzB;AAGA,SAAS,QAAQ,OAAY;AAE3B,MAAI,CAAC,MAAM,WAAW;AACpB;;AAEF,QAAM,OAAO,MAAM,UAAU;AAC7B,QAAM,OAAO,MAAM,UAAU;AAC7B,QAAM,OAAO,MAAM,UAAU;AAC7B,QAAM,OAAO,MAAM,UAAU;AAC7B,QAAM,OAAO,MAAM,UAAU;AAC7B,QAAM,iBAAiB,MAAM,UAAU;AACvC,QAAM,YAAY,MAAM,UAAU;AACpC;AAGA,SAAS,YAAY,OAAc,YAAmB;;AAGpD,MAAI,OAAO,KAAK,KAAK,MAAM,MAAM;AAG/B,UAAM,OAAO,MAAM,KAAK,MAAM,IAAI;AAClC,QAAI,KAAK,WAAW;AAClB,YAAM,EAAE,UAAS,IAAK;AACtB,YAAM,iBAAiB,CAAC,MAAM;AAE9B,UAAI,CAAC,MAAM,kBAAkB,UAAU,QAAQ;AAC7C;;;AAIJ,QAAI,YAAY;AACd;;;AAMJ,QAAM,iBAAiB;AACvB,QAAM;AAGN,SACE,MAAM,QACN,EAAE,MAAM,QAAQ,MAAM,IAAI,KAAK,MAAM,OAAO,MAAM,KAAK,SACvD;AACA,YAAQ,KAAK;AAEb,QAAI,MAAM,MAAM;AAEd,UAAI,OAAO,KAAK,GAAG;AAGjB,aAAI,KAAA,MAAM,UAAI,QAAA,OAAA,SAAA,SAAA,GAAG,MAAM,IAAI,EAAE,WAAW;AACtC,gBAAM,iBAAiB,CAAC,MAAM;;aAE3B;AACL,cAAM,iBAAiB;AACvB,cAAM;;;;AAId;AAEA,SAAS,OAAO,OAAY;AAC1B,QAAM,OACJ,MAAM,QAAQ,MAAM,IAAI,KACxB,OAAO,MAAM,KAAK,MAAM,IAAI,MAAM,YACjC,MAAM,KAAK,MAAM,IAAI;AACxB,SAAO,QAAQ,KAAK;AACtB;AAGA,SAAS,aAAa,OAAY;AAGhC,SACE,MAAM,QAGN,EAAE,MAAM,QAAQ,MAAM,IAAI,KAAK,MAAM,KAAK,MAAM,IAAI,EAAE,SACtD;AACA,YAAQ,KAAK;;AAKf,MAAI,MAAM,MAAM;AACd,gBAAY,OAAO,KAAK;;AAE5B;AAGA,SAAS,IACP,UACA,QAAuB;AAEvB,QAAM,QAAQ,OAAO,KAAK,QAAQ;AAClC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAGrC,UAAM,QAAQ,OAAO,MAAM,SAAS,MAAM,CAAC,CAAC,CAAC;AAC7C,QAAI,SAAS,iBAAiB,OAAO;AACnC,aAAO,EAAE,MAAM,MAAM,CAAC,GAAG,OAAO,MAAM,CAAC,EAAC;;;AAG9C;;;AC7UA,IAAqB,kBAArB,MAAoC;EAKlC,YAAY,YAAkB;AAJtB,SAAA,SAAS;AACT,SAAA,OAAO;AAOR,SAAA,kBAAkB,MAAc,KAAK;AAErC,SAAA,qBAAqB,MAAc,KAAK;AAgBxC,SAAA,MAAM,MAAe,KAAK,YAAY,WAAW,KAAK;AAEtD,SAAA,MAAM,MAAe,KAAK,SAAS;AAEnC,SAAA,OAAO,MAAoB;AAChC,aAAO,KAAK,YAAY,OAAO,KAAK,IAAI,KAAK;IAC/C;AAEO,SAAA,OAAO,MAAa;AACzB,YAAM,OAAO,KAAK,YAAY,OAAO,KAAK,IAAI;AAC9C,WAAK;AACL,aAAO;IACT;AAEO,SAAA,MAAM,CAAC,YAA6C;AACzD,YAAM,YAAY,KAAK,mBAAmB,OAAO;AACjD,UAAI,WAAW;AACb,aAAK,SAAS,KAAK;AACnB,aAAK;AACL,eAAO,KAAK,YAAY,OAAO,KAAK,OAAO,CAAC;;AAE9C,aAAO;IACT;AAEO,SAAA,WAAW,CAAC,UAAgC;AACjD,UAAI,YAAY,KAAK,mBAAmB,KAAK;AAC7C,UAAI,SAAS;AAGb,UAAI,WAAW;AACb,iBAAS;AACT,aAAK,SAAS,KAAK;;AAGrB,aAAO,WAAW;AAChB,aAAK;AACL,oBAAY,KAAK,mBAAmB,KAAK;AACzC,iBAAS;;AAGX,aAAO;IACT;AAEO,SAAA,WAAW,MAAe,KAAK,SAAS,YAAY;AAEpD,SAAA,YAAY,MAAW;AAC5B,WAAK,OAAO,KAAK,YAAY;IAC/B;AAEO,SAAA,SAAS,CAAC,aAA0B;AACzC,WAAK,OAAO;IACd;AAEO,SAAA,QAAQ,CACb,SACA,UAAsC,MACtC,WAAuC,UACZ;AAC3B,UAAI,QAAQ;AACZ,UAAI,QAAQ;AAEZ,UAAI,OAAO,YAAY,UAAU;AAC/B,cAAM,QAAQ,IAAI,OAAO,SAAS,WAAW,MAAM,GAAG;AACtD,gBAAQ,MAAM,KACZ,KAAK,YAAY,MAAM,KAAK,MAAM,KAAK,OAAO,QAAQ,MAAM,CAAC;AAE/D,gBAAQ;iBACC,mBAAmB,QAAQ;AACpC,gBAAQ,KAAK,YAAY,MAAM,KAAK,IAAI,EAAE,MAAM,OAAO;AACvD,gBAAQ,UAAK,QAAL,UAAK,SAAA,SAAL,MAAQ,CAAC;;AAGnB,UACE,SAAS,SACR,OAAO,YAAY,YACjB,iBAAiB,SAIhB,KAAK,YAAY,WAAW,MAAM,CAAC,GAAG,KAAK,IAAI,IACnD;AACA,YAAI,SAAS;AACX,eAAK,SAAS,KAAK;AAEnB,cAAI,SAAS,MAAM,QAAQ;AACzB,iBAAK,QAAQ,MAAM;;;AAGvB,eAAO;;AAIT,aAAO;IACT;AAEO,SAAA,SAAS,CAAC,QAAqB;AACpC,WAAK,QAAQ;IACf;AAEO,SAAA,SAAS,MAAc,KAAK;AAE5B,SAAA,cAAc,MAAa;AAChC,YAAM,QAAQ,KAAK,YAAY,MAAM,KAAK;AAC1C,UAAI,SAAS;AACb,UAAI,SAAS,MAAM,WAAW,GAAG;AAC/B,cAAM,cAAc,MAAM,CAAC;AAC3B,YAAI,MAAM;AACV,eAAO,YAAY,SAAS,KAAK;AAC/B,cAAI,YAAY,WAAW,GAAG,MAAM,GAAG;AACrC,sBAAU;iBACL;AACL;;AAEF;;;AAIJ,aAAO;IACT;AAEO,SAAA,UAAU,MAAc,KAAK,YAAY,MAAM,KAAK,QAAQ,KAAK,IAAI;AA7I1E,SAAK,cAAc;EACrB;EAMQ,mBAAmB,SAAqB;AAC9C,UAAM,YAAY,KAAK,YAAY,OAAO,KAAK,IAAI;AACnD,QAAI,YAAY;AAChB,QAAI,OAAO,YAAY,UAAU;AAC/B,kBAAY,cAAc;WACrB;AACL,kBACE,mBAAmB,SACf,QAAQ,KAAK,SAAS,IACtB,QAAQ,SAAS;;AAEzB,WAAO;EACT;;;;ACbI,SAAU,gBACd,WACA,UAA0B;AAE1B,QAAM,QAAQ,UAAU,MAAM,IAAI;AAClC,QAAM,SAAS,aAAY;AAC3B,MAAI,QAAQ,OAAO,WAAU;AAC7B,MAAI,QAAQ;AAEZ,MAAI,SAA0B,IAAI,gBAAgB,EAAE;AAEpD,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,aAAS,IAAI,gBAAgB,MAAM,CAAC,CAAC;AACrC,WAAO,CAAC,OAAO,IAAG,GAAI;AACpB,cAAQ,OAAO,MAAM,QAAQ,KAAK;AAClC,YAAM,OAAO,SAAS,QAAQ,OAAO,OAAO,CAAC;AAC7C,UAAI,SAAS,SAAS;AACpB;;;AAMJ,aAAS,QAAQ,OAAO,OAAO,CAAC;AAEhC,QAAI,CAAC,MAAM,MAAM;AACf,cAAQ,OAAO,WAAU;;;AAI7B,SAAO;IACL,OAAO,OAAO,gBAAe;IAC7B,KAAK,OAAO,mBAAkB;IAC9B,QAAQ,OAAO,QAAO;IACtB;IACA;;AAEJ;AAEA,IAAY;CAAZ,SAAYC,sBAAmB;AAC7B,EAAAA,qBAAA,aAAA,IAAA;AACA,EAAAA,qBAAA,YAAA,IAAA;AACA,EAAAA,qBAAA,SAAA,IAAA;AACF,GAJY,wBAAA,sBAAmB,CAAA,EAAA;AAMxB,IAAM,oBAA4B;EAEvC,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;EAEL,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;;AAGP,IAAM,gBAAgB,CAAC,QAAgD;AACrE,MAAI,OAAO,oBAAoB;AAC/B,MAAI,KAAK;AACP,QAAI;AACF,YAAM,MAAM,GAAG,GAAG;QAChB,MAAM,MAAI;AACR,cAAI,KAAK,SAAS,YAAY;AAC5B,mBAAO,oBAAoB;AAC3B;;AAEF,cAAI,kBAAkB,SAAS,KAAK,IAAI,GAAG;AACzC,mBAAO,oBAAoB;AAC3B,mBAAO;;AAET,iBAAO;QACT;OACD;aACD,IAAM;AACN,aAAO;;;AAGX,SAAO;AACT;AAEM,SAAU,gBACd,cACA,KAAY;AAEZ,MAAI,QAAG,QAAH,QAAG,SAAA,SAAH,IAAK,SAAS,WAAW,GAAG;AAC9B,WAAO,oBAAoB;;AAE7B,SAAO,cAAc,YAAY;AACnC;AAKM,SAAU,mBACd,WACA,QACA,SAAS,GAAC;AAEV,MAAI,gBAAgB;AACpB,MAAI,gBAAgB;AACpB,MAAI,iBAAiB;AACrB,QAAM,QAAQ,gBAAgB,WAAW,CAAC,QAAQ,OAAO,OAAO,UAAS;AACvE,QACE,UAAU,OAAO,QACjB,OAAO,mBAAkB,IAAK,SAAS,OAAO,YAAY,GAC1D;AACA;;AAEF,oBAAgB;AAChB,oBAAa,OAAA,OAAA,CAAA,GAAQ,KAAK;AAC1B,qBAAiB,OAAO,QAAO;AAC/B,WAAO;EACT,CAAC;AAID,SAAO;IACL,OAAO,MAAM;IACb,KAAK,MAAM;IACX,QAAQ,kBAAkB,MAAM;IAChC,OAAO,iBAAiB,MAAM;IAC9B,OAAO,iBAAiB,MAAM;;AAElC;AAMM,SAAU,qBACd,WACA,QACA,QACA,cACA,SAAsD;AAOtD,QAAM,QACJ,gBAAgB,mBAAmB,WAAW,QAAQ,CAAC;AACzD,MAAI,CAAC,OAAO;AACV,WAAO;;AAGT,QAAM,QACJ,MAAM,MAAM,SAAS,YAAY,MAAM,MAAM,YAAY,MAAM;AACjE,MAAI,CAAC,OAAO;AACV,WAAO;;AAKT,QAAM,WAAW,YAAY,QAAQ,MAAM,KAAK;AAChD,QAAM,QAAO,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,SAAQ,gBAAgB,WAAW,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,GAAG;AACrE,SAAO;IACL;IACA;IACA;IACA;;AAEJ;;;AC7KM,SAAU,YACd,QACAC,OACA,WAAiB;AAEjB,MAAI,cAAc,mBAAmB,QAAQ,OAAO,aAAY,MAAOA,OAAM;AAC3E,WAAO;;AAET,MAAI,cAAc,iBAAiB,QAAQ,OAAO,aAAY,MAAOA,OAAM;AACzE,WAAO;;AAET,MAAI,cAAc,qBAAqB,QAAQ,gBAAgBA,KAAI,GAAG;AACpE,WAAO;;AAET,MAAI,eAAeA,OAAM;AACvB,WAAOA,MAAK,UAAS,EAAG,SAAS;;AAGnC,SAAO;AACT;AAGM,SAAU,aACd,OACA,IAA+C;AAE/C,QAAM,oBAAoB,CAAA;AAC1B,MAAI,QAAkC;AACtC,SAAO,UAAK,QAAL,UAAK,SAAA,SAAL,MAAO,MAAM;AAClB,sBAAkB,KAAK,KAAK;AAC5B,YAAQ,MAAM;;AAEhB,WAAS,IAAI,kBAAkB,SAAS,GAAG,KAAK,GAAG,KAAK;AACtD,OAAG,kBAAkB,CAAC,CAAC;;AAE3B;AAIM,SAAU,mBACd,YAAiB;AAEjB,MAAI;AAGJ,eAAa,YAAY,CAAC,UAAsB;AAC9C,YAAQ,MAAM,MAAM;MAClB,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;AACH,0BAAkB;AAClB;;EAEN,CAAC;AAED,SAAO;AACT;AAIM,SAAU,YACd,QACA,YAAiB;AAEjB,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAIA;AACJ,MAAI;AACJ,eAAa,YAAY,WAAQ;;AAC/B,YAAQ,MAAM,MAAM;MAClB,KAAK,UAAU;MACf,KAAK;AACH,QAAAA,QAAO,OAAO,aAAY;AAC1B;MACF,KAAK,UAAU;AACb,QAAAA,QAAO,OAAO,gBAAe;AAC7B;MACF,KAAK,UAAU;AACb,QAAAA,QAAO,OAAO,oBAAmB;AACjC;MACF,KAAK,UAAU;MACf,KAAK,UAAU;AACb,YAAI,MAAM,MAAM;AACd,UAAAA,QAAO,OAAO,QAAQ,MAAM,IAAI;;AAElC;MACF,KAAK,UAAU;MACf,KAAK,UAAU,eAAe;AAC5B,YAAI,CAACA,SAAQ,CAAC,MAAM,MAAM;AACxB,qBAAW;eACN;AACL,qBAAW,aACP,YAAY,QAAQ,YAAY,MAAM,IAAI,IAC1C;AACJ,UAAAA,QAAO,WAAW,SAAS,OAAO;;AAEpC;;MAEF,KAAK,UAAU;AACb,qBAAa,aAAaA,KAAK;AAC/B;MACF,KAAK,UAAU;AACb,uBAAe,MAAM,OAAO,OAAO,aAAa,MAAM,IAAI,IAAI;AAC9D;MAEF,KAAK,UAAU;AACb,YAAI,MAAM,MAAM;AACd,0BAAgB;AAChB,yBAAe,IAAI,qBAAqB;YACtC,MAAM,MAAM;YACZ,YAAY,CAAA;YACZ,QAAQ,CAAA;WACT;;AAGH;MAEF,KAAK,UAAU;AACb,YAAI,MAAM,MAAM;AACd,yBAAe;AACf,0BAAgB,IAAI,kBAAkB;YACpC,MAAM,MAAM;YACZ,YAAY,CAAA;YACZ,QAAQ,CAAA;WACT;;AAGH;MACF,KAAK,UAAU,WAAW;AACxB,YAAI,MAAM,WAAW;AACnB,kBAAQ,MAAM,UAAU,MAAM;YAC5B,KAAK,UAAU;AACb,wBAAU,YAAa,SAAS;AAChC;YACF,KAAK,UAAU;AACb,wBACE,gBAAiB,aAAa;AAChC;YAEF,KAAK,UAAU,eAAe;AAC5B,oBAAMC,SAAO,KAAA,MAAM,eAAS,QAAA,OAAA,SAAA,SAAA,GAAE;AAC9B,kBAAI,CAACA,OAAM;AACT,0BAAU;AACV;;AAEF,oBAAM,QAAQ,aACV,YAAY,QAAQ,YAAYA,KAAI,IACpC;AACJ,kBAAI,CAAC,OAAO;AACV,0BAAU;AACV;;AAEF,wBAAU,MAAM;AAChB;;YAEF;AACE,wBAAU;AACV;;eAEC;AACL,oBAAU;;AAEZ;;MAEF,KAAK,UAAU;AACb,YAAI,SAAS;AACX,mBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,gBAAI,QAAQ,CAAC,EAAE,SAAS,MAAM,MAAM;AAClC,uBAAS,QAAQ,CAAC;AAClB;;;;AAIN,oBAAY,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ;AACpB;MACF,KAAK,UAAU;MACf,KAAK,UAAU;AACb,QAAAD,QAAO;AACP;MAEF,KAAK,UAAU;AACb,cAAM,WAAW,aAAa,SAAU;AACxC,oBACE,oBAAoB,kBAChB,SACG,UAAS,EACT,KAAK,CAAC,QAA0B,IAAI,UAAU,MAAM,IAAI,IAC3D;AACN;MAEF,KAAK,UAAU;AACb,cAAM,eAAe,gBAAgB,SAAU;AAC/C,oBACE,wBAAwB,cAAc,aAAa,SAAS;AAC9D;MACF,KAAK,UAAU;AACb,cAAM,aAAa,aAAa,SAAU;AAC1C,0BACE,sBAAsB,yBAClB,WAAW,UAAS,IACpB;AACN;MAEF,KAAK,UAAU;AACb,cAAM,cACJ,MAAM,QAAQ,kBAAkB,gBAAgB,MAAM,IAAI,IAAI;AAChE,oBAAY,gBAAW,QAAX,gBAAW,SAAA,SAAX,YAAa;AAEzB,mBAAW;AACX,QAAAA,QAAO,WAAW,SAAS,OAAO;AAClC;MACF,KAAK,UAAU;AACb,YAAI,MAAM,MAAM;AACd,UAAAA,QAAO,OAAO,QAAQ,MAAM,IAAI;;AAMlC;;EAEN,CAAC;AAED,SAAO;IACL;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAAA;IACA;IACA;;AAEJ;;;AC7NO,IAAM,sBAA4C;EACvD,eAAe;EACf,WAAW;EACX,aAAa;EACb,OAAO;EACP,UAAU;EACV,cAAc;EACd,gBAAgB;EAChB,SAAS;EACT,SAAS;EACT,YAAY;EACZ,YAAY;EACZ,iBAAiB;EACjB,cAAc;EACd,YAAY;EACZ,eAAe;EACf,WAAW;EACX,UAAU;EACV,YAAY;EACZ,WAAW;EACX,WAAW;EACX,iBAAiB;EACjB,eAAe;EACf,YAAY;EACZ,sBAAsB;EACtB,eAAe;EACf,YAAY;EACZ,sBAAsB;EACtB,MAAM;EACN,UAAU;;AAmCL,IAAM,YAAS,OAAA,OAAA,OAAA,OAAA,CAAA,GACjB,IAAI,GACJ,mBAAmB;;;ACtHjB,IAAI;AAAA,CACV,SAAUE,cAAa;AACpB,WAAS,GAAG,OAAO;AACf,WAAO,OAAO,UAAU;AAAA,EAC5B;AACA,EAAAA,aAAY,KAAK;AACrB,GAAG,gBAAgB,cAAc,CAAC,EAAE;AAC7B,IAAI;AAAA,CACV,SAAUC,MAAK;AACZ,WAAS,GAAG,OAAO;AACf,WAAO,OAAO,UAAU;AAAA,EAC5B;AACA,EAAAA,KAAI,KAAK;AACb,GAAG,QAAQ,MAAM,CAAC,EAAE;AACb,IAAI;AAAA,CACV,SAAUC,UAAS;AAChB,EAAAA,SAAQ,YAAY;AACpB,EAAAA,SAAQ,YAAY;AACpB,WAAS,GAAG,OAAO;AACf,WAAO,OAAO,UAAU,YAAYA,SAAQ,aAAa,SAAS,SAASA,SAAQ;AAAA,EACvF;AACA,EAAAA,SAAQ,KAAK;AACjB,GAAG,YAAY,UAAU,CAAC,EAAE;AACrB,IAAI;AAAA,CACV,SAAUC,WAAU;AACjB,EAAAA,UAAS,YAAY;AACrB,EAAAA,UAAS,YAAY;AACrB,WAAS,GAAG,OAAO;AACf,WAAO,OAAO,UAAU,YAAYA,UAAS,aAAa,SAAS,SAASA,UAAS;AAAA,EACzF;AACA,EAAAA,UAAS,KAAK;AAClB,GAAG,aAAa,WAAW,CAAC,EAAE;AAKvB,IAAI;AAAA,CACV,SAAUC,WAAU;AAMjB,WAAS,OAAO,MAAM,WAAW;AAC7B,QAAI,SAAS,OAAO,WAAW;AAC3B,aAAO,SAAS;AAAA,IACpB;AACA,QAAI,cAAc,OAAO,WAAW;AAChC,kBAAY,SAAS;AAAA,IACzB;AACA,WAAO,EAAE,MAAM,UAAU;AAAA,EAC7B;AACA,EAAAA,UAAS,SAAS;AAIlB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,cAAc,SAAS,KAAK,GAAG,SAAS,UAAU,IAAI,KAAK,GAAG,SAAS,UAAU,SAAS;AAAA,EACxG;AACA,EAAAA,UAAS,KAAK;AAClB,GAAG,aAAa,WAAW,CAAC,EAAE;AAKvB,IAAI;AAAA,CACV,SAAUC,QAAO;AACd,WAAS,OAAO,KAAK,KAAK,OAAO,MAAM;AACnC,QAAI,GAAG,SAAS,GAAG,KAAK,GAAG,SAAS,GAAG,KAAK,GAAG,SAAS,KAAK,KAAK,GAAG,SAAS,IAAI,GAAG;AACjF,aAAO,EAAE,OAAO,SAAS,OAAO,KAAK,GAAG,GAAG,KAAK,SAAS,OAAO,OAAO,IAAI,EAAE;AAAA,IACjF,WACS,SAAS,GAAG,GAAG,KAAK,SAAS,GAAG,GAAG,GAAG;AAC3C,aAAO,EAAE,OAAO,KAAK,KAAK,IAAI;AAAA,IAClC,OACK;AACD,YAAM,IAAI,MAAM,8CAA8C,GAAG,KAAK,GAAG,KAAK,KAAK,KAAK,IAAI,GAAG;AAAA,IACnG;AAAA,EACJ;AACA,EAAAA,OAAM,SAAS;AAIf,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,cAAc,SAAS,KAAK,SAAS,GAAG,UAAU,KAAK,KAAK,SAAS,GAAG,UAAU,GAAG;AAAA,EACnG;AACA,EAAAA,OAAM,KAAK;AACf,GAAG,UAAU,QAAQ,CAAC,EAAE;AAKjB,IAAI;AAAA,CACV,SAAUC,WAAU;AAMjB,WAAS,OAAO,KAAK,OAAO;AACxB,WAAO,EAAE,KAAK,MAAM;AAAA,EACxB;AACA,EAAAA,UAAS,SAAS;AAIlB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,cAAc,SAAS,KAAK,MAAM,GAAG,UAAU,KAAK,MAAM,GAAG,OAAO,UAAU,GAAG,KAAK,GAAG,UAAU,UAAU,GAAG;AAAA,EAC9H;AACA,EAAAA,UAAS,KAAK;AAClB,GAAG,aAAa,WAAW,CAAC,EAAE;AAKvB,IAAI;AAAA,CACV,SAAUC,eAAc;AAQrB,WAAS,OAAO,WAAW,aAAa,sBAAsB,sBAAsB;AAChF,WAAO,EAAE,WAAW,aAAa,sBAAsB,qBAAqB;AAAA,EAChF;AACA,EAAAA,cAAa,SAAS;AAItB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,cAAc,SAAS,KAAK,MAAM,GAAG,UAAU,WAAW,KAAK,GAAG,OAAO,UAAU,SAAS,KAC/F,MAAM,GAAG,UAAU,oBAAoB,MACtC,MAAM,GAAG,UAAU,oBAAoB,KAAK,GAAG,UAAU,UAAU,oBAAoB;AAAA,EACnG;AACA,EAAAA,cAAa,KAAK;AACtB,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAK/B,IAAI;AAAA,CACV,SAAUC,QAAO;AAId,WAAS,OAAO,KAAK,OAAO,MAAM,OAAO;AACrC,WAAO;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,OAAM,SAAS;AAIf,WAAS,GAAG,OAAO;AACf,UAAM,YAAY;AAClB,WAAO,GAAG,cAAc,SAAS,KAAK,GAAG,YAAY,UAAU,KAAK,GAAG,CAAC,KACjE,GAAG,YAAY,UAAU,OAAO,GAAG,CAAC,KACpC,GAAG,YAAY,UAAU,MAAM,GAAG,CAAC,KACnC,GAAG,YAAY,UAAU,OAAO,GAAG,CAAC;AAAA,EAC/C;AACA,EAAAA,OAAM,KAAK;AACf,GAAG,UAAU,QAAQ,CAAC,EAAE;AAKjB,IAAI;AAAA,CACV,SAAUC,mBAAkB;AAIzB,WAAS,OAAO,OAAO,OAAO;AAC1B,WAAO;AAAA,MACH;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,kBAAiB,SAAS;AAI1B,WAAS,GAAG,OAAO;AACf,UAAM,YAAY;AAClB,WAAO,GAAG,cAAc,SAAS,KAAK,MAAM,GAAG,UAAU,KAAK,KAAK,MAAM,GAAG,UAAU,KAAK;AAAA,EAC/F;AACA,EAAAA,kBAAiB,KAAK;AAC1B,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAKvC,IAAI;AAAA,CACV,SAAUC,oBAAmB;AAI1B,WAAS,OAAO,OAAO,UAAU,qBAAqB;AAClD,WAAO;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,mBAAkB,SAAS;AAI3B,WAAS,GAAG,OAAO;AACf,UAAM,YAAY;AAClB,WAAO,GAAG,cAAc,SAAS,KAAK,GAAG,OAAO,UAAU,KAAK,MACvD,GAAG,UAAU,UAAU,QAAQ,KAAK,SAAS,GAAG,SAAS,OACzD,GAAG,UAAU,UAAU,mBAAmB,KAAK,GAAG,WAAW,UAAU,qBAAqB,SAAS,EAAE;AAAA,EACnH;AACA,EAAAA,mBAAkB,KAAK;AAC3B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAIzC,IAAI;AAAA,CACV,SAAUC,mBAAkB;AAIzB,EAAAA,kBAAiB,UAAU;AAI3B,EAAAA,kBAAiB,UAAU;AAI3B,EAAAA,kBAAiB,SAAS;AAC9B,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAKvC,IAAI;AAAA,CACV,SAAUC,eAAc;AAIrB,WAAS,OAAO,WAAW,SAAS,gBAAgB,cAAc,MAAM,eAAe;AACnF,UAAM,SAAS;AAAA,MACX;AAAA,MACA;AAAA,IACJ;AACA,QAAI,GAAG,QAAQ,cAAc,GAAG;AAC5B,aAAO,iBAAiB;AAAA,IAC5B;AACA,QAAI,GAAG,QAAQ,YAAY,GAAG;AAC1B,aAAO,eAAe;AAAA,IAC1B;AACA,QAAI,GAAG,QAAQ,IAAI,GAAG;AAClB,aAAO,OAAO;AAAA,IAClB;AACA,QAAI,GAAG,QAAQ,aAAa,GAAG;AAC3B,aAAO,gBAAgB;AAAA,IAC3B;AACA,WAAO;AAAA,EACX;AACA,EAAAA,cAAa,SAAS;AAItB,WAAS,GAAG,OAAO;AACf,UAAM,YAAY;AAClB,WAAO,GAAG,cAAc,SAAS,KAAK,GAAG,SAAS,UAAU,SAAS,KAAK,GAAG,SAAS,UAAU,SAAS,MACjG,GAAG,UAAU,UAAU,cAAc,KAAK,GAAG,SAAS,UAAU,cAAc,OAC9E,GAAG,UAAU,UAAU,YAAY,KAAK,GAAG,SAAS,UAAU,YAAY,OAC1E,GAAG,UAAU,UAAU,IAAI,KAAK,GAAG,OAAO,UAAU,IAAI;AAAA,EACpE;AACA,EAAAA,cAAa,KAAK;AACtB,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAK/B,IAAI;AAAA,CACV,SAAUC,+BAA8B;AAIrC,WAAS,OAAO,UAAU,SAAS;AAC/B,WAAO;AAAA,MACH;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,8BAA6B,SAAS;AAItC,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,SAAS,GAAG,UAAU,QAAQ,KAAK,GAAG,OAAO,UAAU,OAAO;AAAA,EAClG;AACA,EAAAA,8BAA6B,KAAK;AACtC,GAAG,iCAAiC,+BAA+B,CAAC,EAAE;AAI/D,IAAI;AAAA,CACV,SAAUC,qBAAoB;AAI3B,EAAAA,oBAAmB,QAAQ;AAI3B,EAAAA,oBAAmB,UAAU;AAI7B,EAAAA,oBAAmB,cAAc;AAIjC,EAAAA,oBAAmB,OAAO;AAC9B,GAAG,uBAAuB,qBAAqB,CAAC,EAAE;AAM3C,IAAI;AAAA,CACV,SAAUC,gBAAe;AAOtB,EAAAA,eAAc,cAAc;AAM5B,EAAAA,eAAc,aAAa;AAC/B,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AAMjC,IAAI;AAAA,CACV,SAAUC,kBAAiB;AACxB,WAAS,GAAG,OAAO;AACf,UAAM,YAAY;AAClB,WAAO,GAAG,cAAc,SAAS,KAAK,GAAG,OAAO,UAAU,IAAI;AAAA,EAClE;AACA,EAAAA,iBAAgB,KAAK;AACzB,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAKrC,IAAI;AAAA,CACV,SAAUC,aAAY;AAInB,WAAS,OAAO,OAAO,SAAS,UAAU,MAAM,QAAQ,oBAAoB;AACxE,QAAI,SAAS,EAAE,OAAO,QAAQ;AAC9B,QAAI,GAAG,QAAQ,QAAQ,GAAG;AACtB,aAAO,WAAW;AAAA,IACtB;AACA,QAAI,GAAG,QAAQ,IAAI,GAAG;AAClB,aAAO,OAAO;AAAA,IAClB;AACA,QAAI,GAAG,QAAQ,MAAM,GAAG;AACpB,aAAO,SAAS;AAAA,IACpB;AACA,QAAI,GAAG,QAAQ,kBAAkB,GAAG;AAChC,aAAO,qBAAqB;AAAA,IAChC;AACA,WAAO;AAAA,EACX;AACA,EAAAA,YAAW,SAAS;AAIpB,WAAS,GAAG,OAAO;AACf,QAAI;AACJ,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KACpB,MAAM,GAAG,UAAU,KAAK,KACxB,GAAG,OAAO,UAAU,OAAO,MAC1B,GAAG,OAAO,UAAU,QAAQ,KAAK,GAAG,UAAU,UAAU,QAAQ,OAChE,GAAG,QAAQ,UAAU,IAAI,KAAK,GAAG,OAAO,UAAU,IAAI,KAAK,GAAG,UAAU,UAAU,IAAI,OACtF,GAAG,UAAU,UAAU,eAAe,KAAM,GAAG,QAAQ,KAAK,UAAU,qBAAqB,QAAQ,OAAO,SAAS,SAAS,GAAG,IAAI,OACnI,GAAG,OAAO,UAAU,MAAM,KAAK,GAAG,UAAU,UAAU,MAAM,OAC5D,GAAG,UAAU,UAAU,kBAAkB,KAAK,GAAG,WAAW,UAAU,oBAAoB,6BAA6B,EAAE;AAAA,EACrI;AACA,EAAAA,YAAW,KAAK;AACpB,GAAG,eAAe,aAAa,CAAC,EAAE;AAK3B,IAAI;AAAA,CACV,SAAUC,UAAS;AAIhB,WAAS,OAAO,OAAO,YAAY,MAAM;AACrC,QAAI,SAAS,EAAE,OAAO,QAAQ;AAC9B,QAAI,GAAG,QAAQ,IAAI,KAAK,KAAK,SAAS,GAAG;AACrC,aAAO,YAAY;AAAA,IACvB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,SAAQ,SAAS;AAIjB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,KAAK,KAAK,GAAG,OAAO,UAAU,OAAO;AAAA,EAC7F;AACA,EAAAA,SAAQ,KAAK;AACjB,GAAG,YAAY,UAAU,CAAC,EAAE;AAKrB,IAAI;AAAA,CACV,SAAUC,WAAU;AAMjB,WAAS,QAAQ,OAAO,SAAS;AAC7B,WAAO,EAAE,OAAO,QAAQ;AAAA,EAC5B;AACA,EAAAA,UAAS,UAAU;AAMnB,WAAS,OAAO,UAAU,SAAS;AAC/B,WAAO,EAAE,OAAO,EAAE,OAAO,UAAU,KAAK,SAAS,GAAG,QAAQ;AAAA,EAChE;AACA,EAAAA,UAAS,SAAS;AAKlB,WAAS,IAAI,OAAO;AAChB,WAAO,EAAE,OAAO,SAAS,GAAG;AAAA,EAChC;AACA,EAAAA,UAAS,MAAM;AACf,WAAS,GAAG,OAAO;AACf,UAAM,YAAY;AAClB,WAAO,GAAG,cAAc,SAAS,KAC1B,GAAG,OAAO,UAAU,OAAO,KAC3B,MAAM,GAAG,UAAU,KAAK;AAAA,EACnC;AACA,EAAAA,UAAS,KAAK;AAClB,GAAG,aAAa,WAAW,CAAC,EAAE;AACvB,IAAI;AAAA,CACV,SAAUC,mBAAkB;AACzB,WAAS,OAAO,OAAO,mBAAmB,aAAa;AACnD,UAAM,SAAS,EAAE,MAAM;AACvB,QAAI,sBAAsB,QAAW;AACjC,aAAO,oBAAoB;AAAA,IAC/B;AACA,QAAI,gBAAgB,QAAW;AAC3B,aAAO,cAAc;AAAA,IACzB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,kBAAiB,SAAS;AAC1B,WAAS,GAAG,OAAO;AACf,UAAM,YAAY;AAClB,WAAO,GAAG,cAAc,SAAS,KAAK,GAAG,OAAO,UAAU,KAAK,MAC1D,GAAG,QAAQ,UAAU,iBAAiB,KAAK,UAAU,sBAAsB,YAC3E,GAAG,OAAO,UAAU,WAAW,KAAK,UAAU,gBAAgB;AAAA,EACvE;AACA,EAAAA,kBAAiB,KAAK;AAC1B,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AACvC,IAAI;AAAA,CACV,SAAUC,6BAA4B;AACnC,WAAS,GAAG,OAAO;AACf,UAAM,YAAY;AAClB,WAAO,GAAG,OAAO,SAAS;AAAA,EAC9B;AACA,EAAAA,4BAA2B,KAAK;AACpC,GAAG,+BAA+B,6BAA6B,CAAC,EAAE;AAC3D,IAAI;AAAA,CACV,SAAUC,oBAAmB;AAQ1B,WAAS,QAAQ,OAAO,SAAS,YAAY;AACzC,WAAO,EAAE,OAAO,SAAS,cAAc,WAAW;AAAA,EACtD;AACA,EAAAA,mBAAkB,UAAU;AAQ5B,WAAS,OAAO,UAAU,SAAS,YAAY;AAC3C,WAAO,EAAE,OAAO,EAAE,OAAO,UAAU,KAAK,SAAS,GAAG,SAAS,cAAc,WAAW;AAAA,EAC1F;AACA,EAAAA,mBAAkB,SAAS;AAO3B,WAAS,IAAI,OAAO,YAAY;AAC5B,WAAO,EAAE,OAAO,SAAS,IAAI,cAAc,WAAW;AAAA,EAC1D;AACA,EAAAA,mBAAkB,MAAM;AACxB,WAAS,GAAG,OAAO;AACf,UAAM,YAAY;AAClB,WAAO,SAAS,GAAG,SAAS,MAAM,iBAAiB,GAAG,UAAU,YAAY,KAAK,2BAA2B,GAAG,UAAU,YAAY;AAAA,EACzI;AACA,EAAAA,mBAAkB,KAAK;AAC3B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAKzC,IAAI;AAAA,CACV,SAAUC,mBAAkB;AAIzB,WAAS,OAAO,cAAc,OAAO;AACjC,WAAO,EAAE,cAAc,MAAM;AAAA,EACjC;AACA,EAAAA,kBAAiB,SAAS;AAC1B,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KACpB,wCAAwC,GAAG,UAAU,YAAY,KACjE,MAAM,QAAQ,UAAU,KAAK;AAAA,EACxC;AACA,EAAAA,kBAAiB,KAAK;AAC1B,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AACvC,IAAI;AAAA,CACV,SAAUC,aAAY;AACnB,WAAS,OAAO,KAAK,SAAS,YAAY;AACtC,QAAI,SAAS;AAAA,MACT,MAAM;AAAA,MACN;AAAA,IACJ;AACA,QAAI,YAAY,WAAc,QAAQ,cAAc,UAAa,QAAQ,mBAAmB,SAAY;AACpG,aAAO,UAAU;AAAA,IACrB;AACA,QAAI,eAAe,QAAW;AAC1B,aAAO,eAAe;AAAA,IAC1B;AACA,WAAO;AAAA,EACX;AACA,EAAAA,YAAW,SAAS;AACpB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,aAAa,UAAU,SAAS,YAAY,GAAG,OAAO,UAAU,GAAG,MAAM,UAAU,YAAY,WAChG,UAAU,QAAQ,cAAc,UAAa,GAAG,QAAQ,UAAU,QAAQ,SAAS,OAAO,UAAU,QAAQ,mBAAmB,UAAa,GAAG,QAAQ,UAAU,QAAQ,cAAc,QAAS,UAAU,iBAAiB,UAAa,2BAA2B,GAAG,UAAU,YAAY;AAAA,EACtS;AACA,EAAAA,YAAW,KAAK;AACpB,GAAG,eAAe,aAAa,CAAC,EAAE;AAC3B,IAAI;AAAA,CACV,SAAUC,aAAY;AACnB,WAAS,OAAO,QAAQ,QAAQ,SAAS,YAAY;AACjD,QAAI,SAAS;AAAA,MACT,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACJ;AACA,QAAI,YAAY,WAAc,QAAQ,cAAc,UAAa,QAAQ,mBAAmB,SAAY;AACpG,aAAO,UAAU;AAAA,IACrB;AACA,QAAI,eAAe,QAAW;AAC1B,aAAO,eAAe;AAAA,IAC1B;AACA,WAAO;AAAA,EACX;AACA,EAAAA,YAAW,SAAS;AACpB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,aAAa,UAAU,SAAS,YAAY,GAAG,OAAO,UAAU,MAAM,KAAK,GAAG,OAAO,UAAU,MAAM,MAAM,UAAU,YAAY,WAClI,UAAU,QAAQ,cAAc,UAAa,GAAG,QAAQ,UAAU,QAAQ,SAAS,OAAO,UAAU,QAAQ,mBAAmB,UAAa,GAAG,QAAQ,UAAU,QAAQ,cAAc,QAAS,UAAU,iBAAiB,UAAa,2BAA2B,GAAG,UAAU,YAAY;AAAA,EACtS;AACA,EAAAA,YAAW,KAAK;AACpB,GAAG,eAAe,aAAa,CAAC,EAAE;AAC3B,IAAI;AAAA,CACV,SAAUC,aAAY;AACnB,WAAS,OAAO,KAAK,SAAS,YAAY;AACtC,QAAI,SAAS;AAAA,MACT,MAAM;AAAA,MACN;AAAA,IACJ;AACA,QAAI,YAAY,WAAc,QAAQ,cAAc,UAAa,QAAQ,sBAAsB,SAAY;AACvG,aAAO,UAAU;AAAA,IACrB;AACA,QAAI,eAAe,QAAW;AAC1B,aAAO,eAAe;AAAA,IAC1B;AACA,WAAO;AAAA,EACX;AACA,EAAAA,YAAW,SAAS;AACpB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,aAAa,UAAU,SAAS,YAAY,GAAG,OAAO,UAAU,GAAG,MAAM,UAAU,YAAY,WAChG,UAAU,QAAQ,cAAc,UAAa,GAAG,QAAQ,UAAU,QAAQ,SAAS,OAAO,UAAU,QAAQ,sBAAsB,UAAa,GAAG,QAAQ,UAAU,QAAQ,iBAAiB,QAAS,UAAU,iBAAiB,UAAa,2BAA2B,GAAG,UAAU,YAAY;AAAA,EAC5S;AACA,EAAAA,YAAW,KAAK;AACpB,GAAG,eAAe,aAAa,CAAC,EAAE;AAC3B,IAAI;AAAA,CACV,SAAUC,gBAAe;AACtB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,cACF,UAAU,YAAY,UAAa,UAAU,oBAAoB,YACjE,UAAU,oBAAoB,UAAa,UAAU,gBAAgB,MAAM,CAAC,WAAW;AACpF,UAAI,GAAG,OAAO,OAAO,IAAI,GAAG;AACxB,eAAO,WAAW,GAAG,MAAM,KAAK,WAAW,GAAG,MAAM,KAAK,WAAW,GAAG,MAAM;AAAA,MACjF,OACK;AACD,eAAO,iBAAiB,GAAG,MAAM;AAAA,MACrC;AAAA,IACJ,CAAC;AAAA,EACT;AACA,EAAAA,eAAc,KAAK;AACvB,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AAuSjC,IAAI;AAAA,CACV,SAAUC,yBAAwB;AAK/B,WAAS,OAAO,KAAK;AACjB,WAAO,EAAE,IAAI;AAAA,EACjB;AACA,EAAAA,wBAAuB,SAAS;AAIhC,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,GAAG;AAAA,EAC3D;AACA,EAAAA,wBAAuB,KAAK;AAChC,GAAG,2BAA2B,yBAAyB,CAAC,EAAE;AAKnD,IAAI;AAAA,CACV,SAAUC,kCAAiC;AAMxC,WAAS,OAAO,KAAK,SAAS;AAC1B,WAAO,EAAE,KAAK,QAAQ;AAAA,EAC1B;AACA,EAAAA,iCAAgC,SAAS;AAIzC,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,GAAG,KAAK,GAAG,QAAQ,UAAU,OAAO;AAAA,EAC5F;AACA,EAAAA,iCAAgC,KAAK;AACzC,GAAG,oCAAoC,kCAAkC,CAAC,EAAE;AAKrE,IAAI;AAAA,CACV,SAAUC,0CAAyC;AAMhD,WAAS,OAAO,KAAK,SAAS;AAC1B,WAAO,EAAE,KAAK,QAAQ;AAAA,EAC1B;AACA,EAAAA,yCAAwC,SAAS;AAIjD,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,GAAG,MAAM,UAAU,YAAY,QAAQ,GAAG,QAAQ,UAAU,OAAO;AAAA,EAC3H;AACA,EAAAA,yCAAwC,KAAK;AACjD,GAAG,4CAA4C,0CAA0C,CAAC,EAAE;AAKrF,IAAI;AAAA,CACV,SAAUC,mBAAkB;AAQzB,WAAS,OAAO,KAAK,YAAY,SAAS,MAAM;AAC5C,WAAO,EAAE,KAAK,YAAY,SAAS,KAAK;AAAA,EAC5C;AACA,EAAAA,kBAAiB,SAAS;AAI1B,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,GAAG,KAAK,GAAG,OAAO,UAAU,UAAU,KAAK,GAAG,QAAQ,UAAU,OAAO,KAAK,GAAG,OAAO,UAAU,IAAI;AAAA,EAC5J;AACA,EAAAA,kBAAiB,KAAK;AAC1B,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAQvC,IAAI;AAAA,CACV,SAAUC,aAAY;AAInB,EAAAA,YAAW,YAAY;AAIvB,EAAAA,YAAW,WAAW;AAItB,WAAS,GAAG,OAAO;AACf,UAAM,YAAY;AAClB,WAAO,cAAcA,YAAW,aAAa,cAAcA,YAAW;AAAA,EAC1E;AACA,EAAAA,YAAW,KAAK;AACpB,GAAG,eAAe,aAAa,CAAC,EAAE;AAC3B,IAAI;AAAA,CACV,SAAUC,gBAAe;AAItB,WAAS,GAAG,OAAO;AACf,UAAM,YAAY;AAClB,WAAO,GAAG,cAAc,KAAK,KAAK,WAAW,GAAG,UAAU,IAAI,KAAK,GAAG,OAAO,UAAU,KAAK;AAAA,EAChG;AACA,EAAAA,eAAc,KAAK;AACvB,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AAIjC,IAAI;AAAA,CACV,SAAUC,qBAAoB;AAC3B,EAAAA,oBAAmB,OAAO;AAC1B,EAAAA,oBAAmB,SAAS;AAC5B,EAAAA,oBAAmB,WAAW;AAC9B,EAAAA,oBAAmB,cAAc;AACjC,EAAAA,oBAAmB,QAAQ;AAC3B,EAAAA,oBAAmB,WAAW;AAC9B,EAAAA,oBAAmB,QAAQ;AAC3B,EAAAA,oBAAmB,YAAY;AAC/B,EAAAA,oBAAmB,SAAS;AAC5B,EAAAA,oBAAmB,WAAW;AAC9B,EAAAA,oBAAmB,OAAO;AAC1B,EAAAA,oBAAmB,QAAQ;AAC3B,EAAAA,oBAAmB,OAAO;AAC1B,EAAAA,oBAAmB,UAAU;AAC7B,EAAAA,oBAAmB,UAAU;AAC7B,EAAAA,oBAAmB,QAAQ;AAC3B,EAAAA,oBAAmB,OAAO;AAC1B,EAAAA,oBAAmB,YAAY;AAC/B,EAAAA,oBAAmB,SAAS;AAC5B,EAAAA,oBAAmB,aAAa;AAChC,EAAAA,oBAAmB,WAAW;AAC9B,EAAAA,oBAAmB,SAAS;AAC5B,EAAAA,oBAAmB,QAAQ;AAC3B,EAAAA,oBAAmB,WAAW;AAC9B,EAAAA,oBAAmB,gBAAgB;AACvC,GAAG,uBAAuB,qBAAqB,CAAC,EAAE;AAK3C,IAAI;AAAA,CACV,SAAUC,mBAAkB;AAIzB,EAAAA,kBAAiB,YAAY;AAW7B,EAAAA,kBAAiB,UAAU;AAC/B,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAOvC,IAAI;AAAA,CACV,SAAUC,oBAAmB;AAI1B,EAAAA,mBAAkB,aAAa;AACnC,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAMzC,IAAI;AAAA,CACV,SAAUC,oBAAmB;AAI1B,WAAS,OAAO,SAAS,QAAQ,SAAS;AACtC,WAAO,EAAE,SAAS,QAAQ,QAAQ;AAAA,EACtC;AACA,EAAAA,mBAAkB,SAAS;AAI3B,WAAS,GAAG,OAAO;AACf,UAAM,YAAY;AAClB,WAAO,aAAa,GAAG,OAAO,UAAU,OAAO,KAAK,MAAM,GAAG,UAAU,MAAM,KAAK,MAAM,GAAG,UAAU,OAAO;AAAA,EAChH;AACA,EAAAA,mBAAkB,KAAK;AAC3B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAOzC,IAAI;AAAA,CACV,SAAUC,iBAAgB;AAQvB,EAAAA,gBAAe,OAAO;AAUtB,EAAAA,gBAAe,oBAAoB;AACvC,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AACnC,IAAI;AAAA,CACV,SAAUC,6BAA4B;AACnC,WAAS,GAAG,OAAO;AACf,UAAM,YAAY;AAClB,WAAO,cAAc,GAAG,OAAO,UAAU,MAAM,KAAK,UAAU,WAAW,YACpE,GAAG,OAAO,UAAU,WAAW,KAAK,UAAU,gBAAgB;AAAA,EACvE;AACA,EAAAA,4BAA2B,KAAK;AACpC,GAAG,+BAA+B,6BAA6B,CAAC,EAAE;AAK3D,IAAI;AAAA,CACV,SAAUC,iBAAgB;AAKvB,WAAS,OAAO,OAAO;AACnB,WAAO,EAAE,MAAM;AAAA,EACnB;AACA,EAAAA,gBAAe,SAAS;AAC5B,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAKnC,IAAI;AAAA,CACV,SAAUC,iBAAgB;AAOvB,WAAS,OAAO,OAAO,cAAc;AACjC,WAAO,EAAE,OAAO,QAAQ,QAAQ,CAAC,GAAG,cAAc,CAAC,CAAC,aAAa;AAAA,EACrE;AACA,EAAAA,gBAAe,SAAS;AAC5B,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AACnC,IAAI;AAAA,CACV,SAAUC,eAAc;AAMrB,WAAS,cAAc,WAAW;AAC9B,WAAO,UAAU,QAAQ,yBAAyB,MAAM;AAAA,EAC5D;AACA,EAAAA,cAAa,gBAAgB;AAI7B,WAAS,GAAG,OAAO;AACf,UAAM,YAAY;AAClB,WAAO,GAAG,OAAO,SAAS,KAAM,GAAG,cAAc,SAAS,KAAK,GAAG,OAAO,UAAU,QAAQ,KAAK,GAAG,OAAO,UAAU,KAAK;AAAA,EAC7H;AACA,EAAAA,cAAa,KAAK;AACtB,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAC/B,IAAI;AAAA,CACV,SAAUC,QAAO;AAId,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,CAAC,CAAC,aAAa,GAAG,cAAc,SAAS,MAAM,cAAc,GAAG,UAAU,QAAQ,KACrF,aAAa,GAAG,UAAU,QAAQ,KAClC,GAAG,WAAW,UAAU,UAAU,aAAa,EAAE,OAAO,MAAM,UAAU,UAAa,MAAM,GAAG,MAAM,KAAK;AAAA,EACjH;AACA,EAAAA,OAAM,KAAK;AACf,GAAG,UAAU,QAAQ,CAAC,EAAE;AAKjB,IAAI;AAAA,CACV,SAAUC,uBAAsB;AAO7B,WAAS,OAAO,OAAO,eAAe;AAClC,WAAO,gBAAgB,EAAE,OAAO,cAAc,IAAI,EAAE,MAAM;AAAA,EAC9D;AACA,EAAAA,sBAAqB,SAAS;AAClC,GAAG,yBAAyB,uBAAuB,CAAC,EAAE;AAK/C,IAAI;AAAA,CACV,SAAUC,uBAAsB;AAC7B,WAAS,OAAO,OAAO,kBAAkB,YAAY;AACjD,QAAI,SAAS,EAAE,MAAM;AACrB,QAAI,GAAG,QAAQ,aAAa,GAAG;AAC3B,aAAO,gBAAgB;AAAA,IAC3B;AACA,QAAI,GAAG,QAAQ,UAAU,GAAG;AACxB,aAAO,aAAa;AAAA,IACxB,OACK;AACD,aAAO,aAAa,CAAC;AAAA,IACzB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,sBAAqB,SAAS;AAClC,GAAG,yBAAyB,uBAAuB,CAAC,EAAE;AAI/C,IAAI;AAAA,CACV,SAAUC,wBAAuB;AAI9B,EAAAA,uBAAsB,OAAO;AAI7B,EAAAA,uBAAsB,OAAO;AAI7B,EAAAA,uBAAsB,QAAQ;AAClC,GAAG,0BAA0B,wBAAwB,CAAC,EAAE;AAKjD,IAAI;AAAA,CACV,SAAUC,oBAAmB;AAM1B,WAAS,OAAO,OAAO,MAAM;AACzB,QAAI,SAAS,EAAE,MAAM;AACrB,QAAI,GAAG,OAAO,IAAI,GAAG;AACjB,aAAO,OAAO;AAAA,IAClB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,mBAAkB,SAAS;AAC/B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAIzC,IAAI;AAAA,CACV,SAAUC,aAAY;AACnB,EAAAA,YAAW,OAAO;AAClB,EAAAA,YAAW,SAAS;AACpB,EAAAA,YAAW,YAAY;AACvB,EAAAA,YAAW,UAAU;AACrB,EAAAA,YAAW,QAAQ;AACnB,EAAAA,YAAW,SAAS;AACpB,EAAAA,YAAW,WAAW;AACtB,EAAAA,YAAW,QAAQ;AACnB,EAAAA,YAAW,cAAc;AACzB,EAAAA,YAAW,OAAO;AAClB,EAAAA,YAAW,YAAY;AACvB,EAAAA,YAAW,WAAW;AACtB,EAAAA,YAAW,WAAW;AACtB,EAAAA,YAAW,WAAW;AACtB,EAAAA,YAAW,SAAS;AACpB,EAAAA,YAAW,SAAS;AACpB,EAAAA,YAAW,UAAU;AACrB,EAAAA,YAAW,QAAQ;AACnB,EAAAA,YAAW,SAAS;AACpB,EAAAA,YAAW,MAAM;AACjB,EAAAA,YAAW,OAAO;AAClB,EAAAA,YAAW,aAAa;AACxB,EAAAA,YAAW,SAAS;AACpB,EAAAA,YAAW,QAAQ;AACnB,EAAAA,YAAW,WAAW;AACtB,EAAAA,YAAW,gBAAgB;AAC/B,GAAG,eAAe,aAAa,CAAC,EAAE;AAM3B,IAAI;AAAA,CACV,SAAUC,YAAW;AAIlB,EAAAA,WAAU,aAAa;AAC3B,GAAG,cAAc,YAAY,CAAC,EAAE;AACzB,IAAI;AAAA,CACV,SAAUC,oBAAmB;AAU1B,WAAS,OAAOC,OAAM,MAAM,OAAO,KAAK,eAAe;AACnD,QAAI,SAAS;AAAA,MACT,MAAAA;AAAA,MACA;AAAA,MACA,UAAU,EAAE,KAAK,MAAM;AAAA,IAC3B;AACA,QAAI,eAAe;AACf,aAAO,gBAAgB;AAAA,IAC3B;AACA,WAAO;AAAA,EACX;AACA,EAAAD,mBAAkB,SAAS;AAC/B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AACzC,IAAI;AAAA,CACV,SAAUE,kBAAiB;AAUxB,WAAS,OAAOD,OAAM,MAAM,KAAK,OAAO;AACpC,WAAO,UAAU,SACX,EAAE,MAAAA,OAAM,MAAM,UAAU,EAAE,KAAK,MAAM,EAAE,IACvC,EAAE,MAAAA,OAAM,MAAM,UAAU,EAAE,IAAI,EAAE;AAAA,EAC1C;AACA,EAAAC,iBAAgB,SAAS;AAC7B,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AACrC,IAAI;AAAA,CACV,SAAUC,iBAAgB;AAWvB,WAAS,OAAOF,OAAM,QAAQ,MAAM,OAAO,gBAAgB,UAAU;AACjE,QAAI,SAAS;AAAA,MACT,MAAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AACA,QAAI,aAAa,QAAW;AACxB,aAAO,WAAW;AAAA,IACtB;AACA,WAAO;AAAA,EACX;AACA,EAAAE,gBAAe,SAAS;AAIxB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,aACH,GAAG,OAAO,UAAU,IAAI,KAAK,GAAG,OAAO,UAAU,IAAI,KACrD,MAAM,GAAG,UAAU,KAAK,KAAK,MAAM,GAAG,UAAU,cAAc,MAC7D,UAAU,WAAW,UAAa,GAAG,OAAO,UAAU,MAAM,OAC5D,UAAU,eAAe,UAAa,GAAG,QAAQ,UAAU,UAAU,OACrE,UAAU,aAAa,UAAa,MAAM,QAAQ,UAAU,QAAQ,OACpE,UAAU,SAAS,UAAa,MAAM,QAAQ,UAAU,IAAI;AAAA,EACrE;AACA,EAAAA,gBAAe,KAAK;AACxB,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAInC,IAAI;AAAA,CACV,SAAUC,iBAAgB;AAIvB,EAAAA,gBAAe,QAAQ;AAIvB,EAAAA,gBAAe,WAAW;AAI1B,EAAAA,gBAAe,WAAW;AAY1B,EAAAA,gBAAe,kBAAkB;AAWjC,EAAAA,gBAAe,iBAAiB;AAahC,EAAAA,gBAAe,kBAAkB;AAMjC,EAAAA,gBAAe,SAAS;AAIxB,EAAAA,gBAAe,wBAAwB;AASvC,EAAAA,gBAAe,eAAe;AAClC,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAMnC,IAAI;AAAA,CACV,SAAUC,wBAAuB;AAI9B,EAAAA,uBAAsB,UAAU;AAOhC,EAAAA,uBAAsB,YAAY;AACtC,GAAG,0BAA0B,wBAAwB,CAAC,EAAE;AAKjD,IAAI;AAAA,CACV,SAAUC,oBAAmB;AAI1B,WAAS,OAAO,aAAa,MAAM,aAAa;AAC5C,QAAI,SAAS,EAAE,YAAY;AAC3B,QAAI,SAAS,UAAa,SAAS,MAAM;AACrC,aAAO,OAAO;AAAA,IAClB;AACA,QAAI,gBAAgB,UAAa,gBAAgB,MAAM;AACnD,aAAO,cAAc;AAAA,IACzB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,mBAAkB,SAAS;AAI3B,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,WAAW,UAAU,aAAa,WAAW,EAAE,MAC1E,UAAU,SAAS,UAAa,GAAG,WAAW,UAAU,MAAM,GAAG,MAAM,OACvE,UAAU,gBAAgB,UAAa,UAAU,gBAAgB,sBAAsB,WAAW,UAAU,gBAAgB,sBAAsB;AAAA,EAC9J;AACA,EAAAA,mBAAkB,KAAK;AAC3B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AACzC,IAAI;AAAA,CACV,SAAUC,aAAY;AACnB,WAAS,OAAO,OAAO,qBAAqB,MAAM;AAC9C,QAAI,SAAS,EAAE,MAAM;AACrB,QAAI,YAAY;AAChB,QAAI,OAAO,wBAAwB,UAAU;AACzC,kBAAY;AACZ,aAAO,OAAO;AAAA,IAClB,WACS,QAAQ,GAAG,mBAAmB,GAAG;AACtC,aAAO,UAAU;AAAA,IACrB,OACK;AACD,aAAO,OAAO;AAAA,IAClB;AACA,QAAI,aAAa,SAAS,QAAW;AACjC,aAAO,OAAO;AAAA,IAClB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,YAAW,SAAS;AACpB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,aAAa,GAAG,OAAO,UAAU,KAAK,MACxC,UAAU,gBAAgB,UAAa,GAAG,WAAW,UAAU,aAAa,WAAW,EAAE,OACzF,UAAU,SAAS,UAAa,GAAG,OAAO,UAAU,IAAI,OACxD,UAAU,SAAS,UAAa,UAAU,YAAY,YACtD,UAAU,YAAY,UAAa,QAAQ,GAAG,UAAU,OAAO,OAC/D,UAAU,gBAAgB,UAAa,GAAG,QAAQ,UAAU,WAAW,OACvE,UAAU,SAAS,UAAa,cAAc,GAAG,UAAU,IAAI;AAAA,EACxE;AACA,EAAAA,YAAW,KAAK;AACpB,GAAG,eAAe,aAAa,CAAC,EAAE;AAK3B,IAAI;AAAA,CACV,SAAUC,WAAU;AAIjB,WAAS,OAAO,OAAO,MAAM;AACzB,QAAI,SAAS,EAAE,MAAM;AACrB,QAAI,GAAG,QAAQ,IAAI,GAAG;AAClB,aAAO,OAAO;AAAA,IAClB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,UAAS,SAAS;AAIlB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,MAAM,GAAG,UAAU,KAAK,MAAM,GAAG,UAAU,UAAU,OAAO,KAAK,QAAQ,GAAG,UAAU,OAAO;AAAA,EACjI;AACA,EAAAA,UAAS,KAAK;AAClB,GAAG,aAAa,WAAW,CAAC,EAAE;AAKvB,IAAI;AAAA,CACV,SAAUC,oBAAmB;AAI1B,WAAS,OAAO,SAAS,cAAc;AACnC,WAAO,EAAE,SAAS,aAAa;AAAA,EACnC;AACA,EAAAA,mBAAkB,SAAS;AAI3B,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,SAAS,UAAU,OAAO,KAAK,GAAG,QAAQ,UAAU,YAAY;AAAA,EACvG;AACA,EAAAA,mBAAkB,KAAK;AAC3B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAKzC,IAAI;AAAA,CACV,SAAUC,eAAc;AAIrB,WAAS,OAAO,OAAO,QAAQ,MAAM;AACjC,WAAO,EAAE,OAAO,QAAQ,KAAK;AAAA,EACjC;AACA,EAAAA,cAAa,SAAS;AAItB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,MAAM,GAAG,UAAU,KAAK,MAAM,GAAG,UAAU,UAAU,MAAM,KAAK,GAAG,OAAO,UAAU,MAAM;AAAA,EAC9H;AACA,EAAAA,cAAa,KAAK;AACtB,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAK/B,IAAI;AAAA,CACV,SAAUC,iBAAgB;AAMvB,WAAS,OAAO,OAAO,QAAQ;AAC3B,WAAO,EAAE,OAAO,OAAO;AAAA,EAC3B;AACA,EAAAA,gBAAe,SAAS;AACxB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,cAAc,SAAS,KAAK,MAAM,GAAG,UAAU,KAAK,MAAM,UAAU,WAAW,UAAaA,gBAAe,GAAG,UAAU,MAAM;AAAA,EAC5I;AACA,EAAAA,gBAAe,KAAK;AACxB,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAQnC,IAAI;AAAA,CACV,SAAUC,qBAAoB;AAC3B,EAAAA,oBAAmB,WAAW,IAAI;AAKlC,EAAAA,oBAAmB,MAAM,IAAI;AAC7B,EAAAA,oBAAmB,OAAO,IAAI;AAC9B,EAAAA,oBAAmB,MAAM,IAAI;AAC7B,EAAAA,oBAAmB,WAAW,IAAI;AAClC,EAAAA,oBAAmB,QAAQ,IAAI;AAC/B,EAAAA,oBAAmB,eAAe,IAAI;AACtC,EAAAA,oBAAmB,WAAW,IAAI;AAClC,EAAAA,oBAAmB,UAAU,IAAI;AACjC,EAAAA,oBAAmB,UAAU,IAAI;AACjC,EAAAA,oBAAmB,YAAY,IAAI;AACnC,EAAAA,oBAAmB,OAAO,IAAI;AAC9B,EAAAA,oBAAmB,UAAU,IAAI;AACjC,EAAAA,oBAAmB,QAAQ,IAAI;AAC/B,EAAAA,oBAAmB,OAAO,IAAI;AAC9B,EAAAA,oBAAmB,SAAS,IAAI;AAChC,EAAAA,oBAAmB,UAAU,IAAI;AACjC,EAAAA,oBAAmB,SAAS,IAAI;AAChC,EAAAA,oBAAmB,QAAQ,IAAI;AAC/B,EAAAA,oBAAmB,QAAQ,IAAI;AAC/B,EAAAA,oBAAmB,QAAQ,IAAI;AAC/B,EAAAA,oBAAmB,UAAU,IAAI;AAIjC,EAAAA,oBAAmB,WAAW,IAAI;AACtC,GAAG,uBAAuB,qBAAqB,CAAC,EAAE;AAQ3C,IAAI;AAAA,CACV,SAAUC,yBAAwB;AAC/B,EAAAA,wBAAuB,aAAa,IAAI;AACxC,EAAAA,wBAAuB,YAAY,IAAI;AACvC,EAAAA,wBAAuB,UAAU,IAAI;AACrC,EAAAA,wBAAuB,QAAQ,IAAI;AACnC,EAAAA,wBAAuB,YAAY,IAAI;AACvC,EAAAA,wBAAuB,UAAU,IAAI;AACrC,EAAAA,wBAAuB,OAAO,IAAI;AAClC,EAAAA,wBAAuB,cAAc,IAAI;AACzC,EAAAA,wBAAuB,eAAe,IAAI;AAC1C,EAAAA,wBAAuB,gBAAgB,IAAI;AAC/C,GAAG,2BAA2B,yBAAyB,CAAC,EAAE;AAInD,IAAI;AAAA,CACV,SAAUC,iBAAgB;AACvB,WAAS,GAAG,OAAO;AACf,UAAM,YAAY;AAClB,WAAO,GAAG,cAAc,SAAS,MAAM,UAAU,aAAa,UAAa,OAAO,UAAU,aAAa,aACrG,MAAM,QAAQ,UAAU,IAAI,MAAM,UAAU,KAAK,WAAW,KAAK,OAAO,UAAU,KAAK,CAAC,MAAM;AAAA,EACtG;AACA,EAAAA,gBAAe,KAAK;AACxB,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAMnC,IAAI;AAAA,CACV,SAAUC,kBAAiB;AAIxB,WAAS,OAAO,OAAO,MAAM;AACzB,WAAO,EAAE,OAAO,KAAK;AAAA,EACzB;AACA,EAAAA,iBAAgB,SAAS;AACzB,WAAS,GAAG,OAAO;AACf,UAAM,YAAY;AAClB,WAAO,cAAc,UAAa,cAAc,QAAQ,MAAM,GAAG,UAAU,KAAK,KAAK,GAAG,OAAO,UAAU,IAAI;AAAA,EACjH;AACA,EAAAA,iBAAgB,KAAK;AACzB,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAMrC,IAAI;AAAA,CACV,SAAUC,4BAA2B;AAIlC,WAAS,OAAO,OAAO,cAAc,qBAAqB;AACtD,WAAO,EAAE,OAAO,cAAc,oBAAoB;AAAA,EACtD;AACA,EAAAA,2BAA0B,SAAS;AACnC,WAAS,GAAG,OAAO;AACf,UAAM,YAAY;AAClB,WAAO,cAAc,UAAa,cAAc,QAAQ,MAAM,GAAG,UAAU,KAAK,KAAK,GAAG,QAAQ,UAAU,mBAAmB,MACrH,GAAG,OAAO,UAAU,YAAY,KAAK,UAAU,iBAAiB;AAAA,EAC5E;AACA,EAAAA,2BAA0B,KAAK;AACnC,GAAG,8BAA8B,4BAA4B,CAAC,EAAE;AAMzD,IAAI;AAAA,CACV,SAAUC,mCAAkC;AAIzC,WAAS,OAAO,OAAO,YAAY;AAC/B,WAAO,EAAE,OAAO,WAAW;AAAA,EAC/B;AACA,EAAAA,kCAAiC,SAAS;AAC1C,WAAS,GAAG,OAAO;AACf,UAAM,YAAY;AAClB,WAAO,cAAc,UAAa,cAAc,QAAQ,MAAM,GAAG,UAAU,KAAK,MACxE,GAAG,OAAO,UAAU,UAAU,KAAK,UAAU,eAAe;AAAA,EACxE;AACA,EAAAA,kCAAiC,KAAK;AAC1C,GAAG,qCAAqC,mCAAmC,CAAC,EAAE;AAOvE,IAAI;AAAA,CACV,SAAUC,qBAAoB;AAI3B,WAAS,OAAO,SAAS,iBAAiB;AACtC,WAAO,EAAE,SAAS,gBAAgB;AAAA,EACtC;AACA,EAAAA,oBAAmB,SAAS;AAI5B,WAAS,GAAG,OAAO;AACf,UAAM,YAAY;AAClB,WAAO,GAAG,QAAQ,SAAS,KAAK,MAAM,GAAG,MAAM,eAAe;AAAA,EAClE;AACA,EAAAA,oBAAmB,KAAK;AAC5B,GAAG,uBAAuB,qBAAqB,CAAC,EAAE;AAM3C,IAAI;AAAA,CACV,SAAUC,gBAAe;AAItB,EAAAA,eAAc,OAAO;AAIrB,EAAAA,eAAc,YAAY;AAC1B,WAAS,GAAG,OAAO;AACf,WAAO,UAAU,KAAK,UAAU;AAAA,EACpC;AACA,EAAAA,eAAc,KAAK;AACvB,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AACjC,IAAI;AAAA,CACV,SAAUC,qBAAoB;AAC3B,WAAS,OAAO,OAAO;AACnB,WAAO,EAAE,MAAM;AAAA,EACnB;AACA,EAAAA,oBAAmB,SAAS;AAC5B,WAAS,GAAG,OAAO;AACf,UAAM,YAAY;AAClB,WAAO,GAAG,cAAc,SAAS,MACzB,UAAU,YAAY,UAAa,GAAG,OAAO,UAAU,OAAO,KAAK,cAAc,GAAG,UAAU,OAAO,OACrG,UAAU,aAAa,UAAa,SAAS,GAAG,UAAU,QAAQ,OAClE,UAAU,YAAY,UAAa,QAAQ,GAAG,UAAU,OAAO;AAAA,EAC3E;AACA,EAAAA,oBAAmB,KAAK;AAC5B,GAAG,uBAAuB,qBAAqB,CAAC,EAAE;AAC3C,IAAI;AAAA,CACV,SAAUC,YAAW;AAClB,WAAS,OAAO,UAAU,OAAO,MAAM;AACnC,UAAM,SAAS,EAAE,UAAU,MAAM;AACjC,QAAI,SAAS,QAAW;AACpB,aAAO,OAAO;AAAA,IAClB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,WAAU,SAAS;AACnB,WAAS,GAAG,OAAO;AACf,UAAM,YAAY;AAClB,WAAO,GAAG,cAAc,SAAS,KAAK,SAAS,GAAG,UAAU,QAAQ,MAC5D,GAAG,OAAO,UAAU,KAAK,KAAK,GAAG,WAAW,UAAU,OAAO,mBAAmB,EAAE,OAClF,UAAU,SAAS,UAAa,cAAc,GAAG,UAAU,IAAI,MAC/D,UAAU,cAAc,UAAc,GAAG,WAAW,UAAU,WAAW,SAAS,EAAE,MACpF,UAAU,YAAY,UAAa,GAAG,OAAO,UAAU,OAAO,KAAK,cAAc,GAAG,UAAU,OAAO,OACrG,UAAU,gBAAgB,UAAa,GAAG,QAAQ,UAAU,WAAW,OACvE,UAAU,iBAAiB,UAAa,GAAG,QAAQ,UAAU,YAAY;AAAA,EACrF;AACA,EAAAA,WAAU,KAAK;AACnB,GAAG,cAAc,YAAY,CAAC,EAAE;AACzB,IAAI;AAAA,CACV,SAAUC,cAAa;AACpB,WAAS,cAAc,OAAO;AAC1B,WAAO,EAAE,MAAM,WAAW,MAAM;AAAA,EACpC;AACA,EAAAA,aAAY,gBAAgB;AAChC,GAAG,gBAAgB,cAAc,CAAC,EAAE;AAC7B,IAAI;AAAA,CACV,SAAUC,uBAAsB;AAC7B,WAAS,OAAO,YAAY,YAAY,OAAO,SAAS;AACpD,WAAO,EAAE,YAAY,YAAY,OAAO,QAAQ;AAAA,EACpD;AACA,EAAAA,sBAAqB,SAAS;AAClC,GAAG,yBAAyB,uBAAuB,CAAC,EAAE;AAC/C,IAAI;AAAA,CACV,SAAUC,uBAAsB;AAC7B,WAAS,OAAO,OAAO;AACnB,WAAO,EAAE,MAAM;AAAA,EACnB;AACA,EAAAA,sBAAqB,SAAS;AAClC,GAAG,yBAAyB,uBAAuB,CAAC,EAAE;AAO/C,IAAI;AAAA,CACV,SAAUC,8BAA6B;AAIpC,EAAAA,6BAA4B,UAAU;AAItC,EAAAA,6BAA4B,YAAY;AAC5C,GAAG,gCAAgC,8BAA8B,CAAC,EAAE;AAC7D,IAAI;AAAA,CACV,SAAUC,yBAAwB;AAC/B,WAAS,OAAO,OAAO,MAAM;AACzB,WAAO,EAAE,OAAO,KAAK;AAAA,EACzB;AACA,EAAAA,wBAAuB,SAAS;AACpC,GAAG,2BAA2B,yBAAyB,CAAC,EAAE;AACnD,IAAI;AAAA,CACV,SAAUC,0BAAyB;AAChC,WAAS,OAAO,aAAa,wBAAwB;AACjD,WAAO,EAAE,aAAa,uBAAuB;AAAA,EACjD;AACA,EAAAA,yBAAwB,SAAS;AACrC,GAAG,4BAA4B,0BAA0B,CAAC,EAAE;AACrD,IAAI;AAAA,CACV,SAAUC,kBAAiB;AACxB,WAAS,GAAG,OAAO;AACf,UAAM,YAAY;AAClB,WAAO,GAAG,cAAc,SAAS,KAAK,IAAI,GAAG,UAAU,GAAG,KAAK,GAAG,OAAO,UAAU,IAAI;AAAA,EAC3F;AACA,EAAAA,iBAAgB,KAAK;AACzB,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAKrC,IAAI;AAAA,CACV,SAAUC,eAAc;AAQrB,WAAS,OAAO,KAAK,YAAY,SAAS,SAAS;AAC/C,WAAO,IAAI,iBAAiB,KAAK,YAAY,SAAS,OAAO;AAAA,EACjE;AACA,EAAAA,cAAa,SAAS;AAItB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,GAAG,MAAM,GAAG,UAAU,UAAU,UAAU,KAAK,GAAG,OAAO,UAAU,UAAU,MAAM,GAAG,SAAS,UAAU,SAAS,KAC/J,GAAG,KAAK,UAAU,OAAO,KAAK,GAAG,KAAK,UAAU,UAAU,KAAK,GAAG,KAAK,UAAU,QAAQ,IAAI,OAAO;AAAA,EAC/G;AACA,EAAAA,cAAa,KAAK;AAClB,WAAS,WAAW,UAAU,OAAO;AACjC,QAAI,OAAO,SAAS,QAAQ;AAC5B,QAAI,cAAc,UAAU,OAAO,CAAC,GAAG,MAAM;AACzC,UAAI,OAAO,EAAE,MAAM,MAAM,OAAO,EAAE,MAAM,MAAM;AAC9C,UAAI,SAAS,GAAG;AACZ,eAAO,EAAE,MAAM,MAAM,YAAY,EAAE,MAAM,MAAM;AAAA,MACnD;AACA,aAAO;AAAA,IACX,CAAC;AACD,QAAI,qBAAqB,KAAK;AAC9B,aAAS,IAAI,YAAY,SAAS,GAAG,KAAK,GAAG,KAAK;AAC9C,UAAI,IAAI,YAAY,CAAC;AACrB,UAAI,cAAc,SAAS,SAAS,EAAE,MAAM,KAAK;AACjD,UAAI,YAAY,SAAS,SAAS,EAAE,MAAM,GAAG;AAC7C,UAAI,aAAa,oBAAoB;AACjC,eAAO,KAAK,UAAU,GAAG,WAAW,IAAI,EAAE,UAAU,KAAK,UAAU,WAAW,KAAK,MAAM;AAAA,MAC7F,OACK;AACD,cAAM,IAAI,MAAM,kBAAkB;AAAA,MACtC;AACA,2BAAqB;AAAA,IACzB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,cAAa,aAAa;AAC1B,WAAS,UAAU,MAAM,SAAS;AAC9B,QAAI,KAAK,UAAU,GAAG;AAElB,aAAO;AAAA,IACX;AACA,UAAMC,KAAK,KAAK,SAAS,IAAK;AAC9B,UAAM,OAAO,KAAK,MAAM,GAAGA,EAAC;AAC5B,UAAM,QAAQ,KAAK,MAAMA,EAAC;AAC1B,cAAU,MAAM,OAAO;AACvB,cAAU,OAAO,OAAO;AACxB,QAAI,UAAU;AACd,QAAI,WAAW;AACf,QAAI,IAAI;AACR,WAAO,UAAU,KAAK,UAAU,WAAW,MAAM,QAAQ;AACrD,UAAI,MAAM,QAAQ,KAAK,OAAO,GAAG,MAAM,QAAQ,CAAC;AAChD,UAAI,OAAO,GAAG;AAEV,aAAK,GAAG,IAAI,KAAK,SAAS;AAAA,MAC9B,OACK;AAED,aAAK,GAAG,IAAI,MAAM,UAAU;AAAA,MAChC;AAAA,IACJ;AACA,WAAO,UAAU,KAAK,QAAQ;AAC1B,WAAK,GAAG,IAAI,KAAK,SAAS;AAAA,IAC9B;AACA,WAAO,WAAW,MAAM,QAAQ;AAC5B,WAAK,GAAG,IAAI,MAAM,UAAU;AAAA,IAChC;AACA,WAAO;AAAA,EACX;AACJ,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAItC,IAAM,mBAAN,MAAuB;AAAA,EACnB,YAAY,KAAK,YAAY,SAAS,SAAS;AAC3C,SAAK,OAAO;AACZ,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,eAAe;AAAA,EACxB;AAAA,EACA,IAAI,MAAM;AACN,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,aAAa;AACb,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,UAAU;AACV,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,QAAQ,OAAO;AACX,QAAI,OAAO;AACP,UAAI,QAAQ,KAAK,SAAS,MAAM,KAAK;AACrC,UAAI,MAAM,KAAK,SAAS,MAAM,GAAG;AACjC,aAAO,KAAK,SAAS,UAAU,OAAO,GAAG;AAAA,IAC7C;AACA,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,OAAO,OAAO,SAAS;AACnB,SAAK,WAAW,MAAM;AACtB,SAAK,WAAW;AAChB,SAAK,eAAe;AAAA,EACxB;AAAA,EACA,iBAAiB;AACb,QAAI,KAAK,iBAAiB,QAAW;AACjC,UAAI,cAAc,CAAC;AACnB,UAAI,OAAO,KAAK;AAChB,UAAI,cAAc;AAClB,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,YAAI,aAAa;AACb,sBAAY,KAAK,CAAC;AAClB,wBAAc;AAAA,QAClB;AACA,YAAI,KAAK,KAAK,OAAO,CAAC;AACtB,sBAAe,OAAO,QAAQ,OAAO;AACrC,YAAI,OAAO,QAAQ,IAAI,IAAI,KAAK,UAAU,KAAK,OAAO,IAAI,CAAC,MAAM,MAAM;AACnE;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,eAAe,KAAK,SAAS,GAAG;AAChC,oBAAY,KAAK,KAAK,MAAM;AAAA,MAChC;AACA,WAAK,eAAe;AAAA,IACxB;AACA,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,WAAW,QAAQ;AACf,aAAS,KAAK,IAAI,KAAK,IAAI,QAAQ,KAAK,SAAS,MAAM,GAAG,CAAC;AAC3D,QAAI,cAAc,KAAK,eAAe;AACtC,QAAI,MAAM,GAAG,OAAO,YAAY;AAChC,QAAI,SAAS,GAAG;AACZ,aAAO,SAAS,OAAO,GAAG,MAAM;AAAA,IACpC;AACA,WAAO,MAAM,MAAM;AACf,UAAI,MAAM,KAAK,OAAO,MAAM,QAAQ,CAAC;AACrC,UAAI,YAAY,GAAG,IAAI,QAAQ;AAC3B,eAAO;AAAA,MACX,OACK;AACD,cAAM,MAAM;AAAA,MAChB;AAAA,IACJ;AAGA,QAAI,OAAO,MAAM;AACjB,WAAO,SAAS,OAAO,MAAM,SAAS,YAAY,IAAI,CAAC;AAAA,EAC3D;AAAA,EACA,SAAS,UAAU;AACf,QAAI,cAAc,KAAK,eAAe;AACtC,QAAI,SAAS,QAAQ,YAAY,QAAQ;AACrC,aAAO,KAAK,SAAS;AAAA,IACzB,WACS,SAAS,OAAO,GAAG;AACxB,aAAO;AAAA,IACX;AACA,QAAI,aAAa,YAAY,SAAS,IAAI;AAC1C,QAAI,iBAAkB,SAAS,OAAO,IAAI,YAAY,SAAU,YAAY,SAAS,OAAO,CAAC,IAAI,KAAK,SAAS;AAC/G,WAAO,KAAK,IAAI,KAAK,IAAI,aAAa,SAAS,WAAW,cAAc,GAAG,UAAU;AAAA,EACzF;AAAA,EACA,IAAI,YAAY;AACZ,WAAO,KAAK,eAAe,EAAE;AAAA,EACjC;AACJ;AACA,IAAI;AAAA,CACH,SAAUC,KAAI;AACX,QAAM,WAAW,OAAO,UAAU;AAClC,WAAS,QAAQ,OAAO;AACpB,WAAO,OAAO,UAAU;AAAA,EAC5B;AACA,EAAAA,IAAG,UAAU;AACb,WAASC,WAAU,OAAO;AACtB,WAAO,OAAO,UAAU;AAAA,EAC5B;AACA,EAAAD,IAAG,YAAYC;AACf,WAAS,QAAQ,OAAO;AACpB,WAAO,UAAU,QAAQ,UAAU;AAAA,EACvC;AACA,EAAAD,IAAG,UAAU;AACb,WAAS,OAAO,OAAO;AACnB,WAAO,SAAS,KAAK,KAAK,MAAM;AAAA,EACpC;AACA,EAAAA,IAAG,SAAS;AACZ,WAAS,OAAO,OAAO;AACnB,WAAO,SAAS,KAAK,KAAK,MAAM;AAAA,EACpC;AACA,EAAAA,IAAG,SAAS;AACZ,WAAS,YAAY,OAAO,KAAK,KAAK;AAClC,WAAO,SAAS,KAAK,KAAK,MAAM,qBAAqB,OAAO,SAAS,SAAS;AAAA,EAClF;AACA,EAAAA,IAAG,cAAc;AACjB,WAASE,SAAQ,OAAO;AACpB,WAAO,SAAS,KAAK,KAAK,MAAM,qBAAqB,eAAe,SAAS,SAAS;AAAA,EAC1F;AACA,EAAAF,IAAG,UAAUE;AACb,WAASC,UAAS,OAAO;AACrB,WAAO,SAAS,KAAK,KAAK,MAAM,qBAAqB,KAAK,SAAS,SAAS;AAAA,EAChF;AACA,EAAAH,IAAG,WAAWG;AACd,WAAS,KAAK,OAAO;AACjB,WAAO,SAAS,KAAK,KAAK,MAAM;AAAA,EACpC;AACA,EAAAH,IAAG,OAAO;AACV,WAAS,cAAc,OAAO;AAI1B,WAAO,UAAU,QAAQ,OAAO,UAAU;AAAA,EAC9C;AACA,EAAAA,IAAG,gBAAgB;AACnB,WAAS,WAAW,OAAO,OAAO;AAC9B,WAAO,MAAM,QAAQ,KAAK,KAAK,MAAM,MAAM,KAAK;AAAA,EACpD;AACA,EAAAA,IAAG,aAAa;AACpB,GAAG,OAAO,KAAK,CAAC,EAAE;;;ACh7DZ,IAAWI;CAAjB,SAAiBA,qBAAkB;AACpB,EAAAA,oBAAA,OAAO;AACP,EAAAA,oBAAA,SAAS;AACT,EAAAA,oBAAA,WAAW;AACX,EAAAA,oBAAA,cAAc;AACd,EAAAA,oBAAA,QAAQ;AACR,EAAAA,oBAAA,WAAW;AACX,EAAAA,oBAAA,QAAQ;AACR,EAAAA,oBAAA,YAAY;AACZ,EAAAA,oBAAA,SAAS;AACT,EAAAA,oBAAA,WAAW;AACX,EAAAA,oBAAA,OAAO;AACP,EAAAA,oBAAA,QAAQ;AACR,EAAAA,oBAAA,OAAO;AACP,EAAAA,oBAAA,UAAU;AACV,EAAAA,oBAAA,UAAU;AACV,EAAAA,oBAAA,QAAQ;AACR,EAAAA,oBAAA,OAAO;AACP,EAAAA,oBAAA,YAAY;AACZ,EAAAA,oBAAA,SAAS;AACT,EAAAA,oBAAA,aAAa;AACb,EAAAA,oBAAA,WAAW;AACX,EAAAA,oBAAA,SAAS;AACT,EAAAA,oBAAA,QAAQ;AACR,EAAAA,oBAAA,WAAW;AACX,EAAAA,oBAAA,gBAAgB;AAC/B,GA1BiBA,wBAAAA,sBAAkB,CAAA,EAAA;;;ACxO7B,SAAU,aAAgB,QAAyB;AACvD,QAAM,OAAO,OAAO,KAAK,MAAM;AAC/B,QAAM,MAAM,KAAK;AACjB,QAAM,SAAS,IAAI,MAAM,GAAG;AAC5B,WAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC5B,WAAO,CAAC,IAAI,OAAO,KAAK,CAAC,CAAC;;AAE5B,SAAO;AACT;AAGM,SAAU,SACd,OACAC,OAAc;AAEd,SAAO,kBAAkBA,OAAM,cAAc,MAAM,MAAM,CAAC;AAC5D;AAIA,SAAS,kBACPA,OACA,MAAY;AAEZ,MACE,CAAC,QACD,KAAK,KAAI,MAAO,MAChB,KAAK,KAAI,MAAO,OAChB,KAAK,KAAI,MAAO,KAChB;AACA,WAAO,eAAkBA,OAAM,WAAS,CAAC,MAAM,YAAY;;AAG7D,QAAM,cAAcA,MAAK,IAAI,YAAU;IACrC,WAAW,aAAa,cAAc,MAAM,KAAK,GAAG,IAAI;IACxD;IACA;AAEF,SAAO,eACL,eAAe,aAAa,UAAQ,KAAK,aAAa,CAAC,GACvD,UAAQ,CAAC,KAAK,MAAM,YAAY,EAE/B,KACC,CAAC,GAAG,OACD,EAAE,MAAM,eAAe,IAAI,MAAM,EAAE,MAAM,eAAe,IAAI,MAC7D,EAAE,YAAY,EAAE,aAChB,EAAE,MAAM,MAAM,SAAS,EAAE,MAAM,MAAM,MAAM,EAE9C,IAAI,UAAQ,KAAK,KAAK;AAC3B;AAIA,SAAS,eACP,OACA,WAAgC;AAEhC,QAAM,WAAW,MAAM,OAAO,SAAS;AACvC,SAAO,SAAS,WAAW,IAAI,QAAQ;AACzC;AAEA,SAAS,cAAc,MAAY;AACjC,SAAO,KAAK,YAAW,EAAG,WAAW,OAAO,EAAE;AAChD;AAGA,SAAS,aAAa,YAAoB,MAAY;AAEpD,MAAI,YAAY,gBAAgB,MAAM,UAAU;AAChD,MAAI,WAAW,SAAS,KAAK,QAAQ;AAEnC,iBAAa,WAAW,SAAS,KAAK,SAAS;AAE/C,iBAAa,WAAW,QAAQ,IAAI,MAAM,IAAI,IAAI;;AAEpD,SAAO;AACT;AAgBA,SAAS,gBAAgB,GAAW,GAAS;AAC3C,MAAI;AACJ,MAAI;AACJ,QAAM,IAAI,CAAA;AACV,QAAM,UAAU,EAAE;AAClB,QAAM,UAAU,EAAE;AAElB,OAAK,IAAI,GAAG,KAAK,SAAS,KAAK;AAC7B,MAAE,CAAC,IAAI,CAAC,CAAC;;AAGX,OAAK,IAAI,GAAG,KAAK,SAAS,KAAK;AAC7B,MAAE,CAAC,EAAE,CAAC,IAAI;;AAGZ,OAAK,IAAI,GAAG,KAAK,SAAS,KAAK;AAC7B,SAAK,IAAI,GAAG,KAAK,SAAS,KAAK;AAC7B,YAAM,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,IAAI;AAEzC,QAAE,CAAC,EAAE,CAAC,IAAI,KAAK,IACb,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,GACd,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,GACd,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,IAAI;AAGxB,UAAI,IAAI,KAAK,IAAI,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG;AACpE,UAAE,CAAC,EAAE,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,IAAI;;;;AAKxD,SAAO,EAAE,OAAO,EAAE,OAAO;AAC3B;AAEA,IAAM,eAAe,CAAC,MAAe;MAAW,MAAC,QAAD,MAAC,SAAD,IAAK,CAAC;;AAE/C,IAAM,gBAAgB,CAC3B,QACAC,OACA,aACU;AACV,MAAI,CAACA,OAAM;AACT,WAAO,aAAQ,QAAR,aAAQ,SAAR,WAAY;;AAGrB,QAAM,YAAY,aAAaA,KAAI;AACnC,MACE,aAAa,SAAS,KACtB,kBAAkB,SAAS,KAC3B,WAAW,SAAS,KACpB,eAAe,SAAS,GACxB;AACA,WAAO,SAAS,aAAY;;AAG9B,SAAO,aAAQ,QAAR,aAAQ,SAAR,WAAY;AACrB;AAEO,IAAM,qBAAqB,CAChC,QACAA,OACA,aACU;AAIV,MAAI,WAAWA,KAAI,GAAG;AACpB,UAAM,WAAW,aAAaA,MAAK,MAAM;AACzC,WAAO,SAAS,IAAI,cAAc,IAAI,UAAU,IAAI,CAAC;;AAEvD,SAAO,cAAc,QAAQA,OAAM,QAAQ;AAC7C;AAQO,IAAM,qBAAqB,CAAC,UAAmC;AACpE,QAAM,eAAe,MAAM,KAAK,OAAO,SACrC,IAAI,KAAK,SAAQ,EAAG,SAAS,GAAG,CAAC;AAEnC,MAAI,CAAC,aAAa,QAAQ;AACxB;;AAEF,SACE,MAAM,OACN,IAAI,aAAa,IACf,CAAC,KAAK,MAAM,GAAG,IAAI,IAAI,MAAM,IAAI,CAAC,EAAE,CACrC,KAAK,cAAc,IAAI,MAAM,MAAM,IAAI,CAAC;AAE7C;;;AC3HO,IAAM,oBAAoB;EAC/B,SAAS;EACT,OAAO;;AAGT,IAAM,sBAAsB,CAAC,OAA0B;AACrD,QAAM,oBAA8C,CAAA;AACpD,MAAI,IAAI;AACN,QAAI;AACF,YAAM,MAAM,EAAE,GAAG;QACf,mBAAmB,KAAG;AACpB,4BAAkB,KAAK,GAAG;QAC5B;OACD;aACD,IAAM;AACN,aAAO,CAAA;;;AAGX,SAAO;AACT;AA8BM,SAAU,2BACd,QACA,WACA,QACA,cACA,cACA,SAAuC;;AAEvC,QAAM,OAAO,OAAA,OAAA,OAAA,OAAA,CAAA,GACR,OAAO,GAAA,EACV,OAAM,CAAA;AAGR,QAAM,UAAU,qBACd,WACA,QACA,QACA,cACA,OAAO;AAET,MAAI,CAAC,SAAS;AACZ,WAAO,CAAA;;AAET,QAAM,EAAE,OAAO,UAAU,MAAM,MAAK,IAAK;AAEzC,QAAM,EAAE,MAAM,MAAM,UAAS,IAAK;AAGlC,MAAI,SAAS,UAAU,UAAU;AAC/B,QAAI,SAAS,oBAAoB,aAAa;AAC5C,aAAO,uCAAuC,KAAK;;AAErD,QAAI,SAAS,oBAAoB,YAAY;AAC3C,aAAO,uCAAuC,KAAK;;AAErD,WAAO,qCAAqC,KAAK;;AAGnD,MAAI,SAAS,UAAU,YAAY;AACjC,WAAO,sCAAsC,KAAK;;AAGpD,QACE,KAAA,cAAS,QAAT,cAAS,SAAA,SAAT,UAAW,eAAS,QAAA,OAAA,SAAA,SAAA,GAAE,UAAS,UAAU,wBACzC,MAAM,MACN;AACA,WAAO,SAAS,OAAO,CAAA,CAAE;;AAI3B,OAAI,cAAS,QAAT,cAAS,SAAA,SAAT,UAAW,UAAS,KAAK,uBAAuB;AAClD,WAAO,SACL,OACA,OAAO,OAAO,OAAO,WAAU,CAAE,EAC9B,OAAO,YAAY,EACnB,IAAI,CAAAC,WAAS;MACZ,OAAOA,MAAK;MACZ,MAAMC,oBAAmB;MACzB,CAAC;;AAKT,OAAI,cAAS,QAAT,cAAS,SAAA,SAAT,UAAW,UAAS,KAAK,uBAAuB;AAClD,WAAO,SACL,OACA,OAAO,OAAO,OAAO,WAAU,CAAE,EAC9B,OAAO,CAAAD,UAAQ,aAAaA,KAAI,KAAK,CAACA,MAAK,KAAK,WAAW,IAAI,CAAC,EAChE,IAAI,CAAAA,WAAS;MACZ,OAAOA,MAAK;MACZ,MAAMC,oBAAmB;MACzB,CAAC;;AAKT,OAAI,cAAS,QAAT,cAAS,SAAA,SAAT,UAAW,UAAS,KAAK,0BAA0B;AACrD,WAAO,SACL,OACA,OAAO,OAAO,OAAO,WAAU,CAAE,EAC9B,OAAO,eAAe,EACtB,IAAI,CAAAD,WAAS;MACZ,OAAOA,MAAK;MACZ,MAAMC,oBAAmB;MACzB,CAAC;;AAKT,OAAI,cAAS,QAAT,cAAS,SAAA,SAAT,UAAW,UAAS,KAAK,sBAAsB;AACjD,WAAO,SACL,OACA,OAAO,OAAO,OAAO,WAAU,CAAE,EAC9B,OAAO,WAAW,EAClB,IAAI,CAAAD,WAAS;MACZ,OAAOA,MAAK;MACZ,MAAMC,oBAAmB;MACzB,CAAC;;AAKT,OAAI,cAAS,QAAT,cAAS,SAAA,SAAT,UAAW,UAAS,KAAK,qBAAqB;AAChD,WAAO,SACL,OACA,OAAO,OAAO,OAAO,WAAU,CAAE,EAC9B,OAAO,CAAAD,UAAQ,WAAWA,KAAI,KAAK,CAACA,MAAK,KAAK,WAAW,IAAI,CAAC,EAC9D,IAAI,CAAAA,WAAS;MACZ,OAAOA,MAAK;MACZ,MAAMC,oBAAmB;MACzB,CAAC;;AAKT,OAAI,cAAS,QAAT,cAAS,SAAA,SAAT,UAAW,UAAS,KAAK,6BAA6B;AACxD,WAAO,SACL,OACA,OAAO,OAAO,OAAO,WAAU,CAAE,EAC9B,OAAO,iBAAiB,EACxB,IAAI,CAAAD,WAAS;MACZ,OAAOA,MAAK;MACZ,MAAMC,oBAAmB;MACzB,CAAC;;AAIT,MACE,SAAS,UAAU,cAClB,SAAS,UAAU,eAAc,cAAS,QAAT,cAAS,SAAA,SAAT,UAAW,UAAS,UAAU,YAChE;AACA,WAAO,4BACL,OACA,OACA,QACA,WACA,QAAQ;;AAKZ,MACE,SAAS,UAAU,iBACnB,SAAS,UAAU,SACnB,SAAS,UAAU,eACnB;AACA,WAAO,4BAA4B,OAAO,UAAU,IAAI;;AAI1D,MACE,SAAS,UAAU,aAClB,SAAS,UAAU,YAAY,SAAS,GACzC;AACA,UAAM,EAAE,QAAO,IAAK;AACpB,QAAI,SAAS;AACX,aAAO,SACL,OACA,QAAQ,IACN,CAAC,WAA2C;;AAAC,eAAC;UAC5C,OAAO,OAAO;UACd,YAAY,mBAAmB,OAAO,OAAO,MAAM,OAAO,IAAI;UAC9D,gBAAgB,eAAe;UAC/B,kBAAkB,iBAAiB;UACnC,SAAS;UACT,cAAc;YACZ,QAAQ,MAAM,OAAO,OAAO,IAAI;;UAElC,gBAAeC,MAAA,OAAO,iBAAW,QAAAA,QAAA,SAAAA,MAAI;UACrC,MAAMD,oBAAmB;UACzB,MAAM,OAAO;;OACb,CACH;;;AAMP,OACG,SAAS,UAAU,gBACjB,SAAS,UAAU,gBAAgB,SAAS,MAC/C,SAAS,iBACT;AACA,UAAM,eAAe,aAAa,SAAS,eAAe;AAC1D,UAAM,iBACJ,SAAS,UAAU,eACfA,oBAAmB,QACnBA,oBAAmB;AACzB,WAAO,SACL,OACA,aAAa,IAAI,WAAQ;;AAAC,aAAC;QACzB,OAAO,MAAM;QACb,QAAQ,OAAO,MAAM,IAAI;QACzB,gBAAeC,MAAA,UAAK,QAAL,UAAK,SAAA,SAAL,MAAO,iBAAW,QAAAA,QAAA,SAAAA,MAAI;QACrC,MAAM;QACN,MAAM,MAAM;QACZ,YAAY,mBAAmB,MAAM,OAAO,MAAM,MAAM,IAAI;QAC5D,gBAAgB,eAAe;QAC/B,kBAAkB,iBAAiB;QACnC,SAAS;;KACT,CAAC;;AAKP,MACE,SAAS,UAAU,cAClB,SAAS,UAAU,cAAc,SAAS,KAC1C,SAAS,UAAU,gBAAgB,SAAS,KAC5C,SAAS,UAAU,YAAY,SAAS,GACzC;AACA,WAAO,6BAA6B,OAAO,UAAU,WAAW,MAAM;;AAGxE,MAAI,SAAS,UAAU,YAAY,SAAS,GAAG;AAC7C,UAAM,iBAAiB,aAAa,SAAS,SAAU;AACvD,UAAM,sBAAsB,uBAC1B,WACA,QACA,KAAK;AAEP,WAAO,SACL,OACA,oBAAoB,OAAO,OAAK,EAAE,YAAW,mBAAc,QAAd,mBAAc,SAAA,SAAd,eAAgB,KAAI,CAAC;;AAKtE,MACG,SAAS,UAAU,kBAAkB,SAAS,KAC9C,SAAS,UAAU,cAClB,aAAa,QACb,UAAU,SAAS,UAAU,gBAC/B;AACA,WAAO,wCACL,OACA,UACA,QACA,IAAI;;AAKR,MAAI,SAAS,UAAU,mBAAmB,SAAS,GAAG;AACpD,WAAO,gCACL,OACA,UACA,QACA,WACA,MAAM,QAAQ,YAAY,IACtB,eACA,oBAAoB,YAAY,CAAC;;AAIzC,QAAM,iBAAiB,WAAW,KAAK;AAEvC,MAAI,eAAe,SAAS,UAAU,WAAW;AAC/C,WAAO,SACL,OACA,OAAO,OAAO,OAAO,WAAU,CAAE,EAC9B,OAAO,CAAAF,UAAQ,aAAaA,KAAI,KAAK,CAACA,MAAK,KAAK,WAAW,IAAI,CAAC,EAChE,IAAI,CAAAA,WAAS;MACZ,OAAOA,MAAK;MACZ,MAAMC,oBAAmB;MACzB,aAAY,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,uBACjBD,MAAK,OAAO,OACZA,MAAK;MACT,gBAAgB,eAAe;MAC/B,CAAC;;AAGT,MAAI,eAAe,SAAS,UAAU,mBAAmB,SAAS,GAAG;AACnE,WAAO,SACL,OACA,OAAO,OAAO,OAAO,WAAU,CAAE,EAC9B,OAAO,CAAAA,UAAQ,YAAYA,KAAI,KAAK,CAACA,MAAK,KAAK,WAAW,IAAI,CAAC,EAC/D,IAAI,CAAAA,WAAS;MACZ,OAAOA,MAAK;MACZ,MAAMC,oBAAmB;MACzB,aAAY,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,uBACjBD,MAAK,OAAO,SACZA,MAAK;MACT,gBAAgB,eAAe;MAC/B,kBAAkB,iBAAiB;MACnC,CAAC;;AAKT,MACG,SAAS,UAAU,uBAAuB,SAAS,KACnD,SAAS,UAAU,aAAa,SAAS,KACzC,SAAS,UAAU,cAClB,cACC,UAAU,SAAS,UAAU,uBAC5B,UAAU,SAAS,UAAU,aAC7B,UAAU,SAAS,UAAU,gBACjC;AACA,WAAO,oCAAoC,OAAO,QAAQ,IAAI;;AAIhE,MAAI,SAAS,UAAU,WAAW;AAChC,WAAO,2BAA2B,OAAO,OAAO,QAAQ,IAAI;;AAE9D,MAAI,SAAS,UAAU,eAAe;AACpC,WAAO,oCAAoC,OAAO,OAAO,QAAQ,IAAI;;AAGvE,SAAO,CAAA;AACT;AAEA,IAAM,4BAA8C;EAClD,EAAE,OAAO,QAAQ,MAAMC,oBAAmB,SAAQ;EAClD,EAAE,OAAO,aAAa,MAAMA,oBAAmB,SAAQ;EACvD,EAAE,OAAO,SAAS,MAAMA,oBAAmB,SAAQ;EACnD,EAAE,OAAO,SAAS,MAAMA,oBAAmB,SAAQ;EACnD,EAAE,OAAO,UAAU,MAAMA,oBAAmB,SAAQ;EACpD,EAAE,OAAO,UAAU,MAAMA,oBAAmB,SAAQ;;AAGtD,IAAM,4BAA8C;EAClD,EAAE,OAAO,SAAS,MAAMA,oBAAmB,SAAQ;EACnD,EAAE,OAAO,YAAY,MAAMA,oBAAmB,SAAQ;EACtD,EAAE,OAAO,gBAAgB,MAAMA,oBAAmB,SAAQ;EAC1D,EAAE,OAAO,YAAY,MAAMA,oBAAmB,SAAQ;EACtD,EAAE,OAAO,KAAK,MAAMA,oBAAmB,YAAW;;AAIpD,SAAS,uCACP,OAAmB;AAEnB,SAAO,SAAS,OAAO;IACrB,EAAE,OAAO,UAAU,MAAMA,oBAAmB,SAAQ;IACpD,GAAG;GACJ;AACH;AAEA,SAAS,uCACP,OAAmB;AAEnB,SAAO,SAAS,OAAO,yBAAyB;AAClD;AAEA,SAAS,qCACP,OAAmB;AAEnB,SAAO,SAAS,OAAO;IACrB,EAAE,OAAO,UAAU,MAAMA,oBAAmB,SAAQ;IACpD,GAAG;IACH,GAAG;GACJ;AACH;AAEA,SAAS,sCACP,OAAmB;AAEnB,SAAO,SAAS,OAAO,yBAAyB;AAClD;AAEA,SAAS,4BACP,OACA,UACA,SAAqC;;AAErC,MAAI,SAAS,YAAY;AACvB,UAAM,EAAE,WAAU,IAAK;AAEvB,QAAI,SAAqC,CAAA;AACzC,QAAI,eAAe,YAAY;AAC7B,eAAS,aAEP,WAAW,UAAS,CAA+B;;AAIvD,QAAI,gBAAgB,UAAU,GAAG;AAC/B,aAAO,KAAK,oBAAoB;;AAElC,QAAI,iBAAe,KAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,YAAM,QAAA,OAAA,SAAA,SAAA,GAAE,aAAY,IAAI;AAClD,aAAO,KAAK,oBAAoB,gBAAgB;;AAGlD,WAAO,SACL,OACA,OAAO,IAAoB,CAAC,OAAO,UAAS;;AAC1C,YAAM,aAA6B;QAEjC,UAAU,OAAO,KAAK,IAAI,MAAM;QAChC,OAAO,MAAM;QACb,QAAQ,OAAO,MAAM,IAAI;QAEzB,gBAAeC,MAAA,MAAM,iBAAW,QAAAA,QAAA,SAAAA,MAAI;QACpC,YAAY,QAAQ,MAAM,iBAAiB;QAC3C,cAAc,QAAQ,MAAM,iBAAiB;QAC7C,mBAAmB,MAAM;QACzB,MAAMD,oBAAmB;QACzB,cAAc;UACZ,QAAQ,MAAM,MAAM,KAAK,SAAQ;;QAGnC,MAAM,MAAM;;AAEd,UAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,qBAAqB;AAMhC,mBAAW,aAAa,mBAAmB,KAAK;AAGhD,YAAI,CAAC,WAAW,YAAY;AAC1B,qBAAW,aAAa,cACtB,MAAM,MACN,MAAM,MAEN,MAAM,QAAQ,MAAM,MAAM,eAAe,KAAK,KAAK;;AAIvD,YAAI,WAAW,YAAY;AACzB,qBAAW,mBAAmB,iBAAiB;AAC/C,qBAAW,iBAAiB,eAAe;AAC3C,qBAAW,UAAU;;;AAIzB,aAAO;IACT,CAAC,CAAC;;AAGN,SAAO,CAAA;AACT;AAEA,SAAS,6BACP,OACA,UACA,WACA,QAAqB;AAErB,QAAM,iBAAiB,aAAa,SAAS,SAAU;AAEvD,QAAM,iBAAmC,uBACvC,WACA,QACA,KAAK,EACL,OAAO,OAAK,EAAE,YAAW,mBAAc,QAAd,mBAAc,SAAA,SAAd,eAAgB,KAAI;AAE/C,MAAI,0BAA0B,iBAAiB;AAC7C,UAAM,SAAS,eAAe,UAAS;AACvC,WAAO,SACL,OACA,OACG,IAAoB,CAAC,UAA2B;;AAAC,aAAC;QACjD,OAAO,MAAM;QACb,QAAQ,OAAO,cAAc;QAC7B,gBAAe,KAAA,MAAM,iBAAW,QAAA,OAAA,SAAA,KAAI;QACpC,YAAY,QAAQ,MAAM,iBAAiB;QAC3C,cAAc,QAAQ,MAAM,iBAAiB;QAC7C,mBAAmB,MAAM;QACzB,MAAMA,oBAAmB;QACzB,MAAM;;KACN,EACD,OAAO,cAAc,CAAC;;AAG7B,MAAI,mBAAmB,gBAAgB;AACrC,WAAO,SACL,OACA,eAAe,OAAO;MACpB;QACE,OAAO;QACP,QAAQ,OAAO,cAAc;QAC7B,eAAe;QACf,MAAMA,oBAAmB;QACzB,MAAM;;MAER;QACE,OAAO;QACP,QAAQ,OAAO,cAAc;QAC7B,eAAe;QACf,MAAMA,oBAAmB;QACzB,MAAM;;KAET,CAAC;;AAIN,SAAO;AACT;AAEA,SAAS,4BACP,OACA,YACA,QACA,cACA,UAAqB;AAGrB,MAAI,WAAW,gBAAgB;AAC7B,WAAO,CAAA;;AAET,QAAM,UAAU,OAAO,WAAU;AAEjC,QAAM,mBAAmB,aAAa,OAAO,EAAE,OAAO,eAAe;AACrE,QAAM,uBAAuB,iBAAiB,IAAI,CAAC,EAAE,MAAAE,MAAI,MAAOA,KAAI;AACpE,QAAM,mBAAgC,oBAAI,IAAG;AAC7C,kBAAgB,cAAc,CAAC,GAAG,UAAgB;;AAChD,QAAI,MAAM,MAAM;AAEd,UACE,MAAM,SAAS,UAAU,iBACzB,CAAC,qBAAqB,SAAS,MAAM,IAAI,GACzC;AACA,yBAAiB,IAAI,MAAM,IAAI;;AAIjC,UACE,MAAM,SAAS,UAAU,gBACzB,KAAA,MAAM,eAAS,QAAA,OAAA,SAAA,SAAA,GAAE,UAAS,UAAU,YACpC;AACA,YAAI,SAAS,cAAc;AACzB,gBAAM,gBAAe,KAAA,SAAS,kBAAY,QAAA,OAAA,SAAA,SAAA,GACtC,cAAa,EACd,KAAK,CAAC,EAAE,MAAAA,MAAI,MAAOA,UAAS,MAAM,IAAI;AACzC,cAAI,cAAc;AAChB;;AAEF,gBAAMH,QAAO,OAAO,QAAQ,MAAM,IAAI;AACtC,gBAAM,mBAAkB,KAAA,SAAS,kBAAY,QAAA,OAAA,SAAA,SAAA,GAAE,SAAQ;AACvD,mBAAS,eAAe,IAAI,qBAAoB,OAAA,OAAA,OAAA,OAAA,CAAA,GAC3C,eAAe,GAAA,EAClB,YAAY;YACV,GAAG,gBAAgB;YAClBA,SACC,IAAI,qBAAqB,EAAE,MAAM,MAAM,MAAM,QAAQ,CAAA,EAAE,CAAE;YAC5D,CAAA,CAAA;mBAEM,SAAS,eAAe;AACjC,gBAAM,gBAAe,KAAA,SAAS,mBAAa,QAAA,OAAA,SAAA,SAAA,GACvC,cAAa,EACd,KAAK,CAAC,EAAE,MAAAG,MAAI,MAAOA,UAAS,MAAM,IAAI;AACzC,cAAI,cAAc;AAChB;;AAEF,gBAAMH,QAAO,OAAO,QAAQ,MAAM,IAAI;AACtC,gBAAM,oBAAmB,KAAA,SAAS,mBAAa,QAAA,OAAA,SAAA,SAAA,GAAE,SAAQ;AACzD,mBAAS,gBAAgB,IAAI,kBAAiB,OAAA,OAAA,OAAA,OAAA,CAAA,GACzC,gBAAgB,GAAA,EACnB,YAAY;YACV,GAAG,iBAAiB;YACnBA,SACC,IAAI,qBAAqB,EAAE,MAAM,MAAM,MAAM,QAAQ,CAAA,EAAE,CAAE;YAC5D,CAAA,CAAA;;;;EAKX,CAAC;AAED,QAAM,sBAAsB,SAAS,gBAAgB,SAAS;AAE9D,QAAM,qBAAoB,wBAAmB,QAAnB,wBAAmB,SAAA,SAAnB,oBAAqB,cAAa,MAAM,CAAA;AAClE,QAAM,wBAAwB,kBAAkB,IAAI,CAAC,EAAE,MAAAG,MAAI,MAAOA,KAAI;AAGtE,QAAM,qBAAqB,iBACxB,OACC,CAAC,GAAG,gBAAgB,EAAE,IAAI,CAAAA,WAAS,EAAE,MAAAA,MAAI,EAA2B,CAAC,EAEtE,OACC,CAAC,EAAE,MAAAA,MAAI,MACLA,WAAS,wBAAmB,QAAnB,wBAAmB,SAAA,SAAnB,oBAAqB,SAC9B,CAAC,sBAAsB,SAASA,KAAI,CAAC;AAG3C,SAAO,SACL,OACA,mBAAmB,IAAI,CAAAH,UAAO;AAC5B,UAAM,SAAS;MACb,OAAOA,MAAK;MACZ,MAAMC,oBAAmB;MACzB,MAAAD;;AAEF,QAAIA,UAAI,QAAJA,UAAI,SAAA,SAAJA,MAAM,aAAa;AACrB,aAAO,gBAAgBA,MAAK;;AAW9B,WAAO;EACT,CAAC,CAAC;AAEN;AAEA,SAAS,wCACP,OACA,UACA,QACA,OAAoC;AAEpC,MAAI;AACJ,MAAI,SAAS,YAAY;AACvB,QAAI,eAAe,SAAS,UAAU,GAAG;AACvC,YAAM,eAAe,mBAAmB,SAAS,UAAU;AAG3D,YAAM,mBAAmB,OAAO,iBAAiB,YAAY;AAC7D,YAAM,mBAAmB,uBAAO,OAAO,IAAI;AAC3C,iBAAWA,SAAQ,kBAAkB;AACnC,mBAAW,SAASA,MAAK,cAAa,GAAI;AACxC,2BAAiB,MAAM,IAAI,IAAI;;;AAGnC,sBAAgB,iBAAiB,OAAO,aAAa,gBAAgB,CAAC;WACjE;AAGL,sBAAgB,CAAC,SAAS,UAAU;;SAEjC;AACL,UAAM,UAAU,OAAO,WAAU;AACjC,oBAAgB,aAAa,OAAO,EAAE,OACpC,CAAAA,UAAQ,gBAAgBA,KAAI,KAAK,CAACA,MAAK,KAAK,WAAW,IAAI,CAAC;;AAGhE,SAAO,SACL,OACA,cAAc,IAAI,CAAAA,UAAO;AACvB,UAAM,YAAY,aAAaA,KAAI;AACnC,WAAO;MACL,OAAO,OAAOA,KAAI;MAClB,gBAAgB,cAAS,QAAT,cAAS,SAAA,SAAT,UAAW,gBAAsC;MACjE,MAAMC,oBAAmB;;EAE7B,CAAC,CAAC;AAEN;AAEA,SAAS,gCACP,OACA,UACA,QACA,WACA,cAAuC;AAEvC,MAAI,CAAC,WAAW;AACd,WAAO,CAAA;;AAET,QAAM,UAAU,OAAO,WAAU;AACjC,QAAM,WAAW,mBAAmB,MAAM,KAAK;AAC/C,QAAM,YAAY,uBAAuB,SAAS;AAElD,MAAI,gBAAgB,aAAa,SAAS,GAAG;AAC3C,cAAU,KAAK,GAAG,YAAY;;AAIhC,QAAM,gBAAgB,UAAU,OAC9B,UAEE,QAAQ,KAAK,cAAc,KAAK,KAAK,KAErC,EACE,YACA,SAAS,SAAS,UAAU,uBAC5B,SAAS,SAAS,KAAK,KAAK,UAG9B,gBAAgB,SAAS,UAAU,KACnC,gBAAgB,QAAQ,KAAK,cAAc,KAAK,KAAK,CAAC,KACtD,eACE,QACA,SAAS,YACT,QAAQ,KAAK,cAAc,KAAK,KAAK,CAAyB,CAC/D;AAGL,SAAO,SACL,OACA,cAAc,IAAI,WAAS;IACzB,OAAO,KAAK,KAAK;IACjB,QAAQ,OAAO,QAAQ,KAAK,cAAc,KAAK,KAAK,CAAC;IACrD,eAAe,YAAY,KAAK,KAAK,KAAK,OAAO,KAAK,cAAc,KAAK,KAAK;IAC9E,cAAc;MACZ,QAAQ,YAAY,KAAK,KAAK,KAAK,OAAO,KAAK,cAAc,KAAK,KAAK;;IAEzE,MAAMA,oBAAmB;IACzB,MAAM,QAAQ,KAAK,cAAc,KAAK,KAAK;IAC3C,CAAC;AAEP;AAGA,IAAM,sBAAsB,CAAC,OAAc,SAAkB;;AAC3D,QAAI,KAAA,MAAM,eAAS,QAAA,OAAA,SAAA,SAAA,GAAE,UAAS,MAAM;AAClC,WAAO,MAAM;;AAEf,QAAI,MAAA,KAAA,MAAM,eAAS,QAAA,OAAA,SAAA,SAAA,GAAE,eAAS,QAAA,OAAA,SAAA,SAAA,GAAE,UAAS,MAAM;AAC7C,WAAO,MAAM,UAAU;;AAEzB,QAAI,MAAA,MAAA,KAAA,MAAM,eAAS,QAAA,OAAA,SAAA,SAAA,GAAE,eAAS,QAAA,OAAA,SAAA,SAAA,GAAE,eAAS,QAAA,OAAA,SAAA,SAAA,GAAE,UAAS,MAAM;AACxD,WAAO,MAAM,UAAU,UAAU;;AAEnC,QAAI,MAAA,MAAA,MAAA,KAAA,MAAM,eAAS,QAAA,OAAA,SAAA,SAAA,GAAE,eAAS,QAAA,OAAA,SAAA,SAAA,GAAE,eAAS,QAAA,OAAA,SAAA,SAAA,GAAE,eAAS,QAAA,OAAA,SAAA,SAAA,GAAE,UAAS,MAAM;AACnE,WAAO,MAAM,UAAU,UAAU,UAAU;;AAE/C;AAEM,SAAU,uBACd,WACA,QACA,OAAmB;AAEnB,MAAI,eAA8B;AAClC,MAAI;AACJ,QAAM,cAAmC,uBAAO,OAAO,CAAA,CAAE;AAEzD,kBAAgB,WAAW,CAAC,GAAG,UAAgB;;AAE7C,SAAI,UAAK,QAAL,UAAK,SAAA,SAAL,MAAO,UAAS,UAAU,YAAY,MAAM,MAAM;AACpD,qBAAe,MAAM;;AAEvB,SAAI,UAAK,QAAL,UAAK,SAAA,SAAL,MAAO,UAAS,UAAU,cAAc,cAAc;AACxD,YAAM,mBAAmB,oBAAoB,OAAO,UAAU,IAAI;AAClE,UAAI,qBAAgB,QAAhB,qBAAgB,SAAA,SAAhB,iBAAkB,MAAM;AAC1B,uBAAe,OAAO,QACpB,qBAAgB,QAAhB,qBAAgB,SAAA,SAAhB,iBAAkB,IAAI;;;AAK5B,QAAI,gBAAgB,gBAAgB,CAAC,YAAY,YAAY,GAAG;AAG9D,YAAM,gBACJ,MAAM,WAAW,SAAO,KAAA,UAAK,QAAL,UAAK,SAAA,SAAL,MAAO,WAAK,QAAA,OAAA,SAAA,SAAA,GAAE,UAAS,aAC3C,eACA,MAAM;AACZ,kBAAY,YAAY,IAAI;QAC1B,QAAQ,aAAa,SAAQ;QAC7B,YAAY;QACZ,OAAO,MAAM;QACb,WAAW;QACX,MAAM;QACN,MAAMA,oBAAmB;;AAG3B,qBAAe;AACf,qBAAe;;EAEnB,CAAC;AAED,SAAO,aAAa,WAAW;AACjC;AAEM,SAAU,uBACd,WAAiB;AAEjB,QAAM,eAAyC,CAAA;AAC/C,kBAAgB,WAAW,CAAC,GAAG,UAAgB;AAC7C,QACE,MAAM,SAAS,UAAU,uBACzB,MAAM,QACN,MAAM,MACN;AACA,mBAAa,KAAK;QAChB,MAAM,UAAU;QAChB,MAAM;UACJ,MAAM,KAAK;UACX,OAAO,MAAM;;QAGf,cAAc;UACZ,MAAM,UAAU;UAChB,YAAY,CAAA;;QAGd,eAAe;UACb,MAAM,UAAU;UAChB,MAAM;YACJ,MAAM,KAAK;YACX,OAAO,MAAM;;;OAGlB;;EAEL,CAAC;AAED,SAAO;AACT;AAEA,SAAS,oCACP,OACA,QACA,OAAa;AAEb,QAAM,eAAe,OAAO,WAAU;AACtC,QAAM,aAAa,aAAa,YAAY,EAAE,OAAO,WAAW;AAChE,SAAO,SACL,OAEA,WAAW,IAAI,CAACD,WAA4B;IAC1C,OAAOA,MAAK;IACZ,gBAAeA,UAAI,QAAJA,UAAI,SAAA,SAAJA,MAAM,gBAAe;IACpC,MAAMC,oBAAmB;IACzB,CAAC;AAEP;AAEA,SAAS,2BACP,OACA,OACA,QACA,OAAa;;AAEb,OAAI,KAAA,MAAM,eAAS,QAAA,OAAA,SAAA,SAAA,GAAE,MAAM;AACzB,UAAM,aAAa,OAChB,cAAa,EACb,OAAO,eAAa,gBAAgB,MAAM,WAAW,SAAS,CAAC;AAClE,WAAO,SACL,OACA,WAAW,IAAI,gBAAc;MAC3B,OAAO,UAAU;MACjB,gBAAe,cAAS,QAAT,cAAS,SAAA,SAAT,UAAW,gBAAe;MACzC,MAAMA,oBAAmB;MACzB,CAAC;;AAGP,SAAO,CAAA;AACT;AAIA,SAAS,oCACP,OACA,OACA,QACA,OAAa;AAEb,QAAM,YAAY,OAAO,cAAa,EAAG,KAAK,OAAK,EAAE,SAAS,MAAM,IAAI;AACxE,SAAO,SACL,QACA,cAAS,QAAT,cAAS,SAAA,SAAT,UAAW,KAAK,IAAI,UAAQ;IAC1B,OAAO,IAAI;IACX,eAAe,IAAI,eAAe;IAClC,MAAMA,oBAAmB;IACzB,MAAK,CAAA,CAAE;AAEb;AAEM,SAAU,gBACd,OACA,WAA2B;AAE3B,MAAI,EAAC,UAAK,QAAL,UAAK,SAAA,SAAL,MAAO,OAAM;AAChB,WAAO;;AAET,QAAM,EAAE,MAAM,UAAS,IAAK;AAC5B,QAAM,EAAE,UAAS,IAAK;AACtB,UAAQ,MAAM;IACZ,KAAK,UAAU;AACb,aAAO,UAAU,SAAS,kBAAkB,KAAK;IACnD,KAAK,UAAU;AACb,aAAO,UAAU,SAAS,kBAAkB,QAAQ;IACtD,KAAK,UAAU;AACb,aAAO,UAAU,SAAS,kBAAkB,YAAY;IAC1D,KAAK,UAAU;IACf,KAAK,UAAU;AACb,aAAO,UAAU,SAAS,kBAAkB,KAAK;IACnD,KAAK,UAAU;AACb,aAAO,UAAU,SAAS,kBAAkB,mBAAmB;IACjE,KAAK,UAAU;AACb,aAAO,UAAU,SAAS,kBAAkB,eAAe;IAC7D,KAAK,UAAU;AACb,aAAO,UAAU,SAAS,kBAAkB,eAAe;IAG7D,KAAK,UAAU;AACb,aAAO,UAAU,SAAS,kBAAkB,MAAM;IACpD,KAAK,UAAU;AACb,aAAO,UAAU,SAAS,kBAAkB,MAAM;IACpD,KAAK,UAAU;AACb,aAAO,UAAU,SAAS,kBAAkB,MAAM;IACpD,KAAK,UAAU;AACb,aAAO,UAAU,SAAS,kBAAkB,gBAAgB;IAC9D,KAAK,UAAU;AACb,aAAO,UAAU,SAAS,kBAAkB,SAAS;IACvD,KAAK,UAAU;AACb,aAAO,UAAU,SAAS,kBAAkB,KAAK;IACnD,KAAK,UAAU;AACb,aAAO,UAAU,SAAS,kBAAkB,IAAI;IAClD,KAAK,UAAU;AACb,aAAO,UAAU,SAAS,kBAAkB,UAAU;IACxD,KAAK,UAAU;AACb,aAAO,UAAU,SAAS,kBAAkB,YAAY;IAC1D,KAAK,UAAU;AACb,YAAM,gBAAgB,cAAS,QAAT,cAAS,SAAA,SAAT,UAAW;AACjC,cAAQ,eAAe;QACrB,KAAK,UAAU;AACb,iBAAO,UAAU,SAAS,kBAAkB,mBAAmB;QACjE,KAAK,UAAU;AACb,iBAAO,UAAU,SAAS,kBAAkB,sBAAsB;;;AAI1E,SAAO;AACT;AAEA,SAAS,WAAW,OAAY;AAC9B,MACE,MAAM,aACN,MAAM,QAEJ;IACE,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IAEZ,SAAS,MAAM,IAAI,GACrB;AACA,WAAO,WAAW,MAAM,SAAS;;AAEnC,SAAO;AACT;;;AC9hCM,IAAOG,SAAP,MAAY;EAGhB,YAAY,OAAkB,KAAc;AAa5C,SAAA,mBAAmB,CAAC,aAAgC;AAClD,UAAI,KAAK,MAAM,SAAS,SAAS,MAAM;AACrC,eAAO,KAAK,MAAM,aAAa,SAAS;;AAE1C,UAAI,KAAK,IAAI,SAAS,SAAS,MAAM;AACnC,eAAO,KAAK,IAAI,aAAa,SAAS;;AAExC,aAAO,KAAK,MAAM,QAAQ,SAAS,QAAQ,KAAK,IAAI,QAAQ,SAAS;IACvE;AApBE,SAAK,QAAQ;AACb,SAAK,MAAM;EACb;EAEA,SAAS,MAAc,WAAiB;AACtC,SAAK,QAAQ,IAAIC,UAAS,MAAM,SAAS;EAC3C;EAEA,OAAO,MAAc,WAAiB;AACpC,SAAK,MAAM,IAAIA,UAAS,MAAM,SAAS;EACzC;;AAaI,IAAOA,YAAP,MAAe;EAGnB,YAAY,MAAc,WAAiB;AAa3C,SAAA,oBAAoB,CAAC,aACnB,KAAK,OAAO,SAAS,QACpB,KAAK,SAAS,SAAS,QAAQ,KAAK,aAAa,SAAS;AAd3D,SAAK,OAAO;AACZ,SAAK,YAAY;EACnB;EAEA,QAAQ,MAAY;AAClB,SAAK,OAAO;EACd;EAEA,aAAa,WAAiB;AAC5B,SAAK,YAAY;EACnB;;;;AC7CF,wBAAuB;AAsBhB,IAAM,gCAAgC,CAC3C,iBACA,wBAC4B;AAC5B,MAAI,CAAC,qBAAqB;AACxB,WAAO,CAAA;;AAGT,QAAM,gBAAgB,oBAAI,IAAG;AAC7B,QAAM,sBAAsB,oBAAI,IAAG;AAEnC,QAAM,iBAAiB;IACrB,mBAAmB,MAAI;AACrB,oBAAc,IAAI,KAAK,KAAK,OAAO,IAAI;IACzC;IACA,eAAe,MAAI;AACjB,UAAI,CAAC,oBAAoB,IAAI,KAAK,KAAK,KAAK,GAAG;AAC7C,4BAAoB,IAAI,KAAK,KAAK,KAAK;;IAE3C;GACD;AAED,QAAM,OAAO,oBAAI,IAAG;AACpB,aAAWC,SAAQ,qBAAqB;AACtC,QAAI,CAAC,cAAc,IAAIA,KAAI,KAAK,oBAAoB,IAAIA,KAAI,GAAG;AAC7D,WAAK,QAAI,kBAAAC,SAAW,oBAAoB,IAAID,KAAI,CAAC,CAAC;;;AAItD,QAAM,sBAAgD,CAAA;AAEtD,aAAW,OAAO,MAAM;AACtB,UAAM,KAAK;MACT,eAAe,MAAI;AACjB,YACE,CAAC,oBAAoB,IAAI,KAAK,KAAK,KAAK,KACxC,oBAAoB,IAAI,KAAK,KAAK,KAAK,GACvC;AACA,eAAK,QAAI,kBAAAC,SAAW,oBAAoB,IAAI,KAAK,KAAK,KAAK,CAAC,CAAC;AAC7D,8BAAoB,IAAI,KAAK,KAAK,KAAK;;MAE3C;KACD;AACD,QAAI,CAAC,cAAc,IAAI,IAAI,KAAK,KAAK,GAAG;AACtC,0BAAoB,KAAK,GAAG;;;AAIhC,SAAO;AACT;;;ACxCA,IAAM,oBAAoB;EACxB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;;AAMI,SAAU,wBACd,QACA,KACA,aACA,mBACA,kBAA0B;AAE1B,QAAM,QAAQ,eAAe,OAAO,UAAO;AAIzC,QAAI,SAAS,yBAAyB,SAAS,2BAA2B;AACxE,aAAO;;AAET,QAAI,qBAAqB,SAAS,wBAAwB;AACxD,aAAO;;AAET,WAAO;EACT,CAAC;AAED,MAAI,aAAa;AACf,UAAM,UAAU,KAAK,MAAM,OAAO,WAAW;;AAE/C,MAAI,kBAAkB;AACpB,UAAM,UAAU,KAAK,MAAM,OAAO,iBAAiB;;AAErD,QAAM,SAAS,SAAS,QAAQ,KAAK,KAAK;AAC1C,SAAO,OAAO,OAAO,WAAQ;AAC3B,QAAI,MAAM,QAAQ,SAAS,mBAAmB,KAAK,MAAM,OAAO;AAC9D,YAAM,OAAO,MAAM,MAAM,CAAC;AAC1B,UAAI,QAAQ,KAAK,SAAS,KAAK,WAAW;AACxC,cAAMC,QAAO,KAAK,KAAK;AACvB,YAAIA,UAAS,eAAeA,UAAS,uBAAuB;AAC1D,iBAAO;;;;AAIb,WAAO;EACT,CAAC;AACH;;;AC9EM,SAAU,iBACd,QACA,aAAyB;AAEzB,QAAM,iBAAiC,uBAAO,OAAO,IAAI;AAEzD,aAAW,cAAc,YAAY,aAAa;AAChD,QAAI,WAAW,SAAS,uBAAuB;AAC7C,YAAM,EAAE,oBAAmB,IAAK;AAChC,UAAI,qBAAqB;AACvB,mBAAW,EAAE,UAAU,MAAAC,MAAI,KAAM,qBAAqB;AACpD,gBAAM,YAAY,YAChB,QACAA,KAAqB;AAEvB,cAAI,WAAW;AACb,2BAAe,SAAS,KAAK,KAAK,IAAI;qBAEtCA,MAAK,SAAS,KAAK,cAEnBA,MAAK,KAAK,UAAU,SACpB;AACA,2BAAe,SAAS,KAAK,KAAK,IAAI;;;;;;AAMhD,SAAO;AACT;;;ACTM,SAAU,qBACd,aACA,QAA6B;AAE7B,QAAM,iBAAiB,SACnB,iBAAiB,QAAQ,WAAW,IACpC;AAGJ,QAAM,aAAwC,CAAA;AAE9C,QAAM,aAAa;IACjB,oBAAoB,MAAI;AACtB,iBAAW,KAAK,IAAI;IACtB;GACD;AAED,SAAO,EAAE,gBAAgB,WAAU;AACrC;AAkBc,SAAP,kBACL,QACA,gBAA8B;AAE9B,MAAI,CAAC,gBAAgB;AACnB;;AAGF,MAAI;AACF,UAAM,cAAc,MAAM,cAAc;AACxC,WAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GACK,qBAAqB,aAAa,MAAM,CAAC,GAAA,EAC5C,YAAW,CAAA;WAEb,IAAM;AACN;;AAEJ;;;AC7DO,IAAM,WAAW;EACtB,OAAO;EACP,SAAS;EACT,aAAa;EACb,MAAM;;AAOD,IAAM,sBAAsB;EACjC,CAAC,SAAS,KAAK,GAAG;EAClB,CAAC,SAAS,OAAO,GAAG;EACpB,CAAC,SAAS,WAAW,GAAG;EACxB,CAAC,SAAS,IAAI,GAAG;;AAGnB,IAAM,YAAY,CAAC,WAAgB,YAAmB;AACpD,MAAI,CAAC,WAAW;AACd,UAAM,IAAI,MAAM,OAAO;;AAE3B;AAEM,SAAU,eACd,OACA,SAA2C,MAC3C,aACA,mBACA,mBAAqD;;AAErD,MAAI,MAAM;AACV,MAAI,YAAY;AAChB,MAAI,mBAAmB;AACrB,gBACE,OAAO,sBAAsB,WACzB,oBACA,kBAAkB,OAChB,CAAC,KAAK,SAAS,MAAM,MAAM,IAAI,IAAI,QACnC,EAAE;;AAGZ,QAAM,gBAAgB,YAAY,GAAG,KAAK;;EAAO,SAAS,KAAK;AAC/D,MAAI;AACF,UAAM,MAAM,aAAa;WAClB,OAAO;AACd,QAAI,iBAAiB,cAAc;AACjC,YAAM,QAAQ,UACZ,MAAA,KAAA,MAAM,eAAS,QAAA,OAAA,SAAA,SAAA,GAAG,CAAC,OAAC,QAAA,OAAA,SAAA,KAAI,EAAE,MAAM,GAAG,QAAQ,EAAC,GAC5C,aAAa;AAGf,aAAO;QACL;UACE,UAAU,oBAAoB;UAC9B,SAAS,MAAM;UACf,QAAQ;UACR;;;;AAIN,UAAM;;AAGR,SAAO,cAAc,KAAK,QAAQ,aAAa,iBAAiB;AAClE;AAEM,SAAU,cACd,KACA,SAA2C,MAC3C,aACA,mBAA2B;AAG3B,MAAI,CAAC,QAAQ;AACX,WAAO,CAAA;;AAGT,QAAM,6BAA6B,wBACjC,QACA,KACA,aACA,iBAAiB,EACjB,QAAQ,WACR,YAAY,OAAO,oBAAoB,OAAO,YAAY,CAAC;AAI7D,QAAM,gCAAgC,SAAS,QAAQ,KAAK;IAC1D;GACD,EAAE,QAAQ,WACT,YAAY,OAAO,oBAAoB,SAAS,aAAa,CAAC;AAEhE,SAAO,2BAA2B,OAAO,6BAA6B;AACxE;AAEA,SAAS,YACP,OACA,UACAC,OAAY;AAEZ,MAAI,CAAC,MAAM,OAAO;AAChB,WAAO,CAAA;;AAET,QAAM,mBAAiC,CAAA;AACvC,aAAW,CAAC,GAAG,IAAI,KAAK,MAAM,MAAM,QAAO,GAAI;AAC7C,UAAM,gBACJ,KAAK,SAAS,cAAc,UAAU,QAAQ,KAAK,SAAS,SACxD,KAAK,OACL,cAAc,QAAQ,KAAK,aAAa,SACtC,KAAK,WACL;AACR,QAAI,eAAe;AACjB,gBACE,MAAM,WACN,8CAA8C;AAKhD,YAAM,MAAM,MAAM,UAAU,CAAC;AAC7B,YAAM,eAAe,YAAY,aAAa;AAC9C,YAAM,MAAM,IAAI,UAAU,aAAa,MAAM,aAAa;AAC1D,uBAAiB,KAAK;QACpB,QAAQ,YAAYA,KAAI;QACxB,SAAS,MAAM;QACf;QACA,OAAO,IAAIC,OACT,IAAIC,UAAS,IAAI,OAAO,GAAG,IAAI,SAAS,CAAC,GACzC,IAAIA,UAAS,IAAI,OAAO,GAAG,GAAG,CAAC;OAElC;;;AAGL,SAAO;AACT;AAEM,SAAU,SAAS,UAA0B,WAAiB;AAClE,QAAM,SAAS,aAAY;AAC3B,QAAM,QAAQ,OAAO,WAAU;AAC/B,QAAM,QAAQ,UAAU,MAAM,IAAI;AAElC,YACE,MAAM,UAAU,SAAS,MACzB,+DAA+D;AAGjE,MAAI,SAAS;AAEb,WAAS,IAAI,GAAG,IAAI,SAAS,MAAM,KAAK;AACtC,aAAS,IAAI,gBAAgB,MAAM,CAAC,CAAC;AACrC,WAAO,CAAC,OAAO,IAAG,GAAI;AACpB,YAAM,QAAQ,OAAO,MAAM,QAAQ,KAAK;AACxC,UAAI,UAAU,eAAe;AAC3B;;;;AAKN,YAAU,QAAQ,yCAAyC;AAC3D,QAAM,OAAO,SAAS,OAAO;AAE7B,QAAM,QAAQ,OAAO,gBAAe;AAEpC,QAAM,MAAM,OAAO,mBAAkB;AACrC,SAAO,IAAID,OAAM,IAAIC,UAAS,MAAM,KAAK,GAAG,IAAIA,UAAS,MAAM,GAAG,CAAC;AACrE;AASA,SAAS,YAAY,MAAS;AAC5B,QAAM,iBAAiB;AACvB,QAAM,WAAW,eAAe;AAChC,YAAU,UAAU,sCAAsC;AAG1D,SAAO;AACT;", "names": ["nullthrows", "GraphQLDocumentMode", "type", "name", "DocumentUri", "URI", "integer", "<PERSON><PERSON><PERSON><PERSON>", "Position", "Range", "Location", "LocationLink", "Color", "ColorInformation", "ColorPresentation", "FoldingRangeKind", "FoldingRange", "DiagnosticRelatedInformation", "DiagnosticSeverity", "DiagnosticTag", "CodeDescription", "Diagnostic", "Command", "TextEdit", "ChangeAnnotation", "ChangeAnnotationIdentifier", "AnnotatedTextEdit", "TextDocumentEdit", "CreateFile", "RenameFile", "DeleteFile", "WorkspaceEdit", "TextDocumentIdentifier", "VersionedTextDocumentIdentifier", "OptionalVersionedTextDocumentIdentifier", "TextDocumentItem", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CompletionItemKind", "InsertTextFormat", "CompletionItemTag", "InsertReplaceEdit", "InsertTextMode", "CompletionItemLabelDetails", "CompletionItem", "CompletionList", "MarkedString", "Hover", "ParameterInformation", "SignatureInformation", "DocumentHighlightKind", "DocumentHighlight", "SymbolKind", "SymbolTag", "SymbolInformation", "name", "WorkspaceSymbol", "DocumentSymbol", "CodeActionKind", "CodeActionTriggerKind", "CodeActionContext", "CodeAction", "CodeLens", "FormattingOptions", "DocumentLink", "SelectionRange", "SemanticTokenTypes", "SemanticTokenModifiers", "SemanticTokens", "InlineValueText", "InlineValueVariableLookup", "InlineValueEvaluatableExpression", "InlineValueContext", "InlayHintKind", "InlayHintLabelPart", "InlayHint", "StringValue", "InlineCompletionItem", "InlineCompletionList", "InlineCompletionTriggerKind", "SelectedCompletionInfo", "InlineCompletionContext", "WorkspaceFolder", "TextDocument", "p", "Is", "undefined", "integer", "<PERSON><PERSON><PERSON><PERSON>", "CompletionItemKind", "list", "type", "type", "CompletionItemKind", "_a", "name", "Range", "Position", "name", "nullthrows", "name", "type", "type", "Range", "Position"]}