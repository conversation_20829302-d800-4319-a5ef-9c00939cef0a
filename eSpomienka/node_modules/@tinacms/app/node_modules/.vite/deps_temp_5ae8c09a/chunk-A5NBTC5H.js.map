{"version": 3, "sources": ["../../../../../node_modules/codemirror/addon/edit/matchbrackets.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  var ie_lt8 = /MSIE \\d/.test(navigator.userAgent) &&\n    (document.documentMode == null || document.documentMode < 8);\n\n  var Pos = CodeMirror.Pos;\n\n  var matching = {\"(\": \")>\", \")\": \"(<\", \"[\": \"]>\", \"]\": \"[<\", \"{\": \"}>\", \"}\": \"{<\", \"<\": \">>\", \">\": \"<<\"};\n\n  function bracketRegex(config) {\n    return config && config.bracketRegex || /[(){}[\\]]/\n  }\n\n  function findMatchingBracket(cm, where, config) {\n    var line = cm.getLineHandle(where.line), pos = where.ch - 1;\n    var afterCursor = config && config.afterCursor\n    if (afterCursor == null)\n      afterCursor = /(^| )cm-fat-cursor($| )/.test(cm.getWrapperElement().className)\n    var re = bracketRegex(config)\n\n    // A cursor is defined as between two characters, but in in vim command mode\n    // (i.e. not insert mode), the cursor is visually represented as a\n    // highlighted box on top of the 2nd character. Otherwise, we allow matches\n    // from before or after the cursor.\n    var match = (!afterCursor && pos >= 0 && re.test(line.text.charAt(pos)) && matching[line.text.charAt(pos)]) ||\n        re.test(line.text.charAt(pos + 1)) && matching[line.text.charAt(++pos)];\n    if (!match) return null;\n    var dir = match.charAt(1) == \">\" ? 1 : -1;\n    if (config && config.strict && (dir > 0) != (pos == where.ch)) return null;\n    var style = cm.getTokenTypeAt(Pos(where.line, pos + 1));\n\n    var found = scanForBracket(cm, Pos(where.line, pos + (dir > 0 ? 1 : 0)), dir, style, config);\n    if (found == null) return null;\n    return {from: Pos(where.line, pos), to: found && found.pos,\n            match: found && found.ch == match.charAt(0), forward: dir > 0};\n  }\n\n  // bracketRegex is used to specify which type of bracket to scan\n  // should be a regexp, e.g. /[[\\]]/\n  //\n  // Note: If \"where\" is on an open bracket, then this bracket is ignored.\n  //\n  // Returns false when no bracket was found, null when it reached\n  // maxScanLines and gave up\n  function scanForBracket(cm, where, dir, style, config) {\n    var maxScanLen = (config && config.maxScanLineLength) || 10000;\n    var maxScanLines = (config && config.maxScanLines) || 1000;\n\n    var stack = [];\n    var re = bracketRegex(config)\n    var lineEnd = dir > 0 ? Math.min(where.line + maxScanLines, cm.lastLine() + 1)\n                          : Math.max(cm.firstLine() - 1, where.line - maxScanLines);\n    for (var lineNo = where.line; lineNo != lineEnd; lineNo += dir) {\n      var line = cm.getLine(lineNo);\n      if (!line) continue;\n      var pos = dir > 0 ? 0 : line.length - 1, end = dir > 0 ? line.length : -1;\n      if (line.length > maxScanLen) continue;\n      if (lineNo == where.line) pos = where.ch - (dir < 0 ? 1 : 0);\n      for (; pos != end; pos += dir) {\n        var ch = line.charAt(pos);\n        if (re.test(ch) && (style === undefined ||\n                            (cm.getTokenTypeAt(Pos(lineNo, pos + 1)) || \"\") == (style || \"\"))) {\n          var match = matching[ch];\n          if (match && (match.charAt(1) == \">\") == (dir > 0)) stack.push(ch);\n          else if (!stack.length) return {pos: Pos(lineNo, pos), ch: ch};\n          else stack.pop();\n        }\n      }\n    }\n    return lineNo - dir == (dir > 0 ? cm.lastLine() : cm.firstLine()) ? false : null;\n  }\n\n  function matchBrackets(cm, autoclear, config) {\n    // Disable brace matching in long lines, since it'll cause hugely slow updates\n    var maxHighlightLen = cm.state.matchBrackets.maxHighlightLineLength || 1000,\n      highlightNonMatching = config && config.highlightNonMatching;\n    var marks = [], ranges = cm.listSelections();\n    for (var i = 0; i < ranges.length; i++) {\n      var match = ranges[i].empty() && findMatchingBracket(cm, ranges[i].head, config);\n      if (match && (match.match || highlightNonMatching !== false) && cm.getLine(match.from.line).length <= maxHighlightLen) {\n        var style = match.match ? \"CodeMirror-matchingbracket\" : \"CodeMirror-nonmatchingbracket\";\n        marks.push(cm.markText(match.from, Pos(match.from.line, match.from.ch + 1), {className: style}));\n        if (match.to && cm.getLine(match.to.line).length <= maxHighlightLen)\n          marks.push(cm.markText(match.to, Pos(match.to.line, match.to.ch + 1), {className: style}));\n      }\n    }\n\n    if (marks.length) {\n      // Kludge to work around the IE bug from issue #1193, where text\n      // input stops going to the textarea whenever this fires.\n      if (ie_lt8 && cm.state.focused) cm.focus();\n\n      var clear = function() {\n        cm.operation(function() {\n          for (var i = 0; i < marks.length; i++) marks[i].clear();\n        });\n      };\n      if (autoclear) setTimeout(clear, 800);\n      else return clear;\n    }\n  }\n\n  function doMatchBrackets(cm) {\n    cm.operation(function() {\n      if (cm.state.matchBrackets.currentlyHighlighted) {\n        cm.state.matchBrackets.currentlyHighlighted();\n        cm.state.matchBrackets.currentlyHighlighted = null;\n      }\n      cm.state.matchBrackets.currentlyHighlighted = matchBrackets(cm, false, cm.state.matchBrackets);\n    });\n  }\n\n  function clearHighlighted(cm) {\n    if (cm.state.matchBrackets && cm.state.matchBrackets.currentlyHighlighted) {\n      cm.state.matchBrackets.currentlyHighlighted();\n      cm.state.matchBrackets.currentlyHighlighted = null;\n    }\n  }\n\n  CodeMirror.defineOption(\"matchBrackets\", false, function(cm, val, old) {\n    if (old && old != CodeMirror.Init) {\n      cm.off(\"cursorActivity\", doMatchBrackets);\n      cm.off(\"focus\", doMatchBrackets)\n      cm.off(\"blur\", clearHighlighted)\n      clearHighlighted(cm);\n    }\n    if (val) {\n      cm.state.matchBrackets = typeof val == \"object\" ? val : {};\n      cm.on(\"cursorActivity\", doMatchBrackets);\n      cm.on(\"focus\", doMatchBrackets)\n      cm.on(\"blur\", clearHighlighted)\n    }\n  });\n\n  CodeMirror.defineExtension(\"matchBrackets\", function() {matchBrackets(this, true);});\n  CodeMirror.defineExtension(\"findMatchingBracket\", function(pos, config, oldConfig){\n    // Backwards-compatibility kludge\n    if (oldConfig || typeof config == \"boolean\") {\n      if (!oldConfig) {\n        config = config ? {strict: true} : null\n      } else {\n        oldConfig.strict = config\n        config = oldConfig\n      }\n    }\n    return findMatchingBracket(this, pos, config)\n  });\n  CodeMirror.defineExtension(\"scanForBracket\", function(pos, dir, style, config){\n    return scanForBracket(this, pos, dir, style, config);\n  });\n});\n"], "mappings": ";;;;;;;;;;;AAGA,KAAC,SAASA,GAAK;AAEXA,QAAIC,GAA+B,CAAA;IAKtC,GAAE,SAASC,GAAY;AACtB,UAAIC,IAAS,UAAU,KAAK,UAAU,SAAS,MAC5C,SAAS,gBAAgB,QAAQ,SAAS,eAAe,IAExDC,IAAMF,EAAW,KAEjBG,IAAW,EAAC,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,KAAI;AAEtG,eAASC,EAAaC,GAAQ;AAC5B,eAAOA,KAAUA,EAAO,gBAAgB;MACzC;AAFQC,QAAAF,GAAA,cAAA;AAIT,eAASG,EAAoBC,GAAIC,GAAOJ,GAAQ;AAC9C,YAAIK,IAAOF,EAAG,cAAcC,EAAM,IAAI,GAAGE,IAAMF,EAAM,KAAK,GACtDG,IAAcP,KAAUA,EAAO;AAC/BO,aAAe,SACjBA,IAAc,0BAA0B,KAAKJ,EAAG,kBAAiB,EAAG,SAAS;AAC/E,YAAIK,IAAKT,EAAaC,CAAM,GAMxBS,IAAS,CAACF,KAAeD,KAAO,KAAKE,EAAG,KAAKH,EAAK,KAAK,OAAOC,CAAG,CAAC,KAAKR,EAASO,EAAK,KAAK,OAAOC,CAAG,CAAC,KACrGE,EAAG,KAAKH,EAAK,KAAK,OAAOC,IAAM,CAAC,CAAC,KAAKR,EAASO,EAAK,KAAK,OAAO,EAAEC,CAAG,CAAC;AAC1E,YAAI,CAACG;AAAO,iBAAO;AACnB,YAAIC,IAAMD,EAAM,OAAO,CAAC,KAAK,MAAM,IAAI;AACvC,YAAIT,KAAUA,EAAO,UAAWU,IAAM,MAAOJ,KAAOF,EAAM;AAAK,iBAAO;AACtE,YAAIO,IAAQR,EAAG,eAAeN,EAAIO,EAAM,MAAME,IAAM,CAAC,CAAC,GAElDM,IAAQC,EAAeV,GAAIN,EAAIO,EAAM,MAAME,KAAOI,IAAM,IAAI,IAAI,EAAE,GAAGA,GAAKC,GAAOX,CAAM;AAC3F,eAAIY,KAAS,OAAa,OACnB;UAAC,MAAMf,EAAIO,EAAM,MAAME,CAAG;UAAG,IAAIM,KAASA,EAAM;UAC/C,OAAOA,KAASA,EAAM,MAAMH,EAAM,OAAO,CAAC;UAAG,SAASC,IAAM;QAAC;MACtE;AAtBQT,QAAAC,GAAA,qBAAA;AA+BT,eAASW,EAAeV,GAAIC,GAAOM,GAAKC,GAAOX,GAAQ;AAQrD,iBAPIc,IAAcd,KAAUA,EAAO,qBAAsB,KACrDe,IAAgBf,KAAUA,EAAO,gBAAiB,KAElDgB,IAAQ,CAAA,GACRR,IAAKT,EAAaC,CAAM,GACxBiB,IAAUP,IAAM,IAAI,KAAK,IAAIN,EAAM,OAAOW,GAAcZ,EAAG,SAAQ,IAAK,CAAC,IACrD,KAAK,IAAIA,EAAG,UAAS,IAAK,GAAGC,EAAM,OAAOW,CAAY,GACrEG,IAASd,EAAM,MAAMc,KAAUD,GAASC,KAAUR,GAAK;AAC9D,cAAIL,IAAOF,EAAG,QAAQe,CAAM;AAC5B,cAAKb,GACL;AAAA,gBAAIC,IAAMI,IAAM,IAAI,IAAIL,EAAK,SAAS,GAAGc,IAAMT,IAAM,IAAIL,EAAK,SAAS;AACvE,gBAAI,EAAAA,EAAK,SAASS;AAElB,mBADII,KAAUd,EAAM,SAAME,IAAMF,EAAM,MAAMM,IAAM,IAAI,IAAI,KACnDJ,KAAOa,GAAKb,KAAOI,GAAK;AAC7B,oBAAIU,IAAKf,EAAK,OAAOC,CAAG;AACxB,oBAAIE,EAAG,KAAKY,CAAE,MAAMT,MAAU,WACTR,EAAG,eAAeN,EAAIqB,GAAQZ,IAAM,CAAC,CAAC,KAAK,QAAQK,KAAS,MAAM;AACrF,sBAAIF,IAAQX,EAASsB,CAAE;AACvB,sBAAIX,KAAUA,EAAM,OAAO,CAAC,KAAK,OAASC,IAAM;AAAIM,sBAAM,KAAKI,CAAE;2BACvDJ,EAAM;AACXA,sBAAM,IAAG;;AADU,2BAAO,EAAC,KAAKnB,EAAIqB,GAAQZ,CAAG,GAAG,IAAIc,EAAE;gBAAA;cAAA;UAAA;QAAA;AAKnE,eAAOF,IAASR,MAAQA,IAAM,IAAIP,EAAG,SAAU,IAAGA,EAAG,UAAS,KAAM,QAAQ;MAC7E;AA1BQF,QAAAY,GAAA,gBAAA;AA4BT,eAASQ,EAAclB,GAAImB,GAAWtB,GAAQ;AAK5C,iBAHIuB,IAAkBpB,EAAG,MAAM,cAAc,0BAA0B,KACrEqB,IAAuBxB,KAAUA,EAAO,sBACtCyB,IAAQ,CAAE,GAAEC,IAASvB,EAAG,eAAc,GACjCwB,IAAI,GAAGA,IAAID,EAAO,QAAQC,KAAK;AACtC,cAAIlB,IAAQiB,EAAOC,CAAC,EAAE,MAAK,KAAMzB,EAAoBC,GAAIuB,EAAOC,CAAC,EAAE,MAAM3B,CAAM;AAC/E,cAAIS,MAAUA,EAAM,SAASe,MAAyB,UAAUrB,EAAG,QAAQM,EAAM,KAAK,IAAI,EAAE,UAAUc,GAAiB;AACrH,gBAAIZ,IAAQF,EAAM,QAAQ,+BAA+B;AACzDgB,cAAM,KAAKtB,EAAG,SAASM,EAAM,MAAMZ,EAAIY,EAAM,KAAK,MAAMA,EAAM,KAAK,KAAK,CAAC,GAAG,EAAC,WAAWE,EAAK,CAAC,CAAC,GAC3FF,EAAM,MAAMN,EAAG,QAAQM,EAAM,GAAG,IAAI,EAAE,UAAUc,KAClDE,EAAM,KAAKtB,EAAG,SAASM,EAAM,IAAIZ,EAAIY,EAAM,GAAG,MAAMA,EAAM,GAAG,KAAK,CAAC,GAAG,EAAC,WAAWE,EAAK,CAAC,CAAC;UAAA;QAAA;AAI/F,YAAIc,EAAM,QAAQ;AAGZ7B,eAAUO,EAAG,MAAM,WAASA,EAAG,MAAA;AAEnC,cAAIyB,IAAQ3B,EAAA,WAAW;AACrBE,cAAG,UAAU,WAAW;AACtB,uBAASwB,IAAI,GAAGA,IAAIF,EAAM,QAAQE;AAAKF,kBAAME,CAAC,EAAE,MAAK;YAC/D,CAAS;UACT,GAJkB,OAAA;AAKZ,cAAIL;AAAW,uBAAWM,GAAO,GAAG;;AAC/B,mBAAOA;QAAA;MAEf;AA5BQ3B,QAAAoB,GAAA,eAAA;AA8BT,eAASQ,EAAgB1B,GAAI;AAC3BA,UAAG,UAAU,WAAW;AAClBA,YAAG,MAAM,cAAc,yBACzBA,EAAG,MAAM,cAAc,qBAAA,GACvBA,EAAG,MAAM,cAAc,uBAAuB,OAEhDA,EAAG,MAAM,cAAc,uBAAuBkB,EAAclB,GAAI,OAAOA,EAAG,MAAM,aAAa;QACnG,CAAK;MACF;AARQF,QAAA4B,GAAA,iBAAA;AAUT,eAASC,EAAiB3B,GAAI;AACxBA,UAAG,MAAM,iBAAiBA,EAAG,MAAM,cAAc,yBACnDA,EAAG,MAAM,cAAc,qBAAA,GACvBA,EAAG,MAAM,cAAc,uBAAuB;MAEjD;AALQF,QAAA6B,GAAA,kBAAA,GAOTnC,EAAW,aAAa,iBAAiB,OAAO,SAASQ,GAAI4B,GAAKC,GAAK;AACjEA,aAAOA,KAAOrC,EAAW,SAC3BQ,EAAG,IAAI,kBAAkB0B,CAAe,GACxC1B,EAAG,IAAI,SAAS0B,CAAe,GAC/B1B,EAAG,IAAI,QAAQ2B,CAAgB,GAC/BA,EAAiB3B,CAAE,IAEjB4B,MACF5B,EAAG,MAAM,gBAAgB,OAAO4B,KAAO,WAAWA,IAAM,CAAA,GACxD5B,EAAG,GAAG,kBAAkB0B,CAAe,GACvC1B,EAAG,GAAG,SAAS0B,CAAe,GAC9B1B,EAAG,GAAG,QAAQ2B,CAAgB;MAEpC,CAAG,GAEDnC,EAAW,gBAAgB,iBAAiB,WAAW;AAAC0B,UAAc,MAAM,IAAI;MAAE,CAAC,GACnF1B,EAAW,gBAAgB,uBAAuB,SAASW,GAAKN,GAAQiC,GAAU;AAEhF,gBAAIA,KAAa,OAAOjC,KAAU,eAC3BiC,KAGHA,EAAU,SAASjC,GACnBA,IAASiC,KAHTjC,IAASA,IAAS,EAAC,QAAQ,KAAI,IAAI,OAMhCE,EAAoB,MAAMI,GAAKN,CAAM;MAChD,CAAG,GACDL,EAAW,gBAAgB,kBAAkB,SAASW,GAAKI,GAAKC,GAAOX,GAAO;AAC5E,eAAOa,EAAe,MAAMP,GAAKI,GAAKC,GAAOX,CAAM;MACvD,CAAG;IACH,CAAC;EAAA,EAAA,IAAA,EAAA;;;", "names": ["mod", "require$$0", "CodeMirror", "ie_lt8", "Pos", "matching", "bracketRegex", "config", "__name", "findMatchingBracket", "cm", "where", "line", "pos", "afterCursor", "re", "match", "dir", "style", "found", "scanForBracket", "maxScanLen", "maxScanLines", "stack", "lineEnd", "lineNo", "end", "ch", "matchBrackets", "autoclear", "maxHighlightLen", "highlightNonMatching", "marks", "ranges", "i", "clear", "doMatchBrackets", "clearHighlighted", "val", "old", "oldConfig"]}