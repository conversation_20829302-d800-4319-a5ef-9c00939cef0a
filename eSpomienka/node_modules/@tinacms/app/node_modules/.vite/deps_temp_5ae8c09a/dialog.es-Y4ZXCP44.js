import {
  k
} from "./chunk-7ARKPMCG.js";
import {
  hu
} from "./chunk-MEKKV4OY.js";
import "./chunk-AUZ3RYOM.js";

// node_modules/@graphiql/react/dist/dialog.es.js
var s = Object.defineProperty;
var n = (e, o) => s(e, "name", { value: o, configurable: true });
function c(e, o) {
  for (var a = 0; a < o.length; a++) {
    const r = o[a];
    if (typeof r != "string" && !Array.isArray(r)) {
      for (const t in r)
        if (t !== "default" && !(t in e)) {
          const i = Object.getOwnPropertyDescriptor(r, t);
          i && Object.defineProperty(e, t, i.get ? i : {
            enumerable: true,
            get: () => r[t]
          });
        }
    }
  }
  return Object.freeze(Object.defineProperty(e, Symbol.toStringTag, { value: "Module" }));
}
n(c, "_mergeNamespaces");
var f = k();
var p = hu(f);
var y = c({
  __proto__: null,
  default: p
}, [f]);
export {
  y as d
};
//# sourceMappingURL=dialog.es-Y4ZXCP44.js.map
