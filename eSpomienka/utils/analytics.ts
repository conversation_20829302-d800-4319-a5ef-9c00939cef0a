// Google Analytics tracking functions
declare global {
  interface Window {
    gtag: (...args: any[]) => void
  }
}

export const trackEvent = (action: string, category: string, label?: string, value?: number) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value,
    })
  }
}

export const trackPackageClick = (packageName: string, packagePrice: string) => {
  trackEvent('click', 'package', packageName, parseInt(packagePrice.replace('€', '')))
}

export const trackCTAClick = (ctaType: 'phone' | 'email', location: string) => {
  trackEvent('click', 'cta', `${ctaType}_${location}`)
}

export const trackPageView = (pageName: string) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', 'GA_MEASUREMENT_ID', {
      page_title: pageName,
      page_location: window.location.href,
    })
  }
}
